package header

import (
	"net/http"
	"strings"

	"flexproxy/common/constants"
)

// FlexProxyInternalHeaders 定义所有需要清理的FlexProxy内部标记
var FlexProxyInternalHeaders = []string{
	// 重试相关头部
	constants.HeaderRetrySame,
	constants.HeaderRetryNew,
	constants.HeaderRetryCount,
	constants.HeaderRetryDelay,
	constants.HeaderRetryProxy,
	constants.HeaderRotationMode,

	// 绕过代理相关头部
	constants.HeaderBypass,
	constants.HeaderBypassTimeout,
	constants.HeaderBypassKeepAlive,
	constants.HeaderBypassDNS,

	// 缓存响应相关头部
	constants.HeaderCacheResponse,
	constants.HeaderCacheDuration,
	constants.HeaderCacheKey,
	constants.HeaderCacheScope,

	// 保存到代理池相关头部
	constants.HeaderSaveToPool,
	constants.HeaderQualityTier,
	constants.HeaderMinScore,
	constants.HeaderPoolName,

	// 空响应相关头部
	constants.HeaderNullResponse,
	constants.HeaderNullStatus,
	constants.HeaderNullContentType,

	// 脚本执行器相关头部
	constants.HeaderScriptExecutor,
	constants.HeaderScriptEngine,
	constants.HeaderScriptTimeout,
	constants.HeaderScriptType,

	// 请求URL执行器相关头部
	constants.HeaderRequestURL,
	constants.HeaderRequestMethod,
	constants.HeaderRequestTimeout,
	constants.HeaderRequestRetries,

	// 缓存执行器相关头部
	constants.HeaderCacheExecutor,
	constants.HeaderCacheMaxUse,
	constants.HeaderIgnoreParams,

	// IP域名封禁相关头部
	constants.HeaderBanIPDomain,
	constants.HeaderBanTarget,
	constants.HeaderBanDuration,
	constants.HeaderBanReason,

	// 硬编码的内部标记
	"X-FlexProxy-Excluded",
	"X-FlexProxy-Blocked",
	"X-FlexProxy-TempBanned",
	"X-FlexProxy-ProxyPool",
	"X-FlexProxy-Target-URL",
	"X-FlexProxy-Request-Body",
	"X-FlexProxy-No-Redirect",
}

// CleanInternalHeaders 清理HTTP请求头中的所有FlexProxy内部标记
// 此函数应在发送请求到外部服务器之前调用，以防止内部标记泄露
func CleanInternalHeaders(header http.Header) {
	if header == nil {
		return
	}

	// 清理预定义的内部标记
	for _, internalHeader := range FlexProxyInternalHeaders {
		header.Del(internalHeader)
	}

	// 清理动态生成的X-FlexProxy-Header-*标记
	var headersToRemove []string
	for headerName := range header {
		if strings.HasPrefix(strings.ToLower(headerName), "x-flexproxy-header-") {
			headersToRemove = append(headersToRemove, headerName)
		}
	}

	// 删除动态标记
	for _, headerName := range headersToRemove {
		header.Del(headerName)
	}
}

// CleanInternalHeadersFromRequest 清理HTTP请求中的所有FlexProxy内部标记
// 这是CleanInternalHeaders的便捷包装函数
func CleanInternalHeadersFromRequest(req *http.Request) {
	if req != nil && req.Header != nil {
		CleanInternalHeaders(req.Header)
	}
}

// IsInternalHeader 检查给定的头部名称是否为FlexProxy内部标记
func IsInternalHeader(headerName string) bool {
	// 检查预定义的内部标记
	for _, internalHeader := range FlexProxyInternalHeaders {
		if strings.EqualFold(headerName, internalHeader) {
			return true
		}
	}

	// 检查动态生成的标记
	if strings.HasPrefix(strings.ToLower(headerName), "x-flexproxy-header-") {
		return true
	}

	return false
}
