package plugin

import (
	"net/http"
	"time"
)

// AuthResult 认证结果
type AuthResult struct {
	Authenticated bool
	UserID        string
	Scopes        []string
	Token         string
	Error         error
}

// EncryptionResult 加密结果
type EncryptionResult struct {
	Ciphertext string
	Nonce      string
	Error      error
}

// PluginContext 插件上下文
type PluginContext struct {
	Request    *http.Request
	Response   *http.Response
	Data       map[string]interface{}
	PluginName string
	HookType   string
	Timestamp  time.Time // 执行时间戳
}

// PluginResult 插件执行结果
type PluginResult struct {
	PluginName string
	Success    bool
	Data       map[string]interface{}
	Error      error
	Duration   time.Duration
	Continue   bool // 是否继续执行后续插件
}

// PluginStats 插件统计信息
type PluginStats struct {
	TotalPlugins         int
	ActivePlugins        int
	DisabledPlugins      int
	TotalExecutions      int64
	SuccessfulExecutions int64
	FailedExecutions     int64
	AverageExecutionTime time.Duration
	HookCounts           map[string]int64         // 各钩子类型的执行次数
	PluginDetails        map[string]*LoadedPlugin // 插件详细信息
	EnabledPlugins       []string                 // 启用的插件列表
}

// LoadedPlugin 已加载的插件信息
type LoadedPlugin struct {
	Name        string
	Path        string
	Enabled     bool
	Config      map[string]interface{}
	LoadedAt    time.Time
	LastUsed    time.Time
	Executions  int64
	Successes   int64
	Failures    int64
	Version     string
	Description string
	ErrorCount  int64 // 错误次数
	LastError   error // 最后一次错误
}
