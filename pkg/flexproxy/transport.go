package flexproxy

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"strings"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
	"flexproxy/pkg/helper/awsurl"
	"h12.io/socks"
)

// 模块级别的日志器
var transportLogger = logger.GetTransportLogger()

// Transport 自动在 HTTP/S 或 SOCKS v4(A) & v5 代理之间切换传输。
//
// 根据协议方案，返回带有 http.Transport.Dialer 或 http.Transport.Proxy 的 http.Transport 值。
// 如果协议方案是 "aws"，将返回默认的 http.Transport。
//
// 注意：httpProxyDNS参数已废弃，建议使用TransportWithDNSResolver函数
func Transport(p string, dnsMode string, reverseDNSResolver interface{}, httpProxyDNS string) (*http.Transport, error) {
	// 为了向后兼容，保持原有接口，但忽略httpProxyDNS参数
	return TransportWithDNSResolver(p, dnsMode, reverseDNSResolver, nil, "")
}

// TransportWithDNSResolver 使用DNS解析器创建传输层
func TransportWithDNSResolver(p string, dnsMode string, reverseDNSResolver interface{}, dnsResolver interface{}, httpProxyDNS string) (*http.Transport, error) {
	var proxyURL *url.URL
	var err error
	// socks5 服务器只支持 socks 代理
	if ServerType == "socks5" && !strings.HasPrefix(p, constants.SOCKS5Prefix) {
		return nil, errors.NewErrorWithDetails(errors.ErrTypeProxy, errors.ErrCodeProxyProtocolMismatch, "SOCKS5服务器只支持SOCKS代理", "代理: "+p+", 服务器类型: "+ServerType)
	}
	tr := new(http.Transport)

	// 解析代理URL
	if !awsurl.IsURL(p) {
		proxyURL, err = url.Parse(p)
		if err != nil {
			return nil, err
		}
	}

	// 设置DNS解析器
	if dnsMode == "remote" && proxyURL != nil {
		// 根据代理类型配置远程DNS解析
		switch proxyURL.Scheme {
		case "http", "https":
			// 使用HTTP代理进行DNS解析
			tr.Proxy = http.ProxyURL(proxyURL)

			// 应用DNS解析器配置
			if dnsResolver != nil {
				// 使用提供的DNS解析器
				tr.DialContext = createDialContextWithDNSResolver(dnsResolver)
			} else {
				// 使用默认的DialContext
				tr.DialContext = func(ctx context.Context, network, address string) (net.Conn, error) {
					return (&net.Dialer{}).DialContext(ctx, network, address)
				}
			}
		case "socks4", "socks4a", "socks5":
			// 使用SOCKS代理进行DNS解析
			// nolint: staticcheck
			tr.Dial = socks.Dial(p)
		default:
			return nil, errors.WrapErrorWithDetails(ErrUnsupportedProxyProtocolScheme, errors.ErrTypeProxy, errors.ErrCodeProxyProtocolUnsupported, "不支持的代理协议", "协议: "+proxyURL.Scheme)
		}
	} else {
		// 默认本地解析
		tr.DialContext = (&net.Dialer{}).DialContext

		// 如果是HTTP/HTTPS代理，设置Proxy
		if proxyURL != nil {
			switch proxyURL.Scheme {
			case "socks4", "socks4a", "socks5":
				// nolint: staticcheck
				tr.Dial = socks.Dial(p)
			case "http", "https":
				tr.Proxy = http.ProxyURL(proxyURL)
			}
		}
	}

	// 添加反向DNS解析支持
	if reverseDNSResolver != nil {
		// 检查是否是"no"模式的反向DNS解析器
		isNoMode := false

		// 安全地检查反向DNS解析器的模式
		if rdnsResolver, ok := reverseDNSResolver.(interface{ GetMode() string }); ok {
			// 使用类型断言检查是否成功
			mode := rdnsResolver.GetMode()
			if mode == "no" {
				isNoMode = true
				transportLogger.Debug("反向DNS解析模式为'no'，跳过反向DNS解析")
			}
		} else {
			// 如果类型断言失败，记录日志并默认不进行反向DNS解析
			transportLogger.Debug("反向DNS解析器不支持GetMode方法，默认跳过反向DNS解析")
			isNoMode = true
		}

		// 只有在非"no"模式下才进行反向DNS解析
		if !isNoMode {
			// 保存原始的DialContext
			originalDialContext := tr.DialContext

			// 创建新的DialContext，添加反向DNS解析
			tr.DialContext = func(ctx context.Context, network, address string) (net.Conn, error) {
				// 先使用原始的DialContext建立连接
				conn, err := originalDialContext(ctx, network, address)
				if err != nil {
					return nil, err
				}

				// 如果是TCP连接，尝试进行反向DNS解析
				if tcpConn, ok := conn.(*net.TCPConn); ok {
					remoteAddr := tcpConn.RemoteAddr().(*net.TCPAddr)
					if resolver, ok := reverseDNSResolver.(interface {
						LookupAddr(ctx context.Context, addr string) ([]string, error)
					}); ok {
						// 异步进行反向DNS解析，不阻塞连接
						go func() {
							// 添加恢复机制，防止panic
							defer func() {
								if r := recover(); r != nil {
									transportLogger.Error(fmt.Sprintf("反向DNS解析崩溃: %v", r))
								}
							}()

							// 确保resolver不为nil
							if resolver == nil {
								transportLogger.Warn("反向DNS解析器为空，跳过解析")
								return
							}

							names, err := resolver.LookupAddr(ctx, remoteAddr.IP.String())
							if err == nil && len(names) > 0 {
								// 记录解析结果
								transportLogger.GetRawLogger().Debugf("反向DNS解析结果: %s -> %v", remoteAddr.IP.String(), names)
							} else if err != nil {
								transportLogger.GetRawLogger().Debugf("反向DNS解析失败: %v", err)
							}
						}()
					}
				}

				return conn, nil
			}
		}
	}

	// AWS URL处理
	if awsurl.IsURL(p) {
		return tr, errors.WrapErrorWithDetails(ErrUnsupportedProxyProtocolScheme, errors.ErrTypeProxy, errors.ErrCodeProxyProtocolUnsupported, "AWS协议不支持作为代理传输", "URL: "+p)
	}

	tr.DisableKeepAlives = true
	tr.TLSClientConfig = &tls.Config{
		InsecureSkipVerify: true,
		MinVersion:         tls.VersionTLS10,
		CipherSuites:       getUnsafeCipherSuites(),
	}

	return tr, nil
}

// createDialContextWithDNSResolver 使用DNS解析器创建DialContext函数
func createDialContextWithDNSResolver(dnsResolver interface{}) func(ctx context.Context, network, address string) (net.Conn, error) {
	return func(ctx context.Context, network, address string) (net.Conn, error) {
		host, port, err := net.SplitHostPort(address)
		if err != nil {
			return nil, err
		}

		// 检查是否已经是IP地址
		if net.ParseIP(host) != nil {
			return (&net.Dialer{}).DialContext(ctx, network, address)
		}

		// 尝试使用DNS解析器解析主机名
		if resolver, ok := dnsResolver.(interface {
			LookupIP(ctx context.Context, host string) ([]net.IP, error)
		}); ok {
			ips, err := resolver.LookupIP(ctx, host)
			if err == nil && len(ips) > 0 {
				// 使用第一个解析到的IP地址
				resolvedAddress := net.JoinHostPort(ips[0].String(), port)
				return (&net.Dialer{}).DialContext(ctx, network, resolvedAddress)
			}
			// 解析失败时记录日志但继续使用原始地址
			transportLogger.GetRawLogger().Debugf("DNS解析失败，使用原始地址: %s, 错误: %v", host, err)
		}

		// 回退到默认行为
		return (&net.Dialer{}).DialContext(ctx, network, address)
	}
}



