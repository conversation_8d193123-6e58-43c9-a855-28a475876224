package constants

import "time"

// 应用程序常量
const (
	// 应用信息
	AppName    = "FlexProxy"
	AppVersion = "1.0.0"
	UserAgent  = AppName + "/" + AppVersion
)

// 网络相关常量
const (
	// 默认端口
	DefaultHTTPPort  = 8080
	DefaultHTTPSPort = 8443
	DefaultSOCKSPort = 1080

	// 默认主机地址
	DefaultServerHost = "0.0.0.0"

	// 超时设置
	DefaultConnectTimeout = 30 * time.Second
	DefaultReadTimeout    = 60 * time.Second
	DefaultWriteTimeout   = 60 * time.Second
	DefaultIdleTimeout    = 120 * time.Second

	// 连接池设置
	DefaultMaxIdleConns        = 100
	DefaultMaxIdleConnsPerHost = 10
	DefaultMaxConnsPerHost     = 50

	// 重试设置
	DefaultMaxRetries    = 3
	DefaultRetryInterval = 1 * time.Second
	MaxRetryInterval     = 30 * time.Second

	// 代理连接重试设置
	DefaultProxyConnectionRetryEnabled = true
	DefaultProxyConnectionMaxRetries   = 3
	DefaultProxyConnectionRetryDelay   = "1s"
	DefaultProxyConnectionRetryBackoff = 2.0
	DefaultProxyConnectionMaxDelay     = "10s"

	// 缓冲区大小
	DefaultBufferSize = 32 * 1024   // 32KB
	MaxBufferSize     = 1024 * 1024 // 1MB

	// 防抖延迟
	DefaultDebounceDelay = 100 * time.Millisecond
)

// 代理相关常量
const (
	// 代理类型
	ProxyTypeHTTP   = "http"
	ProxyTypeHTTPS  = "https"
	ProxyTypeSOCKS  = "socks"
	ProxyTypeSOCKS4 = "socks4"
	ProxyTypeSOCKS5 = "socks5"

	// 代理状态
	ProxyStatusActive   = "active"
	ProxyStatusInactive = "inactive"
	ProxyStatusError    = "error"
	ProxyStatusTesting  = "testing"

	// 代理选择策略
	StrategyRandom     = "random"
	StrategySequential = "sequential"
	StrategyQuality    = "quality"
	StrategySmart      = "smart"
	StrategyCustom     = "custom"

	// 代理质量等级
	QualityTierPremium  = "premium"  // 高质量代理
	QualityTierStandard = "standard" // 标准质量代理
	QualityTierBackup   = "backup"   // 备用代理
)

// 负载均衡常量
const (
	// 负载均衡算法
	LoadBalancerRoundRobin         = "round_robin"
	LoadBalancerWeightedRoundRobin = "weighted_round_robin"
	LoadBalancerLeastConnections   = "least_connections"
	LoadBalancerResponseTime       = "response_time"
	LoadBalancerIPHash             = "ip_hash"

	// 健康检查
	DefaultHealthCheckInterval = 30 * time.Second
	DefaultHealthCheckTimeout  = 5 * time.Second
	DefaultHealthCheckPath     = "/health"
	MaxConsecutiveFailures     = 3
	MaxConsecutiveSuccesses    = 2
)

// 缓存相关常量
const (
	// 缓存类型
	CacheTypeMemory = "memory"
	CacheTypeRedis  = "redis"
	CacheTypeFile   = "file"

	// 缓存默认设置
	DefaultCacheTTL        = 5 * time.Minute
	DefaultCacheTTLSeconds = 300 // 5分钟，单位秒
	DefaultCacheSize       = 1000
	DefaultCleanupInterval = 10 * time.Minute

	// 缓存键前缀
	CacheKeyProxyList   = "proxy:list"
	CacheKeyProxyStatus = "proxy:status:"
	CacheKeyUserSession = "user:session:"
	CacheKeyRateLimit   = "rate:limit:"
)

// 配置相关常量
const (
	// 配置文件路径
	DefaultConfigFile = "config.yaml"
	ConfigDirName     = "flexproxy"

	// 环境变量前缀
	EnvPrefix = "FLEXPROXY_"

	// 配置键
	ConfigKeyLogLevel   = "log.level"
	ConfigKeyLogFormat  = "log.format"
	ConfigKeyServerPort = "server.port"
	ConfigKeyProxyList  = "proxy.list"
	ConfigKeyStrategy   = "proxy.strategy"
)

// 高级配置模块常量
const (
	// 错误恢复配置默认值
	DefaultMaxRetryAttempts    = 3
	DefaultInitialRetryDelay   = "1s"
	DefaultMaxRetryDelay       = "30s"
	DefaultRetryMultiplier     = 2.0
	DefaultFailureThreshold    = 5
	DefaultSuccessThreshold    = 3
	DefaultCircuitTimeout      = "60s"
	DefaultCircuitResetTimeout = "300s"

	// 追踪配置默认值
	DefaultTracingEnabled     = true
	DefaultHexGeneratorLength = 16
	DefaultSequenceModulus    = 10000

	// 性能配置默认值
	DefaultWorkerPoolSize = 10
	DefaultQueueSize      = 1000
	DefaultBatchSize      = 100
	DefaultFlushInterval  = "5s"

	// 调试配置默认值
	DefaultDebugEnabled   = false
	DefaultVerboseLogging = false
	DefaultDumpRequests   = false
	DefaultDumpResponses  = false
	DefaultProfileEnabled = false
	DefaultProfilePort    = 8081

	// 历史记录配置
	DefaultMaxHistory = 1000

	// 风险评估配置默认值
	DefaultEventCountMultiplier           = 5.0    // 事件数量倍数阈值
	DefaultActionCountMultiplier          = 5.0    // 动作数量倍数阈值
	DefaultDataSizeThreshold              = "1GB"  // 数据大小阈值
	DefaultMaxDirectlyAffectedServices    = 3      // 最大直接影响服务数
	DefaultMaxIndirectlyAffectedServices  = 5      // 最大间接影响服务数
	DefaultMinSecurityScore               = 60     // 最低安全评分
	DefaultSecurityWarningScore           = 80     // 安全警告评分
	DefaultMaxPerformanceImpact           = 30     // 最大性能影响百分比

	// 无人值守模式配置默认值
	DefaultUnattendedModeEnabled          = false           // 无人值守模式默认关闭
	DefaultAutoApproveThreshold           = "medium"        // 自动批准阈值
	DefaultManualConfirmTimeout           = "300s"          // 手动确认超时时间
	DefaultFallbackStrategy               = "auto_approve"  // 默认降级策略
	DefaultMaxWaitTime                    = "5m"            // 最大等待时间
	DefaultEmergencyRollback              = true            // 紧急回滚开关
	DefaultBusinessHoursOnly              = false           // 仅业务时间模式
	DefaultBusinessHoursStart             = "09:00"         // 业务时间开始
	DefaultBusinessHoursEnd               = "18:00"         // 业务时间结束
	DefaultBusinessHoursTimezone          = "Local"         // 业务时间时区
	DefaultNotificationRetries            = 3               // 通知重试次数
)

// 风险等级常量
const (
	RiskLevelLow    = "low"    // 低风险
	RiskLevelMedium = "medium" // 中风险
	RiskLevelHigh   = "high"   // 高风险
)

// 降级策略常量
const (
	FallbackStrategyAutoApprove   = "auto_approve"   // 自动批准
	FallbackStrategyAutoRollback  = "auto_rollback"  // 自动回滚
	FallbackStrategyMaintainCurrent = "maintain_current" // 保持当前配置
	FallbackStrategyNotify        = "notify"         // 仅通知
)

// 热重载队列和工作协程常量
const (
	DefaultReloadQueueSize = 100  // 默认重载队列大小
	DefaultReloadWorkers   = 3    // 默认重载工作协程数
	MaxReloadQueueSize     = 1000 // 最大重载队列大小
	MaxReloadWorkers       = 10   // 最大重载工作协程数
)

// 配置验证范围常量
const (
	// 重试次数范围
	MinRetryAttempts = 0
	MaxRetryAttempts = 10

	// 十六进制生成器长度范围
	MinHexLength = 8
	MaxHexLength = 64

	// 工作池大小范围
	MinWorkerPoolSize = 1
	MaxWorkerPoolSize = 1000

	// 队列大小范围
	MinQueueSize = 1
	MaxQueueSize = 100000

	// 批次大小范围
	MinBatchSize = 1
	MaxBatchSize = 10000

	// 序列模数范围
	MinSequenceModulus = 1000
	MaxSequenceModulus = 1000000

	// 端口范围
	MinPort = 1024
	MaxPort = 65535
)

// 系统配置模块常量
const (
	// 系统检测配置
	DefaultOSDetection   = true
	DefaultArchDetection = true

	// 信号处理配置
	DefaultGracefulShutdown = true
	DefaultShutdownTimeout  = "30s"

	// 资源限制默认值
	DefaultMaxMemory      = "1GB"
	DefaultMaxCPU         = "2"
	DefaultMaxConnections = 1000
	DefaultMaxOpenFiles   = 1024

	// 系统信号常量
	SignalTERM = "SIGTERM"
	SignalINT  = "SIGINT"
	SignalHUP  = "SIGHUP"
	SignalUSR1 = "SIGUSR1"
	SignalUSR2 = "SIGUSR2"
)

// 路径配置模块常量
const (
	// 默认目录路径
	DefaultBaseDir    = "./"
	DefaultConfigDir  = "./config"
	DefaultLogsDir    = "./logs"
	DefaultDataDir    = "./data"
	DefaultTempDir    = "./temp"
	DefaultBackupDir  = "./backup"
	DefaultPluginsDir = "./plugins"

	// 默认文件权限
	DefaultFilePermission = 0644
	DefaultDirPermission  = 0755

	// 权限范围
	MinPermission = 0000
	MaxPermission = 0777

	// 文件扩展名常量
	ConfigFileExtension     = ".yaml"
	LogFileExtension        = ".log"
	DataFileExtension       = ".db"
	TempFileExtension       = ".tmp"
	BackupFileExtension     = ".bak"
	PluginFileExtension     = ".so"
	JSONFileExtension       = ".json"
	XMLFileExtension        = ".xml"
	ScriptFileExtensionJS   = ".js"
	ScriptFileExtensionLua  = ".lua"
	ScriptFileExtensionTxt  = ".txt"
)

// 协议配置模块常量
const (
	// HTTP配置默认值
	DefaultHTTPEnabled     = true
	DefaultHTTPVersion     = "1.1"
	DefaultHTTPKeepAlive   = true
	DefaultHTTPCompression = true
	DefaultHTTPTimeout     = "30s"

	// HTTPS配置默认值
	DefaultHTTPSEnabled     = true
	DefaultHTTPSVersion     = "1.1"
	DefaultHTTPSKeepAlive   = true
	DefaultHTTPSCompression = true
	DefaultHTTPSVerifySSL   = true
	DefaultHTTPSTimeout     = "30s"

	// SOCKS4配置默认值
	DefaultSOCKS4Enabled = false
	DefaultSOCKS4Timeout = "30s"

	// SOCKS5配置默认值
	DefaultSOCKS5Enabled      = true
	DefaultSOCKS5Timeout      = "30s"
	DefaultSOCKS5AuthRequired = false

	// DNS配置默认值
	DefaultDNSUDP   = true
	DefaultDNSTCP   = false
	DefaultDNSTLS   = false
	DefaultDNSHTTPS = false
	DefaultDNSDoH   = false
)

// 插件配置模块常量
const (
	// 插件系统配置
	DefaultPluginsEnabled  = true
	DefaultPluginsAutoLoad = true

	// 插件类型
	PluginTypeFilter    = "filter"
	PluginTypeTransform = "transform"
	PluginTypeAuth      = "auth"
	PluginTypeMonitor   = "monitor"
	PluginTypeCustom    = "custom"
)

// 开发配置模块常量
const (
	// 开发模式配置
	DefaultDevelopmentEnabled = false
	DefaultDevelopmentMode    = "production"
	DefaultHotReload          = false

	// 测试配置默认值
	DefaultTestingEnabled = false
	DefaultMockResponses  = false
	DefaultTestDataDir    = "./testdata"

	// 性能分析配置默认值
	DefaultProfilingEnabled = false
	DefaultCPUProfile       = false
	DefaultMemoryProfile    = false
	DefaultBlockProfile     = false
	DefaultMutexProfile     = false

	// 开发模式类型
	ModeDevelopment = "development"
	ModeProduction  = "production"
	ModeTesting     = "testing"
	ModeStaging     = "staging"
)

// 配置管理模块常量
const (
	// 热重载配置默认值
	DefaultHotReloadEnabled     = false
	DefaultReloadInterval       = "30s"
	DefaultBackupOnReload       = true
	DefaultRollbackOnError      = true
	DefaultConfigDebounceDelay  = "2s"  // 重命名避免与网络防抖延迟冲突
	DefaultMaxReloadRetries     = 3

	// 验证配置默认值
	DefaultStrictMode        = false
	DefaultFailFast          = false
	DefaultValidateOnStartup = true
	DefaultValidateOnReload  = true
	DefaultValidateOnSave    = true

	// 备份配置默认值
	DefaultBackupEnabled       = true
	DefaultMaxBackups          = 10
	DefaultBackupCompress      = true
	DefaultBackupAutoCleanup   = true
	DefaultBackupRetentionDays = 30
)

// 配置源和操作常量
const (
	// 配置源常量
	ConfigSourceAPI     = "api"
	ConfigSourceFile    = "file"
	ConfigSourceEnv     = "env"
	ConfigSourceCLI     = "cli"
	ConfigSourceDefault = "default"
	ConfigSourceUser    = "user"
	ConfigSourceSystem  = "system"

	// 配置操作原因常量
	ConfigReasonUpdate  = "config update"
	ConfigReasonReload  = "config reload"
	ConfigReasonManual  = "manual update"
	ConfigReasonDefault = "default value"
	ConfigReasonStartup = "startup initialization"
	ConfigReasonRestore = "restore from backup"
	ConfigReasonMigrate = "configuration migration"
)

// 配置路径常量
const (
	// 高级配置路径
	ConfigPathErrorRecovery                 = "error_recovery"
	ConfigPathErrorRecoveryMaxRetries       = "error_recovery.max_retry_attempts"
	ConfigPathErrorRecoveryInitialDelay     = "error_recovery.initial_retry_delay"
	ConfigPathErrorRecoveryMaxDelay         = "error_recovery.max_retry_delay"
	ConfigPathErrorRecoveryMultiplier       = "error_recovery.retry_multiplier"
	ConfigPathErrorRecoveryFailureThreshold = "error_recovery.failure_threshold"
	ConfigPathErrorRecoverySuccessThreshold = "error_recovery.success_threshold"
	ConfigPathErrorRecoveryCircuitTimeout   = "error_recovery.circuit_timeout"
	ConfigPathErrorRecoveryCircuitResetTimeout = "error_recovery.circuit_reset_timeout"

	// 追踪配置路径
	ConfigPathTracing          = "tracing"
	ConfigPathTracingEnabled   = "tracing.enabled"
	ConfigPathTracingHexLength = "tracing.hex_generator_length"
	ConfigPathTracingSequence  = "tracing.sequence_modulus"

	// 性能配置路径
	ConfigPathPerformance              = "performance"
	ConfigPathPerformanceWorkerPool    = "performance.worker_pool_size"
	ConfigPathPerformanceQueueSize     = "performance.queue_size"
	ConfigPathPerformanceBatchSize     = "performance.batch_size"
	ConfigPathPerformanceFlushInterval = "performance.flush_interval"

	// 调试配置路径
	ConfigPathDebug               = "debug"
	ConfigPathDebugEnabled        = "debug.enabled"
	ConfigPathDebugVerboseLogging = "debug.verbose_logging"
	ConfigPathDebugDumpRequests   = "debug.dump_requests"
	ConfigPathDebugDumpResponses  = "debug.dump_responses"
	ConfigPathDebugProfileEnabled = "debug.profile_enabled"
	ConfigPathDebugProfilePort    = "debug.profile_port"

	// 根配置路径
	ConfigPathRoot = "root"
)

// 时间间隔和大小限制常量
const (
	// 监控间隔
	DefaultMonitorInterval        = "30s"
	DefaultConfigHealthCheckInterval = "10s"  // 重命名避免与代理健康检查冲突
	DefaultMetricsInterval        = "60s"

	// 超时设置
	DefaultStartupTimeout = "60s"
	DefaultStopTimeout    = "30s"
	DefaultConfigTimeout  = "10s"

	// 配置重试间隔 (与网络重试间隔区分)
	DefaultConfigRetryInterval    = "1s"
	DefaultConfigMaxRetryInterval = "60s"

	// 清理间隔
	DefaultConfigCleanupInterval  = "24h"  // 重命名避免与缓存清理冲突
	DefaultTempCleanupInterval    = "1h"
	DefaultLogRotationInterval    = "24h"

	// 内存限制
	DefaultMaxMemoryMB = 1024 // 1GB
	DefaultMaxMemoryGB = 1

	// 文件大小限制
	DefaultMaxLogFileSize    = 100 * 1024 * 1024 // 100MB
	DefaultMaxConfigFileSize = 10 * 1024 * 1024  // 10MB
	DefaultMaxBackupFileSize = 50 * 1024 * 1024  // 50MB

	// 队列限制
	DefaultMaxQueueLength = 10000
	DefaultMaxBatchItems  = 1000

	// 连接限制
	DefaultMaxConcurrentConnections = 1000
	DefaultMaxIdleConnections       = 100
)

// 日志相关常量
const (
	// 日志级别
	LogLevelDebug = "debug"
	LogLevelInfo  = "info"
	LogLevelWarn  = "warn"
	LogLevelError = "error"
	LogLevelFatal = "fatal"

	// 日志格式
	LogFormatJSON = "json"
	LogFormatText = "text"

	// 时间格式
	TimeFormatDefault = "2006-01-02 15:04:05"
	TimeFormatISO8601 = "2006-01-02T15:04:05Z07:00"
	TimeFormatRFC3339 = time.RFC3339

	// 日志文件
	DefaultLogFile       = "flexproxy.log"
	DefaultLogMaxSize    = 100 // MB
	DefaultLogMaxAge     = 30  // 天
	DefaultLogMaxBackups = 10
)

// 注意：错误相关常量已迁移到 common/errors/errors.go 中统一管理

// HTTP 相关常量
const (
	// HTTP 方法
	MethodGET     = "GET"
	MethodPOST    = "POST"
	MethodPUT     = "PUT"
	MethodDELETE  = "DELETE"
	MethodPATCH   = "PATCH"
	MethodHEAD    = "HEAD"
	MethodOPTIONS = "OPTIONS"
	MethodCONNECT = "CONNECT"

	// HTTP 状态码
	StatusOK                  = 200
	StatusBadRequest          = 400
	StatusUnauthorized        = 401
	StatusForbidden           = 403
	StatusNotFound            = 404
	StatusMethodNotAllowed    = 405
	StatusTooManyRequests     = 429
	StatusInternalServerError = 500
	StatusBadGateway          = 502
	StatusServiceUnavailable  = 503
	StatusGatewayTimeout      = 504

	// HTTP 头
	HeaderContentType   = "Content-Type"
	HeaderContentLength = "Content-Length"
	HeaderAuthorization = "Authorization"
	HeaderUserAgent     = "User-Agent"
	HeaderXForwardedFor = "X-Forwarded-For"
	HeaderXRealIP       = "X-Real-IP"
	HeaderXRequestID    = "X-Request-ID"
	HeaderXTraceID      = "X-Trace-ID"

	// Content-Type 值
	ContentTypeJSON = "application/json"
	ContentTypeXML  = "application/xml"
	ContentTypeForm = "application/x-www-form-urlencoded"
	ContentTypeText = "text/plain"
	ContentTypeHTML = "text/html"
)

// 监控和指标常量
const (
	// 指标名称
	MetricRequestTotal      = "requests_total"
	MetricRequestDuration   = "request_duration_seconds"
	MetricProxyStatus       = "proxy_status"
	MetricConnectionsActive = "connections_active"
	MetricErrorsTotal       = "errors_total"

	// 指标标签
	LabelMethod    = "method"
	LabelStatus    = "status"
	LabelProxy     = "proxy"
	LabelStrategy  = "strategy"
	LabelErrorType = "error_type"
)

// 安全相关常量
const (
	// 认证类型
	AuthTypeNone   = "none"
	AuthTypeBasic  = "basic"
	AuthTypeBearer = "bearer"
	AuthTypeAPIKey = "apikey"

	// 加密算法
	EncryptionAES256 = "aes256"
	EncryptionRSA    = "rsa"

	// 默认密钥长度
	DefaultKeyLength = 32

	// 令牌过期时间
	DefaultTokenExpiry = 24 * time.Hour
)

// 限流相关常量
const (
	// 限流算法
	RateLimitTokenBucket   = "token_bucket"
	RateLimitLeakyBucket   = "leaky_bucket"
	RateLimitFixedWindow   = "fixed_window"
	RateLimitSlidingWindow = "sliding_window"

	// 默认限流设置
	DefaultRateLimit     = 1000 // 每分钟请求数
	DefaultBurstSize     = 100
	DefaultWindowSize    = 1 * time.Minute
	DefaultCleanupPeriod = 5 * time.Minute
)

// 文件和路径常量
const (
	// 文件权限
	FilePermission = 0644
	DirPermission  = 0755

	// 文件扩展名
	ExtYAML = ".yaml"
	ExtYML  = ".yml"
	ExtJSON = ".json"
	ExtTOML = ".toml"
	ExtLog  = ".log"

	// 目录名称
	DirLogs   = "logs"
	DirConfig = "config"
	DirData   = "data"
	DirTemp   = "temp"
	DirBackup = "backup"
)

// 系统相关常量
const (
	// 操作系统
	OSWindows = "windows"
	OSLinux   = "linux"
	OSDarwin  = "darwin"

	// 架构
	ArchAMD64 = "amd64"
	ArchARM64 = "arm64"
	Arch386   = "386"

	// 信号
	SigTERM = "SIGTERM"
	SigINT  = "SIGINT"
	SigHUP  = "SIGHUP"
	SigUSR1 = "SIGUSR1"
	SigUSR2 = "SIGUSR2"
)

// DNS相关常量
const (
	// DNS服务器
	DefaultDNSServer     = "8.8.8.8:53"
	DefaultPublicDNS     = "8.8.8.8"
	DefaultSecondaryDNS  = "8.8.4.4"
	DNSTimeout           = 5 * time.Second
	DefaultDNSTimeout    = 5 * time.Second
	DefaultDNSTimeoutMs  = 5000 // 毫秒
	DNSRetries           = 3

	// DNS模式
	DNSModeLocal  = "local"
	DNSModeRemote = "remote"
	DNSModeCustom = "custom"
	DNSModeSystem = "system"
	DNSModeHybrid = "hybrid"

	// DNS缓存相关
	DefaultDNSCacheEnabled = true
	DefaultDNSCacheDisabled = false
	DefaultDNSCacheTTLSeconds = 300 // 5分钟

	// HTTP代理DNS
	DefaultHTTPProxyDNSEnabled = false
	DefaultHTTPProxyDNSMode = "false"

	// 反向DNS查询
	DefaultReverseDNSLookup = "no"
	ReverseDNSLookupNo = "no"
	ReverseDNSLookupDNS = "dns"
	ReverseDNSLookupFile = "file"
)

// 正则表达式常量
const (
	// IP 地址正则
	RegexIPv4 = `^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`
	RegexIPv6 = `^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$`

	// URL 正则
	RegexURL = `^https?://[\w\-]+(\.[\w\-]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?$`

	// 端口正则
	RegexPort = `^([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$`

	// 域名正则
	RegexDomain = `^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?$`
)

// 策略相关常量
const (
	// 最小请求数阈值
	MinRequestsForStats = 10

	// 响应时间基准（毫秒）
	ResponseTimeBaseline = 5000

	// 质量评分相关
	DefaultQualityScore = 0.5
	SuccessRateWeight   = 0.7
	ResponseTimeWeight  = 0.3
	MaxFailureRate      = 0.8
	TopProxyRatio       = 0.3
	TopTargetsRatio     = 0.3

	// 响应时间平滑因子
	ResponseTimeSmoothingFactor = 0.2

	// 代理池相关常量
	LatencySmoothingFactor   = 0.2
	ErrorRateWeight          = 0.6
	LatencyWeight            = 0.4
	HighErrorRateThreshold   = 0.5
	MediumErrorRateThreshold = 0.3
	LowErrorRateThreshold    = 0.1

	// 代理管理器相关常量
	ProxyResponseTimeSmoothingFactor = 0.8
	ProxyLatencySmoothingFactor      = 0.2
	ProxySuccessRateWeight           = 0.7
	ProxyResponseTimeWeight          = 0.3

	// 代理质量评估权重
	StabilityWeight     = 0.15  // 稳定性权重
	UsageBalanceWeight  = 0.15  // 使用均衡权重

	// 动作相关常量
	DefaultRequestTimeoutMS = 5000
	DefaultMaxRedirects     = 10
	DefaultActionTimeoutMS  = 10000

	// 追踪相关常量 (保留原有的SequenceModulus)
	SequenceModulus = 1000000

	// AWS URL 前缀
	AWSURLPrefix = "aws://"

	// 凭证分隔符
	CredentialsSeparator = "@"
	CredentialsDelimiter = ":"

	// SOCKS5 协议前缀
	SOCKS5Prefix = "socks"

	// 默认监控端口
	DefaultMonitoringPort = 9090

	// 默认监控路径
	DefaultMonitoringPath = "/metrics"
)

// 动作类型常量
const (
	// 基础动作类型（8种）
	ActionTypeLog            = "log"
	ActionTypeBanIP          = "banip"
	ActionTypeBanDomain      = "ban_domain"
	ActionTypeBlockRequest   = "block_request"
	ActionTypeModifyRequest  = "modify_request"
	ActionTypeModifyResponse = "modify_response"
	ActionTypeCacheResponse  = "cache_response"
	ActionTypeScript         = "script"

	// 扩展动作类型（8种）
	ActionTypeRetry        = "retry"
	ActionTypeRetrySame    = "retry_same"
	ActionTypeSaveToPool   = "save_to_pool"
	ActionTypeCache        = "cache"
	ActionTypeRequestURL   = "request_url"
	ActionTypeBanIPDomain  = "banipdomain"
	ActionTypeNullResponse = "null_response"
	ActionTypeBypassProxy  = "bypass_proxy"
)

// 安全执行器相关常量
const (
	// 封禁时长常量（秒）
	DefaultBanDurationSeconds   = 3600     // 默认1小时
	RebootBanDurationSeconds    = 86400    // 重启封禁24小时
	PermanentBanDurationSeconds = 31536000 // 永久封禁1年（近似）

	// 特殊时长值
	BanDurationReboot = "reboot"

	// 默认封禁原因
	DefaultBlockReason = "请求被阻止"
	DefaultBanReason   = "安全策略封禁"

	// HTTP状态码范围（使用已有的常量）
	// MinHTTPStatusCode = 100  // 已在Modify动作相关常量中定义
	// MaxHTTPStatusCode = 599  // 已在Modify动作相关常量中定义

	// HTTP状态码常量
	HTTPStatusOK                  = 200
	HTTPStatusForbidden           = 403
	HTTPStatusBadGateway          = 502
	HTTPStatusServiceUnavailable  = 503

	// 默认阻止状态码
	DefaultBlockStatusCode = HTTPStatusForbidden

	// HTTP头部名称常量
	HeaderXFlexProxyBlocked    = "X-FlexProxy-Blocked"
	HeaderXFlexProxyReason     = "X-FlexProxy-Reason"
	HeaderXFlexProxyExcluded   = "X-FlexProxy-Excluded"
	HeaderXFlexProxyTempBanned = "X-FlexProxy-TempBanned"
	HeaderXFlexProxyProxyPool  = "X-FlexProxy-ProxyPool"
	HeaderXFlexProxyErrorType  = "X-FlexProxy-Error-Type"
	HeaderXFlexProxyVersion    = "X-Flexp-Version"           // FlexProxy版本头
	HeaderContentDisposition   = "Content-Disposition"       // 内容处置头
	HeaderCFConnectingIP       = "CF-Connecting-IP"
	HeaderTrueClientIP         = "True-Client-IP"
	HeaderXClientIP            = "X-Client-IP"
	HeaderXClusterClientIP     = "X-Cluster-Client-IP"

	// HTTP状态消息常量
	HTTPStatusOKMessage                  = "200 OK"
	HTTPStatusForbiddenMessage           = "403 Forbidden"
	HTTPStatusBadGatewayMessage          = "502 Bad Gateway"
	HTTPStatusServiceUnavailableMessage  = "503 Service Unavailable"

	// 代理相关错误消息常量
	MessageProxyBlocked           = "Blocked by FlexProxy"
	MessageProxyTempBanned        = "Temporarily banned by FlexProxy"
	MessageProxyConnectionFailed  = "Proxy connection failed"
	MessageProxyPoolExhausted     = "Proxy pool is empty or there are no available proxies, please retry later"

	// 默认封禁范围
	DefaultBanScope = "domain"

	// 代理管理默认值常量
	DefaultMaxProxyAttempts     = 10
	DefaultDNSTimeoutSeconds    = 5
	DefaultProxyRetryCount      = 3

	// 上下文键常量
	ContextKeyCurrentProxy      = "current_proxy"
	ContextKeyStartTime         = "startTime"
	ContextKeyDNSResult         = "dns_result_"
	ContextKeyDNSResolver       = "dns_resolver"
	ContextKeyDNSCacheEnabled   = "dns_cache_enabled"
	ContextKeyDNSCacheTTL       = "dns_cache_ttl"
	ContextKeyDNSTimeout        = "dns_timeout"
	ContextKeyDNSForceRefresh   = "dns_force_refresh"
	ContextKeyIPVersionPriority = "ip_version_priority"
	ContextKeyRetryCount        = "retry_count"
	ContextKeyRetrySameCount    = "retry_same_count"
	ContextKeyRetrySameProxy    = "retry_same_proxy"
	ContextKeyRetryAttempt      = "retry_attempt"
	ContextKeyActionRequiresRetry = "action_requires_retry"
	ContextKeyRetryType         = "retry_type"           // 重试类型上下文键
	ContextKeyStopProcessing    = "stop_processing"

	// IP提取上下文键
	HTTPRequestContextKey = "http_request"
)

// 默认配置值
var DefaultConfig = map[string]interface{}{
	"server.host":             "0.0.0.0",
	"server.port":             DefaultHTTPPort,
	"server.read_timeout":     DefaultReadTimeout,
	"server.write_timeout":    DefaultWriteTimeout,
	"server.idle_timeout":     DefaultIdleTimeout,
	"server.max_header_bytes": 1 << 20, // 1MB

	// 命令行参数对应的服务器配置默认值
	"server.auth": "",         // 认证配置默认为空
	"server.type": ProxyTypeHTTP, // 代理服务器类型默认为HTTP

	"proxy.strategy":              StrategyRandom,
	"proxy.load_balancer":         LoadBalancerRoundRobin,
	"proxy.health_check.enabled":  true,
	"proxy.health_check.interval": DefaultHealthCheckInterval,
	"proxy.health_check.timeout":  DefaultHealthCheckTimeout,
	"proxy.health_check.path":     DefaultHealthCheckPath,
	"proxy.max_retries":           DefaultMaxRetries,
	"proxy.retry_interval":        DefaultRetryInterval,

	// 代理连接重试配置默认值
	"global.proxy_connection_retry_enabled": DefaultProxyConnectionRetryEnabled,
	"global.proxy_connection_max_retries":   DefaultProxyConnectionMaxRetries,
	"global.proxy_connection_retry_delay":   DefaultProxyConnectionRetryDelay,
	"global.proxy_connection_retry_backoff": DefaultProxyConnectionRetryBackoff,
	"global.proxy_connection_max_delay":     DefaultProxyConnectionMaxDelay,

	// 命令行参数对应的全局配置默认值
	"global.verbose":     false,        // 详细模式默认关闭
	"global.daemon":      false,        // 守护进程模式默认关闭
	"global.sync":        false,        // 同步模式默认关闭
	"global.watch":       false,        // 文件监控默认关闭

	"cache.type":             CacheTypeMemory,
	"cache.ttl":              DefaultCacheTTL,
	"cache.size":             DefaultCacheSize,
	"cache.cleanup_interval": DefaultCleanupInterval,

	"log.level":       LogLevelInfo,
	"log.format":      LogFormatJSON,
	"log.file":        DefaultLogFile,
	"log.max_size":    DefaultLogMaxSize,
	"log.max_age":     DefaultLogMaxAge,
	"log.max_backups": DefaultLogMaxBackups,

	// 命令行参数对应的日志配置默认值
	"logging.output_file": "", // 日志输出文件默认为空

	"rate_limit.enabled":   false,
	"rate_limit.algorithm": RateLimitTokenBucket,
	"rate_limit.rate":      DefaultRateLimit,
	"rate_limit.burst":     DefaultBurstSize,
	"rate_limit.window":    DefaultWindowSize,

	"auth.type":         AuthTypeNone,
	"auth.token_expiry": DefaultTokenExpiry,

	"monitoring.enabled": false,
	"monitoring.port":    DefaultMonitoringPort,
	"monitoring.path":    DefaultMonitoringPath,

	// 安全配置默认值
	"security.auth.type":         AuthTypeNone,
	"security.auth.token_expiry": "24h", // 使用字符串格式
	"security.encryption.algorithm": "aes256",
	"security.encryption.key_length": 32,

	// 高级配置默认值
	"advanced.enabled": false,
	"advanced.error_recovery.max_retry_attempts":    3,
	"advanced.error_recovery.initial_retry_delay":   "1s",
	"advanced.error_recovery.max_retry_delay":       "30s",
	"advanced.error_recovery.retry_multiplier":      2.0,
	"advanced.error_recovery.failure_threshold":     5,
	"advanced.error_recovery.success_threshold":     3,
	"advanced.error_recovery.circuit_timeout":       "60s",
	"advanced.error_recovery.circuit_reset_timeout": "300s",

	// 开发配置默认值
	"development.enabled":    false,
	"development.mode":       "production",
	"development.hot_reload": false,
	"development.testing.enabled":        false,
	"development.testing.mock_responses": false,
	"development.testing.test_data_dir":  "./testdata",
	"development.profiling.enabled":       false,
	"development.profiling.cpu_profile":   false,
	"development.profiling.memory_profile": false,
	"development.profiling.block_profile":  false,
	"development.profiling.mutex_profile":  false,

	// 代理配置默认值（补充）
	"proxy.pool_size": 10,

	// 配置管理默认值
	"config_management.hot_reload.enabled":           false,
	"config_management.hot_reload.reload_interval":   "30s",
	"config_management.hot_reload.backup_on_reload":  true,
	"config_management.hot_reload.rollback_on_error": true,
	"config_management.validation.strict_mode":         false,
	"config_management.validation.fail_fast":           false,
	"config_management.validation.validate_on_startup": true,
	"config_management.validation.validate_on_reload":  true,
	"config_management.validation.validate_on_save":    false,
	"config_management.backup.enabled":        false,
	"config_management.backup.backup_dir":     "./backups",
	"config_management.backup.max_backups":    5,
	"config_management.backup.compress":       true,
	"config_management.backup.auto_cleanup":   true,
	"config_management.backup.retention_days": 30,



	// 命令行参数对应的代理检查配置默认值
	"proxy_check.enabled":        false, // 代理检查默认关闭
	"proxy_check.max_goroutines": 50,    // 最大协程数默认50
	"proxy_check.country_codes":  []string{}, // 国家代码默认为空
}

// DNS 服务相关常量
const (
	// DNS 缓存设置
	DefaultDNSCacheTTL = 5 * time.Minute

	// DNS 清理间隔
	DNSCleanupInterval = 10 * time.Minute
)

// Action 服务相关常量
const (
	// Action 队列设置
	DefaultActionQueueSize = 1000
	DefaultActionWorkers   = 10

	// Action 超时设置
	DefaultActionTimeout = 30 * time.Second

	// Action 重试设置
	DefaultActionMaxRetries = 3
	DefaultActionRetryDelay = 1 * time.Second
)

// Trigger 服务相关常量
const (
	// Trigger 检查间隔
	DefaultTriggerCheckInterval = 1 * time.Second

	// Trigger 运行间隔
	DefaultTriggerRunInterval = 5 * time.Second
)

// 代理服务相关常量
const (
	// 代理轮询间隔
	ProxyRotationInterval = 1

	// 代理健康检查
	ProxyHealthCheckInterval = 10 * time.Minute

	// 代理连接池大小
	DefaultProxyPoolSize = 100
)

// 执行器相关常量
const (
	// 重试执行器常量
	RetryExecutorMaxRetryCount = 10   // 最大重试次数
	RetryExecutorDefaultDelay  = "1s" // 默认重试延迟

	// 绕过代理执行器常量
	BypassProxyDefaultTimeout = 30000  // 默认超时时间(毫秒)
	BypassProxyMaxTimeout     = 300000 // 最大超时时间(毫秒)

	// 缓存响应执行器常量
	CacheResponseDefaultDuration = 300    // 默认缓存时间(秒)
	CacheResponseMaxDuration     = 86400  // 最大缓存时间(秒，24小时)
	CacheResponseDefaultKey      = "auto" // 默认缓存键

	// 保存到代理池执行器常量
	SaveToPoolDefaultQuality = QualityTierStandard // 默认质量等级（使用标准质量等级）
	SaveToPoolDefaultScore   = 0.0       // 默认最小分数
	SaveToPoolDefaultPool    = "default" // 默认代理池名称

	// 空响应执行器常量
	NullResponseDefaultStatus      = 200                    // 默认状态码
	NullResponseDefaultContentType = "application/json"     // 默认内容类型
	NullResponseDefaultBody        = `{"status":"success"}` // 默认响应体

	// 脚本执行器常量
	ScriptExecutorDefaultTimeout = 30000        // 默认脚本执行超时时间(毫秒)
	ScriptExecutorMaxTimeout     = 300000       // 最大脚本执行超时时间(毫秒)
	ScriptExecutorDefaultEngine  = "javascript" // 默认脚本引擎
	ScriptExecutorMaxScriptSize  = 1048576      // 最大脚本大小(1MB)
	ScriptExecutorMaxFileSize    = 5242880      // 最大脚本文件大小(5MB)
	ScriptExecutorMaxPathLength  = 512          // 最大脚本文件路径长度

	// 请求URL执行器常量
	RequestURLDefaultTimeout   = 30000           // 默认请求超时时间(毫秒)
	RequestURLMaxTimeout       = 300000          // 最大请求超时时间(毫秒)
	RequestURLDefaultMethod    = "GET"           // 默认HTTP方法
	RequestURLMaxRetries       = 3               // 最大重试次数
	RequestURLDefaultUserAgent = "FlexProxy/1.0" // 默认User-Agent

	// 缓存执行器常量
	CacheExecutorDefaultDuration = 300000   // 默认缓存时间(毫秒)
	CacheExecutorMaxDuration     = 86400000 // 最大缓存时间(毫秒，24小时)
	CacheExecutorDefaultMaxUse   = 0        // 默认最大使用次数(0表示无限制)
	CacheExecutorDefaultScope    = "url"    // 默认缓存作用域

	// IP域名封禁执行器常量
	BanIPDomainDefaultDuration = 3600    // 默认封禁时间(秒，1小时)
	BanIPDomainMaxDuration     = 2592000 // 最大封禁时间(秒，30天)
	BanIPDomainDefaultReason   = "违规行为"  // 默认封禁原因
	BanIPDomainMaxListSize     = 10000   // 最大封禁列表大小
)

// 执行器错误消息常量
const (
	// 基础执行器错误消息
	ErrMsgProxyServiceNotInitIP            = "ProxyService未初始化，无法执行IP封禁"
	ErrMsgProxyServiceNotInitDomain        = "ProxyService未初始化，无法执行域名封禁"
	ErrMsgReadRequestBodyFailed            = "读取原始请求体失败: %v"
	ErrMsgReadResponseBodyFailed           = "读取原始响应体失败: %v"
	ErrMsgModifyRequestPanic               = "请求修改过程中发生panic: %v"
	ErrMsgModifyResponsePanic              = "响应修改过程中发生panic: %v"
	ErrMsgKeywordOperationFailed           = "关键字操作失败: %v"
	ErrMsgHeaderModifyFailed               = "请求头修改失败: %v"
	ErrMsgResponseHeaderModifyFailed       = "响应头修改失败: %v"
	ErrMsgAdvancedBodyModifyFailed         = "高级请求体修改失败: %v"
	ErrMsgAdvancedResponseBodyModifyFailed = "高级响应体修改失败: %v"
	ErrMsgBodyModifyFailed                 = "请求体修改失败: %v"
	ErrMsgResponseBodyModifyFailed         = "响应体修改失败: %v"
	ErrMsgInvalidStatusCode                = "无效的状态码: %d"

	// 响应修改增强错误消息
	ErrMsgResponseIntegrityCheckFailed     = "响应完整性检查失败: %v"
	ErrMsgContentEncodingProcessFailed     = "内容编码处理失败: %v"
	ErrMsgTransferEncodingProcessFailed    = "传输编码处理失败: %v"
	ErrMsgCharsetConversionFailed          = "字符编码转换失败: %v"
	ErrMsgCompressionFailed                = "内容压缩失败: %v"
	ErrMsgDecompressionFailed              = "内容解压失败: %v"
	ErrMsgChunkedEncodingFailed            = "分块编码处理失败: %v"
	ErrMsgProtocolValidationFailed         = "HTTP协议验证失败: %v"
	ErrMsgResponseSizeTooLarge             = "响应体大小超过限制: %d bytes，最大允许: %d bytes"
	ErrMsgUnsupportedContentEncoding       = "不支持的内容编码: %s"
	ErrMsgUnsupportedTransferEncoding      = "不支持的传输编码: %s"
	ErrMsgUnsupportedCharset               = "不支持的字符编码: %s"
	ErrMsgContentLengthMismatch            = "Content-Length与实际内容长度不匹配: 声明=%d, 实际=%d"

	// 请求修改增强错误消息
	ErrMsgRequestIntegrityCheckFailed      = "请求完整性检查失败: %v"
	ErrMsgRequestContentEncodingFailed     = "请求内容编码处理失败: %v"
	ErrMsgRequestTransferEncodingFailed    = "请求传输编码处理失败: %v"
	ErrMsgRequestCharsetConversionFailed   = "请求字符编码转换失败: %v"
	ErrMsgRequestCompressionFailed         = "请求内容压缩失败: %v"
	ErrMsgRequestDecompressionFailed       = "请求内容解压失败: %v"
	ErrMsgRequestChunkedEncodingFailed     = "请求分块编码处理失败: %v"
	ErrMsgRequestProtocolValidationFailed  = "请求HTTP协议验证失败: %v"
	ErrMsgRequestSizeTooLarge              = "请求体大小超过限制: %d bytes，最大允许: %d bytes"
	ErrMsgRequestHeaderCountExceeded       = "请求头数量超过限制: %d，最大允许: %d"
	ErrMsgRequestURLTooLong                = "请求URL长度超过限制: %d，最大允许: %d"
	ErrMsgFormDataProcessFailed            = "表单数据处理失败: %v"
	ErrMsgMultipartFormParseFailed         = "多部分表单解析失败: %v"
	ErrMsgFormFieldCountExceeded           = "表单字段数量超过限制: %d，最大允许: %d"
	ErrMsgInvalidRequestMethod             = "无效的请求方法: %s"
	ErrMsgRequestHeaderNameEmpty           = "请求头名称不能为空"
	ErrMsgRemoveRequestHeaderNameEmpty     = "要删除的请求头名称不能为空"

	// 重试执行器错误消息
	ErrMsgRetryCountInvalid      = "重试次数必须大于0"
	ErrMsgRetryCountExceedsLimit = "重试次数超过最大限制，已调整为: %d"
	ErrMsgRetryCountExceedsProxy = "重试次数超过可用代理数量，已调整为: %d"
	ErrMsgProxyServiceNotInit    = "ProxyService未初始化，无法执行代理切换重试"
	ErrMsgProxyPoolEmpty         = "代理池为空，无法执行代理切换重试"
	ErrMsgGetProxyFailed         = "获取代理失败，无法执行重试: %v"
	ErrMsgSetRetryContextFailed  = "设置重试上下文失败: %v"
	ErrMsgSetBypassContextFailed = "设置绕过代理上下文失败: %v"

	// 参数验证错误消息
	ErrMsgRetryCountFormatInvalid = "重试次数格式无效"
	ErrMsgDelayFormatInvalid      = "延迟参数格式无效"
	ErrMsgRotationModeInvalid     = "IP轮换模式无效"
	ErrMsgTimeoutFormatInvalid    = "超时参数格式无效"
	ErrMsgDNSModeInvalid          = "DNS模式无效"
	ErrMsgNoProxyAvailable        = "没有可用的代理"

	// 配置字段验证错误消息
	ErrMsgConfigFieldRequired = "配置字段是必需的"
	ErrMsgConfigFieldInvalid  = "配置字段值无效"

	// 字符编码检测错误消息
	ErrMsgCharsetDetectionFailed = "字符编码检测失败"

	// 文件上传相关常量
	MaxFileUploadSize = 10 * 1024 * 1024 // 10MB文件上传限制

	// 缓存响应执行器错误消息
	ErrMsgCacheServiceNotInit  = "CacheService未初始化，无法执行缓存操作"
	ErrMsgCacheDurationInvalid = "缓存时间格式无效"
	ErrMsgCacheKeyInvalid      = "缓存键格式无效"
	ErrMsgCacheOperationFailed = "缓存操作失败: %v"

	// 保存到代理池执行器错误消息
	ErrMsgQualityTierInvalid = "质量等级格式无效"
	ErrMsgMinScoreInvalid    = "最小分数格式无效"
	ErrMsgPoolNameInvalid    = "代理池名称格式无效"
	ErrMsgSaveToPoolFailed   = "保存到代理池失败: %v"

	// 空响应执行器错误消息
	ErrMsgStatusCodeInvalid   = "状态码格式无效"
	ErrMsgContentTypeInvalid  = "内容类型格式无效"
	ErrMsgResponseBodyInvalid = "响应体格式无效"
	ErrMsgNullResponseFailed  = "生成空响应失败: %v"

	// 脚本执行器错误消息
	ErrMsgScriptEngineInvalid     = "脚本引擎类型无效"
	ErrMsgScriptContentInvalid    = "脚本内容格式无效"
	ErrMsgScriptTimeoutInvalid    = "脚本超时时间格式无效"
	ErrMsgScriptExecutionFailed   = "脚本执行失败: %v"
	ErrMsgScriptFileMissing       = "脚本文件不存在: %s"
	ErrMsgScriptSizeExceeded      = "脚本大小超过限制"
	ErrMsgScriptFileReadFailed    = "脚本文件读取失败: %v"
	ErrMsgScriptFilePathInvalid   = "脚本文件路径无效: %s"
	ErrMsgScriptFilePathTooLong   = "脚本文件路径过长，最大长度: %d"
	ErrMsgScriptFileSizeExceeded  = "脚本文件大小超过限制，最大大小: %d 字节"
	ErrMsgScriptFileExtInvalid    = "脚本文件扩展名无效: %s"
	ErrMsgScriptPathTraversal     = "检测到路径遍历攻击: %s"
	ErrMsgScriptContentEmpty      = "脚本内容不能为空"
	ErrMsgScriptParameterMissing  = "脚本参数缺失，需要提供 script 或 script_file 参数"

	// 请求URL执行器错误消息
	ErrMsgURLInvalid             = "URL格式无效"
	ErrMsgHTTPMethodInvalid      = "HTTP方法格式无效"
	ErrMsgRequestTimeoutInvalid  = "请求超时时间格式无效"
	ErrMsgRequestRetriesInvalid  = "重试次数格式无效"
	ErrMsgRequestExecutionFailed = "HTTP请求执行失败: %v"
	ErrMsgRequestHeadersInvalid  = "请求头格式无效"

	// 缓存执行器错误消息
	ErrMsgCacheDurationInvalidOld = "缓存时间格式无效"
	ErrMsgCacheMaxUseInvalid      = "最大使用次数格式无效"
	ErrMsgCacheScopeInvalid       = "缓存作用域格式无效"
	ErrMsgCacheExecutionFailed    = "缓存操作执行失败: %v"

	// IP域名封禁执行器错误消息
	ErrMsgBanTargetInvalid      = "封禁目标格式无效"
	ErrMsgBanDurationInvalidMsg = "封禁时间格式无效"
	ErrMsgBanReasonInvalid      = "封禁原因格式无效"
	ErrMsgBanListSizeExceeded   = "封禁列表大小超过限制"
	ErrMsgBanExecutionFailed    = "封禁操作执行失败: %v"

	// 依赖管理错误消息
	ErrMsgCircularDependencyDetected = "检测到循环依赖"
	ErrMsgInvalidServiceName         = "服务名称无效"
	ErrMsgDependencyGraphInconsistent = "依赖关系图数据不一致"
	ErrMsgDependencyAnalysisTimeout   = "依赖分析超时"
	ErrMsgMaxDependencyLevelsExceeded = "依赖层级数超过最大限制"
	ErrMsgDependencyLevelCacheExpired = "依赖层级缓存已过期"

	// 成功日志消息
	// 基础执行器日志消息
	LogMsgBlockRequestSuccess            = "阻止请求动作已执行: reason=%s, status_code=%d"
	LogMsgModifyRequestStart             = "开始修改HTTP请求: headers=%v, remove_headers=%v, body_length=%d, has_body_config=%v, has_keyword_operations=%v"
	LogMsgModifyRequestSuccess           = "修改请求动作已执行: headers=%v, remove_headers=%v, body_length=%d"
	LogMsgModifyRequestComplete          = "HTTP请求修改完成，成功执行步骤: %v"
	LogMsgModifyRequestRollback          = "开始回退请求修改，失败步骤: %s，已完成步骤: %v"
	LogMsgModifyRequestRollbackComplete  = "请求修改回退完成"

	// 请求修改增强日志消息
	LogMsgRequestIntegrityCheckStart     = "开始请求完整性检查"
	LogMsgRequestIntegrityCheckComplete  = "请求完整性检查完成"
	LogMsgRequestHeaderConflictDetected  = "检测到请求头冲突: %s"
	LogMsgRequestHeaderConflictResolved  = "请求头冲突已解决: %s"
	LogMsgRequestProtocolIssueFixed      = "已修复请求协议问题: %s"
	LogMsgRequestCompressionApplied      = "已应用请求压缩: %s，压缩前: %d bytes，压缩后: %d bytes"
	LogMsgRequestDecompressionApplied    = "已应用请求解压: %s，解压前: %d bytes，解压后: %d bytes"
	LogMsgRequestEncodingConverted       = "已转换请求编码: %s -> %s"
	LogMsgFormDataProcessed              = "已处理表单数据，字段数量: %d"
	LogMsgMultipartFormProcessed         = "已处理多部分表单，部分数量: %d"
	LogMsgModifyResponseStart            = "开始修改HTTP响应: headers=%v, remove_headers=%v, status_code=%d, body_length=%d, has_body_config=%v, has_keyword_operations=%v"
	LogMsgModifyResponseSuccess          = "修改响应动作已执行: headers=%v, remove_headers=%v, status_code=%d, body_length=%d"
	LogMsgModifyResponseComplete         = "HTTP响应修改完成，成功执行步骤: %v"
	LogMsgModifyResponseRollback         = "开始回退响应修改，失败步骤: %s，已完成步骤: %v"
	LogMsgModifyResponseRollbackComplete = "响应修改回退完成"

	// 扩展执行器日志消息
	LogMsgRetrySameSuccess        = "使用相同代理重试已配置: 重试次数=%d, 延迟=%s, 代理=%s"
	LogMsgRetrySameSuccessNoProxy = "使用相同代理重试已配置: 重试次数=%d, 延迟=%s"
	LogMsgRetrySuccess            = "使用新代理重试已配置: 重试次数=%d, 延迟=%s, 轮换模式=%s, 可用代理数=%d"
	LogMsgBypassProxySuccess      = "绕过代理已配置: 超时=%dms, 保持连接=%v, DNS模式=%s"
	LogMsgCacheResponseSuccess    = "响应缓存已配置: 缓存时间=%ds, 缓存键=%s, 作用域=%s"
	LogMsgSaveToPoolSuccess       = "保存到代理池已配置: 质量等级=%s, 最小分数=%.1f, 代理池=%s, 域名特定=%v"
	LogMsgNullResponseSuccess     = "空响应已配置: 状态码=%d, 内容类型=%s, 响应体长度=%d"
	LogMsgScriptExecutorSuccess   = "脚本执行已配置: 引擎=%s, 超时=%dms, 脚本类型=%s, 脚本长度=%d"
	LogMsgRequestURLSuccess       = "URL请求已配置: 方法=%s, URL=%s, 超时=%dms, 重试次数=%d"
	LogMsgCacheExecutorSuccess    = "缓存已配置: 时间=%dms, 最大使用=%d, 作用域=%s, 忽略参数=%v"
	LogMsgBanIPDomainSuccess      = "IP域名封禁已配置: 目标=%s, 时间=%ds, 原因=%s, 类型=%s"

	// 警告日志消息
	LogMsgCacheTimeAdjusted      = "缓存时间无效或超过限制，已调整为默认值: %d秒"
	LogMsgScriptTimeoutAdjusted  = "脚本超时时间无效或超过限制，已调整为默认值: %dms"
	LogMsgBanTimeAdjusted        = "封禁时间无效或超过限制，已调整为默认值: %ds"
	LogMsgProxyNotFoundForPool   = "未找到当前代理信息，无法保存到代理池"
	LogMsgCacheTimeAdjustedMs    = "缓存时间无效或超过限制，已调整为默认值: %dms"
	LogMsgRequestTimeoutAdjusted = "请求超时时间无效或超过限制，已调整为默认值: %dms"

	// 执行器描述消息
	// 基础执行器描述
	DescLogExecutor            = "记录日志信息"
	DescBanIPExecutor          = "封禁IP地址"
	DescBanDomainExecutor      = "封禁域名"
	DescBlockRequestExecutor   = "阻止请求"
	DescModifyRequestExecutor  = "修改请求内容"
	DescModifyResponseExecutor = "修改响应内容"
	DescCacheResponseExecutor  = "缓存HTTP响应内容"
	DescScriptExecutor         = "执行JavaScript或Lua脚本"

	// 扩展执行器描述
	DescRetrySameExecutor    = "使用相同代理重试请求"
	DescRetryExecutor        = "使用新代理重试请求"
	DescBypassProxyExecutor  = "绕过代理直接连接目标服务器"
	DescSaveToPoolExecutor   = "保存代理到质量池"
	DescNullResponseExecutor = "返回空响应或自定义响应"
	DescRequestURLExecutor   = "向指定URL发送HTTP请求"
	DescCacheExecutor        = "缓存请求和响应数据"
	DescBanIPDomainExecutor  = "封禁IP地址或域名"
)

// HTTP头部常量
const (
	// 重试相关头部
	HeaderRetrySame    = "X-FlexProxy-Retry-Same"
	HeaderRetryNew     = "X-FlexProxy-Retry-New"
	HeaderRetryCount   = "X-FlexProxy-Retry-Count"
	HeaderRetryDelay   = "X-FlexProxy-Retry-Delay"
	HeaderRetryProxy   = "X-FlexProxy-Retry-Proxy"
	HeaderRotationMode = "X-FlexProxy-Rotation-Mode"

	// 绕过代理相关头部
	HeaderBypass          = "X-FlexProxy-Bypass"
	HeaderBypassTimeout   = "X-FlexProxy-Bypass-Timeout"
	HeaderBypassKeepAlive = "X-FlexProxy-Bypass-KeepAlive"
	HeaderBypassDNS       = "X-FlexProxy-Bypass-DNS"

	// 缓存响应相关头部
	HeaderCacheResponse = "X-FlexProxy-Cache-Response"
	HeaderCacheDuration = "X-FlexProxy-Cache-Duration"
	HeaderCacheKey      = "X-FlexProxy-Cache-Key"
	HeaderCacheScope    = "X-FlexProxy-Cache-Scope"

	// 保存到代理池相关头部
	HeaderSaveToPool  = "X-FlexProxy-Save-To-Pool"
	HeaderQualityTier = "X-FlexProxy-Quality-Tier"
	HeaderMinScore    = "X-FlexProxy-Min-Score"
	HeaderPoolName    = "X-FlexProxy-Pool-Name"

	// 空响应相关头部
	HeaderNullResponse    = "X-FlexProxy-Null-Response"
	HeaderNullStatus      = "X-FlexProxy-Null-Status"
	HeaderNullContentType = "X-FlexProxy-Null-Content-Type"

	// 脚本执行器相关头部
	HeaderScriptExecutor = "X-FlexProxy-Script-Executor"
	HeaderScriptEngine   = "X-FlexProxy-Script-Engine"
	HeaderScriptTimeout  = "X-FlexProxy-Script-Timeout"
	HeaderScriptType     = "X-FlexProxy-Script-Type"

	// 请求URL执行器相关头部
	HeaderRequestURL     = "X-FlexProxy-Request-URL"
	HeaderRequestMethod  = "X-FlexProxy-Request-Method"
	HeaderRequestTimeout = "X-FlexProxy-Request-Timeout"
	HeaderRequestRetries = "X-FlexProxy-Request-Retries"

	// 缓存执行器相关头部
	HeaderCacheExecutor = "X-FlexProxy-Cache-Executor"
	HeaderCacheMaxUse   = "X-FlexProxy-Cache-Max-Use"
	HeaderIgnoreParams  = "X-FlexProxy-Ignore-Params"

	// IP域名封禁相关头部
	HeaderBanIPDomain = "X-FlexProxy-Ban-IP-Domain"
	HeaderBanTarget   = "X-FlexProxy-Ban-Target"
	HeaderBanDuration = "X-FlexProxy-Ban-Duration"
	HeaderBanReason   = "X-FlexProxy-Ban-Reason"
)

// 注意：错误消息常量已迁移到 common/errors/errors.go 中统一管理

// 状态常量
const (
	// 服务状态
	ServiceStatusStarting = "starting"
	ServiceStatusRunning  = "running"
	ServiceStatusStopping = "stopping"
	ServiceStatusStopped  = "stopped"
	ServiceStatusError    = "error"

	// 任务状态
	TaskStatusPending   = "pending"
	TaskStatusRunning   = "running"
	TaskStatusCompleted = "completed"
	TaskStatusFailed    = "failed"
	TaskStatusCancelled = "cancelled"
)

// 检查器相关常量
const (
	// 检查状态
	CheckStatusLive = "LIVE"
	CheckStatusDied = "DIED"

	// 检查器默认设置
	DefaultGoroutines = 50
)

// 证书管理常量
const (
	// 证书目录和文件
	DefaultCertDir = "./certs"
	CACertFile     = "ca.crt"
	CAKeyFile      = "ca.key"

	// 证书有效期
	CACertValidityYears = 10
	CertValidityDays    = 365

	// 证书权限
	CertFilePermission = 0644
	KeyFilePermission  = 0600

	// 证书组织信息
	CertOrganization = "FlexProxy"
	CertCountry      = "US"
	CertLocality     = "San Francisco"
	CertCommonName   = "FlexProxy Root CA"
)

// 服务器相关常量
const (
	// MIME类型
	MimeTextPlain = "text/plain"

	// 全局代理使用跟踪
	DefaultProxyCooldownMinutes = 5
	DefaultProxyCooldownSeconds = 300

	// 内存和性能阈值
	MemoryThresholdBytes    = 1024 * 1024 * 1024 // 1GB
	GoroutineThresholdCount = 1000
)

// 热重载系统常量
const (
	// 配置项路径分隔符
	ConfigPathSeparator     = "."
	ConfigPathSeparatorChar = '.'

	// 热重载默认配置
	DefaultHotReloadCheckInterval    = 1 * time.Second  // 默认检查间隔
	DefaultHotReloadDebounceDelay    = 500 * time.Millisecond // 防抖延迟
	DefaultConfigItemTimeout         = 30 * time.Second // 配置项超时时间
	DefaultMaxConfigItemVersions     = 10               // 最大配置项版本数
	DefaultReferenceCounterCleanup   = 5 * time.Minute  // 引用计数器清理间隔

	// 热重载性能参数
	MaxConcurrentConfigChanges = 100    // 最大并发配置变更数
	ConfigItemCacheSize        = 1000   // 配置项缓存大小
	ChangeEventBufferSize      = 500    // 变更事件缓冲区大小

	// 热重载状态
	HotReloadStatusDisabled = "disabled"
	HotReloadStatusEnabled  = "enabled"
	HotReloadStatusError    = "error"
	HotReloadStatusPending  = "pending"
)

// 关键配置路径列表，这些配置项的变更需要立即通知相关服务
var CriticalConfigPaths = []string{
	"server.host",
	"server.port",
	"proxy.strategy",
	"proxy.load_balancer",
	"cache.enabled",
	"logging.level",
	"security.enabled",
	"dns_service.enabled",
}

// 处理阶段常量
const (
	PreProcess        = "pre"         // 请求前处理
	PostHeaderProcess = "post_header" // 响应头处理
	PostBodyProcess   = "post_body"   // 响应体处理

	// 触发器处理阶段
	PreRequest = "pre"
	PostHeader = "post_header"
	PostBody   = "post_body"
)

// 触发器类型常量
const (
	TriggerTypeStatus         = "status"
	TriggerTypeBody           = "body"
	TriggerTypeMaxRequestTime = "max_request_time"
	TriggerTypeConnTimeOut    = "conn_time_out"
	TriggerTypeMinRequestTime = "min_request_time"
	TriggerTypeURL            = "url"
	TriggerTypeDomain         = "domain"
	TriggerTypeCombined       = "combined"
	TriggerTypeJavaScript     = "javascript"
	TriggerTypeRequestBody    = "request_body"
	TriggerTypeRequestHeader  = "request_header"
	TriggerTypeResponseHeader = "response_header"
)

// 触发器名称格式常量
const (
	TriggerNameFormatStatus         = "status_trigger_%v"
	TriggerNameFormatBody           = "body_trigger_%d_patterns"
	TriggerNameFormatMaxRequestTime = "max_request_time_trigger_%dms"
	TriggerNameFormatConnTimeOut    = "conn_timeout_trigger_%dms"
	TriggerNameFormatMinRequestTime = "min_request_time_trigger_%dms"
	TriggerNameFormatURL            = "url_trigger_%d_patterns"
	TriggerNameFormatDomain         = "domain_trigger_%d_patterns"
	TriggerNameFormatCombined       = "combined_trigger_%d_codes_%d_patterns"
	TriggerNameFormatJavaScript     = "javascript_trigger_%s"
	TriggerNameFormatRequestHeader  = "request_header_trigger_%d_headers"
	TriggerNameFormatResponseHeader = "response_header_trigger_%d_headers"
	TriggerNameFormatEnhanced       = "enhanced_conditional_trigger_%s"
	TriggerNameFormatUnknown        = "unknown_trigger"
	TriggerNameFormatGeneric        = "%T_%p"
)

// 热重载相关常量
const (
	HotReloadTimeoutSeconds     = 30  // 热重载超时时间（秒）
	HotReloadMaxBodySize        = 1024 * 1024 // 1MB
	HotReloadChecksumAlgorithm  = "md5"
	HotReloadCompressionEnabled = true
	HotReloadWaitTimeoutSeconds = 5   // 等待热重载完成的超时时间（秒）
	HotReloadCleanupDelaySeconds = 5  // 版本清理延迟时间（秒）

	// 状态管理相关常量
	DefaultMaxSnapshotCount        = 20                // 默认最大快照数量
	DefaultMaxRollbackDepth        = 10                // 默认最大回滚深度
	DefaultSnapshotRetentionPeriod = 24 * time.Hour    // 默认快照保留期间

	// 依赖管理相关常量
	DependencyLevelCacheTTL        = 5 * time.Minute  // 依赖层级缓存TTL
	MaxDependencyLevels            = 20               // 最大依赖层级数
	CircularDependencyCheckTimeout = 10 * time.Second // 循环依赖检测超时时间
	DependencyGraphMaxNodes        = 1000             // 依赖图最大节点数
	DependencyAnalysisTimeout      = 30 * time.Second // 依赖分析超时时间
)

// 条件匹配相关常量
const (
	ConditionMatchTimeoutMs     = 5000 // 条件匹配超时时间（毫秒）
	ConditionBodyReadLimit      = 1024 * 1024 // 条件匹配时响应体读取限制（1MB）
	ConditionPatternCacheSize   = 1000 // 正则表达式缓存大小
	ConditionDefaultRelation    = "AND" // 默认条件关系
	ConditionMaxPatterns        = 100   // 单个条件最大模式数量
)

// 条件关系常量
const (
	ConditionRelationAND = "AND"
	ConditionRelationOR  = "OR"
	ConditionRelationNOT = "NOT"
)

// AWS 和代理网关常量
const (
	// AWS API Gateway
	AWSAPIGatewayStageName = "flexp-proxy-gateway"

	// 检查器端点
	DefaultIPInfoEndpoint = "https://ipinfo.io/json"

	// 环境变量
	EnvVarPrefix = "FLEXPROXY_"
)

// 调试和监控常量
const (
	// 调试服务
	DebugServiceName = "debug"

	// 断点相关
	BreakpointPrefix = "bp_"

	// 监控健康检查名称
	HealthCheckMemory     = "memory"
	HealthCheckGoroutines = "goroutines"

	// 性能分析
	ProfileCPU    = "cpu"
	ProfileMemory = "memory"
	ProfileBlock  = "block"
	ProfileMutex  = "mutex"
)

// 文件和目录权限常量
const (
	// 标准权限
	StandardFilePermission = 0644
	StandardDirPermission  = 0755
	SecureFilePermission   = 0600
	SecureDirPermission    = 0700
)

// 状态快照和序列化相关常量
const (
	// 哈希算法相关
	DefaultHashAlgorithm     = "sha256"           // 默认哈希算法
	FallbackHashAlgorithm    = "md5"              // 回退哈希算法
	ChecksumHexLength        = 64                 // SHA-256哈希值十六进制长度

	// 序列化相关
	SerializationFormatJSON  = "json"             // JSON序列化格式
	SerializationFormatGOB   = "gob"              // GOB序列化格式
	SerializationFormatMsgPack = "msgpack"        // MessagePack序列化格式
	DefaultSerializationFormat = SerializationFormatJSON

	// 状态快照相关
	SnapshotVersionPrefix    = "snapshot_v"       // 快照版本前缀
	SnapshotCompressionLevel = 6                  // 压缩级别（1-9）
	MaxSnapshotSize          = 100 * 1024 * 1024  // 最大快照大小（100MB）
	MaxSerializationDepth    = 32                 // 最大序列化深度

	// 校验和计算相关
	ChecksumCalculationTimeout = 30 * time.Second // 校验和计算超时时间
	MaxChecksumRetries        = 3                 // 最大校验和计算重试次数
	ChecksumBufferSize        = 64 * 1024         // 校验和计算缓冲区大小

	// 状态数据类型标识
	StateDataTypeString      = "string"
	StateDataTypeNumber      = "number"
	StateDataTypeBoolean     = "boolean"
	StateDataTypeArray       = "array"
	StateDataTypeObject      = "object"
	StateDataTypeNull        = "null"
	StateDataTypeTime        = "time"
	StateDataTypeDuration    = "duration"
	StateDataTypeInterface   = "interface"
)

// 网络和协议常量
const (
	// 协议前缀
	HTTPPrefix  = "http://"
	HTTPSPrefix = "https://"

	// 特殊地址
	LocalhostIPv4 = "127.0.0.1"
	LocalhostIPv6 = "::1"
	AllInterfaces = "0.0.0.0"

	// 默认用户代理
	DefaultUserAgent = "FlexProxy/1.0.0"
)

// 协议常量
const (
	// 网络协议
	ProtocolHTTP   = "http"
	ProtocolHTTPS  = "https"
	ProtocolSOCKS4 = "socks4"
	ProtocolSOCKS5 = "socks5"
	ProtocolTCP    = "tcp"
	ProtocolUDP    = "udp"

	// DNS 协议
	DNSProtocolUDP   = "udp"
	DNSProtocolTCP   = "tcp"
	DNSProtocolTLS   = "tls"
	DNSProtocolHTTPS = "https"
	DNSProtocolDOH   = "doh"
)

// 操作符常量
const (
	// 比较操作符
	OperatorEquals      = "equals"
	OperatorNotEquals   = "not_equals"
	OperatorContains    = "contains"
	OperatorNotContains = "not_contains"
	OperatorStartsWith  = "starts_with"
	OperatorEndsWith    = "ends_with"
	OperatorIn          = "in"
	OperatorNotIn       = "not_in"
	OperatorGreaterThan = "greater_than"
	OperatorLessThan    = "less_than"
	OperatorRegex       = "regex"
)

// 字段名常量
const (
	// HTTP 字段
	FieldURL    = "url"
	FieldMethod = "method"
	FieldHeader = "header"
	FieldBody   = "body"
	FieldIP     = "ip"

	// 时间字段
	FieldHour    = "hour"
	FieldWeekday = "weekday"
	FieldDate    = "date"
	FieldTime    = "time"

	// 响应字段
	FieldStatus      = "status"
	FieldContentType = "content_type"
	FieldSize        = "size"
)

// Modify动作相关常量
const (
	// 支持的内容格式
	FormatJSON       = "json"
	FormatXML        = "xml"
	FormatHTML       = "html"
	FormatText       = "text"
	FormatPlain      = "plain"
	FormatForm       = "form"
	FormatURLEncoded = "urlencoded"
	FormatBinary     = "binary"
	FormatOctet      = "octet"
	FormatAuto       = "auto"

	// 支持的编码格式
	EncodingUTF8   = "utf-8"
	EncodingBase64 = "base64"

	// HTTP内容编码格式
	ContentEncodingGzip     = "gzip"
	ContentEncodingDeflate  = "deflate"
	ContentEncodingBrotli   = "br"
	ContentEncodingCompress = "compress"
	ContentEncodingIdentity = "identity"

	// HTTP传输编码格式
	TransferEncodingChunked  = "chunked"
	TransferEncodingCompress = "compress"
	TransferEncodingDeflate  = "deflate"
	TransferEncodingGzip     = "gzip"
	TransferEncodingIdentity = "identity"

	// 字符编码格式
	CharsetUTF8     = "utf-8"
	CharsetUTF16    = "utf-16"
	CharsetUTF16BE  = "utf-16be"
	CharsetUTF16LE  = "utf-16le"

	// 中文编码
	CharsetGBK      = "gbk"
	CharsetGB2312   = "gb2312"
	CharsetGB18030  = "gb18030"
	CharsetBig5     = "big5"

	// 日文编码
	CharsetShiftJIS = "shift_jis"
	CharsetEUCJP    = "euc-jp"
	CharsetISO2022JP = "iso-2022-jp"

	// 韩文编码
	CharsetEUCKR    = "euc-kr"

	// ISO编码系列
	CharsetISO88591 = "iso-8859-1"
	CharsetISO88592 = "iso-8859-2"
	CharsetISO88593 = "iso-8859-3"
	CharsetISO88594 = "iso-8859-4"
	CharsetISO88595 = "iso-8859-5"
	CharsetISO88596 = "iso-8859-6"
	CharsetISO88597 = "iso-8859-7"
	CharsetISO88598 = "iso-8859-8"
	CharsetISO88599 = "iso-8859-9"
	CharsetISO885910 = "iso-8859-10"
	CharsetISO885913 = "iso-8859-13"
	CharsetISO885914 = "iso-8859-14"
	CharsetISO885915 = "iso-8859-15"
	CharsetISO885916 = "iso-8859-16"

	// Windows编码系列
	CharsetWindows1250 = "windows-1250"
	CharsetWindows1251 = "windows-1251"
	CharsetWindows1252 = "windows-1252"
	CharsetWindows1253 = "windows-1253"
	CharsetWindows1254 = "windows-1254"
	CharsetWindows1255 = "windows-1255"
	CharsetWindows1256 = "windows-1256"
	CharsetWindows1257 = "windows-1257"
	CharsetWindows1258 = "windows-1258"

	// 其他编码
	CharsetKOI8R    = "koi8-r"
	CharsetKOI8U    = "koi8-u"
	CharsetASCII    = "ascii"

	// 关键字操作类型
	OperationAdd     = "add"
	OperationReplace = "replace"
	OperationRemove  = "remove"
	OperationAppend  = "append"

	// 匹配类型
	MatchTypeExact    = "exact"
	MatchTypeContains = "contains"
	MatchTypeWildcard = "wildcard"
	MatchTypeRegex    = "regex"

	// 条件类型
	ConditionExists     = "exists"
	ConditionNotExists  = "not_exists"
	ConditionValueMatch = "value_match"

	// 默认值和限制
	MaxHeaderValueLength   = 8192
	DefaultAutoContentType = true

	// 响应修改相关限制
	MaxResponseBodySize        = 100 * 1024 * 1024 // 最大响应体大小：100MB
	MaxChunkSize              = 8192               // 最大分块大小：8KB
	DefaultResponseBufferSize = 4096               // 默认响应缓冲区大小：4KB
	MaxCompressionLevel       = 9                  // 最大压缩级别
	DefaultCompressionLevel   = 6                  // 默认压缩级别

	// 响应修改默认值
	DefaultResponseIntegrityCheck = true        // 默认启用响应完整性检查
	DefaultAutoFixProtocolIssues  = true        // 默认启用协议问题自动修复
	DefaultPreserveOriginalEncoding = false     // 默认不保留原始编码

	// 请求修改相关限制
	MaxRequestBodySize         = 50 * 1024 * 1024 // 最大请求体大小：50MB
	MaxRequestHeaderCount      = 100               // 最大请求头数量
	MaxFormFieldCount          = 1000              // 最大表单字段数量
	DefaultRequestBufferSize   = 4096              // 默认请求缓冲区大小：4KB
	MaxMultipartMemory         = 32 * 1024 * 1024  // 最大多部分表单内存：32MB
	MaxURLLength               = 8192              // 最大URL长度：8KB

	// 请求修改默认值
	DefaultRequestIntegrityCheck    = true  // 默认启用请求完整性检查
	DefaultAutoFixRequestIssues     = true  // 默认启用请求问题自动修复
	DefaultPreserveRequestEncoding  = false // 默认不保留原始请求编码
	DefaultValidateFormData         = true  // 默认启用表单数据验证

	// HTTP状态码范围
	MinHTTPStatusCode = 100
	MaxHTTPStatusCode = 599
)

// 支持的格式列表（用于验证）
var ValidFormats = []string{
	FormatJSON, FormatXML, FormatHTML, FormatText, FormatPlain,
	FormatForm, FormatURLEncoded, FormatBinary, FormatOctet, FormatAuto,
}

// 支持的编码列表（用于验证）
// 包括内容编码（base64）和字符编码
var ValidEncodings = []string{
	// 内容编码
	EncodingUTF8, EncodingBase64,

	// UTF编码系列
	CharsetUTF8, CharsetUTF16, CharsetUTF16BE, CharsetUTF16LE,

	// 中文编码
	CharsetGBK, CharsetGB2312, CharsetGB18030, CharsetBig5,

	// 日文编码
	CharsetShiftJIS, CharsetEUCJP, CharsetISO2022JP,

	// 韩文编码
	CharsetEUCKR,

	// 常用ISO编码
	CharsetISO88591, CharsetISO88592, CharsetISO88595, CharsetISO88599, CharsetISO885915,

	// 常用Windows编码
	CharsetWindows1250, CharsetWindows1251, CharsetWindows1252,

	// 其他编码
	CharsetKOI8R, CharsetASCII,
}

// 支持的内容编码列表（用于验证）
var ValidContentEncodings = []string{
	ContentEncodingGzip, ContentEncodingDeflate, ContentEncodingBrotli,
	ContentEncodingCompress, ContentEncodingIdentity,
}

// 支持的传输编码列表（用于验证）
var ValidTransferEncodings = []string{
	TransferEncodingChunked, TransferEncodingCompress,
	TransferEncodingDeflate, TransferEncodingGzip, TransferEncodingIdentity,
}

// 支持的字符编码列表（用于验证）
var ValidCharsets = []string{
	// UTF编码系列
	CharsetUTF8, CharsetUTF16, CharsetUTF16BE, CharsetUTF16LE,

	// 中文编码
	CharsetGBK, CharsetGB2312, CharsetGB18030, CharsetBig5,

	// 日文编码
	CharsetShiftJIS, CharsetEUCJP, CharsetISO2022JP,

	// 韩文编码
	CharsetEUCKR,

	// ISO编码系列
	CharsetISO88591, CharsetISO88592, CharsetISO88593, CharsetISO88594, CharsetISO88595,
	CharsetISO88596, CharsetISO88597, CharsetISO88598, CharsetISO88599, CharsetISO885910,
	CharsetISO885913, CharsetISO885914, CharsetISO885915, CharsetISO885916,

	// Windows编码系列
	CharsetWindows1250, CharsetWindows1251, CharsetWindows1252, CharsetWindows1253,
	CharsetWindows1254, CharsetWindows1255, CharsetWindows1256, CharsetWindows1257,
	CharsetWindows1258,

	// 其他编码
	CharsetKOI8R, CharsetKOI8U, CharsetASCII,
}

// 支持的操作类型列表（用于验证）
var ValidOperations = []string{
	OperationAdd, OperationReplace, OperationRemove, OperationAppend,
}

// 支持的匹配类型列表（用于验证）
var ValidMatchTypes = []string{
	MatchTypeExact, MatchTypeContains, MatchTypeWildcard, MatchTypeRegex,
}

// 支持的条件类型列表（用于验证）
var ValidConditions = []string{
	ConditionExists, ConditionNotExists, ConditionValueMatch,
}

// 服务器处理器相关常量
const (
	// 分隔符和格式常量
	GatewayKeySeparator = "|"  // 网关键分隔符，用于生成唯一的网关映射键
	SpaceSeparator      = " "  // 空格分隔符，用于字符串分割和格式化

	// 认证相关常量
	ProxyAuthHeaderName         = "Proxy-Authorization"  // 代理认证HTTP头名称
	ProxyAuthCredentialsParts   = 2                      // 代理认证信息分割后的期望部分数量
	ProxyOkCountInitial         = 1                      // 代理成功计数初始值

	// 默认数值常量
	DefaultMinProxyPoolSize     = 1   // 默认最小代理池大小
	DefaultRetryMaxCount        = 0   // 默认重试最大次数（禁用重试）
	DefaultConnectionMaxRetries = 3   // 默认连接最大重试次数
	DefaultRetryBackoffFactor   = 2.0 // 默认重试退避倍数
	DefaultMaxDelaySeconds      = 10  // 默认最大延迟秒数

	// IP轮询模式常量
	DefaultIPRotationMode = "sequential"  // 默认IP轮询模式

	// DNS模式常量
	DNSModeLocalValue = "local"  // 本地DNS模式值

	// 上下文键常量
	ContextKeyProxyResetter = "proxy_resetter"  // 代理重置器上下文键

	// 布尔默认值
	DefaultStopProcessing = false  // 默认停止处理标志
)

// HTTP状态码范围常量
const (
	HTTPSuccessStatusMin = 200  // HTTP成功状态码最小值
	HTTPSuccessStatusMax = 399  // HTTP成功状态码最大值（不包含400）
)

// 证书和文件相关常量
const (
	// 证书路径和文件名
	CertificatePath     = "/cert"                // 证书获取路径
	CertificateFilename = "goproxy-cacert.der"   // 证书文件名
	CertificateAttachmentPrefix = "attachment; filename="  // Content-Disposition附件前缀

	// MIME类型
	MimeOctetStream = "application/octet-stream"  // 二进制流MIME类型
)

// 错误和日志消息常量
const (
	// 代理认证错误消息
	ErrMsgProxyAuthDecodeFailed = "%s: 解码代理认证失败"      // 代理认证解码失败消息模板
	ErrMsgProxyAuthInvalid      = "%s: 代理认证无效"         // 代理认证无效消息模板
	ErrMsgProxyAuthRequired     = "%s: %s 代理请求认证失败"   // 代理认证必需消息模板

	// 证书相关错误消息
	ErrMsgCertificateAuthFailed = "获取代理证书授权失败。"     // 证书授权失败消息

	// 服务器错误消息
	ErrMsgNonProxyRequest = "This is a FlexProxy proxy server. It does not respond to non-proxy requests."  // 非代理请求错误消息
	ErrMsgProxyServerError = "Proxy server error"  // 代理服务器错误消息

	// 代理池相关错误消息
	ErrMsgProxyPoolInsufficient = "代理池可用数量不足，暂停所有请求"           // 代理池数量不足消息
	ErrMsgProxyPoolExhaustedMsg = "代理池无可用代理"                      // 代理池耗尽消息
	ErrMsgProxyBanned           = "获取到的代理被封禁"                     // 代理被封禁消息

	// 代理获取相关错误消息
	ErrMsgNoCurrentProxy    = "当前没有代理，需要获取新代理"               // 无当前代理消息
	ErrMsgGetProxyIPFailed  = "无法获取代理 IP：%s"                     // 获取代理IP失败消息模板
	ErrMsgProxyObtained     = "获取到代理: %s"                          // 获取代理成功消息模板
	ErrMsgRuleSystemEnabled = "规则系统已启用，检查代理是否被封禁"          // 规则系统启用消息
	ErrMsgProxyBannedCheck  = "代理 %s 是否被封禁: %v"                  // 代理封禁检查消息模板
	ErrMsgSkipBannedProxy   = "跳过禁止的代理：%s"                       // 跳过被封禁代理消息模板
	ErrMsgFoundAvailableProxy = "找到可用代理: %s"                      // 找到可用代理消息模板
	ErrMsgContinueCurrentProxy = "继续使用当前代理: %s"                  // 继续使用当前代理消息模板
	ErrMsgProxyUsageIncreased = "当前代理使用次数增加到: %d"              // 代理使用次数增加消息模板
	ErrMsgRotateProxyComplete = "rotateProxy函数执行完成，返回代理: %s"    // 代理轮换完成消息模板
)

// 日志格式模板常量
const (
	// 基础日志格式
	LogFormatRequestInfo     = "%s %s %s"                                    // 请求信息日志格式：RemoteAddr Method URL
	LogFormatRequestDetails  = "%s %s %s %s"                                // 请求详细信息日志格式：RemoteAddr Method URL Error
	LogFormatIPRotationMode  = "IP轮询模式: %s"                              // IP轮询模式日志格式
	LogFormatMaxAttempts     = "最大尝试次数: %d"                             // 最大尝试次数日志格式
	LogFormatProxyPoolInfo   = "代理池最小可用数量: %d, 当前代理池数量: %d"      // 代理池信息日志格式
	LogFormatProxyPoolCount  = "当前代理池数量: %d"                           // 代理池数量日志格式

	// 触发器相关日志格式
	LogFormatTriggerExecution = "%s阶段触发器，请求URL: %s，请求时间: %dms"     // 触发器执行日志格式
	LogFormatTriggerCount     = "找到%d个%s阶段触发器"                        // 触发器数量日志格式
	LogFormatTriggerMatch     = "触发器匹配成功，阶段: %s，优先级: %d"          // 触发器匹配日志格式
	LogFormatTriggerType      = "触发器匹配成功 - 类型: %s"                   // 触发器类型日志格式
	LogFormatActionCount      = "触发器有 %d 个动作配置需要执行"                // 动作数量日志格式
	LogFormatActionExecution  = "准备执行动作类型: %s, 参数: %v"               // 动作执行日志格式
	LogFormatTriggerCrash     = "触发器执行崩溃: %v"                          // 触发器崩溃日志格式
	LogFormatStatusTrigger    = "状态码触发器检查 - 当前状态码: %d"             // 状态码触发器日志格式
	LogFormatDNSResolverSwitch = "为事件临时切换DNS解析器模式: %s"             // DNS解析器切换日志格式

	// DNS相关日志格式
	LogFormatDNSCacheDisabled = "DNS缓存已禁用，跳过预解析主机名: %s"           // DNS缓存禁用日志格式
	LogFormatDNSPreResolve    = "预先解析主机名: %s 并存入缓存"                // DNS预解析日志格式
	LogFormatDNSResolveFailed = "预解析主机名失败: %s, 错误: %v"               // DNS解析失败日志格式
	LogFormatDNSResolveSuccess = "成功预解析主机名: %s, 找到 %d 个IP地址"       // DNS解析成功日志格式
	LogFormatDNSCacheHit      = "主机名 %s 已在缓存中，跳过预解析"              // DNS缓存命中日志格式
)

// 重试相关常量
const (
	// 重试动作日志格式
	LogFormatRetrySameSuccess = "handleRetrySameAction: 第 %d 次重试成功，状态码: %d"           // 相同代理重试成功日志格式
	LogFormatRetrySameError   = "handleRetrySameAction: 第 %d 次重试返回错误状态码: %d"        // 相同代理重试错误日志格式
	LogFormatRetryStart       = "开始代理连接重试，最大重试次数: %d，初始延迟: %v"              // 重试开始日志格式
	LogFormatRetryDelay       = "代理连接重试延迟: %v"                                      // 重试延迟日志格式

	// 重试相关消息
	MsgRetryAllFailed         = "所有 %d 次重试都失败"                                      // 所有重试失败消息模板
	MsgRetryCompleted         = "完成所有 %d 次重试，返回最后一次成功的响应"                   // 重试完成消息模板
	MsgRetryWithNewProxy      = "开始使用新IP重试 %d 次"                                   // 新代理重试开始消息模板
	MsgRetryWithSameProxy     = "开始使用相同代理 %s 重试 %d 次"                           // 相同代理重试开始消息模板
)

// 错误分类关键字常量
const (
	// TLS相关错误关键字
	ErrorKeywordTLS                = "tls"                                           // TLS错误关键字
	ErrorKeywordHandshake          = "handshake"                                    // 握手错误关键字
	ErrorKeywordTLSHandshakeRecord = "first record does not look like a tls handshake"  // TLS握手记录错误关键字

	// 连接相关错误关键字
	ErrorKeywordConnectionRefused  = "connection refused"                           // 连接拒绝错误关键字
	ErrorKeywordConnectRefused     = "connect: connection refused"                  // 连接拒绝详细错误关键字
	ErrorKeywordTimeout            = "timeout"                                      // 超时错误关键字
	ErrorKeywordDeadlineExceeded   = "deadline exceeded"                           // 截止时间超过错误关键字
	ErrorKeywordNetworkUnreachable = "network is unreachable"                      // 网络不可达错误关键字
	ErrorKeywordNoRouteToHost      = "no route to host"                            // 无路由到主机错误关键字

	// 代理相关错误关键字
	ErrorKeywordProxyConnect       = "proxyconnect"                                // 代理连接错误关键字
	ErrorKeywordProxy              = "proxy"                                       // 代理错误关键字
	ErrorKeywordAuthentication     = "authentication"                              // 认证错误关键字

	// DNS相关错误关键字
	ErrorKeywordNoSuchHost         = "no such host"                                // 无此主机错误关键字
	ErrorKeywordDNS                = "dns"                                         // DNS错误关键字

	// HTTP相关错误关键字
	ErrorKeywordMalformedHTTP      = "malformed http"                              // 格式错误HTTP关键字
	ErrorKeywordBadRequest         = "bad request"                                 // 错误请求关键字
)

// 错误分类类型常量
const (
	ErrorTypeTLSError              = "tls_error"                                   // TLS错误类型
	ErrorTypeTLSProtocolMismatch   = "tls_protocol_mismatch"                      // TLS协议不匹配错误类型
	ErrorTypeConnectionRefused     = "connection_refused"                          // 连接拒绝错误类型
	ErrorTypeTimeout               = "timeout"                                     // 超时错误类型
	ErrorTypeNetworkUnreachable    = "network_unreachable"                        // 网络不可达错误类型
	ErrorTypeProxyConnectError     = "proxy_connect_error"                        // 代理连接错误类型
	ErrorTypeProxyAuthError        = "proxy_auth_error"                           // 代理认证错误类型
	ErrorTypeDNSError              = "dns_error"                                  // DNS错误类型
	ErrorTypeHTTPError             = "http_error"                                 // HTTP错误类型
	ErrorTypeConnectionError       = "connection_error"                           // 连接错误类型（通用）
	ErrorTypeUnknown               = "unknown"                                    // 未知错误类型
)

// 代理池耗尽检测关键字常量
const (
	// 中文错误关键字
	ProxyPoolKeywordNoAvailable    = "代理池无可用代理"                              // 代理池无可用代理关键字
	ProxyPoolKeywordNoAvailableAlt = "代理池中没有可用代理"                          // 代理池无可用代理备选关键字
	ProxyPoolKeywordNone           = "没有可用的代理"                               // 没有可用代理关键字
	ProxyPoolKeywordAllBanned      = "所有代理都被封禁"                             // 所有代理被封禁关键字

	// 英文错误关键字
	ProxyPoolKeywordPool           = "proxy pool"                                  // 代理池英文关键字
	ProxyPoolKeywordNoProxies      = "no available proxies"                       // 无可用代理英文关键字
	ProxyPoolKeywordAllProxiesBanned = "all proxies banned"                       // 所有代理被封禁英文关键字
)

// 代理管理相关日志消息常量
const (
	// 代理池状态日志
	LogMsgCurrentProxyPoolCount     = "当前代理池数量: %d"                                    // 当前代理池数量日志
	LogMsgProxyPoolExhaustedError   = "代理池耗尽错误 - %s %s"                              // 代理池耗尽错误日志
	LogMsgHandleProxyPoolExhausted  = "处理代理池耗尽的请求响应"                              // 处理代理池耗尽响应日志
	LogMsgProxyConnectionError      = "代理连接错误 [%s] - %s %s"                          // 代理连接错误日志
	LogMsgReleaseMutex              = "释放互斥锁"                                         // 释放互斥锁日志
	LogMsgAcquiredMutexStartProxy   = "已获取互斥锁，开始处理代理选择"                        // 获取互斥锁开始代理选择日志

	// HTTP代理设置日志
	LogMsgSetHTTPProxy              = "设置HTTP代理: %s"                                   // 设置HTTP代理日志
	LogMsgDetectDirectProxyConn     = "检测到直接连接到代理服务器 %s，跳过DNS解析"            // 检测直接代理连接日志
	LogMsgDetectProxyAddress        = "检测到地址 %s 不是请求的目标域名 %s，可能是代理地址，直接连接" // 检测代理地址日志

	// 重试代理相关日志
	LogMsgCannotGetCurrentProxy     = "handleRetrySameAction: 无法获取当前代理"             // 无法获取当前代理日志
	LogMsgRetrySameAttempt          = "handleRetrySameAction: 第 %d 次重试，使用代理: %s"   // 相同代理重试尝试日志
	LogMsgRetryPoolInfo             = "handleRetryAction: 代理池总数量: %d, 最小要求: %d, 需要重试次数: %d" // 重试代理池信息日志
	LogMsgRetryPoolInsufficient     = "handleRetryAction: 代理池数量(%d)等于或小于最小要求(%d)，无法进行新IP重试" // 重试代理池不足日志
	LogMsgRetryInsufficientProxies  = "handleRetryAction: 可用于重试的代理数量(%d)不足以满足重试需求(%d次)，停止重试" // 重试可用代理不足日志
	LogMsgRetryAddCurrentProxy      = "handleRetryAction: 将当前代理 %s 添加到已使用列表"     // 重试添加当前代理日志
	LogMsgRetryAttemptNewProxy      = "handleRetryAction: 第 %d 次重试，尝试获取新代理"       // 重试尝试新代理日志
	LogMsgRetryPoolCountCheck       = "handleRetryAction: 代理池数量(%d)已降至最小要求(%d)或以下，停止重试" // 重试代理池数量检查日志
	LogMsgRetryGetProxyFailed       = "handleRetryAction: 第 %d 次重试获取代理失败: %v"      // 重试获取代理失败日志
	LogMsgRetryProxySessionUsed     = "handleRetryAction: 代理 %s 在本次重试会话中已使用过，尝试获取其他代理" // 重试代理会话已使用日志
	LogMsgRetryProxyGlobalCooldown  = "handleRetryAction: 代理 %s 在全局冷却期内被使用过，尝试获取其他代理" // 重试代理全局冷却日志
	LogMsgRetryTooManyAttempts      = "handleRetryAction: 尝试获取新代理次数过多，可能代理池中没有足够的不同代理" // 重试尝试次数过多日志
	LogMsgRetryProxyGlobalBanned    = "handleRetryAction: 代理 %s 被全局封禁，跳过"          // 重试代理全局封禁日志
	LogMsgRetryProxyDomainBanned    = "handleRetryAction: 代理 %s 被域名 %s 封禁，跳过"     // 重试代理域名封禁日志
	LogMsgRetryProxyURLBanned       = "handleRetryAction: 代理 %s 被URL %s 封禁，跳过"     // 重试代理URL封禁日志
	LogMsgRetryProxyPermanentBanned = "handleRetryAction: 代理 %s 被永久封禁，跳过"          // 重试代理永久封禁日志
	LogMsgRetryMaxAttemptsReached   = "handleRetryAction: 第 %d 次重试达到最大尝试次数(%d)，无法找到可用代理，停止后续重试" // 重试达到最大尝试次数日志
	LogMsgRetryCannotGetNewProxy    = "handleRetryAction: 第 %d 次重试无法获取新代理: %v，停止后续重试" // 重试无法获取新代理日志
	LogMsgRetryUsingNewProxy        = "handleRetryAction: 第 %d 次重试使用新代理: %s"        // 重试使用新代理日志
	LogMsgRetryNewProxySuccess      = "handleRetryAction: 第 %d 次重试成功，状态码: %d，使用代理: %s" // 重试新代理成功日志
	LogMsgRetryReadResponseFailed   = "handleRetryAction: 读取重试响应体失败: %v"            // 重试读取响应体失败日志
	LogMsgRetryNewProxyError        = "handleRetryAction: 第 %d 次重试返回错误状态码: %d，使用代理: %s" // 重试新代理错误日志

	// 代理缓存清理日志
	LogMsgCurrentProxyCacheCleared  = "已清除当前代理缓存"                                  // 当前代理缓存清理日志

	// 代理连接重试日志
	LogMsgProxyRetryGetFailed       = "代理连接重试第 %d 次获取代理失败: %v"                 // 代理连接重试获取失败日志
	LogMsgProxyRetrySkipUsed        = "代理连接重试第 %d 次跳过已使用的代理: %s"             // 代理连接重试跳过已使用日志
	LogMsgProxyRetryUsingNew        = "代理连接重试第 %d 次使用新代理: %s"                  // 代理连接重试使用新代理日志
	LogMsgProxyRetryFailed          = "代理连接重试第 %d 次失败，代理: %s，错误: %v"         // 代理连接重试失败日志
	LogMsgProxyRetryReadFailed      = "代理连接重试第 %d 次读取响应体失败: %v"               // 代理连接重试读取失败日志
	LogMsgProxyRetrySuccess         = "代理连接重试第 %d 次成功，使用代理: %s，状态码: %d"    // 代理连接重试成功日志
	LogMsgProxyRetryAllFailed       = "代理连接重试全部失败，已尝试 %d 次"                   // 代理连接重试全部失败日志
)

// 剩余硬编码日志消息常量
const (
	// DNS和网络相关日志
	LogMsgGetDomainForDNS           = "获取请求的实际域名用于DNS解析: %s"                      // 获取域名用于DNS解析日志
	LogMsgGetDNSResolverFromContext = "从上下文中获取DNS解析器"                              // 从上下文获取DNS解析器日志
	LogMsgHostIsIPAddress           = "主机名 %s 已经是IP地址，跳过DNS解析"                   // 主机名是IP地址日志
	LogMsgUseCachedDNSResult        = "使用上下文中缓存的DNS解析结果: %s -> %v"              // 使用缓存DNS结果日志
	LogMsgUseDNSResolver            = "使用DNS解析器解析主机名: %s"                         // 使用DNS解析器日志
	LogMsgDNSResolveFallback        = "使用自定义DNS服务器解析失败: %v，尝试使用系统默认解析器" // DNS解析回退日志

	// 触发器相关日志
	LogMsgTriggerManagerEmpty       = "触发器管理器为空，跳过触发器执行"                      // 触发器管理器为空日志
	LogMsgProcessTriggerType        = "处理触发器类型: %s, 处理阶段: %s, 事件名称: %s"       // 处理触发器类型日志
	LogMsgTriggerNotMatch           = "触发器不匹配，继续检查下一个触发器"                    // 触发器不匹配日志
	LogMsgTriggerConfigMissing      = "无法获取触发器 %T 的事件配置，跳过此触发器的动作执行"   // 触发器配置缺失日志
	LogMsgTriggerMatchAttempt       = "尝试匹配触发器，类型: %T"                           // 尝试匹配触发器日志
	LogMsgActionInstanceNil         = "executeTriggers: actionInstance is nil AFTER BuildActionFromConfig for type %s. buildErr: %v" // 动作实例为空日志
	LogMsgActionInstanceNotNil      = "executeTriggers: actionInstance is NOT nil AFTER BuildActionFromConfig for type %s. Type: %T. buildErr: %v" // 动作实例非空日志
	LogMsgExecutingAction           = "Executing action %T for event %s"                  // 执行动作日志
	LogMsgActionContextNil          = "executeTriggers: actCtx is nil before calling actionInstance.Execute for action type %s!" // 动作上下文为空日志
	LogMsgActionContextNotNil       = "executeTriggers: actCtx is NOT nil before calling actionInstance.Execute. Type: %T" // 动作上下文非空日志
	LogMsgRequestContextNil         = "executeTriggers: reqWithDNSCtx is nil before calling actionInstance.Execute for action type %s!" // 请求上下文为空日志
	LogMsgRequestContextNotNil      = "executeTriggers: reqWithDNSCtx is NOT nil before calling actionInstance.Execute. Method: %s, URL: %s" // 请求上下文非空日志
	LogMsgActionSetFinalResponse    = "动作 %s 已设置最终响应或停止处理。"                    // 动作设置最终响应日志
	LogMsgHighPriorityTrigger       = "最高优先级触发器或停止信号，立即返回响应"              // 最高优先级触发器日志
	LogMsgActionBuildFailed         = "Failed to build action %s for event %s: %v"       // 动作构建失败日志
	LogMsgActionExecuteFailed       = "执行动作 %s 失败: %v"                              // 动作执行失败日志

	// 阶段触发器相关日志
	LogMsgStartPostHeaderTrigger    = "开始执行post_header阶段触发器，URL: %s"              // 开始post_header触发器日志
	LogMsgPostHeaderTriggerComplete = "post_header阶段触发器执行完成，耗时: %v"             // post_header触发器完成日志
	LogMsgPostHeaderNewResponse     = "post_header阶段触发器生成了新的响应，状态码: %d"      // post_header新响应日志
	LogMsgNoPostHeaderTrigger       = "没有post_header阶段触发器或触发器管理器为空"         // 无post_header触发器日志
	LogMsgStartPostBodyTrigger      = "开始执行post_body阶段触发器，URL: %s"               // 开始post_body触发器日志
	LogMsgPostBodyTriggerComplete   = "post_body阶段触发器执行完成，耗时: %v"              // post_body触发器完成日志
	LogMsgPostBodyNewResponse       = "post_body阶段触发器生成了新的响应，状态码: %d"       // post_body新响应日志
	LogMsgNoPostBodyTrigger         = "没有post_body阶段触发器或触发器管理器为空"          // 无post_body触发器日志

	// 通用处理日志
	LogMsgRequestTotalTime          = "请求总耗时: %v"                                    // 请求总耗时日志
	LogMsgRequestContextMissingStartTime = "请求上下文缺少 startTime，设置 requestTime=0" // 请求上下文缺少开始时间日志
	LogMsgStartRotateProxy          = "开始执行rotateProxy函数"                          // 开始代理轮换日志
	LogMsgTryAcquireMutex           = "尝试获取互斥锁"                                   // 尝试获取互斥锁日志
	LogMsgEventConfigEmpty          = "事件配置为空，无法创建DNS解析器"                    // 事件配置为空日志
	LogMsgCreateDNSResolverForEvent = "为事件创建DNS解析器，模式: %s"                     // 为事件创建DNS解析器日志
	LogMsgRequestURLInfo            = "请求URL: %s, 主机名: %s"                         // 请求URL信息日志
)

// 剩余的硬编码日志消息常量
const (
	// 动作重试相关日志
	LogMsgActionRequiresSameProxyRetry = "动作 %s 要求使用相同代理重试(retry_same) %d 次"     // 动作要求相同代理重试日志
	LogMsgActionRequiresNewProxyRetry  = "动作 %s 要求使用新代理重试(retry) %d 次"           // 动作要求新代理重试日志
	LogMsgActionUnknownRetryType       = "动作 %s 要求未知的重试类型: %s"                   // 动作未知重试类型日志
	LogMsgActionStopProcessingNil      = "Action %s indicated stopProcessing, finalResp might be nil." // 动作停止处理响应为空日志
	LogMsgCreatedNullResponse          = "Created null response for stopProcessing action %s"          // 创建空响应日志
	LogMsgActionCanceled               = "Action %s for event %s was canceled, stopping further processing in this sequence." // 动作被取消日志
)

// 性能监控相关常量
const (
	// 性能阈值
	TriggerExecutionSlowThreshold = 100 * time.Millisecond // 触发器执行慢查询阈值
	DNSCacheSlowThreshold         = 50 * time.Millisecond  // DNS缓存操作慢查询阈值

	// CPU监控相关常量
	CPUMonitoringSampleInterval   = 100 * time.Millisecond // CPU采样间隔
	CPUMonitoringWindowSize       = 10                     // CPU监控时间窗口大小（样本数）
	CPUMonitoringMaxSamples       = 100                    // CPU监控最大样本数
	CPUMonitoringMinSamples       = 3                      // CPU监控最小样本数
	CPUMonitoringDefaultUsage     = 0.0                    // CPU监控默认使用率
	CPUMonitoringMaxUsage         = 100.0                  // CPU监控最大使用率
	CPUMonitoringSmoothingFactor  = 0.3                    // CPU使用率平滑因子
	CPUMonitoringGCWeightFactor   = 0.1                    // GC时间权重因子
	CPUMonitoringBaselineUsage    = 1.0                    // 基线CPU使用率

	// 性能监控日志消息
	LogMsgTriggerExecutionSlow = "触发器执行时间过长: %v, 阶段: %s, URL: %s"     // 触发器执行慢查询日志
	LogMsgDNSCacheOperationSlow = "DNS缓存操作时间过长: %v, 主机名: %s"        // DNS缓存操作慢查询日志
	LogMsgConcurrentProxyAccess = "并发代理访问统计: 读取次数: %d, 写入次数: %d" // 并发代理访问统计日志
	LogMsgCPUMonitoringSample   = "CPU监控采样: 使用率=%.2f%%, 样本数=%d"      // CPU监控采样日志
	LogMsgCPUMonitoringReset    = "CPU监控重置: 清除%d个样本"                // CPU监控重置日志

	// 代理质量管理错误消息
	ErrMsgProxyQualityUpdateFailed = "代理质量信息更新失败"
	ErrMsgProxyQualityDataInvalid  = "代理质量数据无效"
	ErrMsgProxyMetricsNotFound     = "代理性能指标未找到"

	// 智能策略错误消息
	ErrMsgSmartStrategyInitFailed  = "智能策略初始化失败"
	ErrMsgDecisionContextInvalid   = "决策上下文无效"
	ErrMsgAdaptiveWeightsInvalid   = "自适应权重配置无效"

	// 表达式解析器错误消息
	ErrMsgExpressionParseError     = "表达式解析错误"
	ErrMsgExpressionEvaluateError  = "表达式求值错误"
	ErrMsgExpressionSyntaxError    = "表达式语法错误"
	ErrMsgExpressionTypeError      = "表达式类型错误"
	ErrMsgFunctionNotFound         = "函数未找到"
	ErrMsgInvalidArgumentCount     = "参数数量无效"
	ErrMsgDivisionByZero          = "除零错误"
	ErrMsgInvalidOperator         = "无效的运算符"
	ErrMsgTypeConversionFailed    = "类型转换失败"
)

// 验证相关常量
const (
	// 长度限制常量
	MaxCacheKeyLength        = 255  // 最大缓存键长度
	MaxPoolNameLength        = 100  // 最大代理池名称长度
	MaxBanReasonLength       = 255  // 最大封禁原因长度
	MaxHeaderStringLength    = 8192 // 最大头部字符串长度
	MaxHeaderCount           = 50   // 最大头部数量
	MaxScriptHeaderSize      = 8192 // 最大脚本头部大小

	// 分数范围常量
	MinQualityScore = 0.0   // 最小质量分数
	MaxQualityScore = 100.0 // 最大质量分数

	// 注意：质量等级常量已统一到代理相关常量部分（第76-79行）
	// 使用 QualityTierPremium, QualityTierStandard, QualityTierBackup

	// 缓存作用域常量
	CacheScopeURL    = "url"    // URL作用域
	CacheScopeDomain = "domain" // 域名作用域
	CacheScopeGlobal = "global" // 全局作用域

	// 脚本引擎常量
	ScriptEngineJavaScript = "javascript" // JavaScript引擎
	ScriptEngineJS         = "js"         // JavaScript引擎简写
	ScriptEngineLua        = "lua"        // Lua引擎

	// 脚本类型常量
	ScriptTypeInline = "inline" // 内联脚本
	ScriptTypeFile   = "file"   // 文件脚本

	// 头部前缀常量
	HeaderPrefixFlexProxyCustom = "X-FlexProxy-Custom-"  // 自定义头部前缀
	HeaderPrefixFlexProxyHeader = "X-FlexProxy-Header-"  // 请求头部前缀

	// 特殊头部常量
	HeaderFlexProxyNullBody        = "X-FlexProxy-Null-Body"        // 空响应体头部
	HeaderFlexProxyScriptContent   = "X-FlexProxy-Script-Content"   // 脚本内容头部
	HeaderFlexProxyTargetURL       = "X-FlexProxy-Target-URL"       // 目标URL头部
	HeaderFlexProxyRequestBody     = "X-FlexProxy-Request-Body"     // 请求体头部
	HeaderFlexProxyNoRedirect      = "X-FlexProxy-No-Redirect"      // 不跟随重定向头部
	HeaderFlexProxyBanType         = "X-FlexProxy-Ban-Type"         // 封禁类型头部
	HeaderFlexProxyBanPermanent    = "X-FlexProxy-Ban-Permanent"    // 永久封禁头部
	HeaderFlexProxyDomainSpecific  = "X-FlexProxy-Domain-Specific"  // 域名特定头部
	HeaderFlexProxyTargetDomain    = "X-FlexProxy-Target-Domain"    // 目标域名头部
	HeaderFlexProxyCurrentProxy    = "X-FlexProxy-Current-Proxy"    // 当前代理头部
	HeaderFlexProxyCustomCacheKey  = "X-FlexProxy-Custom-Cache-Key" // 自定义缓存键头部

	// 默认值常量
	DefaultCacheScopeGlobal = "global"     // 默认全局缓存作用域
	DefaultUserAgentHeader  = "User-Agent" // 默认用户代理头部名称

	// 日志消息常量
	LogMsgHTTPRequestBlocked     = "HTTP请求已被阻止: method=%s, url=%s, reason=%s, status_code=%d, remote_addr=%s"
	LogMsgParameterMissing       = "无法获取要封禁的%s"
	LogMsgIPBanFailureDetails    = "IP封禁失败: ip=%s, duration=%d秒, error=%v"
	LogMsgDomainBanFailureDetails = "域名封禁失败: domain=%s, duration=%d秒, scope=%s, permanent=%v, error=%v"

	// 错误消息常量
	ErrMsgIPBanExecutionFailed    = "IP封禁执行失败"
	ErrMsgDomainBanExecutionFailed = "域名封禁执行失败"
	ErrMsgInlineScriptSizeExceeded = "内联脚本大小超过限制"

	// 封禁原因前缀常量
	BanReasonPermanentPrefix = "永久"        // 永久封禁原因前缀
	BanReasonScopePrefix     = " (范围: "    // 封禁范围前缀
	BanReasonScopeSuffix     = ")"          // 封禁范围后缀

	// 目标类型常量
	TargetTypeIP     = "ip"     // IP目标类型
	TargetTypeDomain = "domain" // 域名目标类型
	TargetTypeCIDR   = "cidr"   // CIDR目标类型

	// 参数名称常量
	ParamNameIP              = "ip"              // IP参数名
	ParamNameDomain          = "domain"          // 域名参数名
	ParamNameCIDR            = "cidr"            // CIDR参数名
	ParamNameRetryCount      = "retry_count"     // 重试次数参数名
	ParamNameAttempts        = "attempts"        // 尝试次数参数名
	ParamNameDelay           = "delay"           // 延迟参数名
	ParamNameRotationMode    = "rotation_mode"   // 轮换模式参数名
	ParamNameTimeoutMS       = "timeout_ms"      // 超时毫秒参数名
	ParamNameTimeout         = "timeout"         // 超时参数名
	ParamNameKeepAlive       = "keep_alive"      // 保持连接参数名
	ParamNameDNSMode         = "dns_mode"        // DNS模式参数名
	ParamNameDuration        = "duration"        // 持续时间参数名
	ParamNameKey             = "key"             // 键参数名
	ParamNameCacheScope      = "cache_scope"     // 缓存作用域参数名
	ParamNameQualityTier     = "quality_tier"    // 质量等级参数名
	ParamNameDomainSpecific  = "domain_specific" // 域名特定参数名
	ParamNameMinScore        = "min_score"       // 最小分数参数名
	ParamNamePoolName        = "pool_name"       // 代理池名称参数名
	ParamNameStatusCode      = "status_code"     // 状态码参数名
	ParamNameContentType     = "content_type"    // 内容类型参数名
	ParamNameBody            = "body"            // 响应体参数名
	ParamNameHeaders         = "headers"         // 头部参数名
	ParamNameEngine          = "engine"          // 引擎参数名
	ParamNameScript          = "script"          // 脚本参数名
	ParamNameScriptFile      = "script_file"     // 脚本文件参数名
	ParamNameURL             = "url"             // URL参数名
	ParamNameMethod          = "method"          // 方法参数名
	ParamNameRetries         = "retries"         // 重试参数名
	ParamNameFollowRedirect  = "follow_redirect" // 跟随重定向参数名
	ParamNameMaxUseCount     = "max_use_count"   // 最大使用次数参数名
	ParamNameCustomKey       = "custom_key"      // 自定义键参数名
	ParamNameIgnoreParams    = "ignore_params"   // 忽略参数参数名
	ParamNameReason          = "reason"          // 原因参数名
	ParamNamePermanent       = "permanent"       // 永久参数名

	// 头部分隔符常量
	HeaderValueSeparator = ","  // 头部值分隔符
	HeaderKeyValueSeparator = ":" // 头部键值分隔符

	// 默认重试次数
	DefaultRetryCountZero = 0 // 默认不重试
)

// 热重载扩展功能常量
const (
	// 热重载相关常量
	DefaultMaxMigrationHistory = 100
	DefaultWatchInterval       = 1 * time.Second
	DefaultDebounceInterval    = 500 * time.Millisecond
	DefaultMaxChangeHistory    = 200
	DefaultBackupInterval      = 1 * time.Hour

	// 优先级常量
	HighPriority   = 1
	MediumPriority = 2
	LowPriority    = 3

	// 性能相关常量
	DefaultLearningRate     = 0.01
	DefaultDecayFactor      = 0.99
	DefaultStabilityWindow  = 24
	MaxErrorRecords         = 100
)
