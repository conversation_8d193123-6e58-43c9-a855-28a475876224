package common

import (
	"context"
	"time"

	"github.com/fsnotify/fsnotify"
)

// 注意：配置类型已移动到 internal/config 包
// 如需使用配置类型，请直接导入 "flexproxy/internal/config"

// ProxyManagerInterface 代理管理器接口
// 定义了代理管理器的核心功能，包括代理操作、质量管理、封禁管理等
type ProxyManagerInterface interface {
	// 基本代理操作
	GetProxy(mode string) (string, error)
	RotateProxy(domain ...string) (string, error)
	GetProxyForDomain(mode, domain string) (string, error)
	GetQualityProxy(tier, domain string) (string, error)

	// 代理池管理
	GetProxyCount() int
	Count() int // 别名方法，与GetProxyCount相同
	GetProxies() []string
	GetAvailableProxies() []string
	RefreshProxyPool() error
	ValidateProxy(proxy string) bool
	RemoveProxy(proxy string) error

	// 质量管理
	UpdateProxyQuality(proxy string, success bool, responseTime time.Duration)
	GetProxyQuality(proxy string) float64
	GetQualityStats() map[string]interface{}

	// 失败代理管理
	MarkProxyAsFailed(proxy string)
	ClearProxyFailureStatus(proxy string)
	ClearProxyCacheIfFailed(proxy string)

	// 封禁管理
	BanIP(ip string, duration string, reason string, domain string) error
	BanDomain(domain string, duration string) error
	UnbanIP(ip string) error
	UnbanDomain(domain string) error
	IsIPBanned(ip, resource, scope string) bool
	IsDomainBanned(domain string) bool
	IsIPPermanentlyBlocked(ip string) bool
	GetBannedIPs() []string
	GetBannedDomains() []string

	// 配置管理
	UpdateConfig(config interface{}) error
	GetConfig() interface{}
	ReloadConfig() error

	// 统计信息
	GetStats() map[string]interface{}
	GetUsageStats() map[string]interface{}
	ResetStats()

	// 生命周期管理
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
	IsRunning() bool
	Health() error
	InitBanSystem(cfg interface{})
	StartBanCleaner(ctx context.Context)
	Watch() (*fsnotify.Watcher, error)
	Reload() error

	// 高级功能
	SetRetryPolicy(policy string) error
	GetRetryPolicy() string
	EnableSmartMode(enabled bool)
	IsSmartModeEnabled() bool
}
