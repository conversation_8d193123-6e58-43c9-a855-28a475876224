// Package logger 提供日志功能
package logger

import (
	"os"
	"sync"

	"github.com/mbndr/logo"
)

// Logger 直接使用logo.Logger作为统一接口
type Logger = *logo.Logger

// LoggerAdapter 适配器，用于兼容container.LogService接口
type LoggerAdapter struct {
	logger *logo.Logger
}

// NewLoggerAdapter 创建日志适配器
func NewLoggerAdapter(module string) *LoggerAdapter {
	return &LoggerAdapter{
		logger: logo.NewSimpleLogger(os.Stdout, logo.DEBUG, module+": ", true),
	}
}

// Info 记录信息日志
func (la *LoggerAdapter) Info(msg string, args ...interface{}) {
	if len(args) > 0 {
		la.logger.Infof(msg, args...)
	} else {
		la.logger.Info(msg)
	}
}

// Warn 记录警告日志
func (la *LoggerAdapter) Warn(msg string, args ...interface{}) {
	if len(args) > 0 {
		la.logger.Warnf(msg, args...)
	} else {
		la.logger.Warn(msg)
	}
}

// Error 记录错误日志
func (la *LoggerAdapter) Error(msg string, args ...interface{}) {
	if len(args) > 0 {
		la.logger.Errorf(msg, args...)
	} else {
		la.logger.Error(msg)
	}
}

// Debug 记录调试日志
func (la *LoggerAdapter) Debug(msg string, args ...interface{}) {
	if len(args) > 0 {
		la.logger.Debugf(msg, args...)
	} else {
		la.logger.Debug(msg)
	}
}

// Fatal 记录致命错误日志
func (la *LoggerAdapter) Fatal(msg string, args ...interface{}) {
	if len(args) > 0 {
		la.logger.Fatalf(msg, args...)
	} else {
		la.logger.Fatal(msg)
	}
}

// WithTraceID 添加跟踪ID支持
func (la *LoggerAdapter) WithTraceID(traceID string) interface{} {
	// 创建新的适配器实例，包含traceID信息
	newAdapter := &LoggerAdapter{
		logger: la.logger,
	}
	return newAdapter
}

// WithFields 添加字段支持
func (la *LoggerAdapter) WithFields(fields map[string]interface{}) interface{} {
	// 创建新的适配器实例，包含字段信息
	newAdapter := &LoggerAdapter{
		logger: la.logger,
	}
	return newAdapter
}

// LogError 记录错误
func (la *LoggerAdapter) LogError(err error) {
	if err != nil {
		la.logger.Error(err.Error())
	}
}

// GetLogger 获取logger实例
func (la *LoggerAdapter) GetLogger() interface{} {
	return la
}

// UpdateConfig 更新配置
// LoggerAdapter作为基础日志适配器，使用固定的logo.Logger配置
// 设计为轻量级适配器，不支持动态配置更新
// 如需完整的配置管理功能，请使用 internal/services/basic.logService
func (la *LoggerAdapter) UpdateConfig(config interface{}) error {
	// 基本参数验证：对于简单适配器，nil配置是可接受的
	// 此方法主要用于满足 interfaces.LogService 接口要求

	// LoggerAdapter使用固定配置，不执行实际的配置更新操作
	// 这是架构设计的分层：基础适配器保持简单，复杂配置管理在服务层实现
	return nil
}

// NewSimpleLogger 创建基于logo库的日志器
func NewSimpleLogger(module string) Logger {
	// 直接返回logo实例，使用模块名作为前缀
	return logo.NewSimpleLogger(os.Stdout, logo.DEBUG, module+": ", true)
}

// LoggerManager 管理日志器，统一管理所有模块的logger实例
type LoggerManager struct {
	loggers  map[string]Logger
	adapters map[string]*LoggerAdapter
	mutex    sync.RWMutex
}

var (
	// 全局logger管理器实例
	manager *LoggerManager
	once    sync.Once
)

// GetManager 获取全局logger管理器实例（单例模式）
func GetManager() *LoggerManager {
	once.Do(func() {
		manager = &LoggerManager{
			loggers:  make(map[string]Logger),
			adapters: make(map[string]*LoggerAdapter),
		}
	})
	return manager
}

// GetLogger 获取指定模块的logger，如果不存在则创建
func (lm *LoggerManager) GetLogger(module string) Logger {
	lm.mutex.RLock()
	logger, exists := lm.loggers[module]
	lm.mutex.RUnlock()

	if exists {
		return logger
	}

	// 如果不存在，创建新的logger
	lm.mutex.Lock()
	defer lm.mutex.Unlock()

	// 双重检查，防止并发创建
	if logger, exists := lm.loggers[module]; exists {
		return logger
	}

	// 创建新的logger实例
	logger = NewSimpleLogger(module)
	lm.loggers[module] = logger
	return logger
}

// GetLoggerAdapter 获取指定模块的logger适配器，如果不存在则创建
func (lm *LoggerManager) GetLoggerAdapter(module string) *LoggerAdapter {
	lm.mutex.RLock()
	adapter, exists := lm.adapters[module]
	lm.mutex.RUnlock()

	if exists {
		return adapter
	}

	// 如果不存在，创建新的适配器
	lm.mutex.Lock()
	defer lm.mutex.Unlock()

	// 双重检查，防止并发创建
	if adapter, exists := lm.adapters[module]; exists {
		return adapter
	}

	// 创建新的适配器实例
	adapter = NewLoggerAdapter(module)
	lm.adapters[module] = adapter
	return adapter
}

// GetLogger 获取日志器，便捷函数，直接获取指定模块的logger
func GetLogger(module string) Logger {
	return GetManager().GetLogger(module)
}

// 便捷函数，直接获取指定模块的logger适配器
func GetLoggerAdapter(module string) *LoggerAdapter {
	return GetManager().GetLoggerAdapter(module)
}

// 预定义的模块常量
const (
	ModuleMain                = "MAIN"
	ModuleRunner              = "RUNNER"
	ModuleDaemon              = "DAEMON"
	ModuleHandler             = "HANDLER"
	ModuleLogService          = "LOG_SERVICE"
	ModuleTrigger             = "TRIGGER"
	ModuleDNSService          = "DNS_SERVICE"
	ModuleConfigService       = "CONFIG_SERVICE"
	ModuleProxyService        = "PROXY_SERVICE"
	ModuleActionService       = "ACTION_SERVICE"
	ModuleCacheService        = "CACHE_SERVICE"
	ModuleTransport           = "TRANSPORT"
	ModuleAction              = "ACTION"
	ModuleErrorChain          = "ERROR_CHAIN"
	ModuleProxyManager        = "PROXY_MANAGER"
	ModuleTriggerService      = "TRIGGER_SERVICE"
	ModuleServer              = "SERVER"
	ModuleError               = "ERROR"
	ModuleRateLimitingService = "RATE_LIMITING_SERVICE"
	ModuleSecurityService     = "SECURITY_SERVICE"
	ModulePluginService       = "PLUGIN_SERVICE"
	ModuleHotReload           = "HOT_RELOAD"
	ModuleProxy               = "PROXY"
	ModuleStatistics          = "STATISTICS"
	ModuleCache               = "CACHE"
	ModuleConfig              = "CONFIG"
)
