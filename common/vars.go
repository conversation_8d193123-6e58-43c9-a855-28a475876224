package common

var (
	// App 应用程序名称
	App = "FlexProxy"
	// Version FlexProxy版本号
	Version = ""
	// Author 作者信息
	Author = "x-Ai"
	// 横幅信息
	Banner = `
               ` + Version + `
  _____ _           ____
 |  ___| | _____  _|  _ \ _ __ _____  ___   _
 | |_  | |/ _ \ \/ / |_) | '__/ _ \ \/ / | | |
 |  _| | |  __/>  <|  __/| | | (_) >  <| |_| |
 |_|   |_|\___/_/\_\_|   |_|  \___/_/\_\\__, |
                                        |___/
  by ` + Author
	// 使用说明
	Usage = `
  FlexProxy [-c|-a :8080] -f file.txt [options...]

Options:
  GENERAL
    -f, --file <FILE>                Proxy file (required)
    -o, --output <FILE>              Write log output to FILE
    -t, --timeout <TIME>             Max. time allowed for connection (default: 30s)
    -v, --verbose                    Verbose mode
    -V, --version                    Show current FlexProxy version
        --max-retries <N>            Max. retries for failed HTTP requests (default: 0)

  PROXY CHECKER
    -c, --check                      Perform proxy check
    -g, --goroutine <N>              Max. goroutine to use (default: 50)
        --only-cc <AA>,<BB>          Only for specific country code (comma separated)

  IP ROTATOR
    -a, --address <ADDR>:<PORT>      Run proxy server (optional if config file specifies ports)
    -A, --auth <USER>:<PASS>         Set authorization for proxy server
    -d, --daemon                     Daemonize proxy server
    -s, --sync                       Syncrounus mode
    -w, --watch                      Watch proxy and config file, live-reload from changes
	--type                       http or socks5 proxy server type ( default: http ) ！socks5 does not support rule engines！

Examples:
  FlexProxy -f proxies.txt --check --output live.txt
  FlexProxy -a localhost:8089 -f live.txt -config config.yaml
  FlexProxy -f live.txt -config config.yaml                    # Uses ports from config file

`
)
