package errors

import (
	"fmt"
	"runtime"
	"strings"
	"time"

	"flexproxy/common/logger"
)

// 模块级别的日志器
var errorLogger = logger.GetErrorLogger()

// ErrorChain 错误链，用于追踪错误的传播路径
type Error<PERSON>hain struct {
	Errors    []*FlexProxyError `json:"errors"`
	TraceID   string            `json:"trace_id"`
	StartTime time.Time         `json:"start_time"`
	EndTime   time.Time         `json:"end_time"`
}

// NewErrorChain 创建新的错误链
func NewErrorChain(traceID string) *ErrorChain {
	return &ErrorChain{
		Errors:    make([]*FlexProxyError, 0),
		TraceID:   traceID,
		StartTime: time.Now(),
	}
}

// AddError 向错误链添加错误
func (ec *Error<PERSON>hain) AddError(err *FlexProxyError) {
	if err != nil {
		err.TraceID = ec.TraceID
		ec.Errors = append(ec.Errors, err)
		ec.EndTime = time.Now()
	}
}

// GetRootError 获取根错误（第一个错误）
func (ec *ErrorChain) GetRootError() *FlexProxyError {
	if len(ec.Errors) > 0 {
		return ec.Errors[0]
	}
	return nil
}

// GetLatestError 获取最新错误（最后一个错误）
func (ec *ErrorChain) GetLatestError() *FlexProxyError {
	if len(ec.Errors) > 0 {
		return ec.Errors[len(ec.Errors)-1]
	}
	return nil
}

// GetErrorCount 获取错误数量
func (ec *ErrorChain) GetErrorCount() int {
	return len(ec.Errors)
}

// GetDuration 获取错误链持续时间
func (ec *ErrorChain) GetDuration() time.Duration {
	if ec.EndTime.IsZero() {
		return time.Since(ec.StartTime)
	}
	return ec.EndTime.Sub(ec.StartTime)
}

// String 返回错误链的字符串表示
func (ec *ErrorChain) String() string {
	if len(ec.Errors) == 0 {
		return "空错误链"
	}

	var builder strings.Builder
	builder.WriteString(fmt.Sprintf("错误链 [TraceID: %s, 持续时间: %v]:\n", ec.TraceID, ec.GetDuration()))

	for i, err := range ec.Errors {
		builder.WriteString(fmt.Sprintf("  %d. [%s] %s", i+1, err.Timestamp.Format("15:04:05.000"), err.Error()))
		if err.File != "" {
			builder.WriteString(fmt.Sprintf(" (%s:%d)", getShortFileName(err.File), err.Line))
		}
		builder.WriteString("\n")
	}

	return builder.String()
}

// ErrorTracker 错误追踪器
type ErrorTracker struct {
	chains    map[string]*ErrorChain
	maxChains int
}

// NewErrorTracker 创建新的错误追踪器
func NewErrorTracker(maxChains int) *ErrorTracker {
	if maxChains <= 0 {
		maxChains = 1000 // 默认最大1000个错误链
	}
	return &ErrorTracker{
		chains:    make(map[string]*ErrorChain),
		maxChains: maxChains,
	}
}

// StartChain 开始一个新的错误链
func (et *ErrorTracker) StartChain(traceID string) *ErrorChain {
	// 如果达到最大链数，清理最旧的链
	if len(et.chains) >= et.maxChains {
		et.cleanup()
	}

	chain := NewErrorChain(traceID)
	et.chains[traceID] = chain
	return chain
}

// GetChain 获取指定的错误链
func (et *ErrorTracker) GetChain(traceID string) *ErrorChain {
	return et.chains[traceID]
}

// TrackError 追踪错误到指定的链
func (et *ErrorTracker) TrackError(traceID string, err *FlexProxyError) {
	chain := et.GetChain(traceID)
	if chain == nil {
		chain = et.StartChain(traceID)
	}
	chain.AddError(err)
}

// GetAllChains 获取所有错误链
func (et *ErrorTracker) GetAllChains() map[string]*ErrorChain {
	return et.chains
}

// cleanup 清理最旧的错误链
func (et *ErrorTracker) cleanup() {
	if len(et.chains) == 0 {
		return
	}

	// 找到最旧的链并删除
	var oldestTraceID string
	var oldestTime time.Time

	for traceID, chain := range et.chains {
		if oldestTime.IsZero() || chain.StartTime.Before(oldestTime) {
			oldestTime = chain.StartTime
			oldestTraceID = traceID
		}
	}

	if oldestTraceID != "" {
		delete(et.chains, oldestTraceID)
	}
}

// 全局错误追踪器
var globalErrorTracker = NewErrorTracker(1000)

// TrackErrorGlobal 向全局错误追踪器添加错误
func TrackErrorGlobal(traceID string, err *FlexProxyError) {
	globalErrorTracker.TrackError(traceID, err)
}

// GetErrorChain 获取全局错误链
func GetErrorChain(traceID string) *ErrorChain {
	return globalErrorTracker.GetChain(traceID)
}

// StartErrorChain 开始全局错误链
func StartErrorChain(traceID string) *ErrorChain {
	return globalErrorTracker.StartChain(traceID)
}

// ErrorWithChain 带错误链的错误创建函数
func ErrorWithChain(traceID string, errorType ErrorType, code ErrorCode, message string) *FlexProxyError {
	err := NewError(errorType, code, message)
	err.TraceID = traceID
	TrackErrorGlobal(traceID, err)
	return err
}

// ErrorWithChainAndDetails 带错误链和详细信息的错误创建函数
func ErrorWithChainAndDetails(traceID string, errorType ErrorType, code ErrorCode, message, details string) *FlexProxyError {
	err := NewErrorWithDetails(errorType, code, message, details)
	err.TraceID = traceID
	TrackErrorGlobal(traceID, err)
	return err
}

// WrapErrorWithChain 包装错误并添加到错误链
func WrapErrorWithChain(traceID string, cause error, errorType ErrorType, code ErrorCode, message string) *FlexProxyError {
	err := WrapError(cause, errorType, code, message)
	err.TraceID = traceID
	TrackErrorGlobal(traceID, err)
	return err
}

// WrapErrorWithChainAndDetails 包装错误并添加到错误链（带详细信息）
func WrapErrorWithChainAndDetails(traceID string, cause error, errorType ErrorType, code ErrorCode, message, details string) *FlexProxyError {
	err := WrapErrorWithDetails(cause, errorType, code, message, details)
	err.TraceID = traceID
	TrackErrorGlobal(traceID, err)
	return err
}

// getShortFileName 获取短文件名
func getShortFileName(fullPath string) string {
	if idx := strings.LastIndex(fullPath, "/"); idx != -1 {
		return fullPath[idx+1:]
	}
	return fullPath
}

// PrintErrorChain 打印错误链（用于调试）
func PrintErrorChain(traceID string) {
	chain := GetErrorChain(traceID)
	if chain != nil {
		errorLogger.Debug(chain.String())
	} else {
		// 使用原始logger的Debugf方法进行格式化输出
		errorLogger.GetRawLogger().Debugf("未找到 TraceID: %s 的错误链", traceID)
	}
}

// GetErrorStatistics 获取错误统计信息
func GetErrorStatistics() map[string]interface{} {
	chains := globalErrorTracker.GetAllChains()
	totalChains := len(chains)
	totalErrors := 0
	errorTypeCount := make(map[ErrorType]int)
	errorCodeCount := make(map[ErrorCode]int)

	for _, chain := range chains {
		totalErrors += len(chain.Errors)
		for _, err := range chain.Errors {
			errorTypeCount[err.Type]++
			errorCodeCount[err.Code]++
		}
	}

	return map[string]interface{}{
		"total_chains":             totalChains,
		"total_errors":             totalErrors,
		"error_type_count":         errorTypeCount,
		"error_code_count":         errorCodeCount,
		"average_errors_per_chain": float64(totalErrors) / float64(totalChains),
	}
}

// ClearErrorChains 清理所有错误链（用于测试或重置）
func ClearErrorChains() {
	globalErrorTracker.chains = make(map[string]*ErrorChain)
}

// GenerateTraceID 生成追踪ID
func GenerateTraceID() string {
	// 使用时间戳和调用栈信息生成唯一ID
	pc, _, _, _ := runtime.Caller(1)
	funcName := runtime.FuncForPC(pc).Name()
	timestamp := time.Now().UnixNano()
	return fmt.Sprintf("%s-%d", getShortFunctionName(funcName), timestamp)
}

// getShortFunctionName 获取短函数名
func getShortFunctionName(fullName string) string {
	if idx := strings.LastIndex(fullName, "."); idx != -1 {
		return fullName[idx+1:]
	}
	return fullName
}
