// Package errors 提供错误处理功能
package errors

import (
	"fmt"
	"runtime"
	"time"
)

// ErrorType 错误类型枚举
type ErrorType string

// String 返回错误类型字符串
func (e ErrorType) String() string {
	return string(e)
}

const (
	// 错误类型
	ErrTypeSystem         ErrorType = "SYSTEM"
	ErrTypeNetwork        ErrorType = "NETWORK"
	ErrTypeProxy          ErrorType = "PROXY"
	ErrTypeProxyManager   ErrorType = "PROXY_MANAGER"
	ErrTypeTrigger        ErrorType = "TRIGGER"
	ErrTypeAction         ErrorType = "ACTION"
	ErrTypePolicy         ErrorType = "POLICY"
	ErrTypeStrategy       ErrorType = "STRATEGY"
	ErrTypeLoadBalancer   ErrorType = "LOAD_BALANCER"
	ErrTypeDNS            ErrorType = "DNS"
	ErrTypeCache          ErrorType = "CACHE"
	ErrTypeValidation     ErrorType = "VALIDATION"
	ErrTypeAuth           ErrorType = "AUTH"
	ErrTypeFile           ErrorType = "FILE"
	ErrTypeConfig         ErrorType = "CONFIG"
	ErrTypeConfiguration  ErrorType = "CONFIGURATION"
	ErrTypeInitialization ErrorType = "INITIALIZATION"
	ErrTypeTimeout        ErrorType = "TIMEOUT"
	ErrTypeHTTP           ErrorType = "HTTP"
	ErrTypeAWS            ErrorType = "AWS"
	ErrTypeOperation      ErrorType = "OPERATION"
	ErrTypeMonitoring     ErrorType = "MONITORING"
	ErrTypeSecurity       ErrorType = "SECURITY"
	ErrTypeHotReload      ErrorType = "HOT_RELOAD"
	ErrTypeConcurrency    ErrorType = "CONCURRENCY"
)

// ErrorCode 错误代码
type ErrorCode int

// String 返回错误代码字符串
func (e ErrorCode) String() string {
	return fmt.Sprintf("%d", int(e))
}

const (
	// 系统错误代码 (1000-1999)
	ErrCodeSystemFailure          ErrorCode = 1001
	ErrCodeSystemError            ErrorCode = 1002
	ErrCodeNetworkFailure         ErrorCode = 1003
	ErrCodeConfigInvalid          ErrorCode = 1004
	ErrCodeInvalidConfig          ErrorCode = 1005
	ErrCodeValidationFailed       ErrorCode = 1006
	ErrCodeInitializationFailed   ErrorCode = 1007
	ErrCodeFileOperationFailed    ErrorCode = 1008
	ErrCodeFileReadFailed         ErrorCode = 1009
	ErrCodeFileWriteFailed        ErrorCode = 1010
	ErrCodeFilePathInvalid        ErrorCode = 1011
	ErrCodeFileNotFound           ErrorCode = 1012
	ErrCodeConfigParseFailed      ErrorCode = 1013
	ErrCodeConfigValidationFailed ErrorCode = 1014
	ErrCodeConfigLoadFailed       ErrorCode = 1015

	// 配置验证具体错误代码 (1016-1099)
	ErrCodeConfigFieldRequired         ErrorCode = 1016
	ErrCodeConfigFieldInvalid          ErrorCode = 1017
	ErrCodeConfigProxyFileEmpty        ErrorCode = 1018
	ErrCodeConfigProxyFileNotFound     ErrorCode = 1019
	ErrCodeConfigPortInvalid           ErrorCode = 1020
	ErrCodeConfigPortReserved          ErrorCode = 1021
	ErrCodeConfigAuthTypeEmpty         ErrorCode = 1022
	ErrCodeConfigAuthIncomplete        ErrorCode = 1023
	ErrCodeConfigPasswordWeak          ErrorCode = 1024
	ErrCodeConfigDNSServerEmpty        ErrorCode = 1025
	ErrCodeConfigDNSServerInvalid      ErrorCode = 1026
	ErrCodeConfigDNSTimeoutInvalid     ErrorCode = 1027
	ErrCodeConfigCacheTypeEmpty        ErrorCode = 1028
	ErrCodeConfigCacheSizeInvalid      ErrorCode = 1029
	ErrCodeConfigCacheTTLInvalid       ErrorCode = 1030
	ErrCodeConfigMonitoringPortInvalid ErrorCode = 1031
	ErrCodeConfigMonitoringPathEmpty   ErrorCode = 1032
	ErrCodeConfigServiceDisabled       ErrorCode = 1033

	// 配置管理错误代码 (1034-1049)
	ErrCodeConfigFileNotFound        ErrorCode = 1034
	ErrCodeConfigParseError          ErrorCode = 1035
	ErrCodeConfigNotLoaded           ErrorCode = 1036
	ErrCodeConfigSerializationFailed ErrorCode = 1037
	ErrCodeConfigVersionNotFound     ErrorCode = 1038
	ErrCodeConfigExportFailed        ErrorCode = 1039
	ErrCodeConfigCallbackFailed      ErrorCode = 1040

	// 高级配置验证错误代码 (1051-1069)
	ErrCodeConfigRetryAttemptsInvalid   ErrorCode = 1051
	ErrCodeConfigRetryMultiplierInvalid ErrorCode = 1052
	ErrCodeConfigHexLengthInvalid       ErrorCode = 1053
	ErrCodeConfigSequenceModulusInvalid ErrorCode = 1054
	ErrCodeConfigValueTypeInvalid       ErrorCode = 1055

	// 监控服务错误代码 (1056-1069)
	ErrCodeMonitoringMemoryHigh    ErrorCode = 1056
	ErrCodeMonitoringGoroutineHigh ErrorCode = 1057
	ErrCodeMonitoringCPUHigh       ErrorCode = 1058
	ErrCodeMonitoringCPUInitFailed ErrorCode = 1059
	ErrCodeMonitoringCPUSampleFailed ErrorCode = 1060

	// 调试服务错误代码 (1070-1079)
	ErrCodeDebugBreakpointEmpty  ErrorCode = 1070
	ErrCodeDebugBreakpointExists ErrorCode = 1071

	// 热重载系统错误代码 (1080-1099)
	ErrCodeConfigNotInitialized      ErrorCode = 1080
	ErrCodeConfigPathNotFound        ErrorCode = 1081
	ErrCodeConfigFieldNotFound       ErrorCode = 1082
	ErrCodeConfigMappingFailed       ErrorCode = 1083
	ErrCodeConfigCompareFailed       ErrorCode = 1084
	ErrCodeConfigApplyFailed         ErrorCode = 1085
	ErrCodeConfigSwitchFailed        ErrorCode = 1086
	ErrCodeConfigItemExpired         ErrorCode = 1087
	ErrCodeConfigReloadFailed        ErrorCode = 1088
	ErrCodeConfigInitializationFailed ErrorCode = 1089
	ErrCodeSystemNotRunning          ErrorCode = 1090
	ErrCodeSystemStartFailed         ErrorCode = 1091
	ErrCodeSystemFeatureDisabled     ErrorCode = 1092
	ErrCodeTimeoutExceeded           ErrorCode = 1093
	ErrCodeConcurrencyLimitExceeded  ErrorCode = 1094
	ErrCodeConcurrencyLockFailed     ErrorCode = 1095

	// 网络错误代码 (1100-1199)
	ErrCodeConnectionFailed    ErrorCode = 1101
	ErrCodeConnectionTimeout   ErrorCode = 1102
	ErrCodeDNSResolutionFailed ErrorCode = 1103
	ErrCodeInvalidURL          ErrorCode = 1104

	// 代理错误代码 (2000-2999)
	ErrCodeNoProxyAvailable         ErrorCode = 2001
	ErrCodeProxyTimeout             ErrorCode = 2002
	ErrCodeProxyAuthFailed          ErrorCode = 2003
	ErrCodeProxyConnFailed          ErrorCode = 2004
	ErrCodeProxyBanned              ErrorCode = 2005
	ErrCodeProxyPoolExhausted       ErrorCode = 2006
	ErrCodeInvalidProxyURL          ErrorCode = 2007
	ErrCodeUnsupportedProtocol      ErrorCode = 2008
	ErrCodeTransportSwitchFailed    ErrorCode = 2009
	ErrCodeProxyProtocolMismatch    ErrorCode = 2010
	ErrCodeProxyProtocolUnsupported ErrorCode = 2011
	ErrCodeProxyUnavailable         ErrorCode = 2012
	ErrCodeInvalidInput             ErrorCode = 2013

	// 代理管理器相关错误代码 (2100-2199)
	ErrCodeProxyManagerNoValidProxies            ErrorCode = 2101
	ErrCodeProxyManagerQualityPoolNotInitialized ErrorCode = 2102
	ErrCodeProxyManagerNoDomainProxies           ErrorCode = 2103
	ErrCodeProxyManagerNoAvailableProxies        ErrorCode = 2104
	ErrCodeProxyManagerAllProxiesBanned          ErrorCode = 2105
	ErrCodeProxyManagerIPAlreadyBlocked          ErrorCode = 2106
	ErrCodeProxyManagerInvalidDuration           ErrorCode = 2107
	ErrCodeInvalidResource                       ErrorCode = 2108
	ErrCodeProxyManagerTrustedIPCannotBan        ErrorCode = 2109
	ErrCodeInvalidScope                          ErrorCode = 2110
	ErrCodeTrustedResource                       ErrorCode = 2111
	ErrCodePermanentlyBlocked                    ErrorCode = 2112
	ErrCodeWatcherCreationFailed                 ErrorCode = 2113
	ErrCodeWatcherAddFailed                      ErrorCode = 2114
	ErrCodeNoSuitableProxy                       ErrorCode = 2115
	ErrCodeProxyManagerLoadFailed                ErrorCode = 2116
	ErrCodeProxyNotFound                         ErrorCode = 2117

	// 触发器错误代码 (3000-3999)
	ErrCodeTriggerNotFound         ErrorCode = 3001
	ErrCodeTriggerInvalidCondition ErrorCode = 3002
	ErrCodeTriggerExecutionFailed  ErrorCode = 3003
	ErrCodeTriggerExecFailed       ErrorCode = 3004
	ErrCodeInvalidTriggerType      ErrorCode = 3005
	ErrCodeTriggerConditionFailed  ErrorCode = 3006
	ErrCodeCreationFailed          ErrorCode = 3007

	// 动作错误代码 (4000-4999)
	ErrCodeActionParamMissing          ErrorCode = 4001
	ErrCodeActionTypeUnknown           ErrorCode = 4002
	ErrCodeActionManagerNotInitialized ErrorCode = 4010
	ErrCodeActionNotFound              ErrorCode = 4003
	ErrCodeActionExecFailed            ErrorCode = 4004
	ErrCodeInvalidActionType           ErrorCode = 4005
	ErrCodeActionTimeout               ErrorCode = 4006
	ErrCodeActionQueueFull             ErrorCode = 4007
	ErrCodeMissingParameter            ErrorCode = 4008
	ErrCodeInvalidParameter            ErrorCode = 4009
	ErrCodeUnsupportedActionType       ErrorCode = 4011
	ErrCodeActionDisabled              ErrorCode = 4012
	ErrCodeParameterValidationFailed   ErrorCode = 4013
	ErrCodeQueueFull                   ErrorCode = 4014
	ErrCodeResourceNotFound            ErrorCode = 4015
	ErrCodeInvalidConfiguration        ErrorCode = 4016
	ErrCodeResourceAlreadyExists       ErrorCode = 4017
	ErrCodeActionExecutionFailed       ErrorCode = 4018

	// 服务状态错误代码 (4020-4039)
	ErrCodeServiceAlreadyRunning ErrorCode = 4020
	ErrCodeServiceNotRunning     ErrorCode = 4021
	ErrCodeServiceStartFailed    ErrorCode = 4022
	ErrCodeServiceStopFailed     ErrorCode = 4023

	// 策略错误代码 (5000-5999)
	ErrCodeStrategyNotFound    ErrorCode = 5001
	ErrCodeStrategyExecFailed  ErrorCode = 5002
	ErrCodeInvalidStrategyType ErrorCode = 5003

	// 负载均衡相关错误代码 (5100-5199)
	ErrCodeLoadBalancerNoTargets        ErrorCode = 5101
	ErrCodeLoadBalancerNoHealthyTargets ErrorCode = 5102
	ErrCodeLoadBalancerConfigInvalid    ErrorCode = 5103

	// DNS错误代码 (6000-6999)
	ErrCodeDNSServerUnavailable   ErrorCode = 6001
	ErrCodeInvalidDNSServer       ErrorCode = 6002
	ErrCodeDNSQueryFailed         ErrorCode = 6003
	ErrCodeDNSConfigInvalid       ErrorCode = 6004
	ErrCodeDNSNoResult            ErrorCode = 6005
	ErrCodeDNSProtocolUnsupported ErrorCode = 6006
	ErrCodeProxyConnectionFailed  ErrorCode = 6007
	ErrCodeProxyTypeUnsupported   ErrorCode = 6008
	ErrCodeProxyShutdownFailed    ErrorCode = 6009

	// 缓存错误代码 (7000-7999)
	ErrCodeCacheOperationFailed ErrorCode = 7001
	ErrCodeCacheKeyNotFound     ErrorCode = 7002
	ErrCodeCacheExpired         ErrorCode = 7003

	// 认证错误代码 (8000-8999)
	ErrCodeAuthenticationFailed ErrorCode = 8001
	ErrCodeAuthorizationFailed  ErrorCode = 8002
	ErrCodeInvalidCredentials   ErrorCode = 8003
	ErrCodeTokenExpired         ErrorCode = 8004

	// 安全错误代码 (8500-8599)
	ErrCodeSecurityViolation    ErrorCode = 8501
	ErrCodePathTraversalAttack  ErrorCode = 8502
	ErrCodeInvalidFileAccess    ErrorCode = 8503
	ErrCodePermissionDenied     ErrorCode = 8504

	// HTTP 相关错误代码 (9000-9099)
	ErrCodeHTTPRequestFailed       ErrorCode = 9001
	ErrCodeHTTPResponseInvalid     ErrorCode = 9002
	ErrCodeHTTPStatusError         ErrorCode = 9003
	ErrCodeHTTPHeaderInvalid       ErrorCode = 9004
	ErrCodeHTTPBodyInvalid         ErrorCode = 9005
	ErrCodeHTTPRequestCreateFailed ErrorCode = 9006
	ErrCodeHTTPRequestInvalid      ErrorCode = 9007

	// AWS 相关错误代码 (9100-9199)
	ErrCodeAWSCredentialsFailed    ErrorCode = 9101
	ErrCodeAWSRegionInvalid        ErrorCode = 9102
	ErrCodeAWSConfigLoadFailed     ErrorCode = 9103
	ErrCodeAWSEndpointInvalid      ErrorCode = 9104
	ErrCodeAWSAPICallFailed        ErrorCode = 9105
	ErrCodeAWSConfigInvalid        ErrorCode = 9106
	ErrCodeAWSEndpointNotAvailable ErrorCode = 9107

	// 热重载相关错误代码 (9200-9299)
	ErrCodeHotReloadTimeout        ErrorCode = 9201
	ErrCodeHotReloadStateSaveFailed ErrorCode = 9202
	ErrCodeHotReloadStateRestoreFailed ErrorCode = 9203
	ErrCodeHotReloadChecksumMismatch ErrorCode = 9204
	ErrCodeHotReloadVersionNotFound ErrorCode = 9205
	ErrCodeHotReloadCompressionFailed ErrorCode = 9206
	ErrCodeHotReloadDecompressionFailed ErrorCode = 9207
	ErrCodeTriggerTypeUnknown      ErrorCode = 9208
	ErrCodeHotReloadInProgress     ErrorCode = 9209
	ErrCodeHotReloadStateMergeFailed ErrorCode = 9210
	ErrCodeSerializationFailed     ErrorCode = 9211
	ErrCodeDeserializationFailed   ErrorCode = 9212
	ErrCodeChecksumCalculationFailed ErrorCode = 9213
	ErrCodeStateDataCorrupted      ErrorCode = 9214
	ErrCodeCircularReferenceDetected ErrorCode = 9215
	ErrCodeSerializationDepthExceeded ErrorCode = 9216
	ErrCodeSnapshotSizeExceeded    ErrorCode = 9217
	ErrCodeUnsupportedDataType     ErrorCode = 9218

	// 状态管理相关错误代码 (9219-9239)
	ErrCodeVersionNotFound         ErrorCode = 9219
	ErrCodeSnapshotCreationFailed  ErrorCode = 9220
	ErrCodeSnapshotCorrupted       ErrorCode = 9221
	ErrCodeNoRollbackPoint         ErrorCode = 9222
	ErrCodeRollbackFailed          ErrorCode = 9223

	// 依赖管理相关错误代码 (9300-9399)
	ErrCodeCircularDependency        ErrorCode = 9301
	ErrCodeInvalidServiceName        ErrorCode = 9302
	ErrCodeDependencyGraphInconsistent ErrorCode = 9303
	ErrCodeDependencyAnalysisTimeout   ErrorCode = 9304
	ErrCodeMaxDependencyLevelsExceeded ErrorCode = 9305
	ErrCodeDependencyLevelCacheExpired ErrorCode = 9306
)

// FlexProxyError 统一错误类型
type FlexProxyError struct {
	Type      ErrorType `json:"type"`
	Code      ErrorCode `json:"code"`
	Message   string    `json:"message"`
	Details   string    `json:"details,omitempty"`
	Cause     error     `json:"-"` // 原始错误，不序列化
	Timestamp time.Time `json:"timestamp"`
	File      string    `json:"file,omitempty"`
	Line      int       `json:"line,omitempty"`
	Function  string    `json:"function,omitempty"`
	TraceID   string    `json:"trace_id,omitempty"`
}

// Error 实现error接口
func (e *FlexProxyError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%s:%d] %s: %s", e.Type, e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("[%s:%d] %s", e.Type, e.Code, e.Message)
}

// Unwrap 支持错误链
func (e *FlexProxyError) Unwrap() error {
	return e.Cause
}

// Is 支持errors.Is
func (e *FlexProxyError) Is(target error) bool {
	if t, ok := target.(*FlexProxyError); ok {
		return e.Type == t.Type && e.Code == t.Code
	}
	return false
}

// NewError 创建新错误
func NewError(errorType ErrorType, code ErrorCode, message string) *FlexProxyError {
	pc, file, line, _ := runtime.Caller(1)
	func_name := runtime.FuncForPC(pc).Name()

	return &FlexProxyError{
		Type:      errorType,
		Code:      code,
		Message:   message,
		Timestamp: time.Now(),
		File:      file,
		Line:      line,
		Function:  func_name,
	}
}

// NewErrorWithDetails 创建带详细信息的错误
func NewErrorWithDetails(errorType ErrorType, code ErrorCode, message, details string) *FlexProxyError {
	err := NewError(errorType, code, message)
	err.Details = details
	return err
}

// WrapError 包装错误
func WrapError(cause error, errorType ErrorType, code ErrorCode, message string) *FlexProxyError {
	err := NewError(errorType, code, message)
	err.Cause = cause
	return err
}

// WrapErrorWithDetails 包装现有错误并添加详细信息
func WrapErrorWithDetails(cause error, errorType ErrorType, code ErrorCode, message, details string) *FlexProxyError {
	err := NewErrorWithDetails(errorType, code, message, details)
	err.Cause = cause
	return err
}

// WithTraceID 添加追踪ID
func (e *FlexProxyError) WithTraceID(traceID string) *FlexProxyError {
	e.TraceID = traceID
	return e
}

// NewFlexProxyError 创建FlexProxyError的便捷函数（向后兼容）
func NewFlexProxyError(errorType ErrorType, code ErrorCode, message string) *FlexProxyError {
	return NewError(errorType, code, message)
}

// NewHotReloadError 创建热重载相关错误
func NewHotReloadError(code ErrorCode, message string, cause error) *FlexProxyError {
	err := NewError(ErrTypeHotReload, code, message)
	err.Cause = cause
	return err
}

// 预定义的常用错误
var (
	// 代理相关错误
	ErrNoProxyLeft     = NewError(ErrTypeProxy, ErrCodeNoProxyAvailable, "代理池中没有可用代理")
	ErrProxyTimeout    = NewError(ErrTypeProxy, ErrCodeProxyTimeout, "代理请求超时")
	ErrProxyAuthFailed = NewError(ErrTypeProxy, ErrCodeProxyAuthFailed, "代理认证失败")
	ErrProxyConnFailed = NewError(ErrTypeProxy, ErrCodeProxyConnFailed, "代理连接失败")

	// 执行器相关错误
	ErrRetryCountInvalid       = NewError(ErrTypeAction, ErrCodeInvalidParameter, "重试次数必须大于0")
	ErrRetryCountFormatInvalid = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "重试次数格式无效")
	ErrDelayFormatInvalid      = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "延迟参数格式无效")
	ErrRotationModeInvalid     = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "IP轮换模式无效")
	ErrTimeoutFormatInvalid    = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "超时参数格式无效")
	ErrDNSModeInvalid          = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "DNS模式无效")
	ErrProxyPoolEmpty          = NewError(ErrTypeProxy, ErrCodeNoProxyAvailable, "代理池为空")
	ErrGetProxyFailed          = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "获取代理失败")
	ErrSetContextFailed        = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "设置上下文失败")

	// 缓存响应执行器错误
	ErrCacheServiceNotInitialized = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "CacheService未初始化")
	ErrCacheDurationInvalid       = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "缓存时间格式无效")
	ErrCacheKeyInvalid            = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "缓存键格式无效")
	ErrCacheOperationFailed       = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "缓存操作失败")

	// 保存到代理池执行器错误
	ErrQualityTierInvalid = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "质量等级格式无效")
	ErrMinScoreInvalid    = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "最小分数格式无效")
	ErrPoolNameInvalid    = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "代理池名称格式无效")
	ErrSaveToPoolFailed   = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "保存到代理池失败")

	// 空响应执行器错误
	ErrStatusCodeInvalid   = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "状态码格式无效")
	ErrContentTypeInvalid  = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "内容类型格式无效")
	ErrResponseBodyInvalid = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "响应体格式无效")
	ErrNullResponseFailed  = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "生成空响应失败")

	// 脚本执行器错误
	ErrScriptEngineInvalid     = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "脚本引擎类型无效")
	ErrScriptContentInvalid    = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "脚本内容格式无效")
	ErrScriptTimeoutInvalid    = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "脚本超时时间格式无效")
	ErrScriptExecutionFailed   = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "脚本执行失败")
	ErrScriptFileMissing       = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "脚本文件不存在")
	ErrScriptSizeExceeded      = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "脚本大小超过限制")
	ErrScriptFileReadFailed    = NewError(ErrTypeFile, ErrCodeFileReadFailed, "脚本文件读取失败")
	ErrScriptFilePathInvalid   = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "脚本文件路径无效")
	ErrScriptFilePathTooLong   = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "脚本文件路径过长")
	ErrScriptFileSizeExceeded  = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "脚本文件大小超过限制")
	ErrScriptFileExtInvalid    = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "脚本文件扩展名无效")
	ErrScriptPathTraversal     = NewError(ErrTypeSecurity, ErrCodeSecurityViolation, "检测到路径遍历攻击")
	ErrScriptContentEmpty      = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "脚本内容不能为空")
	ErrScriptParameterMissing  = NewError(ErrTypeValidation, ErrCodeMissingParameter, "脚本参数缺失")

	// 请求URL执行器错误
	ErrURLInvalid             = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "URL格式无效")
	ErrHTTPMethodInvalid      = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "HTTP方法格式无效")
	ErrRequestTimeoutInvalid  = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "请求超时时间格式无效")
	ErrRequestRetriesInvalid  = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "重试次数格式无效")
	ErrRequestExecutionFailed = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "HTTP请求执行失败")
	ErrRequestHeadersInvalid  = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "请求头格式无效")

	// 缓存执行器错误
	ErrCacheDurationInvalidOld = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "缓存时间格式无效")
	ErrCacheMaxUseInvalid      = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "最大使用次数格式无效")
	ErrCacheScopeInvalid       = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "缓存作用域格式无效")
	ErrCacheExecutionFailed    = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "缓存操作执行失败")

	// IP域名封禁执行器错误
	ErrBanTargetInvalid    = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "封禁目标格式无效")
	ErrBanDurationInvalid  = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "封禁时间格式无效")
	ErrBanReasonInvalid    = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "封禁原因格式无效")
	ErrBanListSizeExceeded = NewError(ErrTypeValidation, ErrCodeInvalidParameter, "封禁列表大小超过限制")
	ErrBanExecutionFailed  = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "封禁操作执行失败")
	ErrProxyBanned         = NewError(ErrTypeProxy, ErrCodeProxyBanned, "代理已被封禁")
	ErrProxyPoolExhausted  = NewError(ErrTypeProxy, ErrCodeProxyPoolExhausted, "代理池已耗尽")
	ErrInvalidProxyURL     = NewError(ErrTypeProxy, ErrCodeInvalidProxyURL, "无效的代理URL")

	// 安全执行器相关错误
	ErrIPAddressNotFound          = NewError(ErrTypeAction, ErrCodeMissingParameter, "无法获取要封禁的IP地址")
	ErrDomainNotFound             = NewError(ErrTypeAction, ErrCodeMissingParameter, "无法获取要封禁的域名")
	ErrHTTPRequestEmpty           = NewError(ErrTypeAction, ErrCodeMissingParameter, "HTTP请求对象为空")
	ErrHTTPResponseEmpty          = NewError(ErrTypeAction, ErrCodeMissingParameter, "HTTP响应对象为空")
	ErrProxyServiceNotInitialized = NewError(ErrTypeAction, ErrCodeInvalidConfiguration, "ProxyService未初始化")
	ErrIPBanFailed                = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "IP封禁执行失败")
	ErrDomainBanFailed            = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "域名封禁执行失败")

	// 动作执行器通用错误
	ErrActionNotFound                  = NewError(ErrTypeAction, ErrCodeActionNotFound, "未找到动作执行器")
	ErrActionDisabled                  = NewError(ErrTypeAction, ErrCodeActionDisabled, "动作已禁用")
	ErrUnsupportedMatchType            = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "不支持的匹配类型")
	ErrUnsupportedHeaderOperation      = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "不支持的Header操作类型")
	ErrUnsupportedBodyOperation        = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "不支持的Body操作类型")
	ErrUnsupportedEncoding             = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "不支持的编码格式")
	ErrInvalidJSONFormat               = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "无效的JSON格式")
	ErrInvalidXMLFormat                = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "无效的XML格式")
	ErrHeaderNameEmpty                 = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "请求头名称不能为空")
	ErrResponseHeaderNameEmpty         = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "响应头名称不能为空")
	ErrHeaderValueTooLong              = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "请求头值过长")
	ErrResponseHeaderValueTooLong      = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "响应头值过长")
	ErrRemoveHeaderNameEmpty           = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "要删除的请求头名称不能为空")
	ErrRemoveResponseHeaderNameEmpty   = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "要删除的响应头名称不能为空")
	ErrInvalidStatusCodeRange          = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "无效的状态码，状态码必须在100-599范围内")
	ErrMissingMessageParameter         = NewError(ErrTypeAction, ErrCodeMissingParameter, "缺少message参数")
	ErrMissingRequiredMessageParameter = NewError(ErrTypeAction, ErrCodeMissingParameter, "缺少必需的message参数")
	ErrMissingActionType               = NewError(ErrTypeAction, ErrCodeMissingParameter, "动作配置缺少type字段")

	// 配置和验证错误
	ErrConfigInvalid        = NewError(ErrTypeConfig, ErrCodeConfigInvalid, "配置无效")
	ErrValidationFailed     = NewError(ErrTypeValidation, ErrCodeValidationFailed, "验证失败")
	ErrInitializationFailed = NewError(ErrTypeInitialization, ErrCodeInitializationFailed, "初始化失败")

	// 配置验证具体错误
	ErrConfigFieldRequired         = NewError(ErrTypeValidation, ErrCodeConfigFieldRequired, "配置字段是必需的")
	ErrConfigFieldInvalid          = NewError(ErrTypeValidation, ErrCodeConfigFieldInvalid, "配置字段值无效")
	ErrConfigProxyFileEmpty        = NewError(ErrTypeValidation, ErrCodeConfigProxyFileEmpty, "代理文件路径不能为空")
	ErrConfigProxyFileNotFound     = NewError(ErrTypeFile, ErrCodeConfigProxyFileNotFound, "代理文件不存在")
	ErrConfigPortInvalid           = NewError(ErrTypeValidation, ErrCodeConfigPortInvalid, "端口配置无效")
	ErrConfigPortReserved          = NewError(ErrTypeValidation, ErrCodeConfigPortReserved, "使用系统保留端口")
	ErrConfigAuthTypeEmpty         = NewError(ErrTypeValidation, ErrCodeConfigAuthTypeEmpty, "认证类型不能为空")
	ErrConfigAuthIncomplete        = NewError(ErrTypeValidation, ErrCodeConfigAuthIncomplete, "认证配置不完整")
	ErrConfigPasswordWeak          = NewError(ErrTypeValidation, ErrCodeConfigPasswordWeak, "密码强度不足")
	ErrConfigDNSServerEmpty        = NewError(ErrTypeValidation, ErrCodeConfigDNSServerEmpty, "DNS服务器地址不能为空")
	ErrConfigDNSServerInvalid      = NewError(ErrTypeValidation, ErrCodeConfigDNSServerInvalid, "DNS服务器地址格式无效")
	ErrConfigDNSTimeoutInvalid     = NewError(ErrTypeValidation, ErrCodeConfigDNSTimeoutInvalid, "DNS超时配置无效")
	ErrConfigCacheTypeEmpty        = NewError(ErrTypeValidation, ErrCodeConfigCacheTypeEmpty, "缓存类型不能为空")
	ErrConfigCacheSizeInvalid      = NewError(ErrTypeValidation, ErrCodeConfigCacheSizeInvalid, "缓存大小配置无效")
	ErrConfigCacheTTLInvalid       = NewError(ErrTypeValidation, ErrCodeConfigCacheTTLInvalid, "缓存TTL配置无效")
	ErrConfigMonitoringPortInvalid = NewError(ErrTypeValidation, ErrCodeConfigMonitoringPortInvalid, "监控端口配置无效")
	ErrConfigMonitoringPathEmpty   = NewError(ErrTypeValidation, ErrCodeConfigMonitoringPathEmpty, "监控路径不能为空")
	ErrConfigServiceDisabled       = NewError(ErrTypeValidation, ErrCodeConfigServiceDisabled, "代理服务未启用")

	// 高级配置验证错误
	ErrConfigRetryAttemptsInvalid   = NewError(ErrTypeValidation, ErrCodeConfigRetryAttemptsInvalid, "最大重试次数配置无效")
	ErrConfigRetryMultiplierInvalid = NewError(ErrTypeValidation, ErrCodeConfigRetryMultiplierInvalid, "退避因子配置无效")
	ErrConfigHexLengthInvalid       = NewError(ErrTypeValidation, ErrCodeConfigHexLengthInvalid, "十六进制生成器长度配置无效")
	ErrConfigSequenceModulusInvalid = NewError(ErrTypeValidation, ErrCodeConfigSequenceModulusInvalid, "序列模数配置无效")
	ErrConfigValueTypeInvalid       = NewError(ErrTypeValidation, ErrCodeConfigValueTypeInvalid, "配置值类型无效")

	// 监控服务错误
	ErrMonitoringMemoryHigh      = NewError(ErrTypeMonitoring, ErrCodeMonitoringMemoryHigh, "内存使用过高")
	ErrMonitoringGoroutineHigh   = NewError(ErrTypeMonitoring, ErrCodeMonitoringGoroutineHigh, "Goroutine数量过多")
	ErrMonitoringCPUHigh         = NewError(ErrTypeMonitoring, ErrCodeMonitoringCPUHigh, "CPU使用率过高")
	ErrMonitoringCPUInitFailed   = NewError(ErrTypeMonitoring, ErrCodeMonitoringCPUInitFailed, "CPU监控初始化失败")
	ErrMonitoringCPUSampleFailed = NewError(ErrTypeMonitoring, ErrCodeMonitoringCPUSampleFailed, "CPU采样失败")

	// 调试服务错误
	ErrDebugBreakpointEmpty  = NewError(ErrTypeValidation, ErrCodeDebugBreakpointEmpty, "断点ID和位置不能为空")
	ErrDebugBreakpointExists = NewError(ErrTypeValidation, ErrCodeDebugBreakpointExists, "断点已存在")

	// 网络相关错误
	ErrConnectionFailed    = NewError(ErrTypeNetwork, ErrCodeConnectionFailed, "连接失败")
	ErrConnectionTimeout   = NewError(ErrTypeNetwork, ErrCodeConnectionTimeout, "连接超时")
	ErrDNSResolutionFailed = NewError(ErrTypeNetwork, ErrCodeDNSResolutionFailed, "DNS解析失败")
	ErrInvalidURL          = NewError(ErrTypeNetwork, ErrCodeInvalidURL, "无效的URL")

	// 触发器相关错误
	ErrTriggerNotFound    = NewError(ErrTypeTrigger, ErrCodeTriggerNotFound, "触发器未找到")
	ErrTriggerExecFailed  = NewError(ErrTypeTrigger, ErrCodeTriggerExecFailed, "触发器执行失败")
	ErrInvalidTriggerType = NewError(ErrTypeTrigger, ErrCodeInvalidTriggerType, "无效的触发器类型")

	// 动作相关错误
	ErrActionExecFailed  = NewError(ErrTypeAction, ErrCodeActionExecFailed, "动作执行失败")
	ErrInvalidActionType = NewError(ErrTypeAction, ErrCodeInvalidActionType, "无效的动作类型")
	ErrActionTimeout     = NewError(ErrTypeAction, ErrCodeActionTimeout, "动作执行超时")
	ErrActionQueueFull   = NewError(ErrTypeAction, ErrCodeActionQueueFull, "动作队列已满")

	// 依赖管理相关错误
	ErrCircularDependency        = NewError(ErrTypeHotReload, ErrCodeCircularDependency, "检测到循环依赖")
	ErrInvalidServiceName        = NewError(ErrTypeValidation, ErrCodeInvalidServiceName, "服务名称无效")
	ErrDependencyGraphInconsistent = NewError(ErrTypeHotReload, ErrCodeDependencyGraphInconsistent, "依赖关系图数据不一致")
	ErrDependencyAnalysisTimeout   = NewError(ErrTypeHotReload, ErrCodeDependencyAnalysisTimeout, "依赖分析超时")
	ErrMaxDependencyLevelsExceeded = NewError(ErrTypeHotReload, ErrCodeMaxDependencyLevelsExceeded, "依赖层级数超过最大限制")
	ErrDependencyLevelCacheExpired = NewError(ErrTypeHotReload, ErrCodeDependencyLevelCacheExpired, "依赖层级缓存已过期")
	ErrMissingParameter  = NewError(ErrTypeAction, ErrCodeMissingParameter, "缺少必需参数")
	ErrInvalidParameter  = NewError(ErrTypeAction, ErrCodeInvalidParameter, "无效参数")

	// 策略相关错误
	ErrStrategyNotFound    = NewError(ErrTypeStrategy, ErrCodeStrategyNotFound, "策略未找到")
	ErrStrategyExecFailed  = NewError(ErrTypeStrategy, ErrCodeStrategyExecFailed, "策略执行失败")
	ErrInvalidStrategyType = NewError(ErrTypeStrategy, ErrCodeInvalidStrategyType, "无效的策略类型")

	// DNS相关错误
	ErrDNSServerUnavailable = NewError(ErrTypeDNS, ErrCodeDNSServerUnavailable, "DNS服务器不可用")
	ErrInvalidDNSServer     = NewError(ErrTypeDNS, ErrCodeInvalidDNSServer, "无效的DNS服务器")
	ErrDNSQueryFailed       = NewError(ErrTypeDNS, ErrCodeDNSQueryFailed, "DNS查询失败")

	// 缓存相关错误
	ErrCacheKeyNotFound = NewError(ErrTypeCache, ErrCodeCacheKeyNotFound, "缓存键未找到")
	ErrCacheExpired     = NewError(ErrTypeCache, ErrCodeCacheExpired, "缓存条目已过期")

	// 认证相关错误
	ErrAuthenticationFailed = NewError(ErrTypeAuth, ErrCodeAuthenticationFailed, "认证失败")
	ErrAuthorizationFailed  = NewError(ErrTypeAuth, ErrCodeAuthorizationFailed, "授权失败")
	ErrInvalidCredentials   = NewError(ErrTypeAuth, ErrCodeInvalidCredentials, "无效的凭据")
	ErrTokenExpired         = NewError(ErrTypeAuth, ErrCodeTokenExpired, "令牌已过期")

	// HTTP相关错误
	ErrHTTPRequestFailed       = NewError(ErrTypeHTTP, ErrCodeHTTPRequestFailed, "HTTP请求失败")
	ErrHTTPResponseInvalid     = NewError(ErrTypeHTTP, ErrCodeHTTPResponseInvalid, "HTTP响应无效")
	ErrHTTPStatusError         = NewError(ErrTypeHTTP, ErrCodeHTTPStatusError, "HTTP状态错误")
	ErrHTTPHeaderInvalid       = NewError(ErrTypeHTTP, ErrCodeHTTPHeaderInvalid, "HTTP头部无效")
	ErrHTTPBodyInvalid         = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "HTTP主体无效")
	ErrHTTPRequestCreateFailed = NewError(ErrTypeHTTP, ErrCodeHTTPRequestCreateFailed, "HTTP请求创建失败")

	// 响应修改相关错误
	ErrResponseIntegrityCheckFailed     = NewError(ErrTypeHTTP, ErrCodeHTTPResponseInvalid, "响应完整性检查失败")
	ErrContentEncodingProcessFailed     = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "内容编码处理失败")
	ErrTransferEncodingProcessFailed    = NewError(ErrTypeHTTP, ErrCodeHTTPHeaderInvalid, "传输编码处理失败")
	ErrCharsetConversionFailed          = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "字符编码转换失败")
	ErrCompressionFailed                = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "内容压缩失败")
	ErrDecompressionFailed              = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "内容解压失败")
	ErrChunkedEncodingFailed            = NewError(ErrTypeHTTP, ErrCodeHTTPHeaderInvalid, "分块编码处理失败")
	ErrProtocolValidationFailed         = NewError(ErrTypeHTTP, ErrCodeHTTPResponseInvalid, "HTTP协议验证失败")
	ErrResponseSizeTooLarge             = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "响应体大小超过限制")
	ErrUnsupportedContentEncoding       = NewError(ErrTypeHTTP, ErrCodeHTTPHeaderInvalid, "不支持的内容编码")
	ErrUnsupportedTransferEncoding      = NewError(ErrTypeHTTP, ErrCodeHTTPHeaderInvalid, "不支持的传输编码")
	ErrUnsupportedCharset               = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "不支持的字符编码")
	ErrContentLengthMismatch            = NewError(ErrTypeHTTP, ErrCodeHTTPHeaderInvalid, "Content-Length与实际内容长度不匹配")

	// 请求修改相关错误
	ErrRequestIntegrityCheckFailed      = NewError(ErrTypeHTTP, ErrCodeHTTPRequestInvalid, "请求完整性检查失败")
	ErrRequestContentEncodingFailed     = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "请求内容编码处理失败")
	ErrRequestTransferEncodingFailed    = NewError(ErrTypeHTTP, ErrCodeHTTPHeaderInvalid, "请求传输编码处理失败")
	ErrRequestCharsetConversionFailed   = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "请求字符编码转换失败")
	ErrRequestCompressionFailed         = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "请求内容压缩失败")
	ErrRequestDecompressionFailed       = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "请求内容解压失败")
	ErrRequestChunkedEncodingFailed     = NewError(ErrTypeHTTP, ErrCodeHTTPHeaderInvalid, "请求分块编码处理失败")
	ErrRequestProtocolValidationFailed  = NewError(ErrTypeHTTP, ErrCodeHTTPRequestInvalid, "请求HTTP协议验证失败")
	ErrRequestSizeTooLarge              = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "请求体大小超过限制")
	ErrRequestHeaderCountExceeded       = NewError(ErrTypeHTTP, ErrCodeHTTPHeaderInvalid, "请求头数量超过限制")
	ErrRequestURLTooLong                = NewError(ErrTypeHTTP, ErrCodeHTTPRequestInvalid, "请求URL长度超过限制")
	ErrFormDataProcessFailed            = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "表单数据处理失败")
	ErrMultipartFormParseFailed         = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "多部分表单解析失败")
	ErrFormFieldCountExceeded           = NewError(ErrTypeHTTP, ErrCodeHTTPBodyInvalid, "表单字段数量超过限制")
	ErrInvalidRequestMethod             = NewError(ErrTypeHTTP, ErrCodeHTTPRequestInvalid, "无效的请求方法")
	ErrRequestHeaderNameEmpty           = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "请求头名称不能为空")
	ErrRemoveRequestHeaderNameEmpty     = NewError(ErrTypeAction, ErrCodeActionExecutionFailed, "要删除的请求头名称不能为空")

	// AWS相关错误
	ErrAWSCredentialsFailed    = NewError(ErrTypeAWS, ErrCodeAWSCredentialsFailed, "AWS凭据失败")
	ErrAWSRegionInvalid        = NewError(ErrTypeAWS, ErrCodeAWSRegionInvalid, "AWS区域无效")
	ErrAWSConfigLoadFailed     = NewError(ErrTypeAWS, ErrCodeAWSConfigLoadFailed, "AWS配置加载失败")
	ErrAWSEndpointInvalid      = NewError(ErrTypeAWS, ErrCodeAWSEndpointInvalid, "AWS端点无效")
	ErrAWSAPICallFailed        = NewError(ErrTypeAWS, ErrCodeAWSAPICallFailed, "AWS API调用失败")
	ErrAWSConfigInvalid        = NewError(ErrTypeAWS, ErrCodeAWSConfigInvalid, "AWS配置无效")
	ErrAWSEndpointNotAvailable = NewError(ErrTypeAWS, ErrCodeAWSEndpointNotAvailable, "AWS端点不可用")
)
