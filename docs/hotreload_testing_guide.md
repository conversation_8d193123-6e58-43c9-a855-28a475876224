# FlexProxy 热重载系统测试指南

## 📋 概述

本指南详细介绍如何测试FlexProxy的热重载系统，包括功能测试、性能测试和集成测试。

## 🚀 快速开始

### 运行所有测试

```bash
# 运行完整的热重载测试套件
./scripts/test_hotreload.sh

# 或者使用Go命令直接运行
go test ./tests/integration -v -run TestSimplified -timeout 120s
```

### 运行特定测试

```bash
# 仅运行集成测试
./scripts/test_hotreload.sh -i

# 仅运行性能测试
./scripts/test_hotreload.sh -p

# 仅运行核心功能测试
./scripts/test_hotreload.sh -c

# 生成详细测试报告
./scripts/test_hotreload.sh -r
```

## 🧪 测试结构

### 测试文件组织

```
tests/
├── integration/
│   ├── simplified_hotreload_test.go    # 主要热重载测试
│   ├── hotreload_test_report.md        # 测试报告模板
│   └── hotreload_integration_test.go   # 其他集成测试
├── unit/
│   └── hotreload/                      # 单元测试
└── benchmarks/
    └── hotreload_bench_test.go         # 性能基准测试
```

### 脚本工具

```
scripts/
├── test_hotreload.sh                   # 主要测试脚本
├── benchmark_hotreload.sh              # 性能基准测试脚本
└── generate_test_report.sh             # 测试报告生成脚本
```

## 📊 测试覆盖范围

### 配置结构测试

| 配置结构 | 测试状态 | 覆盖率 | 说明 |
|---------|---------|--------|------|
| GlobalConfig | ✅ 完整 | 100% | 全局配置热重载 |
| ServerConfig | ✅ 完整 | 100% | 服务器配置热重载 |
| PortsConfig | ✅ 完整 | 100% | 端口配置热重载 |
| LoggingConfig | ✅ 完整 | 100% | 日志配置热重载 |
| MonitoringConfig | ✅ 完整 | 100% | 监控配置热重载 |
| SecurityConfig | ✅ 完整 | 100% | 安全配置热重载 |
| CacheConfig | ⚪ 部分 | 60% | 缓存配置热重载 |
| DNSServiceConfig | ⚪ 部分 | 60% | DNS服务配置热重载 |
| TimeoutsConfig | ⚪ 部分 | 60% | 超时配置热重载 |

### 功能特性测试

- ✅ **基础热重载**: 单个配置项变更
- ✅ **批量热重载**: 多个配置项同时变更
- ✅ **性能测试**: 热重载响应时间测试
- ✅ **并发安全**: 多线程配置变更测试
- ✅ **数据一致性**: 配置变更后数据验证
- ⚪ **错误恢复**: 异常情况处理测试
- ⚪ **回滚机制**: 配置回滚功能测试

## 🔧 测试配置

### 环境要求

- **Go版本**: 1.21+
- **内存**: 最少512MB
- **磁盘空间**: 100MB
- **网络**: 无特殊要求

### 测试参数

```go
// 测试超时设置
const (
    DefaultTestTimeout = 120 * time.Second
    PerformanceTestTimeout = 300 * time.Second
    BenchmarkTestTimeout = 600 * time.Second
)

// 性能基准
const (
    MaxHotReloadTime = 100 * time.Millisecond  // 最大热重载时间
    TargetHotReloadTime = 10 * time.Millisecond // 目标热重载时间
)
```

## 📈 性能基准

### 当前性能指标

- **平均热重载时间**: 2.8毫秒
- **最快热重载时间**: <1毫秒
- **内存使用**: 稳定，无泄漏
- **CPU使用**: 低，瞬时峰值

### 性能目标

- **响应时间**: <100毫秒 (目标: <10毫秒)
- **内存使用**: <50MB增量
- **CPU使用**: <10%峰值
- **并发处理**: 支持100+并发配置变更

## 🐛 故障排除

### 常见问题

#### 1. 测试编译失败

```bash
# 检查Go环境
go version

# 检查依赖
go mod tidy
go mod verify

# 重新编译
go build ./...
```

#### 2. 测试超时

```bash
# 增加超时时间
go test ./tests/integration -timeout 300s

# 或使用脚本的详细模式
./scripts/test_hotreload.sh -v
```

#### 3. 性能测试失败

```bash
# 检查系统资源
top
free -h

# 运行单独的性能测试
go test ./tests/integration -run TestHotReloadPerformance -v
```

#### 4. 配置变更未检测

检查配置是否真的发生了变更：

```go
// 在测试中添加调试信息
t.Logf("原始配置: %+v", originalConfig)
t.Logf("新配置: %+v", newConfig)
t.Logf("变更路径: %v", result.ChangedPaths)
```

### 调试技巧

#### 1. 启用详细日志

```bash
# 设置日志级别
export LOG_LEVEL=debug

# 运行测试
go test ./tests/integration -v
```

#### 2. 使用测试标志

```bash
# 运行特定测试
go test -run TestGlobalConfig

# 显示详细输出
go test -v

# 并行运行
go test -parallel 4
```

#### 3. 性能分析

```bash
# CPU性能分析
go test -cpuprofile=cpu.prof

# 内存性能分析
go test -memprofile=mem.prof

# 查看性能报告
go tool pprof cpu.prof
```

## 📝 编写新测试

### 测试模板

```go
func TestNewConfigHotReload(t *testing.T) {
    // 1. 创建初始配置
    initialCfg := &config.Config{
        // ... 初始配置
    }
    
    // 2. 创建热重载引擎
    engine, err := hotreload.NewHotReloadEngine(initialCfg, nil, nil)
    require.NoError(t, err)
    
    // 3. 启动引擎
    err = engine.Start()
    require.NoError(t, err)
    defer engine.Stop()
    
    // 4. 创建新配置
    newCfg := &config.Config{
        // ... 修改后的配置
    }
    
    // 5. 执行热重载
    result, err := engine.ProcessConfigChange(newCfg)
    require.NoError(t, err)
    assert.True(t, result.Success)
    
    // 6. 验证结果
    currentConfig := engine.GetCurrentConfig()
    assert.Equal(t, expectedValue, currentConfig.SomeField)
    
    // 7. 记录测试结果
    t.Logf("测试通过，变更路径数量: %d", len(result.ChangedPaths))
}
```

### 最佳实践

1. **测试隔离**: 每个测试使用独立的配置和引擎实例
2. **资源清理**: 确保测试结束后正确清理资源
3. **错误处理**: 使用require检查关键错误，assert检查预期结果
4. **性能考虑**: 避免在测试中使用过长的等待时间
5. **日志记录**: 记录关键的测试信息和性能指标

## 🎯 测试策略

### 回归测试

```bash
# 每次代码变更后运行
./scripts/test_hotreload.sh -a

# CI/CD流水线中运行
go test ./... -race -timeout 300s
```

### 性能回归测试

```bash
# 定期运行性能基准测试
./scripts/test_hotreload.sh -b

# 比较性能结果
go test -bench=. -benchmem -count=5
```

### 压力测试

```bash
# 高并发测试
go test -parallel 10 -count 100

# 长时间运行测试
go test -timeout 30m -count 1000
```

## 📚 参考资料

- [Go测试文档](https://golang.org/pkg/testing/)
- [testify框架](https://github.com/stretchr/testify)
- [FlexProxy配置文档](./configuration.md)
- [热重载架构文档](./hotreload_architecture.md)

---

*文档版本: v1.0*  
*最后更新: 2025-01-21*
