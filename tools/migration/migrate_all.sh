#!/bin/bash

# ================================
# FlexProxy 配置文件一键迁移脚本
# ================================
# 功能：将旧配置文件迁移为经过五阶段优化的新配置文件
# 技术：使用FlexProxy Tool统一工具执行迁移
# 作者：FlexProxy Team
# 版本：v3.0 (统一工具版)
# 更新时间：2025-01-14

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    echo
    print_message $CYAN "================================"
    print_message $CYAN "$1"
    print_message $CYAN "================================"
}

# 打印成功消息
print_success() {
    print_message $GREEN "✅ $1"
}

# 打印警告消息
print_warning() {
    print_message $YELLOW "⚠️  $1"
}

# 打印错误消息
print_error() {
    print_message $RED "❌ $1"
}

# 打印信息消息
print_info() {
    print_message $BLUE "ℹ️  $1"
}

# 检查参数
check_arguments() {
    if [ $# -lt 1 ]; then
        print_error "使用方法: $0 <input_config> [output_config]"
        echo
        echo "示例:"
        echo "  $0 config.yaml                    # 输出到 config_optimized.yaml"
        echo "  $0 old_config.yaml new_config.yaml # 输出到指定文件"
        echo
        exit 1
    fi
}

# 检查文件是否存在
check_file_exists() {
    local file=$1
    if [ ! -f "$file" ]; then
        print_error "配置文件不存在: $file"
        exit 1
    fi
}

# 检查Go环境
check_go_environment() {
    if ! command -v go &> /dev/null; then
        print_error "Go环境未安装，请先安装Go"
        exit 1
    fi
}

# 检查FlexProxy Tool
check_flexproxy_tool() {
    local flexproxy_tool="../flexproxy-tool/flexproxy-tool"

    if [ ! -f "$flexproxy_tool" ]; then
        print_error "FlexProxy Tool不存在: $flexproxy_tool"
        print_info "请先编译FlexProxy Tool: cd ../flexproxy-tool && go build -o flexproxy-tool ."
        exit 1
    fi

    # 检查工具是否可执行
    if [ ! -x "$flexproxy_tool" ]; then
        print_error "FlexProxy Tool不可执行: $flexproxy_tool"
        exit 1
    fi
}

# 创建备份
create_backup() {
    local input_file=$1
    local backup_file="${input_file}.backup.$(date +%s)"
    
    cp "$input_file" "$backup_file"
    print_success "原配置已备份到: $backup_file"
    echo "$backup_file"
}

# 执行迁移步骤
execute_migration_step() {
    local step_num=$1
    local step_name=$2
    local migrate_type=$3
    local input_file=$4
    local output_file=$5
    local flexproxy_tool="../flexproxy-tool/flexproxy-tool"

    print_message $PURPLE "🔄 第${step_num}步: ${step_name}"

    if "$flexproxy_tool" migrate "$migrate_type" "$input_file" "$output_file"; then
        print_success "${step_name}完成"
        return 0
    else
        print_error "${step_name}失败"
        return 1
    fi
}

# 验证配置文件
validate_config() {
    local config_file=$1
    local flexproxy_tool="../flexproxy-tool/flexproxy-tool"

    print_message $PURPLE "🔍 验证配置文件: $config_file"

    if "$flexproxy_tool" config validate "$config_file"; then
        print_success "配置验证通过"
        return 0
    else
        print_warning "配置验证失败，但迁移已完成"
        return 1
    fi
}

# 生成配置对比报告
generate_comparison_report() {
    local original_file=$1
    local final_file=$2
    local report_file="migration_report_$(date +%Y%m%d_%H%M%S).txt"
    local flexproxy_tool="../flexproxy-tool/flexproxy-tool"

    print_message $PURPLE "📊 生成配置对比报告"

    if "$flexproxy_tool" config compare "$original_file" "$final_file" > "$report_file" 2>&1; then
        print_success "配置对比报告已生成: $report_file"
    else
        print_warning "配置对比报告生成失败"
    fi
}

# 清理临时文件
cleanup_temp_files() {
    local temp_files=("$@")
    
    for file in "${temp_files[@]}"; do
        if [ -f "$file" ]; then
            rm -f "$file"
        fi
    done
}

# 显示迁移结果
show_migration_results() {
    local original_file=$1
    local final_file=$2
    local backup_file=$3
    
    print_title "🎉 迁移完成！"
    
    echo
    print_info "文件信息:"
    echo "  📁 原配置文件: $original_file"
    echo "  📁 新配置文件: $final_file"
    echo "  📁 备份文件: $backup_file"
    
    echo
    print_info "文件大小对比:"
    if command -v wc &> /dev/null; then
        local original_size=$(wc -c < "$original_file" 2>/dev/null || echo "未知")
        local final_size=$(wc -c < "$final_file" 2>/dev/null || echo "未知")
        local original_lines=$(wc -l < "$original_file" 2>/dev/null || echo "未知")
        local final_lines=$(wc -l < "$final_file" 2>/dev/null || echo "未知")
        
        echo "  📊 原文件: ${original_size} 字节, ${original_lines} 行"
        echo "  📊 新文件: ${final_size} 字节, ${final_lines} 行"
        
        if [[ "$original_size" =~ ^[0-9]+$ ]] && [[ "$final_size" =~ ^[0-9]+$ ]]; then
            local size_diff=$((final_size - original_size))
            local size_percent=$(echo "scale=1; $size_diff * 100 / $original_size" | bc -l 2>/dev/null || echo "N/A")
            
            if [ "$size_diff" -lt 0 ]; then
                print_success "文件大小减少: ${size_diff#-} 字节 (${size_percent#-}%)"
            else
                print_info "文件大小增加: $size_diff 字节 ($size_percent%)"
            fi
        fi
    fi
    
    echo
    print_info "优化内容:"
    echo "  ✨ DNS配置统一 - 消除重复配置"
    echo "  ✨ 超时配置统一 - 按功能分类管理"
    echo "  ✨ 端口配置统一 - 增加冲突检测"
    echo "  ✨ 缓存配置层次化 - 五层企业架构"
    echo "  ✨ 模块启用统一管理 - 智能依赖管理"
    
    echo
    print_info "下一步操作:"
    echo "  1. 检查新配置文件: $final_file"
    echo "  2. 根据需要调整配置参数"
    echo "  3. 使用新配置启动FlexProxy"
    echo "  4. 验证系统运行正常"
    
    echo
    print_success "🎊 恭喜！您的FlexProxy配置已升级到世界顶级水平！"
}

# 主函数
main() {
    print_title "FlexProxy 配置文件一键迁移工具"
    
    # 检查参数
    check_arguments "$@"
    
    local input_file=$1
    local output_file=${2:-"${input_file%.*}_optimized.yaml"}
    
    # 如果输出文件名没有扩展名，添加.yaml
    if [[ "$output_file" != *.yaml ]] && [[ "$output_file" != *.yml ]]; then
        output_file="${output_file}.yaml"
    fi
    
    print_info "输入文件: $input_file"
    print_info "输出文件: $output_file"
    
    # 环境检查
    print_message $PURPLE "🔍 环境检查"
    check_file_exists "$input_file"
    check_go_environment
    check_flexproxy_tool
    print_success "环境检查通过"
    
    # 创建备份
    print_message $PURPLE "💾 创建备份"
    backup_file=$(create_backup "$input_file")
    
    # 定义临时文件
    local temp_dir=$(mktemp -d)
    local step1_file="$temp_dir/step1_dns.yaml"
    local step2_file="$temp_dir/step2_timeout.yaml"
    local step3_file="$temp_dir/step3_port.yaml"
    local step4_file="$temp_dir/step4_cache.yaml"
    local step5_file="$output_file"
    
    # 执行五阶段迁移
    print_title "🚀 开始五阶段配置迁移"
    
    # 第一阶段：DNS配置统一
    if ! execute_migration_step 1 "DNS配置统一" "dns" "$input_file" "$step1_file"; then
        cleanup_temp_files "$temp_dir"
        exit 1
    fi

    # 第二阶段：超时配置统一
    if ! execute_migration_step 2 "超时配置统一" "timeout" "$step1_file" "$step2_file"; then
        cleanup_temp_files "$temp_dir"
        exit 1
    fi

    # 第三阶段：端口配置统一
    if ! execute_migration_step 3 "端口配置统一" "port" "$step2_file" "$step3_file"; then
        cleanup_temp_files "$temp_dir"
        exit 1
    fi

    # 第四阶段：缓存配置层次化
    if ! execute_migration_step 4 "缓存配置层次化" "cache" "$step3_file" "$step4_file"; then
        cleanup_temp_files "$temp_dir"
        exit 1
    fi

    # 第五阶段：模块启用统一管理
    if ! execute_migration_step 5 "模块启用统一管理" "module" "$step4_file" "$step5_file"; then
        cleanup_temp_files "$temp_dir"
        exit 1
    fi
    
    # 验证最终配置
    validate_config "$output_file"
    
    # 生成对比报告
    generate_comparison_report "$input_file" "$output_file"
    
    # 清理临时文件
    rm -rf "$temp_dir"
    
    # 显示结果
    show_migration_results "$input_file" "$output_file" "$backup_file"
}

# 执行主函数
main "$@"
