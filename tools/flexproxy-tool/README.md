# FlexProxy Tool - 统一开发工具

FlexProxy Tool 是一个统一的命令行工具，整合了所有FlexProxy的开发、维护、测试和调试功能。

## 🚀 快速开始

### 安装
```bash
cd tools/flexproxy-tool
go build -o flexproxy-tool .
```

### 基本用法
```bash
# 显示帮助
./flexproxy-tool --help

# 验证配置文件
./flexproxy-tool config validate config.yaml

# 诊断配置问题
./flexproxy-tool config doctor config.yaml

# 完整配置迁移
./flexproxy-tool migrate all old_config.yaml new_config.yaml
```

## 📋 命令参考

### 全局选项
```
-v, --verbose    详细输出模式
-q, --quiet      静默模式
-h, --help       显示帮助
```

### 配置管理 (config)

#### `config validate` - 配置验证
验证配置文件的格式和内容。

```bash
flexproxy-tool config validate [config-file]

# 示例
flexproxy-tool config validate config.yaml
flexproxy-tool config validate --verbose config.yaml
```

**功能**:
- 早期配置文件验证
- 完整配置加载和验证
- 配置摘要显示
- 关键配置项验证

#### `config doctor` - 配置诊断
诊断配置问题并提供解决方案。

```bash
flexproxy-tool config doctor [config-file]

# 示例
flexproxy-tool config doctor config.yaml
```

**功能**:
- 文件存在性检查
- YAML格式验证
- 配置验证
- 依赖检查
- 优化建议

#### `config fix` - 配置修复
自动修复常见的配置问题。

```bash
flexproxy-tool config fix [config-file]

# 示例
flexproxy-tool config fix config.yaml
```

**功能**:
- 启用全局服务
- 设置默认代理文件路径
- 创建默认端口配置
- 修复后验证

#### `config compare` - 配置对比
对比两个配置文件的差异。

```bash
flexproxy-tool config compare <old-config> <new-config>

# 示例
flexproxy-tool config compare old.yaml new.yaml
```

### 配置迁移 (migrate)

#### `migrate dns` - DNS配置迁移
```bash
flexproxy-tool migrate dns <input-config> [output-config]
```

#### `migrate timeout` - 超时配置迁移
```bash
flexproxy-tool migrate timeout <input-config> [output-config]
```

#### `migrate port` - 端口配置迁移
```bash
flexproxy-tool migrate port <input-config> [output-config]
```

#### `migrate cache` - 缓存配置迁移
```bash
flexproxy-tool migrate cache <input-config> [output-config]
```

#### `migrate module` - 模块配置迁移
```bash
flexproxy-tool migrate module <input-config> [output-config]
```

#### `migrate all` - 完整配置迁移
执行完整的五阶段配置迁移流程。

```bash
flexproxy-tool migrate all <input-config> [output-config]

# 示例
flexproxy-tool migrate all old_config.yaml new_config.yaml
```

### 功能测试 (test)

#### `test certificate` - 证书功能测试
```bash
flexproxy-tool test certificate
```

测试HTTPS证书的生成、管理和验证功能。

#### `test server` - 服务器启动测试
```bash
flexproxy-tool test server
```

测试FlexProxy服务器的启动过程。

#### `test smart` - 智能模式测试
```bash
flexproxy-tool test smart
```

测试智能代理模式的功能和策略。

### 调试工具 (debug)

#### `debug events` - 事件系统调试
```bash
flexproxy-tool debug events
```

调试FlexProxy的事件系统和触发器机制。

#### `debug yaml` - YAML配置调试
```bash
flexproxy-tool debug yaml [config-file]

# 示例
flexproxy-tool debug yaml config.yaml
```

验证和调试YAML配置文件的格式和内容。

## 🔧 开发信息

### 项目结构
```
flexproxy-tool/
├── main.go              # 主入口
├── cmd/                 # 命令实现
│   ├── root.go          # 根命令和全局功能
│   ├── config.go        # 配置管理命令组
│   ├── migrate.go       # 迁移命令组
│   ├── test.go          # 测试命令组
│   └── debug.go         # 调试命令组
├── go.mod               # Go模块定义
└── README.md            # 使用文档
```

### 技术栈
- **CLI框架**: [Cobra](https://github.com/spf13/cobra)
- **YAML处理**: gopkg.in/yaml.v3
- **项目集成**: 直接使用FlexProxy内部API

### 构建和测试
```bash
# 构建
go build -o flexproxy-tool .

# 测试编译
go build ./...

# 运行测试
go test ./...
```

## 🎯 使用场景

### 日常开发
```bash
# 验证配置文件
flexproxy-tool config validate config.yaml

# 快速诊断问题
flexproxy-tool config doctor config.yaml

# 自动修复配置
flexproxy-tool config fix config.yaml
```

### 配置升级
```bash
# 完整迁移流程
flexproxy-tool migrate all old_config.yaml new_config.yaml

# 单独迁移DNS配置
flexproxy-tool migrate dns config.yaml
```

### 功能测试
```bash
# 测试证书功能
flexproxy-tool test certificate

# 测试服务器启动
flexproxy-tool test server

# 测试智能模式
flexproxy-tool test smart
```

### 问题调试
```bash
# 调试YAML配置
flexproxy-tool debug yaml config.yaml

# 调试事件系统
flexproxy-tool debug events
```

## 📈 优势

1. **统一入口**: 所有工具功能集中在一个命令中
2. **一致体验**: 统一的帮助系统、错误处理和输出格式
3. **易于维护**: 共享代码，减少重复
4. **专业设计**: 符合现代CLI工具的设计标准
5. **扩展性强**: 易于添加新功能和命令

## 🔄 迁移指南

### 从独立工具迁移
原有的独立工具命令可以直接映射到新的统一工具：

```bash
# 原命令 → 新命令
go run ./tools/validate_config config.yaml
→ flexproxy-tool config validate config.yaml

go run ./tools/config-doctor config.yaml  
→ flexproxy-tool config doctor config.yaml

go run ./tools/fix-config config.yaml
→ flexproxy-tool config fix config.yaml

cd tools/migration && ./migrate_all.sh old.yaml new.yaml
→ flexproxy-tool migrate all old.yaml new.yaml
```

### 脚本集成
在脚本中使用统一工具：

```bash
#!/bin/bash
# 验证配置
if ! flexproxy-tool config validate config.yaml; then
    echo "配置验证失败"
    exit 1
fi

# 启动服务
./flexproxy -config config.yaml
```

---

**注意**: 这是FlexProxy工具集的下一代版本，提供了更好的用户体验和维护性。原有的独立工具仍然可用，但建议迁移到这个统一工具。
