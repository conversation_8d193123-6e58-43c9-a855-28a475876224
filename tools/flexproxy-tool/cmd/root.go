package cmd

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
)

var (
	verbose bool
	quiet   bool
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "flexproxy-tool",
	Short: "FlexProxy 开发和维护工具集",
	Long: `FlexProxy Tool - 统一的开发和维护工具集

这个工具提供了完整的 FlexProxy 配置管理、迁移、测试和调试功能。
所有原本独立的工具现在都整合在这个统一的命令行界面中。

示例用法:
  flexproxy-tool config validate config.yaml     # 验证配置文件
  flexproxy-tool config doctor config.yaml       # 诊断配置问题
  flexproxy-tool migrate all old.yaml new.yaml   # 完整配置迁移
  flexproxy-tool test certificate                 # 测试证书功能

使用 'flexproxy-tool <command> --help' 获取特定命令的详细帮助。`,
	Version: "1.0.0",
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() error {
	return rootCmd.Execute()
}

func init() {
	// 全局标志
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "详细输出模式")
	rootCmd.PersistentFlags().BoolVarP(&quiet, "quiet", "q", false, "静默模式")

	// 添加子命令
	rootCmd.AddCommand(configCmd)
	rootCmd.AddCommand(migrateCmd)
	rootCmd.AddCommand(testCmd)
	rootCmd.AddCommand(debugCmd)
}

// 全局辅助函数
func isVerbose() bool {
	return verbose && !quiet
}

func isQuiet() bool {
	return quiet
}

func printInfo(format string, args ...interface{}) {
	if !quiet {
		fmt.Printf(format, args...)
	}
}

func printVerbose(format string, args ...interface{}) {
	if verbose && !quiet {
		fmt.Printf("🔍 "+format, args...)
	}
}

func printSuccess(format string, args ...interface{}) {
	if !quiet {
		fmt.Printf("✅ "+format, args...)
	}
}

func printWarning(format string, args ...interface{}) {
	if !quiet {
		fmt.Printf("⚠️  "+format, args...)
	}
}

func printError(format string, args ...interface{}) {
	fmt.Fprintf(os.Stderr, "❌ "+format, args...)
}

func exitWithError(format string, args ...interface{}) {
	printError(format, args...)
	os.Exit(1)
}
