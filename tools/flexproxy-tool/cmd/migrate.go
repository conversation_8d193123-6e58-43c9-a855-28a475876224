package cmd

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/cobra"
)

// migrateCmd represents the migrate command
var migrateCmd = &cobra.Command{
	Use:   "migrate",
	Short: "配置文件迁移工具",
	Long: `配置文件迁移工具集，支持各种配置的迁移和升级。

可用的子命令:
  dns         DNS配置迁移
  timeout     超时配置迁移
  port        端口配置迁移
  cache       缓存配置迁移
  module      模块配置迁移
  all         完整配置迁移流程`,
}

// migrateDnsCmd represents the migrate dns command
var migrateDnsCmd = &cobra.Command{
	Use:   "dns <input-config> [output-config]",
	Short: "DNS配置迁移",
	Long: `将旧版本的DNS配置迁移到新的dns_service配置结构。

迁移内容包括:
- dns_lookup_mode → dns_service.lookup_mode
- custom_dns_servers → dns_service.servers
- reverse_dns_lookup → dns_service.reverse_lookup
- dns_cache_ttl → dns_service.cache.ttl`,
	Args: cobra.RangeArgs(1, 2),
	Run:  runMigrateDns,
}

// migrateTimeoutCmd represents the migrate timeout command
var migrateTimeoutCmd = &cobra.Command{
	Use:   "timeout <input-config> [output-config]",
	Short: "超时配置迁移",
	Long: `将分散在各个模块中的超时配置统一到timeouts配置节。

迁移内容包括:
- server.read_timeout → timeouts.network.read
- server.write_timeout → timeouts.network.write
- proxy.retry_interval → timeouts.proxy.retry
- dns_service.timeout → timeouts.dns.query`,
	Args: cobra.RangeArgs(1, 2),
	Run:  runMigrateTimeout,
}

// migratePortCmd represents the migrate port command
var migratePortCmd = &cobra.Command{
	Use:   "port <input-config> [output-config]",
	Short: "端口配置迁移",
	Long: `将分散在各个模块中的端口配置统一到ports配置节。

迁移内容包括:
- server.port → ports.http
- server.https_port → ports.https
- server.socks_port → ports.socks
- monitoring.port → ports.monitoring`,
	Args: cobra.RangeArgs(1, 2),
	Run:  runMigratePort,
}

// migrateCacheCmd represents the migrate cache command
var migrateCacheCmd = &cobra.Command{
	Use:   "cache <input-config> [output-config]",
	Short: "缓存配置迁移",
	Long: `将缓存配置迁移到层次化的新结构。

迁移内容包括:
- cache.enabled → cache.global.enabled
- cache.ttl → cache.global.default_ttl
- cache.size → cache.global.default_size
- 添加模块化缓存配置`,
	Args: cobra.RangeArgs(1, 2),
	Run:  runMigrateCache,
}

// migrateModuleCmd represents the migrate module command
var migrateModuleCmd = &cobra.Command{
	Use:   "module <input-config> [output-config]",
	Short: "模块配置迁移",
	Long: `统一管理各个模块的启用状态。

迁移内容包括:
- 收集各模块的启用状态
- 创建统一的modules.enabled配置
- 智能依赖管理`,
	Args: cobra.RangeArgs(1, 2),
	Run:  runMigrateModule,
}

// migrateAllCmd represents the migrate all command
var migrateAllCmd = &cobra.Command{
	Use:   "all <input-config> [output-config]",
	Short: "完整配置迁移",
	Long: `执行完整的配置文件迁移流程，包括所有迁移步骤。

迁移流程:
1. DNS配置迁移
2. 超时配置迁移
3. 端口配置迁移
4. 缓存配置迁移
5. 模块配置迁移

自动创建备份文件并生成迁移报告。`,
	Args: cobra.RangeArgs(1, 2),
	Run:  runMigrateAll,
}

func init() {
	migrateCmd.AddCommand(migrateDnsCmd)
	migrateCmd.AddCommand(migrateTimeoutCmd)
	migrateCmd.AddCommand(migratePortCmd)
	migrateCmd.AddCommand(migrateCacheCmd)
	migrateCmd.AddCommand(migrateModuleCmd)
	migrateCmd.AddCommand(migrateAllCmd)
}

func runMigrateDns(cmd *cobra.Command, args []string) {
	inputFile := args[0]
	outputFile := inputFile
	if len(args) > 1 {
		outputFile = args[1]
	}

	printInfo("=== DNS配置迁移 ===\n")
	printInfo("输入文件: %s\n", inputFile)
	printInfo("输出文件: %s\n", outputFile)

	// 检查输入文件是否存在
	if _, err := os.Stat(inputFile); os.IsNotExist(err) {
		exitWithError("配置文件不存在: %s\n", inputFile)
	}

	// 创建输出目录
	if outputDir := filepath.Dir(outputFile); outputDir != "." {
		if err := os.MkdirAll(outputDir, 0755); err != nil {
			exitWithError("创建输出目录失败: %v\n", err)
		}
	}

	// TODO: 调用原有的dns_migration工具逻辑
	printInfo("🚧 DNS配置迁移功能正在集成中...\n")
	printInfo("💡 您可以暂时使用: go run ./tools/dns_migration %s %s\n", inputFile, outputFile)
}

func runMigrateTimeout(cmd *cobra.Command, args []string) {
	inputFile := args[0]
	outputFile := inputFile
	if len(args) > 1 {
		outputFile = args[1]
	}

	printInfo("=== 超时配置迁移 ===\n")
	printInfo("输入文件: %s\n", inputFile)
	printInfo("输出文件: %s\n", outputFile)

	// 检查输入文件是否存在
	if _, err := os.Stat(inputFile); os.IsNotExist(err) {
		exitWithError("配置文件不存在: %s\n", inputFile)
	}

	// TODO: 调用原有的timeout_migration工具逻辑
	printInfo("🚧 超时配置迁移功能正在集成中...\n")
	printInfo("💡 您可以暂时使用: go run ./tools/timeout_migration %s %s\n", inputFile, outputFile)
}

func runMigratePort(cmd *cobra.Command, args []string) {
	inputFile := args[0]
	outputFile := inputFile
	if len(args) > 1 {
		outputFile = args[1]
	}

	printInfo("=== 端口配置迁移 ===\n")
	printInfo("输入文件: %s\n", inputFile)
	printInfo("输出文件: %s\n", outputFile)

	// 检查输入文件是否存在
	if _, err := os.Stat(inputFile); os.IsNotExist(err) {
		exitWithError("配置文件不存在: %s\n", inputFile)
	}

	// TODO: 调用原有的port_migration工具逻辑
	printInfo("🚧 端口配置迁移功能正在集成中...\n")
	printInfo("💡 您可以暂时使用: go run ./tools/port_migration %s %s\n", inputFile, outputFile)
}

func runMigrateCache(cmd *cobra.Command, args []string) {
	inputFile := args[0]
	outputFile := inputFile
	if len(args) > 1 {
		outputFile = args[1]
	}

	printInfo("=== 缓存配置迁移 ===\n")
	printInfo("输入文件: %s\n", inputFile)
	printInfo("输出文件: %s\n", outputFile)

	// 检查输入文件是否存在
	if _, err := os.Stat(inputFile); os.IsNotExist(err) {
		exitWithError("配置文件不存在: %s\n", inputFile)
	}

	// TODO: 调用原有的cache_migration工具逻辑
	printInfo("🚧 缓存配置迁移功能正在集成中...\n")
	printInfo("💡 您可以暂时使用: go run ./tools/cache_migration %s %s\n", inputFile, outputFile)
}

func runMigrateModule(cmd *cobra.Command, args []string) {
	inputFile := args[0]
	outputFile := inputFile
	if len(args) > 1 {
		outputFile = args[1]
	}

	printInfo("=== 模块配置迁移 ===\n")
	printInfo("输入文件: %s\n", inputFile)
	printInfo("输出文件: %s\n", outputFile)

	// 检查输入文件是否存在
	if _, err := os.Stat(inputFile); os.IsNotExist(err) {
		exitWithError("配置文件不存在: %s\n", inputFile)
	}

	// TODO: 调用原有的module_migration工具逻辑
	printInfo("🚧 模块配置迁移功能正在集成中...\n")
	printInfo("💡 您可以暂时使用: go run ./tools/module_migration %s %s\n", inputFile, outputFile)
}

func runMigrateAll(cmd *cobra.Command, args []string) {
	inputFile := args[0]
	outputFile := "config_migrated.yaml"
	if len(args) > 1 {
		outputFile = args[1]
	}

	printInfo("=== 完整配置迁移 ===\n")
	printInfo("输入文件: %s\n", inputFile)
	printInfo("输出文件: %s\n", outputFile)

	// 检查输入文件是否存在
	if _, err := os.Stat(inputFile); os.IsNotExist(err) {
		exitWithError("配置文件不存在: %s\n", inputFile)
	}

	// 创建备份
	backupFile := fmt.Sprintf("%s.backup.%d", inputFile, os.Getpid())
	if err := copyFile(inputFile, backupFile); err != nil {
		exitWithError("创建备份文件失败: %v\n", err)
	}
	printSuccess("原配置已备份到: %s\n", backupFile)

	printInfo("\n🔄 开始五阶段配置迁移流程...\n")

	// TODO: 实现完整的迁移流程
	// 1. DNS配置迁移
	// 2. 超时配置迁移
	// 3. 端口配置迁移
	// 4. 缓存配置迁移
	// 5. 模块配置迁移

	printInfo("🚧 完整配置迁移功能正在集成中...\n")
	printInfo("💡 您可以暂时使用: cd tools/migration && ./migrate_all.sh %s %s\n", inputFile, outputFile)
}

// 辅助函数
func copyFile(src, dst string) error {
	data, err := os.ReadFile(src)
	if err != nil {
		return err
	}
	return os.WriteFile(dst, data, 0644)
}
