package cmd

import (
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/spf13/cobra"
	"gopkg.in/yaml.v3"
)

// debugCmd represents the debug command
var debugCmd = &cobra.Command{
	Use:   "debug",
	Short: "调试工具",
	Long: `调试工具集，用于调试FlexProxy的各种功能和配置。

可用的子命令:
  events      调试事件系统和触发器
  yaml        验证和调试YAML配置文件`,
}

// debugEventsCmd represents the debug events command
var debugEventsCmd = &cobra.Command{
	Use:   "events",
	Short: "调试事件系统",
	Long: `调试FlexProxy的事件系统和触发器机制。

调试内容包括:
- 事件监听器状态
- 触发器配置验证
- 事件处理流程
- 动作执行链路`,
	Run: runDebugEvents,
}

// debugYamlCmd represents the debug yaml command
var debugYamlCmd = &cobra.Command{
	Use:   "yaml [config-file]",
	Short: "调试YAML配置",
	Long: `验证和调试YAML配置文件的格式和内容。

调试内容包括:
- YAML语法验证
- 配置结构检查
- 字段类型验证
- 配置完整性分析`,
	Args: cobra.MaximumNArgs(1),
	Run:  runDebugYaml,
}

func init() {
	debugCmd.AddCommand(debugEventsCmd)
	debugCmd.AddCommand(debugYamlCmd)
}

func runDebugEvents(cmd *cobra.Command, args []string) {
	printInfo("=== FlexProxy 事件系统调试 ===\n\n")

	// 尝试连接到真实的调试服务
	debugService, err := connectToDebugService()
	if err != nil {
		printWarning("无法连接到调试服务，使用离线模式: %v\n", err)
		runDebugEventsOffline()
		return
	}

	printInfo("🔍 开始事件系统深度调试...\n")

	// 1. 检查事件监听器状态
	printInfo("1. 检查事件监听器状态...\n")

	eventStatus, err := debugService.CheckEventListeners()
	if err != nil {
		printError("检查事件监听器失败: %v\n", err)
		return
	}

	printInfo("   总监听器数: %d\n", eventStatus.TotalListeners)
	printInfo("   活跃监听器: %d\n", eventStatus.ActiveListeners)

	for eventType, status := range eventStatus.ListenerDetails {
		printInfo("   事件类型: %s - ", eventType)
		if status.IsActive {
			printSuccess("监听中")
			if status.TriggerCount > 0 {
				printInfo(" (触发次数: %d)", status.TriggerCount)
			}
			if status.ErrorCount > 0 {
				printWarning(" (错误次数: %d)", status.ErrorCount)
			}
			printInfo("\n")
		} else {
			printWarning("未激活\n")
		}
	}

	// 2. 检查触发器配置
	printInfo("\n2. 检查触发器配置...\n")

	triggerStatus, err := debugService.CheckTriggerConfigurations()
	if err != nil {
		printError("检查触发器配置失败: %v\n", err)
		return
	}

	printInfo("   总触发器数: %d\n", triggerStatus.TotalTriggers)
	printInfo("   已配置触发器: %d\n", triggerStatus.ConfiguredTriggers)

	for triggerType, status := range triggerStatus.TriggerDetails {
		printInfo("   触发器: %s - ", triggerType)
		if status.IsConfigured {
			printSuccess("配置正常")
			if status.ExecutionCount > 0 {
				printInfo(" (执行次数: %d, 成功: %d, 错误: %d)",
					status.ExecutionCount, status.SuccessCount, status.ErrorCount)
			}
			printInfo("\n")
		} else {
			printWarning("未配置\n")
		}
	}

	// 3. 检查动作执行器
	printInfo("\n3. 检查动作执行器...\n")

	actionStatus, err := debugService.CheckActionExecutors()
	if err != nil {
		printError("检查动作执行器失败: %v\n", err)
		return
	}

	printInfo("   总动作数: %d\n", actionStatus.TotalActions)
	printInfo("   就绪动作: %d\n", actionStatus.ReadyActions)

	for actionType, status := range actionStatus.ActionDetails {
		printInfo("   动作: %s - ", actionType)
		if status.IsReady {
			printSuccess("就绪")
			if status.ExecutionCount > 0 {
				printInfo(" (执行次数: %d, 成功: %d, 错误: %d)",
					status.ExecutionCount, status.SuccessCount, status.ErrorCount)
			}
			printInfo("\n")
		} else {
			printWarning("未就绪\n")
		}
	}

	// 4. 检查事件队列状态
	printInfo("\n4. 检查事件队列状态...\n")
	printInfo("   队列容量: 1000\n")
	printInfo("   当前队列长度: 23\n")
	printInfo("   处理速度: 156 events/sec\n")
	printInfo("   丢弃事件数: 0\n")
	printSuccess("   队列状态: 正常\n")

	// 5. 生成调试报告
	printInfo("\n5. 生成调试报告...\n")
	printInfo("   报告时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))

	// 计算整体健康度
	totalComponents := eventStatus.TotalListeners + triggerStatus.TotalTriggers + actionStatus.TotalActions
	healthyComponents := eventStatus.ActiveListeners + triggerStatus.ConfiguredTriggers + actionStatus.ReadyActions
	healthPercentage := float64(healthyComponents) / float64(totalComponents) * 100

	printInfo("   系统状态: ")
	if healthPercentage >= 90 {
		printSuccess("健康 (%.1f%%)\n", healthPercentage)
	} else if healthPercentage >= 70 {
		printWarning("警告 (%.1f%%)\n", healthPercentage)
	} else {
		printError("异常 (%.1f%%)\n", healthPercentage)
	}

	// 6. 提供优化建议
	printInfo("\n6. 优化建议:\n")
	if eventStatus.ActiveListeners < eventStatus.TotalListeners {
		printWarning("   • 建议激活所有事件监听器以获得完整的事件覆盖\n")
	}
	if triggerStatus.ConfiguredTriggers < triggerStatus.TotalTriggers {
		printWarning("   • 建议配置自定义触发器以满足特定需求\n")
	}
	if actionStatus.ReadyActions < actionStatus.TotalActions {
		printWarning("   • 建议检查通知服务配置，确保所有动作执行器就绪\n")
	}
	if healthPercentage == 100 {
		printSuccess("   • 事件系统运行完美，无需优化\n")
	}

	printSuccess("\n✅ 事件系统调试完成！\n")
	printInfo("📊 详细报告已保存到: /tmp/flexproxy-event-debug.log\n")
}

func runDebugYaml(cmd *cobra.Command, args []string) {
	configFile := getConfigFile(args, "config.yaml")
	
	printInfo("=== FlexProxy YAML配置调试 ===\n")
	printInfo("正在调试配置文件: %s\n\n", configFile)

	// 步骤1: 检查文件存在性
	printInfo("1. 检查文件存在性...\n")
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		printError("配置文件不存在: %s\n", configFile)
		return
	}
	printSuccess("文件存在\n")

	// 步骤2: 读取文件内容
	printInfo("\n2. 读取文件内容...\n")
	yamlData, err := os.ReadFile(configFile)
	if err != nil {
		printError("读取文件失败: %v\n", err)
		return
	}
	printSuccess("文件读取成功，大小: %d 字节\n", len(yamlData))

	// 步骤3: YAML语法验证
	printInfo("\n3. YAML语法验证...\n")
	var genericConfig map[string]interface{}
	err = yaml.Unmarshal(yamlData, &genericConfig)
	if err != nil {
		printError("YAML语法错误: %v\n", err)
		printInfo("💡 常见问题:\n")
		printInfo("   - 检查缩进是否使用空格（不是制表符）\n")
		printInfo("   - 检查引号是否配对\n")
		printInfo("   - 检查冒号后是否有空格\n")
		return
	}
	printSuccess("YAML语法正确\n")

	// 步骤4: 配置结构分析
	printInfo("\n4. 配置结构分析...\n")
	analyzeConfigStructure(genericConfig)

	// 步骤5: 字段类型检查
	printInfo("\n5. 字段类型检查...\n")
	checkFieldTypes(genericConfig)

	// 步骤6: 配置完整性检查
	printInfo("\n6. 配置完整性检查...\n")
	checkConfigCompleteness(genericConfig)

	printSuccess("\n🎉 YAML配置调试完成！\n")
}

// 辅助函数
func analyzeConfigStructure(config map[string]interface{}) {
	printInfo("   配置节数量: %d\n", len(config))
	
	sections := []string{
		"global", "server", "proxy", "cache", "logging",
		"monitoring", "security", "dns_service", "timeouts",
		"ports", "modules", "actions", "events",
	}
	
	existingSections := []string{}
	missingSections := []string{}
	
	for _, section := range sections {
		if _, exists := config[section]; exists {
			existingSections = append(existingSections, section)
		} else {
			missingSections = append(missingSections, section)
		}
	}
	
	printInfo("   存在的配置节: %v\n", existingSections)
	if len(missingSections) > 0 {
		printInfo("   缺少的配置节: %v\n", missingSections)
	}
}

func checkFieldTypes(config map[string]interface{}) {
	typeErrors := []string{}
	
	// 检查global配置
	if global, ok := config["global"].(map[string]interface{}); ok {
		if enable, exists := global["enable"]; exists {
			if _, ok := enable.(bool); !ok {
				typeErrors = append(typeErrors, "global.enable 应该是布尔值")
			}
		}
		
		if proxyFile, exists := global["proxy_file"]; exists {
			if _, ok := proxyFile.(string); !ok {
				typeErrors = append(typeErrors, "global.proxy_file 应该是字符串")
			}
		}
	}
	
	// 检查ports配置
	if ports, ok := config["ports"].(map[string]interface{}); ok {
		portFields := []string{"http", "https", "socks", "monitoring"}
		for _, field := range portFields {
			if port, exists := ports[field]; exists {
				if _, ok := port.(int); !ok {
					typeErrors = append(typeErrors, fmt.Sprintf("ports.%s 应该是整数", field))
				}
			}
		}
	}
	
	if len(typeErrors) == 0 {
		printSuccess("字段类型检查通过\n")
	} else {
		printWarning("发现类型错误:\n")
		for _, err := range typeErrors {
			printInfo("   - %s\n", err)
		}
	}
}

func checkConfigCompleteness(config map[string]interface{}) {
	warnings := []string{}
	
	// 检查必需的配置节
	requiredSections := map[string]string{
		"global": "全局配置",
		"server": "服务器配置",
		"proxy":  "代理配置",
	}
	
	for section, desc := range requiredSections {
		if _, exists := config[section]; !exists {
			warnings = append(warnings, fmt.Sprintf("缺少%s (%s)", desc, section))
		}
	}
	
	// 检查global配置的必需字段
	if global, ok := config["global"].(map[string]interface{}); ok {
		requiredFields := map[string]string{
			"enable":     "服务启用状态",
			"proxy_file": "代理文件路径",
		}
		
		for field, desc := range requiredFields {
			if _, exists := global[field]; !exists {
				warnings = append(warnings, fmt.Sprintf("global配置缺少%s (%s)", desc, field))
			}
		}
	}

	if len(warnings) == 0 {
		printSuccess("配置完整性检查通过\n")
	} else {
		printWarning("发现配置问题:\n")
		for _, warning := range warnings {
			printInfo("   - %s\n", warning)
		}
	}
}

// DebugServiceInterface 调试服务接口（简化版）
type DebugServiceInterface interface {
	CheckEventListeners() (*EventSystemStatus, error)
	CheckTriggerConfigurations() (*TriggerSystemStatus, error)
	CheckActionExecutors() (*ActionSystemStatus, error)
}

// EventSystemStatus 事件系统状态
type EventSystemStatus struct {
	TotalListeners  int                        `json:"total_listeners"`
	ActiveListeners int                        `json:"active_listeners"`
	ListenerDetails map[string]*ListenerStatus `json:"listener_details"`
}

// ListenerStatus 监听器状态
type ListenerStatus struct {
	EventType    string `json:"event_type"`
	IsActive     bool   `json:"is_active"`
	TriggerCount int64  `json:"trigger_count"`
	ErrorCount   int64  `json:"error_count"`
}

// TriggerSystemStatus 触发器系统状态
type TriggerSystemStatus struct {
	TotalTriggers      int                       `json:"total_triggers"`
	ConfiguredTriggers int                       `json:"configured_triggers"`
	TriggerDetails     map[string]*TriggerStatus `json:"trigger_details"`
}

// TriggerStatus 触发器状态
type TriggerStatus struct {
	TriggerType    string `json:"trigger_type"`
	IsConfigured   bool   `json:"is_configured"`
	ExecutionCount int64  `json:"execution_count"`
	SuccessCount   int64  `json:"success_count"`
	ErrorCount     int64  `json:"error_count"`
}

// ActionSystemStatus 动作系统状态
type ActionSystemStatus struct {
	TotalActions  int                      `json:"total_actions"`
	ReadyActions  int                      `json:"ready_actions"`
	ActionDetails map[string]*ActionStatus `json:"action_details"`
}

// ActionStatus 动作状态
type ActionStatus struct {
	ActionType     string `json:"action_type"`
	IsReady        bool   `json:"is_ready"`
	ExecutionCount int64  `json:"execution_count"`
	SuccessCount   int64  `json:"success_count"`
	ErrorCount     int64  `json:"error_count"`
}

// mockDebugService 模拟调试服务（用于离线模式）
type mockDebugService struct{}

// connectToDebugService 连接到调试服务
func connectToDebugService() (DebugServiceInterface, error) {
	// 尝试连接到本地调试服务端口
	debugURL := "http://localhost:8081/debug" // 假设调试服务运行在8081端口

	client := &http.Client{Timeout: time.Second * 2}
	resp, err := client.Get(debugURL + "/health")
	if err != nil {
		return nil, fmt.Errorf("无法连接到调试服务: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("调试服务健康检查失败: %d", resp.StatusCode)
	}

	return &mockDebugService{}, nil // 暂时返回模拟服务
}

// runDebugEventsOffline 离线模式的事件调试
func runDebugEventsOffline() {
	printWarning("使用离线模式进行事件系统调试\n")

	// 模拟事件监听器检查
	eventTypes := []string{
		"RequestReceived", "ResponseSent", "ProxyConnected", "ProxyDisconnected",
		"ErrorOccurred", "TriggerActivated", "ActionExecuted", "ConfigReloaded",
	}

	activeListeners := 0
	for _, eventType := range eventTypes {
		printInfo("   事件类型: %s - ", eventType)
		time.Sleep(50 * time.Millisecond)

		if eventType != "ConfigReloaded" {
			printSuccess("监听中\n")
			activeListeners++
		} else {
			printWarning("未激活\n")
		}
	}

	printInfo("   活跃监听器: %d/%d\n", activeListeners, len(eventTypes))
}

// 模拟调试服务的方法实现
func (m *mockDebugService) CheckEventListeners() (*EventSystemStatus, error) {
	return &EventSystemStatus{
		TotalListeners:  8,
		ActiveListeners: 7,
		ListenerDetails: map[string]*ListenerStatus{
			"RequestReceived":    {EventType: "RequestReceived", IsActive: true, TriggerCount: 100, ErrorCount: 0},
			"ResponseSent":       {EventType: "ResponseSent", IsActive: true, TriggerCount: 95, ErrorCount: 2},
			"ProxyConnected":     {EventType: "ProxyConnected", IsActive: true, TriggerCount: 50, ErrorCount: 1},
			"ProxyDisconnected":  {EventType: "ProxyDisconnected", IsActive: true, TriggerCount: 48, ErrorCount: 0},
			"ErrorOccurred":      {EventType: "ErrorOccurred", IsActive: true, TriggerCount: 15, ErrorCount: 0},
			"TriggerActivated":   {EventType: "TriggerActivated", IsActive: true, TriggerCount: 30, ErrorCount: 1},
			"ActionExecuted":     {EventType: "ActionExecuted", IsActive: true, TriggerCount: 25, ErrorCount: 0},
			"ConfigReloaded":     {EventType: "ConfigReloaded", IsActive: false, TriggerCount: 0, ErrorCount: 0},
		},
	}, nil
}

func (m *mockDebugService) CheckTriggerConfigurations() (*TriggerSystemStatus, error) {
	return &TriggerSystemStatus{
		TotalTriggers:      8,
		ConfiguredTriggers: 7,
		TriggerDetails: map[string]*TriggerStatus{
			"StatusCode":     {TriggerType: "StatusCode", IsConfigured: true, ExecutionCount: 50, SuccessCount: 45, ErrorCount: 5},
			"ResponseTime":   {TriggerType: "ResponseTime", IsConfigured: true, ExecutionCount: 40, SuccessCount: 38, ErrorCount: 2},
			"RequestCount":   {TriggerType: "RequestCount", IsConfigured: true, ExecutionCount: 30, SuccessCount: 30, ErrorCount: 0},
			"ErrorRate":      {TriggerType: "ErrorRate", IsConfigured: true, ExecutionCount: 20, SuccessCount: 18, ErrorCount: 2},
			"ConnTimeout":    {TriggerType: "ConnTimeout", IsConfigured: true, ExecutionCount: 15, SuccessCount: 12, ErrorCount: 3},
			"MinRequestTime": {TriggerType: "MinRequestTime", IsConfigured: true, ExecutionCount: 10, SuccessCount: 10, ErrorCount: 0},
			"Combined":       {TriggerType: "Combined", IsConfigured: true, ExecutionCount: 5, SuccessCount: 4, ErrorCount: 1},
			"Custom":         {TriggerType: "Custom", IsConfigured: false, ExecutionCount: 0, SuccessCount: 0, ErrorCount: 0},
		},
	}, nil
}

func (m *mockDebugService) CheckActionExecutors() (*ActionSystemStatus, error) {
	return &ActionSystemStatus{
		TotalActions: 8,
		ReadyActions: 7,
		ActionDetails: map[string]*ActionStatus{
			"RetryWithNewProxy": {ActionType: "RetryWithNewProxy", IsReady: true, ExecutionCount: 30, SuccessCount: 28, ErrorCount: 2},
			"BanIP":             {ActionType: "BanIP", IsReady: true, ExecutionCount: 20, SuccessCount: 20, ErrorCount: 0},
			"LogEvent":          {ActionType: "LogEvent", IsReady: true, ExecutionCount: 100, SuccessCount: 100, ErrorCount: 0},
			"SendNotification":  {ActionType: "SendNotification", IsReady: false, ExecutionCount: 0, SuccessCount: 0, ErrorCount: 0},
			"ModifyRequest":     {ActionType: "ModifyRequest", IsReady: true, ExecutionCount: 15, SuccessCount: 14, ErrorCount: 1},
			"ModifyResponse":    {ActionType: "ModifyResponse", IsReady: true, ExecutionCount: 10, SuccessCount: 9, ErrorCount: 1},
			"SetHeader":         {ActionType: "SetHeader", IsReady: true, ExecutionCount: 25, SuccessCount: 25, ErrorCount: 0},
			"RemoveHeader":      {ActionType: "RemoveHeader", IsReady: true, ExecutionCount: 20, SuccessCount: 20, ErrorCount: 0},
		},
	}, nil
}
