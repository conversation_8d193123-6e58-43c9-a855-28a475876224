package cmd

import (
	"os"
	"path/filepath"
	"strings"

	"github.com/spf13/cobra"
	"flexproxy/internal/config"
	"flexproxy/common/errors"
)

// configCmd represents the config command
var configCmd = &cobra.Command{
	Use:   "config",
	Short: "配置文件管理工具",
	Long: `配置文件管理工具集，包含验证、诊断、修复和对比功能。

可用的子命令:
  validate    验证配置文件格式和内容
  doctor      诊断配置问题并提供解决方案
  fix         自动修复常见的配置问题
  compare     对比两个配置文件的差异`,
}

// configValidateCmd represents the config validate command
var configValidateCmd = &cobra.Command{
	Use:   "validate [config-file]",
	Short: "验证配置文件",
	Long: `验证配置文件的格式和内容，包括：
- 早期配置文件验证
- 完整配置加载和验证
- 配置摘要显示
- 关键配置项验证`,
	Args: cobra.MaximumNArgs(1),
	Run:  runConfigValidate,
}

// configDoctorCmd represents the config doctor command
var configDoctorCmd = &cobra.Command{
	Use:   "doctor [config-file]",
	Short: "诊断配置问题",
	Long: `诊断配置文件问题并提供解决方案，包括：
- 文件存在性检查
- YAML格式验证
- 配置验证
- 依赖检查
- 优化建议`,
	Args: cobra.MaximumNArgs(1),
	Run:  runConfigDoctor,
}

// configFixCmd represents the config fix command
var configFixCmd = &cobra.Command{
	Use:   "fix [config-file]",
	Short: "修复配置问题",
	Long: `自动修复常见的配置问题，包括：
- 启用全局服务
- 设置默认代理文件路径
- 创建默认端口配置
- 修复后验证`,
	Args: cobra.MaximumNArgs(1),
	Run:  runConfigFix,
}

// configCompareCmd represents the config compare command
var configCompareCmd = &cobra.Command{
	Use:   "compare <old-config> <new-config>",
	Short: "对比配置文件",
	Long: `对比两个配置文件的差异，显示：
- 新增的配置项
- 删除的配置项
- 修改的配置项
- 迁移建议`,
	Args: cobra.ExactArgs(2),
	Run:  runConfigCompare,
}

func init() {
	configCmd.AddCommand(configValidateCmd)
	configCmd.AddCommand(configDoctorCmd)
	configCmd.AddCommand(configFixCmd)
	configCmd.AddCommand(configCompareCmd)
}

func runConfigValidate(cmd *cobra.Command, args []string) {
	configFile := getConfigFile(args, "config.yaml")
	
	printInfo("=== FlexProxy 配置验证工具 ===\n")
	printInfo("正在验证配置文件: %s\n\n", configFile)

	// 获取绝对路径
	absPath, err := filepath.Abs(configFile)
	if err != nil {
		exitWithError("无法获取配置文件绝对路径: %v\n", err)
	}

	// 测试1: 早期配置文件验证
	printInfo("1. 执行早期配置文件验证...\n")
	if err := config.ValidateConfigFile(absPath); err != nil {
		printError("早期验证失败: %v\n", err)
		printErrorDetails(err)
		os.Exit(2)
	}
	printSuccess("早期验证通过\n")

	// 测试2: 完整配置加载和验证
	printInfo("\n2. 执行完整配置加载和验证...\n")
	cfg, err := config.LoadConfigFromYAML(absPath)
	if err != nil {
		printError("配置加载失败: %v\n", err)
		printErrorDetails(err)
		os.Exit(2)
	}
	printSuccess("配置加载成功\n")

	// 测试3: 显示配置摘要
	printInfo("\n3. 配置摘要:\n")
	printConfigSummary(cfg)

	// 测试4: 验证关键配置项
	printInfo("\n4. 验证关键配置项:\n")
	validateKeyConfigurations(cfg)

	printSuccess("\n=== 所有验证测试通过 ===\n")
}

func runConfigDoctor(cmd *cobra.Command, args []string) {
	configFile := getConfigFile(args, "config.yaml")
	
	printInfo("=== FlexProxy 配置诊断工具 ===\n")
	printInfo("此工具将帮助您诊断和修复配置文件问题\n\n")

	// 获取绝对路径
	absPath, err := filepath.Abs(configFile)
	if err != nil {
		exitWithError("无法获取配置文件绝对路径: %v\n", err)
	}

	printInfo("正在诊断配置文件: %s\n", absPath)
	printInfo("%s\n", strings.Repeat("=", 60))

	// 步骤1: 检查文件存在性
	printInfo("📁 步骤1: 检查配置文件存在性\n")
	if _, err := os.Stat(absPath); os.IsNotExist(err) {
		printError("配置文件不存在: %s\n", absPath)
		printInfo("💡 解决方案:\n")
		printInfo("   - 检查文件路径是否正确\n")
		printInfo("   - 确保配置文件已创建\n")
		printInfo("   - 使用示例配置文件作为模板\n")
		os.Exit(3)
	}
	printSuccess("配置文件存在\n")

	// 步骤2: 检查文件可读性
	printInfo("\n📖 步骤2: 检查文件可读性\n")
	if _, err := os.ReadFile(absPath); err != nil {
		printError("无法读取配置文件: %v\n", err)
		printInfo("💡 解决方案:\n")
		printInfo("   - 检查文件权限\n")
		printInfo("   - 确保文件未被其他程序占用\n")
		os.Exit(3)
	}
	printSuccess("配置文件可读\n")

	// 步骤3: 尝试解析YAML
	printInfo("\n🔍 步骤3: 解析YAML格式\n")
	cfg, err := config.LoadConfigFromYAMLWithoutValidation(absPath)
	if err != nil {
		printError("YAML解析失败: %v\n", err)
		printInfo("💡 解决方案:\n")
		printInfo("   - 检查YAML语法是否正确\n")
		printInfo("   - 确保缩进使用空格而非制表符\n")
		printInfo("   - 检查引号和特殊字符的使用\n")
		printInfo("   - 使用YAML验证工具检查语法\n")
		os.Exit(2)
	}
	printSuccess("YAML格式正确\n")

	// 步骤4: 执行配置验证
	printInfo("\n✅ 步骤4: 执行配置验证\n")
	if err := config.ValidateConfig(cfg); err != nil {
		printError("配置验证失败: %v\n", err)
		printInfo("\n")
		
		// 提供详细的错误分析和解决方案
		analyzeConfigError(err, cfg)
		os.Exit(2)
	}
	printSuccess("配置验证通过\n")

	// 步骤5: 检查关键依赖
	printInfo("\n🔗 步骤5: 检查关键依赖\n")
	checkDependencies(cfg)

	// 步骤6: 提供配置优化建议
	printInfo("\n💡 步骤6: 配置优化建议\n")
	provideOptimizationSuggestions(cfg)

	printInfo("\n%s\n", strings.Repeat("=", 60))
	printSuccess("配置诊断完成！您的配置文件没有问题。\n")
}

func runConfigFix(cmd *cobra.Command, args []string) {
	configFile := getConfigFile(args, "config.yaml")
	
	printInfo("=== FlexProxy 配置修复工具 ===\n")
	printInfo("此工具将尝试自动修复常见的配置问题\n\n")

	// 获取绝对路径
	absPath, err := filepath.Abs(configFile)
	if err != nil {
		exitWithError("无法获取配置文件绝对路径: %v\n", err)
	}

	printInfo("正在修复配置文件: %s\n", absPath)
	printInfo("%s\n", strings.Repeat("=", 60))

	// 检查文件是否存在
	if _, err := os.Stat(absPath); os.IsNotExist(err) {
		printError("配置文件不存在: %s\n", absPath)
		printInfo("💡 建议:\n")
		printInfo("   - 从示例配置文件复制一份\n")
		printInfo("   - 或者使用 config.yaml 作为模板\n")
		os.Exit(3)
	}

	// 尝试加载配置
	cfg, err := config.LoadConfigFromYAMLWithoutValidation(absPath)
	if err != nil {
		printError("无法加载配置文件: %v\n", err)
		printInfo("💡 这通常是YAML语法错误，需要手动修复\n")
		os.Exit(2)
	}

	printSuccess("配置文件加载成功\n")

	// 应用自动修复
	fixed := false

	// 修复全局配置
	if !cfg.Global.Enable {
		printInfo("🔧 修复: 启用全局服务\n")
		cfg.Global.Enable = true
		fixed = true
	}

	if cfg.Global.ProxyFile == "" {
		printInfo("🔧 修复: 设置默认代理文件路径\n")
		cfg.Global.ProxyFile = "./proxies.txt"
		fixed = true
	}

	// 修复服务器配置
	if cfg.Server != nil {
		if cfg.Server.Host == "" {
			printInfo("🔧 修复: 设置默认服务器主机\n")
			cfg.Server.Host = "0.0.0.0"
			fixed = true
		}
	}

	// 修复端口配置
	if cfg.Ports == nil {
		printInfo("🔧 修复: 创建默认端口配置\n")
		cfg.Ports = &config.PortsConfig{
			HTTP:  8080,
			HTTPS: 8443,
			SOCKS: 1080,
		}
		fixed = true
	}

	if fixed {
		printInfo("\n📝 应用的修复:\n")
		printInfo("   - 已自动修复常见的配置问题\n")
		printInfo("   - 请手动验证修复结果\n")
		printInfo("   - 建议备份原始配置文件\n")
	}

	// 验证修复后的配置
	printInfo("\n🔍 验证修复后的配置...\n")
	if err := config.ValidateConfig(cfg); err != nil {
		printWarning("修复后仍有验证错误: %v\n", err)
		printInfo("💡 这些问题需要手动修复:\n")
		printInfo("   - 检查必需字段的值\n")
		printInfo("   - 验证枚举类型的取值\n")
		printInfo("   - 确保数值在有效范围内\n")
		os.Exit(2)
	}

	printSuccess("修复后的配置验证通过\n")

	// 检查关键依赖
	printInfo("\n🔗 检查关键依赖...\n")
	if cfg.Global.ProxyFile != "" {
		if _, err := os.Stat(cfg.Global.ProxyFile); os.IsNotExist(err) {
			printWarning("代理文件不存在: %s\n", cfg.Global.ProxyFile)
			printInfo("💡 请创建代理文件或修改配置中的路径\n")
		} else {
			printSuccess("代理文件存在: %s\n", cfg.Global.ProxyFile)
		}
	}

	printInfo("\n%s\n", strings.Repeat("=", 60))
	if fixed {
		printSuccess("配置修复完成！请检查修复结果并重新测试。\n")
		printInfo("💡 注意: 此工具只能修复基本问题，复杂配置仍需手动调整。\n")
	} else {
		printSuccess("配置文件状态良好，无需修复。\n")
	}
}

func runConfigCompare(cmd *cobra.Command, args []string) {
	oldConfig := args[0]
	newConfig := args[1]

	printInfo("=== FlexProxy 配置对比工具 ===\n")
	printInfo("对比配置文件:\n")
	printInfo("  旧配置: %s\n", oldConfig)
	printInfo("  新配置: %s\n", newConfig)
	printInfo("============================================================\n\n")

	// 实现配置对比逻辑
	if err := compareConfigFiles(oldConfig, newConfig); err != nil {
		printError("配置对比失败: %v\n", err)
		os.Exit(1)
	}
}

// compareConfigFiles 对比两个配置文件
func compareConfigFiles(oldPath, newPath string) error {
	printInfo("📊 开始配置对比分析...\n")

	// 1. 检查文件是否存在
	if _, err := os.Stat(oldPath); os.IsNotExist(err) {
		return fmt.Errorf("旧配置文件不存在: %s", oldPath)
	}
	if _, err := os.Stat(newPath); os.IsNotExist(err) {
		return fmt.Errorf("新配置文件不存在: %s", newPath)
	}

	// 2. 加载配置文件
	printInfo("🔍 加载配置文件...\n")
	oldCfg, err := config.LoadConfig(oldPath)
	if err != nil {
		return fmt.Errorf("加载旧配置失败: %v", err)
	}

	newCfg, err := config.LoadConfig(newPath)
	if err != nil {
		return fmt.Errorf("加载新配置失败: %v", err)
	}

	// 3. 执行详细对比
	differences := analyzeConfigDifferences(oldCfg, newCfg)

	// 4. 输出对比结果
	printConfigComparisonResults(differences)

	return nil
}

// ConfigDifference 配置差异
type ConfigDifference struct {
	Section   string      `json:"section"`
	Field     string      `json:"field"`
	OldValue  interface{} `json:"old_value"`
	NewValue  interface{} `json:"new_value"`
	ChangeType string     `json:"change_type"` // added, removed, modified
	Impact    string      `json:"impact"`      // low, medium, high
}

// analyzeConfigDifferences 分析配置差异
func analyzeConfigDifferences(oldCfg, newCfg *config.Config) []ConfigDifference {
	var differences []ConfigDifference

	// 对比全局配置
	differences = append(differences, compareGlobalConfig(oldCfg.Global, newCfg.Global)...)

	// 对比服务器配置
	differences = append(differences, compareServerConfig(oldCfg.Server, newCfg.Server)...)

	// 对比代理配置
	differences = append(differences, compareProxyConfig(oldCfg.Proxy, newCfg.Proxy)...)

	// 对比事件配置
	differences = append(differences, compareEventsConfig(oldCfg.Events, newCfg.Events)...)

	// 对比动作配置
	differences = append(differences, compareActionsConfig(oldCfg.Actions, newCfg.Actions)...)

	return differences
}

// compareGlobalConfig 对比全局配置
func compareGlobalConfig(oldGlobal, newGlobal config.GlobalConfig) []ConfigDifference {
	var differences []ConfigDifference

	if oldGlobal.Enable != newGlobal.Enable {
		differences = append(differences, ConfigDifference{
			Section:    "global",
			Field:      "enable",
			OldValue:   oldGlobal.Enable,
			NewValue:   newGlobal.Enable,
			ChangeType: "modified",
			Impact:     "high",
		})
	}

	if oldGlobal.ProxyFile != newGlobal.ProxyFile {
		differences = append(differences, ConfigDifference{
			Section:    "global",
			Field:      "proxy_file",
			OldValue:   oldGlobal.ProxyFile,
			NewValue:   newGlobal.ProxyFile,
			ChangeType: "modified",
			Impact:     "medium",
		})
	}

	if oldGlobal.IPRotationMode != newGlobal.IPRotationMode {
		differences = append(differences, ConfigDifference{
			Section:    "global",
			Field:      "ip_rotation_mode",
			OldValue:   oldGlobal.IPRotationMode,
			NewValue:   newGlobal.IPRotationMode,
			ChangeType: "modified",
			Impact:     "medium",
		})
	}

	return differences
}

// compareServerConfig 对比服务器配置
func compareServerConfig(oldServer, newServer *config.ServerConfig) []ConfigDifference {
	var differences []ConfigDifference

	if oldServer == nil && newServer != nil {
		differences = append(differences, ConfigDifference{
			Section:    "server",
			Field:      "entire_section",
			OldValue:   nil,
			NewValue:   "added",
			ChangeType: "added",
			Impact:     "high",
		})
		return differences
	}

	if oldServer != nil && newServer == nil {
		differences = append(differences, ConfigDifference{
			Section:    "server",
			Field:      "entire_section",
			OldValue:   "existed",
			NewValue:   nil,
			ChangeType: "removed",
			Impact:     "high",
		})
		return differences
	}

	if oldServer != nil && newServer != nil {
		if oldServer.ListenAddress != newServer.ListenAddress {
			differences = append(differences, ConfigDifference{
				Section:    "server",
				Field:      "listen_address",
				OldValue:   oldServer.ListenAddress,
				NewValue:   newServer.ListenAddress,
				ChangeType: "modified",
				Impact:     "high",
			})
		}

		if oldServer.Host != newServer.Host {
			differences = append(differences, ConfigDifference{
				Section:    "server",
				Field:      "host",
				OldValue:   oldServer.Host,
				NewValue:   newServer.Host,
				ChangeType: "modified",
				Impact:     "medium",
			})
		}
	}

	return differences
}

// compareProxyConfig 对比代理配置
func compareProxyConfig(oldProxy, newProxy *config.ProxyConfig) []ConfigDifference {
	var differences []ConfigDifference

	// 简化实现：检查主要字段
	if oldProxy == nil && newProxy != nil {
		differences = append(differences, ConfigDifference{
			Section:    "proxy",
			Field:      "entire_section",
			OldValue:   nil,
			NewValue:   "added",
			ChangeType: "added",
			Impact:     "medium",
		})
	} else if oldProxy != nil && newProxy == nil {
		differences = append(differences, ConfigDifference{
			Section:    "proxy",
			Field:      "entire_section",
			OldValue:   "existed",
			NewValue:   nil,
			ChangeType: "removed",
			Impact:     "medium",
		})
	}

	return differences
}

// compareEventsConfig 对比事件配置
func compareEventsConfig(oldEvents, newEvents []config.EventConfig) []ConfigDifference {
	var differences []ConfigDifference

	if len(oldEvents) != len(newEvents) {
		differences = append(differences, ConfigDifference{
			Section:    "events",
			Field:      "count",
			OldValue:   len(oldEvents),
			NewValue:   len(newEvents),
			ChangeType: "modified",
			Impact:     "medium",
		})
	}

	return differences
}

// compareActionsConfig 对比动作配置
func compareActionsConfig(oldActions, newActions map[string]config.ActionSequenceName) []ConfigDifference {
	var differences []ConfigDifference

	if len(oldActions) != len(newActions) {
		differences = append(differences, ConfigDifference{
			Section:    "actions",
			Field:      "count",
			OldValue:   len(oldActions),
			NewValue:   len(newActions),
			ChangeType: "modified",
			Impact:     "medium",
		})
	}

	return differences
}

// printConfigComparisonResults 输出配置对比结果
func printConfigComparisonResults(differences []ConfigDifference) {
	if len(differences) == 0 {
		printSuccess("✅ 配置文件完全相同，无差异\n")
		return
	}

	printInfo("📋 发现 %d 个配置差异:\n\n", len(differences))

	highImpactCount := 0
	mediumImpactCount := 0
	lowImpactCount := 0

	for i, diff := range differences {
		// 根据影响级别选择颜色
		switch diff.Impact {
		case "high":
			printError("🔴 差异 %d (高影响):\n", i+1)
			highImpactCount++
		case "medium":
			printWarning("🟡 差异 %d (中影响):\n", i+1)
			mediumImpactCount++
		default:
			printInfo("🟢 差异 %d (低影响):\n", i+1)
			lowImpactCount++
		}

		printInfo("   节点: %s.%s\n", diff.Section, diff.Field)
		printInfo("   类型: %s\n", diff.ChangeType)
		printInfo("   旧值: %v\n", diff.OldValue)
		printInfo("   新值: %v\n", diff.NewValue)
		printInfo("\n")
	}

	// 输出统计信息
	printInfo("📊 差异统计:\n")
	printInfo("   🔴 高影响: %d 个\n", highImpactCount)
	printInfo("   🟡 中影响: %d 个\n", mediumImpactCount)
	printInfo("   🟢 低影响: %d 个\n", lowImpactCount)
	printInfo("   📝 总计: %d 个\n", len(differences))

	// 提供建议
	if highImpactCount > 0 {
		printWarning("\n⚠️  警告: 发现高影响配置变更，建议仔细检查后再应用\n")
	}
}

// 辅助函数
func getConfigFile(args []string, defaultFile string) string {
	if len(args) > 0 {
		return args[0]
	}
	return defaultFile
}

func printErrorDetails(err error) {
	if flexErr, ok := err.(*errors.FlexProxyError); ok {
		printInfo("   错误类型: %s\n", flexErr.Type)
		printInfo("   错误代码: %s\n", flexErr.Code)
		printInfo("   错误信息: %s\n", flexErr.Message)
		if flexErr.Details != "" {
			printInfo("   详细信息: %s\n", flexErr.Details)
		}
		if flexErr.Cause != nil {
			printInfo("   原始错误: %v\n", flexErr.Cause)
		}
	}
}

func printConfigSummary(cfg *config.Config) {
	printInfo("   全局启用状态: %v\n", cfg.Global.Enable)
	printInfo("   代理文件路径: %s\n", cfg.Global.ProxyFile)

	if cfg.Server != nil {
		printInfo("   服务器主机: %s\n", cfg.Server.Host)
		if cfg.Server.ListenAddress != "" {
			printInfo("   监听地址: %s\n", cfg.Server.ListenAddress)
		}
	}

	if cfg.DNSService != nil {
		checker := config.NewConfigChecker(cfg)
		printInfo("   DNS服务启用: %v\n", checker.IsDNSServiceEnabled())
		if cfg.DNSService.Servers != nil {
			totalServers := len(cfg.DNSService.Servers.Primary) + len(cfg.DNSService.Servers.Secondary)
			printInfo("   DNS服务器数量: %d\n", totalServers)
		}
		printInfo("   DNS超时: %s\n", cfg.DNSService.Timeout)
	}

	if cfg.Cache != nil {
		if cfg.Cache.Storage != nil {
			printInfo("   缓存类型: %s\n", cfg.Cache.Storage.Type)
		}
		if cfg.Cache.Global != nil {
			printInfo("   缓存大小: %d\n", cfg.Cache.Global.DefaultSize)
			printInfo("   TTL: %s\n", cfg.Cache.Global.DefaultTTL)
		}
	}

	printInfo("   事件规则数量: %d\n", len(cfg.Events))
	printInfo("   动作序列数量: %d\n", len(cfg.Actions))
}

func validateKeyConfigurations(cfg *config.Config) {
	// 验证全局配置
	if cfg.Global.Enable {
		printSuccess("全局服务已启用\n")
	} else {
		printWarning("全局服务未启用\n")
	}

	// 验证代理文件
	if cfg.Global.ProxyFile != "" {
		if _, err := os.Stat(cfg.Global.ProxyFile); err == nil {
			printSuccess("代理文件存在: %s\n", cfg.Global.ProxyFile)
		} else {
			printWarning("代理文件不存在: %s\n", cfg.Global.ProxyFile)
		}
	} else {
		printWarning("未配置代理文件路径\n")
	}

	// 验证服务器配置
	if cfg.Server != nil && cfg.Server.Host != "" {
		printSuccess("服务器主机已配置: %s\n", cfg.Server.Host)
	} else {
		printWarning("服务器主机未配置\n")
	}
}

func analyzeConfigError(err error, cfg *config.Config) {
	printInfo("🔧 错误分析和解决方案:\n")
	
	if flexErr, ok := err.(*errors.FlexProxyError); ok {
		switch flexErr.Type {
		case errors.ErrTypeValidation:
			printInfo("   这是一个配置验证错误\n")
			printInfo("   💡 建议检查配置文件中的字段值和格式\n")
		case errors.ErrTypeFile:
			printInfo("   这是一个文件相关错误\n")
			printInfo("   💡 建议检查文件路径和权限\n")
		default:
			printInfo("   💡 请检查配置文件的语法和内容\n")
		}
	}
}

func checkDependencies(cfg *config.Config) {
	// 检查代理文件
	if cfg.Global.ProxyFile != "" {
		if _, err := os.Stat(cfg.Global.ProxyFile); os.IsNotExist(err) {
			printWarning("代理文件不存在: %s\n", cfg.Global.ProxyFile)
			printInfo("   💡 请创建代理文件或修改配置中的路径\n")
		} else {
			printSuccess("代理文件存在: %s\n", cfg.Global.ProxyFile)
		}
	}

	// 检查其他依赖...
	printVerbose("依赖检查完成\n")
}

func provideOptimizationSuggestions(cfg *config.Config) {
	suggestions := []string{}
	checker := config.NewConfigChecker(cfg)

	// DNS配置建议
	if checker.IsDNSServiceEnabled() {
		if cfg.DNSService != nil && cfg.DNSService.Servers != nil {
			if len(cfg.DNSService.Servers.Primary) == 0 && len(cfg.DNSService.Servers.Secondary) == 0 {
				suggestions = append(suggestions, "考虑配置DNS服务器以提高域名解析性能")
			}
		}
	}

	// 缓存配置建议
	if cfg.Cache != nil && cfg.Cache.Storage != nil && cfg.Cache.Storage.Type == "memory" {
		if cfg.Cache.Global != nil && cfg.Cache.Global.DefaultSize < 100 {
			suggestions = append(suggestions, "考虑增加内存缓存大小以提高性能")
		}
	}

	// 监控配置建议
	if !checker.IsMonitoringEnabled() {
		suggestions = append(suggestions, "建议启用监控功能以便观察代理服务状态")
	}

	// 安全配置建议
	if cfg.Security == nil || cfg.Security.Auth == nil || cfg.Security.Auth.Type == "none" {
		suggestions = append(suggestions, "考虑启用认证以提高安全性")
	}

	if len(suggestions) == 0 {
		printSuccess("您的配置已经很好了！\n")
	} else {
		for i, suggestion := range suggestions {
			printInfo("   %d. %s\n", i+1, suggestion)
		}
	}
}
