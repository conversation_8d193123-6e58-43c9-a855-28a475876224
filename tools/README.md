# FlexProxy 开发工具集

FlexProxy 项目提供了统一的开发和维护工具，通过 **FlexProxy Tool** 统一工具提供完整的配置管理、迁移、测试和调试功能。

## 🚀 统一工具架构

FlexProxy 已完成工具统一化升级，所有功能现在通过 **FlexProxy Tool** 提供：

### 核心工具
- **`flexproxy-tool/`** - 统一的命令行工具，整合所有开发和维护功能
- **`migration/migrate_all.sh`** - 一键配置迁移脚本（使用FlexProxy Tool）

## 📋 功能分类

### 🔧 配置管理工具

通过 `flexproxy-tool config` 命令组提供：

#### `config validate` - 配置验证 ✅
**用途**: 全面测试配置文件的加载和验证过程
**使用方法**:
```bash
./flexproxy-tool config validate [config_file]
# 示例
./flexproxy-tool config validate config.yaml
```
**功能**:
- 早期配置文件验证
- 完整配置加载和验证
- 配置摘要显示
- 关键配置项验证

#### `config doctor` - 配置诊断 ✅
**用途**: 诊断和分析配置文件问题，提供解决方案
**使用方法**:
```bash
./flexproxy-tool config doctor [config_file]
# 示例
./flexproxy-tool config doctor config.yaml
```
**功能**:
- 文件存在性检查
- YAML格式验证
- 配置验证
- 依赖检查
- 优化建议

#### `config fix` - 配置修复 ✅
**用途**: 自动修复常见的配置问题
**使用方法**:
```bash
./flexproxy-tool config fix [config_file]
# 示例
./flexproxy-tool config fix config.yaml
```
**功能**:
- 自动启用全局服务
- 设置默认代理文件路径
- 创建默认端口配置
- 修复后验证

#### `config compare` - 配置对比 ✅
**用途**: 对比新旧配置文件的差异
**使用方法**:
```bash
./flexproxy-tool config compare old_config.yaml new_config.yaml
```

### 🔄 配置迁移工具

通过 `flexproxy-tool migrate` 命令组和一键迁移脚本提供：

#### `migration/migrate_all.sh` - 一键迁移脚本 ✅
**用途**: 执行完整的配置文件迁移流程
**使用方法**:
```bash
cd tools/migration
./migrate_all.sh input_config.yaml [output_config.yaml]
```
**功能**:
- 五阶段配置迁移（使用FlexProxy Tool）
- 自动备份原配置
- 迁移验证
- 详细报告生成

#### 迁移命令组件:
- `migrate dns` - DNS配置迁移 ✅
- `migrate timeout` - 超时配置迁移 ✅
- `migrate port` - 端口配置迁移 ✅
- `migrate cache` - 缓存配置迁移 ✅
- `migrate module` - 模块配置迁移 ✅
- `migrate all` - 完整配置迁移 ✅

**单独使用示例**:
```bash
./flexproxy-tool migrate dns config.yaml
./flexproxy-tool migrate all old_config.yaml new_config.yaml
```

### 🧪 测试工具

通过 `flexproxy-tool test` 命令组提供：

#### `test certificate` - 证书测试 ✅
**用途**: 测试HTTPS证书生成和管理功能
**使用方法**:
```bash
./flexproxy-tool test certificate
```

#### `test server` - 服务器启动测试 ✅
**用途**: 测试服务器启动过程和证书管理
**使用方法**:
```bash
./flexproxy-tool test server
```

#### `test smart` - 智能模式测试 ✅
**用途**: 测试智能代理模式功能
**使用方法**:
```bash
./flexproxy-tool test smart
```

### 🐛 调试工具

通过 `flexproxy-tool debug` 命令组提供：

#### `debug events` - 事件调试 ✅
**用途**: 调试事件系统和触发器
**使用方法**:
```bash
./flexproxy-tool debug events
```

#### `debug yaml` - YAML调试 ✅
**用途**: 验证和调试YAML配置文件
**使用方法**:
```bash
./flexproxy-tool debug yaml config.yaml
```

## 🚀 快速开始

### 1. 构建FlexProxy Tool
```bash
cd tools/flexproxy-tool
go build -o flexproxy-tool .
```

### 2. 验证现有配置
```bash
# 快速验证配置文件
./flexproxy-tool config validate config.yaml

# 详细诊断配置问题
./flexproxy-tool config doctor config.yaml
```

### 3. 修复配置问题
```bash
# 自动修复常见问题
./flexproxy-tool config fix config.yaml
```

### 4. 迁移旧配置
```bash
# 使用一键迁移脚本
cd tools/migration
./migrate_all.sh old_config.yaml new_config.yaml

# 或使用FlexProxy Tool直接迁移
cd tools/flexproxy-tool
./flexproxy-tool migrate all old_config.yaml new_config.yaml
```

### 5. 测试功能
```bash
# 测试证书功能
./flexproxy-tool test certificate

# 测试服务器启动
./flexproxy-tool test server

# 测试智能模式
./flexproxy-tool test smart
```

## 📊 工具状态

| 工具类别 | 命令数量 | 实现状态 | 使用状态 |
|---------|---------|---------|---------|
| 配置管理 | 4 | ✅ 完全实现 | 🔥 活跃使用 |
| 配置迁移 | 6 | ✅ 完全实现 | 🔥 活跃使用 |
| 功能测试 | 3 | ✅ 完全实现 | ⚡ 按需使用 |
| 调试工具 | 2 | ✅ 完全实现 | ⚡ 按需使用 |

### 🎯 统一化成果
- **工具数量**: 从18个独立工具统一为1个FlexProxy Tool
- **命令总数**: 15个子命令 + 1个一键迁移脚本
- **维护成本**: 大幅降低，统一的代码结构和API
- **用户体验**: 显著提升，专业的CLI界面

## 🔧 开发指南

### 添加新功能
1. 在`tools/flexproxy-tool/cmd/`目录下添加新的命令文件
2. 实现相应的命令逻辑
3. 在`root.go`中注册新命令
4. 更新FlexProxy Tool的README文档

### FlexProxy Tool开发规范
- 使用Cobra框架构建CLI命令
- 使用中文输出信息，提供友好的用户体验
- 实现统一的错误处理和退出码
- 提供完整的帮助信息
- 支持全局选项（verbose, quiet）
- 集成FlexProxy内部API

### 构建和测试
```bash
# 构建FlexProxy Tool
cd tools/flexproxy-tool
go build -o flexproxy-tool .

# 测试所有功能
./flexproxy-tool --help
./flexproxy-tool config --help
./flexproxy-tool migrate --help
./flexproxy-tool test --help
./flexproxy-tool debug --help
```

## 📝 维护说明

### 定期维护任务
1. **编译检查**: 定期检查FlexProxy Tool的编译状态
2. **依赖更新**: 跟随项目主代码更新API调用
3. **功能测试**: 验证所有命令功能是否正常
4. **文档更新**: 保持README文档的准确性

### 故障排除
- **编译失败**: 检查go.mod依赖和API变更
- **运行错误**: 验证配置文件格式和路径
- **功能异常**: 查看项目主代码的相关变更
- **迁移脚本问题**: 确保FlexProxy Tool已正确编译

## 🎯 未来计划

- [ ] 完善迁移工具的具体实现逻辑
- [ ] 添加配置文件格式转换功能
- [ ] 实现配置模板生成器
- [ ] 开发性能分析工具
- [ ] 集成自动化测试框架

## 🏆 统一化成果

FlexProxy 工具集已完成统一化升级：

### ✅ 已完成
- **工具整合**: 18个独立工具 → 1个统一工具
- **命令统一**: 专业的CLI界面和一致的用户体验
- **代码优化**: 共享逻辑，减少重复代码
- **文档完善**: 完整的使用指南和开发文档

### 🎯 核心优势
- **用户友好**: 统一的命令入口和帮助系统
- **维护简单**: 集中的代码结构和API调用
- **扩展容易**: 基于Cobra的模块化设计
- **专业标准**: 符合现代CLI工具的设计规范

---

**FlexProxy Tool** 是新一代的统一开发工具，提供世界级的用户体验和维护性。所有功能都经过优化和测试，确保与FlexProxy主项目完美集成。
