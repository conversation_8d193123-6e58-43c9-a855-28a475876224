// Package hotreload 事件通知系统实现
// 提供配置变更事件的发布订阅机制和服务通知功能
package hotreload

import (
	"context"
	"fmt"
	"sync"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
)

// EventBus 事件总线，负责配置变更事件的发布和订阅
type EventBus struct {
	subscribers   map[string][]*Subscriber // 订阅者映射（按事件类型）
	eventChannel  chan *ConfigChangeEvent  // 事件通道
	logger        logger.Logger            // 日志记录器
	ctx           context.Context          // 上下文
	cancel        context.CancelFunc       // 取消函数
	mu            sync.RWMutex             // 并发控制锁
	running       bool                     // 是否正在运行
	options       *EventBusOptions         // 事件总线选项
	eventHistory  []*ConfigChangeEvent     // 事件历史记录
	maxHistory    int                      // 最大历史记录数
}

// EventBusOptions 事件总线选项
type EventBusOptions struct {
	BufferSize       int           // 事件缓冲区大小
	MaxHistory       int           // 最大历史记录数
	DeliveryTimeout  time.Duration // 事件投递超时时间
	RetryAttempts    int           // 重试次数
	EnableHistory    bool          // 是否启用历史记录
	EnableMetrics    bool          // 是否启用指标统计
}

// DefaultEventBusOptions 默认事件总线选项
func DefaultEventBusOptions() *EventBusOptions {
	return &EventBusOptions{
		BufferSize:      constants.ChangeEventBufferSize,
		MaxHistory:      1000,
		DeliveryTimeout: 5 * time.Second,
		RetryAttempts:   3,
		EnableHistory:   true,
		EnableMetrics:   true,
	}
}

// Subscriber 订阅者，包含订阅信息和回调函数
type Subscriber struct {
	ID          string                                    // 订阅者ID
	Name        string                                    // 订阅者名称
	EventTypes  []string                                  // 订阅的事件类型
	Callback    func(*ConfigChangeEvent) error            // 回调函数
	Filter      func(*ConfigChangeEvent) bool             // 事件过滤器
	Priority    int                                       // 优先级（数字越小优先级越高）
	CreatedAt   time.Time                                 // 创建时间
	LastNotified time.Time                               // 最后通知时间
	NotifyCount int64                                     // 通知次数
	ErrorCount  int64                                     // 错误次数
}

// ConfigChangeEvent 配置变更事件，扩展了基础的ChangeEvent
type ConfigChangeEvent struct {
	*ChangeEvent                          // 基础变更事件
	EventID      string                   // 事件ID
	ServiceName  string                   // 触发服务名称
	Severity     EventSeverity            // 事件严重程度
	Tags         map[string]string        // 事件标签
	Metadata     map[string]interface{}   // 元数据
	Processed    bool                     // 是否已处理
	ProcessedAt  *time.Time               // 处理时间
	Retries      int                      // 重试次数
}

// EventSeverity 事件严重程度
type EventSeverity int

const (
	SeverityInfo EventSeverity = iota // 信息级别
	SeverityWarn                      // 警告级别
	SeverityError                     // 错误级别
	SeverityCritical                  // 严重级别
)

// String 返回事件严重程度的字符串表示
func (es EventSeverity) String() string {
	switch es {
	case SeverityInfo:
		return "info"
	case SeverityWarn:
		return "warn"
	case SeverityError:
		return "error"
	case SeverityCritical:
		return "critical"
	default:
		return "unknown"
	}
}

// NewEventBus 创建新的事件总线
func NewEventBus(log logger.Logger, options *EventBusOptions) *EventBus {
	if log == nil {
		log = logger.GetLogger("event_bus")
	}
	
	if options == nil {
		options = DefaultEventBusOptions()
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &EventBus{
		subscribers:  make(map[string][]*Subscriber),
		eventChannel: make(chan *ConfigChangeEvent, options.BufferSize),
		logger:       log,
		ctx:          ctx,
		cancel:       cancel,
		running:      false,
		options:      options,
		eventHistory: make([]*ConfigChangeEvent, 0),
		maxHistory:   options.MaxHistory,
	}
}

// Start 启动事件总线
func (eb *EventBus) Start() error {
	eb.mu.Lock()
	defer eb.mu.Unlock()

	if eb.running {
		return nil
	}

	eb.logger.Info("启动配置变更事件总线")

	// 启动事件处理协程
	go eb.eventProcessingRoutine()

	eb.running = true
	eb.logger.Info("配置变更事件总线已启动")

	return nil
}

// Stop 停止事件总线
func (eb *EventBus) Stop() error {
	eb.mu.Lock()
	defer eb.mu.Unlock()

	if !eb.running {
		return nil
	}

	eb.logger.Info("停止配置变更事件总线")

	// 取消上下文
	eb.cancel()

	// 关闭事件通道
	close(eb.eventChannel)

	eb.running = false
	eb.logger.Info("配置变更事件总线已停止")

	return nil
}

// Subscribe 订阅配置变更事件
func (eb *EventBus) Subscribe(subscriber *Subscriber) error {
	if subscriber == nil {
		return errors.NewError(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"订阅者不能为空",
		)
	}

	if subscriber.ID == "" {
		return errors.NewError(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"订阅者ID不能为空",
		)
	}

	if subscriber.Callback == nil {
		return errors.NewError(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"回调函数不能为空",
		)
	}

	eb.mu.Lock()
	defer eb.mu.Unlock()

	// 设置默认值
	if subscriber.CreatedAt.IsZero() {
		subscriber.CreatedAt = time.Now()
	}

	// 为每个事件类型添加订阅者
	for _, eventType := range subscriber.EventTypes {
		if eb.subscribers[eventType] == nil {
			eb.subscribers[eventType] = make([]*Subscriber, 0)
		}
		eb.subscribers[eventType] = append(eb.subscribers[eventType], subscriber)
	}

	eb.logger.Infof("添加配置变更事件订阅者: %s (%s), 事件类型: %v",
		subscriber.ID, subscriber.Name, subscriber.EventTypes)

	return nil
}

// Unsubscribe 取消订阅
func (eb *EventBus) Unsubscribe(subscriberID string) error {
	if subscriberID == "" {
		return errors.NewError(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"订阅者ID不能为空",
		)
	}

	eb.mu.Lock()
	defer eb.mu.Unlock()

	removedCount := 0

	// 从所有事件类型中移除订阅者
	for eventType, subscribers := range eb.subscribers {
		newSubscribers := make([]*Subscriber, 0)
		for _, subscriber := range subscribers {
			if subscriber.ID != subscriberID {
				newSubscribers = append(newSubscribers, subscriber)
			} else {
				removedCount++
			}
		}
		eb.subscribers[eventType] = newSubscribers
	}

	if removedCount > 0 {
		eb.logger.Info("移除配置变更事件订阅者: %s, 移除数量: %d", subscriberID, removedCount)
	}

	return nil
}

// PublishChange 发布配置变更事件
func (eb *EventBus) PublishChange(change *ChangeEvent, serviceName string, severity EventSeverity) error {
	if change == nil {
		return errors.NewError(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"变更事件不能为空",
		)
	}

	if !eb.running {
		return errors.NewError(
			errors.ErrTypeSystem,
			errors.ErrCodeSystemNotRunning,
			"事件总线未运行",
		)
	}

	// 创建配置变更事件
	configChangeEvent := &ConfigChangeEvent{
		ChangeEvent: change,
		EventID:     eb.generateEventID(),
		ServiceName: serviceName,
		Severity:    severity,
		Tags:        make(map[string]string),
		Metadata:    make(map[string]interface{}),
		Processed:   false,
		Retries:     0,
	}

	// 设置事件标签
	configChangeEvent.Tags["path"] = change.Path.String()
	configChangeEvent.Tags["type"] = change.Type.String()
	configChangeEvent.Tags["service"] = serviceName
	configChangeEvent.Tags["severity"] = severity.String()

	// 发送到事件通道
	select {
	case eb.eventChannel <- configChangeEvent:
		eb.logger.Debugf("发布配置变更事件: %s, 路径: %s, 类型: %s",
			configChangeEvent.EventID, change.Path, change.Type.String())
		return nil
	case <-time.After(eb.options.DeliveryTimeout):
		return errors.NewErrorWithDetails(
			errors.ErrTypeTimeout,
			errors.ErrCodeTimeoutExceeded,
			"发布事件超时",
			fmt.Sprintf("事件ID: %s", configChangeEvent.EventID),
		)
	}
}

// eventProcessingRoutine 事件处理协程
func (eb *EventBus) eventProcessingRoutine() {
	eb.logger.Info("启动配置变更事件处理协程")
	defer eb.logger.Info("配置变更事件处理协程已停止")

	for {
		select {
		case event, ok := <-eb.eventChannel:
			if !ok {
				return // 通道已关闭
			}
			eb.processEvent(event)

		case <-eb.ctx.Done():
			return
		}
	}
}

// processEvent 处理单个事件
func (eb *EventBus) processEvent(event *ConfigChangeEvent) {
	eb.logger.Debugf("处理配置变更事件: %s, 路径: %s", event.EventID, event.Path)

	// 添加到历史记录
	if eb.options.EnableHistory {
		eb.addToHistory(event)
	}

	// 获取订阅者
	eventType := event.Type.String()
	subscribers := eb.getSubscribers(eventType)

	if len(subscribers) == 0 {
		eb.logger.Debugf("没有找到事件订阅者: %s", eventType)
		return
	}

	// 按优先级排序订阅者
	eb.sortSubscribersByPriority(subscribers)

	// 通知所有订阅者
	successCount := 0
	for _, subscriber := range subscribers {
		if eb.notifySubscriber(subscriber, event) {
			successCount++
		}
	}

	// 标记事件为已处理
	event.Processed = true
	now := time.Now()
	event.ProcessedAt = &now

	eb.logger.Debugf("事件处理完成: %s, 成功通知: %d/%d",
		event.EventID, successCount, len(subscribers))
}

// notifySubscriber 通知单个订阅者
func (eb *EventBus) notifySubscriber(subscriber *Subscriber, event *ConfigChangeEvent) bool {
	// 应用过滤器
	if subscriber.Filter != nil && !subscriber.Filter(event) {
		return true // 过滤掉的事件也算成功
	}

	// 创建通知上下文
	ctx, cancel := context.WithTimeout(eb.ctx, eb.options.DeliveryTimeout)
	defer cancel()

	// 异步通知
	done := make(chan error, 1)
	go func() {
		done <- subscriber.Callback(event)
	}()

	select {
	case err := <-done:
		if err != nil {
			subscriber.ErrorCount++
			eb.logger.Warn("通知订阅者失败: %s, 事件: %s, 错误: %v", 
				subscriber.ID, event.EventID, err)
			return false
		}
		
		subscriber.NotifyCount++
		subscriber.LastNotified = time.Now()
		eb.logger.Debug("通知订阅者成功: %s, 事件: %s", subscriber.ID, event.EventID)
		return true

	case <-ctx.Done():
		subscriber.ErrorCount++
		eb.logger.Warn(fmt.Sprintf("通知订阅者超时: %s, 事件: %s", subscriber.ID, event.EventID))
		return false
	}
}

// getSubscribers 获取指定事件类型的订阅者
func (eb *EventBus) getSubscribers(eventType string) []*Subscriber {
	eb.mu.RLock()
	defer eb.mu.RUnlock()

	subscribers := make([]*Subscriber, 0)
	
	// 获取精确匹配的订阅者
	if subs, exists := eb.subscribers[eventType]; exists {
		subscribers = append(subscribers, subs...)
	}

	// 获取通配符订阅者
	if subs, exists := eb.subscribers["*"]; exists {
		subscribers = append(subscribers, subs...)
	}

	return subscribers
}

// sortSubscribersByPriority 按优先级排序订阅者
func (eb *EventBus) sortSubscribersByPriority(subscribers []*Subscriber) {
	for i := 0; i < len(subscribers)-1; i++ {
		for j := i + 1; j < len(subscribers); j++ {
			if subscribers[i].Priority > subscribers[j].Priority {
				subscribers[i], subscribers[j] = subscribers[j], subscribers[i]
			}
		}
	}
}

// addToHistory 添加事件到历史记录
func (eb *EventBus) addToHistory(event *ConfigChangeEvent) {
	eb.mu.Lock()
	defer eb.mu.Unlock()

	eb.eventHistory = append(eb.eventHistory, event)

	// 限制历史记录数量
	if len(eb.eventHistory) > eb.maxHistory {
		eb.eventHistory = eb.eventHistory[1:]
	}
}

// generateEventID 生成事件ID
func (eb *EventBus) generateEventID() string {
	return fmt.Sprintf("config_change_%d_%d", 
		time.Now().UnixNano(), 
		len(eb.eventHistory))
}

// GetEventHistory 获取事件历史记录
func (eb *EventBus) GetEventHistory(limit int) []*ConfigChangeEvent {
	eb.mu.RLock()
	defer eb.mu.RUnlock()

	if limit <= 0 || limit > len(eb.eventHistory) {
		limit = len(eb.eventHistory)
	}

	// 返回最近的事件
	start := len(eb.eventHistory) - limit
	result := make([]*ConfigChangeEvent, limit)
	copy(result, eb.eventHistory[start:])

	return result
}

// GetSubscriberStats 获取订阅者统计信息
func (eb *EventBus) GetSubscriberStats() map[string]*SubscriberStats {
	eb.mu.RLock()
	defer eb.mu.RUnlock()

	stats := make(map[string]*SubscriberStats)
	
	for _, subscribers := range eb.subscribers {
		for _, subscriber := range subscribers {
			if _, exists := stats[subscriber.ID]; !exists {
				stats[subscriber.ID] = &SubscriberStats{
					ID:           subscriber.ID,
					Name:         subscriber.Name,
					EventTypes:   subscriber.EventTypes,
					Priority:     subscriber.Priority,
					CreatedAt:    subscriber.CreatedAt,
					LastNotified: subscriber.LastNotified,
					NotifyCount:  subscriber.NotifyCount,
					ErrorCount:   subscriber.ErrorCount,
				}
			}
		}
	}

	return stats
}

// SubscriberStats 订阅者统计信息
type SubscriberStats struct {
	ID           string    `json:"id"`            // 订阅者ID
	Name         string    `json:"name"`          // 订阅者名称
	EventTypes   []string  `json:"event_types"`   // 订阅的事件类型
	Priority     int       `json:"priority"`      // 优先级
	CreatedAt    time.Time `json:"created_at"`    // 创建时间
	LastNotified time.Time `json:"last_notified"` // 最后通知时间
	NotifyCount  int64     `json:"notify_count"`  // 通知次数
	ErrorCount   int64     `json:"error_count"`   // 错误次数
}

// GetEventBusStats 获取事件总线统计信息
func (eb *EventBus) GetEventBusStats() *EventBusStats {
	eb.mu.RLock()
	defer eb.mu.RUnlock()

	totalSubscribers := 0
	for _, subscribers := range eb.subscribers {
		totalSubscribers += len(subscribers)
	}

	return &EventBusStats{
		Running:           eb.running,
		TotalSubscribers:  totalSubscribers,
		EventTypes:        len(eb.subscribers),
		HistorySize:       len(eb.eventHistory),
		MaxHistory:        eb.maxHistory,
		BufferSize:        eb.options.BufferSize,
		ChannelLength:     len(eb.eventChannel),
	}
}

// EventBusStats 事件总线统计信息
type EventBusStats struct {
	Running          bool `json:"running"`           // 是否运行中
	TotalSubscribers int  `json:"total_subscribers"` // 总订阅者数
	EventTypes       int  `json:"event_types"`       // 事件类型数
	HistorySize      int  `json:"history_size"`      // 历史记录大小
	MaxHistory       int  `json:"max_history"`       // 最大历史记录数
	BufferSize       int  `json:"buffer_size"`       // 缓冲区大小
	ChannelLength    int  `json:"channel_length"`    // 通道长度
}
