// Package hotreload 配置差异检测引擎实现
// 提供配置项级别的深度比较和变更检测功能
package hotreload

import (
	"fmt"
	"reflect"
	"sort"
	"strings"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
)

// ConfigDiffer 配置差异检测器
// 负责比较两个配置对象，识别具体变更的配置项
type ConfigDiffer struct {
	mapper    *ConfigMapper     // 配置映射器
	logger    logger.Logger     // 日志记录器
	options   *DifferOptions    // 差异检测选项
}

// DifferOptions 差异检测选项
type DifferOptions struct {
	IgnorePaths          []ConfigPath // 忽略的配置路径
	DeepCompare          bool         // 是否进行深度比较
	ComparePointers      bool         // 是否比较指针地址
	ComparePrivateFields bool         // 是否比较私有字段
	MaxDepth             int          // 最大比较深度
	Timeout              time.Duration // 比较超时时间
}

// DefaultDifferOptions 默认差异检测选项
func DefaultDifferOptions() *DifferOptions {
	return &DifferOptions{
		IgnorePaths:          make([]ConfigPath, 0),
		DeepCompare:          true,
		ComparePointers:      false,
		ComparePrivateFields: false,
		MaxDepth:             10,
		Timeout:              constants.DefaultConfigItemTimeout,
	}
}

// ChangeReport 配置变更报告
type ChangeReport struct {
	Changes     []*ChangeEvent    // 变更事件列表
	Summary     *ChangeSummary    // 变更摘要
	Timestamp   time.Time         // 报告生成时间
	Duration    time.Duration     // 比较耗时
	TotalPaths  int               // 总配置路径数
	ChangedPaths int              // 变更的配置路径数
}

// ChangeSummary 变更摘要
type ChangeSummary struct {
	AddedCount    int      // 新增配置项数量
	ModifiedCount int      // 修改配置项数量
	DeletedCount  int      // 删除配置项数量
	CriticalPaths []string // 关键路径变更列表
}

// NewConfigDiffer 创建新的配置差异检测器
func NewConfigDiffer(mapper *ConfigMapper, log logger.Logger, options *DifferOptions) *ConfigDiffer {
	if log == nil {
		log = logger.GetLogger("config_differ")
	}
	
	if options == nil {
		options = DefaultDifferOptions()
	}

	return &ConfigDiffer{
		mapper:  mapper,
		logger:  log,
		options: options,
	}
}

// CompareConfigs 比较两个配置对象，返回变更报告
func (cd *ConfigDiffer) CompareConfigs(oldConfig, newConfig *config.Config) (*ChangeReport, error) {
	startTime := time.Now()
	cd.logger.Info("开始配置差异检测")

	// 创建变更报告
	report := &ChangeReport{
		Changes:   make([]*ChangeEvent, 0),
		Timestamp: startTime,
		Summary: &ChangeSummary{
			CriticalPaths: make([]string, 0),
		},
	}

	// 检查输入参数
	if oldConfig == nil || newConfig == nil {
		return nil, errors.NewError(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"配置对象不能为空",
		)
	}

	// 确保映射器已初始化
	if !cd.mapper.IsInitialized() {
		return nil, errors.NewError(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigNotInitialized,
			"配置映射器未初始化",
		)
	}

	// 获取所有配置路径
	allPaths := cd.mapper.GetAllPaths()
	report.TotalPaths = len(allPaths)

	// 比较每个配置项
	for _, path := range allPaths {
		// 检查是否需要忽略此路径
		if cd.shouldIgnorePath(path) {
			continue
		}

		// 获取字段信息
		fieldInfo, err := cd.mapper.GetFieldByPath(path)
		if err != nil {
			cd.logger.Warnf("获取字段信息失败: %s, 错误: %v", path, err)
			continue
		}

		// 比较配置项
		change, err := cd.compareConfigItem(path, fieldInfo, oldConfig, newConfig)
		if err != nil {
			cd.logger.Warnf("比较配置项失败: %s, 错误: %v", path, err)
			continue
		}

		// 如果有变更，添加到报告中
		if change != nil {
			report.Changes = append(report.Changes, change)
			report.ChangedPaths++

			// 检查是否为关键路径
			if cd.isCriticalPath(path) {
				report.Summary.CriticalPaths = append(report.Summary.CriticalPaths, path.String())
			}

			// 更新摘要统计
			switch change.Type {
			case ChangeTypeAdd:
				report.Summary.AddedCount++
			case ChangeTypeModify:
				report.Summary.ModifiedCount++
			case ChangeTypeDelete:
				report.Summary.DeletedCount++
			}
		}
	}

	// 完成报告
	report.Duration = time.Since(startTime)
	cd.logger.Infof("配置差异检测完成，耗时: %v, 变更项: %d/%d",
		report.Duration, report.ChangedPaths, report.TotalPaths)

	return report, nil
}

// compareConfigItem 比较单个配置项
func (cd *ConfigDiffer) compareConfigItem(path ConfigPath, fieldInfo *FieldInfo, oldConfig, newConfig *config.Config) (*ChangeEvent, error) {
	// 获取旧值和新值
	oldValue, err := cd.getConfigValue(path, fieldInfo, oldConfig)
	if err != nil {
		return nil, fmt.Errorf("获取旧配置值失败: %w", err)
	}

	newValue, err := cd.getConfigValue(path, fieldInfo, newConfig)
	if err != nil {
		return nil, fmt.Errorf("获取新配置值失败: %w", err)
	}

	// 比较值
	changeType, hasChanged := cd.compareValues(oldValue, newValue, fieldInfo.Type)
	if !hasChanged {
		return nil, nil // 无变更
	}

	// 创建变更事件
	change := NewChangeEvent(path, changeType, oldValue, newValue)
	// 设置版本信息（从字段标签获取，如果没有则使用默认值1）
	versionTag := fieldInfo.Field.Tag.Get("version")
	if versionTag != "" {
		change.Version = 1 // 暂时使用固定版本，后续可以解析字符串版本
	} else {
		change.Version = 1
	}

	return change, nil
}

// getConfigValue 获取配置项的值
func (cd *ConfigDiffer) getConfigValue(path ConfigPath, fieldInfo *FieldInfo, cfg *config.Config) (interface{}, error) {
	// 使用反射获取配置值
	configValue := reflect.ValueOf(cfg).Elem()
	
	// 解析路径，逐级访问字段
	pathParts := strings.Split(path.String(), constants.ConfigPathSeparator)
	currentValue := configValue
	currentType := reflect.TypeOf(cfg).Elem()

	for _, part := range pathParts {
		// 处理指针类型
		if currentType.Kind() == reflect.Ptr {
			if currentValue.IsNil() {
				return nil, nil // 指针为nil
			}
			currentValue = currentValue.Elem()
			currentType = currentType.Elem()
		}

		// 处理结构体字段
		if currentType.Kind() == reflect.Struct {
			field, found := currentType.FieldByName(cd.getFieldNameFromYamlTag(part, currentType))
			if !found {
				return nil, fmt.Errorf("字段不存在: %s", part)
			}

			currentValue = currentValue.FieldByName(field.Name)
			currentType = field.Type
		} else if currentType.Kind() == reflect.Slice {
			// 处理切片索引
			if strings.HasPrefix(part, "[") && strings.HasSuffix(part, "]") {
				indexStr := part[1 : len(part)-1]
				index := 0
				if _, err := fmt.Sscanf(indexStr, "%d", &index); err != nil {
					return nil, fmt.Errorf("无效的切片索引: %s", part)
				}

				if index >= currentValue.Len() {
					return nil, nil // 索引超出范围
				}

				currentValue = currentValue.Index(index)
				currentType = currentType.Elem()
			}
		} else if currentType.Kind() == reflect.Map {
			// 处理映射键
			keyValue := reflect.ValueOf(part)
			currentValue = currentValue.MapIndex(keyValue)
			if !currentValue.IsValid() {
				return nil, nil // 键不存在
			}
			currentType = currentType.Elem()
		} else {
			return nil, fmt.Errorf("不支持的类型: %s", currentType.Kind())
		}

		// 检查值是否有效
		if !currentValue.IsValid() {
			return nil, nil
		}
	}

	// 返回实际值
	if currentValue.CanInterface() {
		return currentValue.Interface(), nil
	}

	return nil, fmt.Errorf("无法获取字段值")
}

// compareValues 比较两个值，返回变更类型和是否有变更
func (cd *ConfigDiffer) compareValues(oldValue, newValue interface{}, fieldType reflect.Type) (ChangeType, bool) {
	// 处理nil值情况
	if oldValue == nil && newValue == nil {
		return ChangeTypeUnknown, false
	}
	if oldValue == nil && newValue != nil {
		return ChangeTypeAdd, true
	}
	if oldValue != nil && newValue == nil {
		return ChangeTypeDelete, true
	}

	// 使用反射进行深度比较
	if cd.options.DeepCompare {
		return cd.deepCompareValues(oldValue, newValue, fieldType, 0)
	}

	// 简单比较
	if reflect.DeepEqual(oldValue, newValue) {
		return ChangeTypeUnknown, false
	}

	return ChangeTypeModify, true
}

// deepCompareValues 深度比较两个值
func (cd *ConfigDiffer) deepCompareValues(oldValue, newValue interface{}, fieldType reflect.Type, depth int) (ChangeType, bool) {
	// 检查最大深度
	if depth > cd.options.MaxDepth {
		cd.logger.Warnf("达到最大比较深度: %d", cd.options.MaxDepth)
		return ChangeTypeModify, !reflect.DeepEqual(oldValue, newValue)
	}

	oldVal := reflect.ValueOf(oldValue)
	newVal := reflect.ValueOf(newValue)

	// 类型不同，认为是修改
	if oldVal.Type() != newVal.Type() {
		return ChangeTypeModify, true
	}

	switch oldVal.Kind() {
	case reflect.Struct:
		return cd.compareStructValues(oldVal, newVal, depth)
	case reflect.Slice:
		return cd.compareSliceValues(oldVal, newVal, depth)
	case reflect.Map:
		return cd.compareMapValues(oldVal, newVal, depth)
	case reflect.Ptr:
		return cd.comparePointerValues(oldVal, newVal, depth)
	default:
		// 基本类型直接比较
		if reflect.DeepEqual(oldValue, newValue) {
			return ChangeTypeUnknown, false
		}
		return ChangeTypeModify, true
	}
}

// compareStructValues 比较结构体值
func (cd *ConfigDiffer) compareStructValues(oldVal, newVal reflect.Value, depth int) (ChangeType, bool) {
	structType := oldVal.Type()
	
	for i := 0; i < structType.NumField(); i++ {
		field := structType.Field(i)
		
		// 跳过私有字段（如果配置不比较私有字段）
		if !cd.options.ComparePrivateFields && !field.IsExported() {
			continue
		}

		oldFieldVal := oldVal.Field(i)
		newFieldVal := newVal.Field(i)

		// 递归比较字段
		_, hasChanged := cd.deepCompareValues(
			oldFieldVal.Interface(), 
			newFieldVal.Interface(), 
			field.Type, 
			depth+1,
		)

		if hasChanged {
			return ChangeTypeModify, true
		}
	}

	return ChangeTypeUnknown, false
}

// compareSliceValues 比较切片值
func (cd *ConfigDiffer) compareSliceValues(oldVal, newVal reflect.Value, depth int) (ChangeType, bool) {
	// 长度不同，认为有变更
	if oldVal.Len() != newVal.Len() {
		return ChangeTypeModify, true
	}

	// 逐个比较元素
	for i := 0; i < oldVal.Len(); i++ {
		_, hasChanged := cd.deepCompareValues(
			oldVal.Index(i).Interface(),
			newVal.Index(i).Interface(),
			oldVal.Type().Elem(),
			depth+1,
		)

		if hasChanged {
			return ChangeTypeModify, true
		}
	}

	return ChangeTypeUnknown, false
}

// compareMapValues 比较映射值
func (cd *ConfigDiffer) compareMapValues(oldVal, newVal reflect.Value, depth int) (ChangeType, bool) {
	// 长度不同，认为有变更
	if oldVal.Len() != newVal.Len() {
		return ChangeTypeModify, true
	}

	// 比较所有键值对
	for _, key := range oldVal.MapKeys() {
		oldMapVal := oldVal.MapIndex(key)
		newMapVal := newVal.MapIndex(key)

		if !newMapVal.IsValid() {
			return ChangeTypeModify, true // 键在新映射中不存在
		}

		_, hasChanged := cd.deepCompareValues(
			oldMapVal.Interface(),
			newMapVal.Interface(),
			oldVal.Type().Elem(),
			depth+1,
		)

		if hasChanged {
			return ChangeTypeModify, true
		}
	}

	return ChangeTypeUnknown, false
}

// comparePointerValues 比较指针值
func (cd *ConfigDiffer) comparePointerValues(oldVal, newVal reflect.Value, depth int) (ChangeType, bool) {
	// 如果配置比较指针地址
	if cd.options.ComparePointers {
		if oldVal.Pointer() != newVal.Pointer() {
			return ChangeTypeModify, true
		}
	}

	// 比较指针指向的值
	if oldVal.IsNil() && newVal.IsNil() {
		return ChangeTypeUnknown, false
	}
	if oldVal.IsNil() || newVal.IsNil() {
		return ChangeTypeModify, true
	}

	return cd.deepCompareValues(
		oldVal.Elem().Interface(),
		newVal.Elem().Interface(),
		oldVal.Type().Elem(),
		depth+1,
	)
}

// shouldIgnorePath 检查是否应该忽略指定路径
func (cd *ConfigDiffer) shouldIgnorePath(path ConfigPath) bool {
	for _, ignorePath := range cd.options.IgnorePaths {
		if path == ignorePath {
			return true
		}
	}
	return false
}

// isCriticalPath 检查是否为关键路径
func (cd *ConfigDiffer) isCriticalPath(path ConfigPath) bool {
	pathStr := path.String()
	for _, criticalPath := range constants.CriticalConfigPaths {
		if pathStr == criticalPath {
			return true
		}
	}
	return false
}

// getFieldNameFromYamlTag 根据YAML标签获取字段名
func (cd *ConfigDiffer) getFieldNameFromYamlTag(yamlName string, structType reflect.Type) string {
	for i := 0; i < structType.NumField(); i++ {
		field := structType.Field(i)
		yamlTag := field.Tag.Get("yaml")
		
		if yamlTag != "" {
			tagParts := strings.Split(yamlTag, ",")
			if len(tagParts) > 0 && tagParts[0] == yamlName {
				return field.Name
			}
		} else if strings.ToLower(field.Name) == yamlName {
			return field.Name
		}
	}
	
	// 如果找不到，返回首字母大写的版本
	return strings.Title(yamlName)
}

// GetChangesSummary 获取变更摘要的文本描述
func (cr *ChangeReport) GetChangesSummary() string {
	if cr.ChangedPaths == 0 {
		return "无配置变更"
	}

	summary := fmt.Sprintf("检测到 %d 项配置变更", cr.ChangedPaths)
	
	if cr.Summary.AddedCount > 0 {
		summary += fmt.Sprintf("，新增 %d 项", cr.Summary.AddedCount)
	}
	if cr.Summary.ModifiedCount > 0 {
		summary += fmt.Sprintf("，修改 %d 项", cr.Summary.ModifiedCount)
	}
	if cr.Summary.DeletedCount > 0 {
		summary += fmt.Sprintf("，删除 %d 项", cr.Summary.DeletedCount)
	}

	if len(cr.Summary.CriticalPaths) > 0 {
		summary += fmt.Sprintf("，包含 %d 项关键配置变更", len(cr.Summary.CriticalPaths))
	}

	return summary
}

// SortChangesByPriority 按优先级排序变更事件
func (cr *ChangeReport) SortChangesByPriority() {
	sort.Slice(cr.Changes, func(i, j int) bool {
		// 关键路径优先
		iCritical := cr.isCriticalChange(cr.Changes[i])
		jCritical := cr.isCriticalChange(cr.Changes[j])
		
		if iCritical && !jCritical {
			return true
		}
		if !iCritical && jCritical {
			return false
		}

		// 按变更类型排序：删除 > 修改 > 新增
		if cr.Changes[i].Type != cr.Changes[j].Type {
			return cr.Changes[i].Type > cr.Changes[j].Type
		}

		// 按路径排序
		return cr.Changes[i].Path < cr.Changes[j].Path
	})
}

// isCriticalChange 检查是否为关键变更
func (cr *ChangeReport) isCriticalChange(change *ChangeEvent) bool {
	pathStr := change.Path.String()
	for _, criticalPath := range constants.CriticalConfigPaths {
		if pathStr == criticalPath {
			return true
		}
	}
	return false
}
