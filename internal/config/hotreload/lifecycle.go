// Package hotreload 配置项生命周期管理器实现
// 提供配置项的引用计数、版本控制和自动清理功能
package hotreload

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
)

// LifecycleManager 配置项生命周期管理器
// 负责管理配置项的生命周期，包括引用计数、版本控制和自动清理
type LifecycleManager struct {
	configItems    map[ConfigPath]*ConfigItem // 配置项映射
	refCounter     *ReferenceCounter          // 引用计数器
	versionCounter int64                      // 全局版本计数器
	cleanupTicker  *time.Ticker               // 清理定时器
	logger         logger.Logger              // 日志记录器
	ctx            context.Context            // 上下文
	cancel         context.CancelFunc         // 取消函数
	mu             sync.RWMutex               // 并发控制锁
	running        bool                       // 是否正在运行
	options        *LifecycleOptions          // 生命周期选项
}

// LifecycleOptions 生命周期管理选项
type LifecycleOptions struct {
	CleanupInterval    time.Duration // 清理间隔
	MaxVersions        int           // 最大版本数
	ItemTimeout        time.Duration // 配置项超时时间
	EnableAutoCleanup  bool          // 是否启用自动清理
	EnableVersionLimit bool          // 是否启用版本限制
}

// DefaultLifecycleOptions 默认生命周期选项
func DefaultLifecycleOptions() *LifecycleOptions {
	return &LifecycleOptions{
		CleanupInterval:    constants.DefaultReferenceCounterCleanup,
		MaxVersions:        constants.DefaultMaxConfigItemVersions,
		ItemTimeout:        constants.DefaultConfigItemTimeout,
		EnableAutoCleanup:  true,
		EnableVersionLimit: true,
	}
}

// ConfigItemReference 配置项引用，用于追踪配置项的使用
type ConfigItemReference struct {
	Path        ConfigPath    // 配置项路径
	Version     int64         // 引用的版本
	AcquiredAt  time.Time     // 获取时间
	ReleasedAt  *time.Time    // 释放时间
	Owner       string        // 引用持有者
	Purpose     string        // 使用目的
	item        *ConfigItem   // 配置项引用
	released    int32         // 是否已释放（原子操作）
}

// NewLifecycleManager 创建新的生命周期管理器
func NewLifecycleManager(log logger.Logger, options *LifecycleOptions) *LifecycleManager {
	if log == nil {
		log = logger.GetLogger("lifecycle_manager")
	}
	
	if options == nil {
		options = DefaultLifecycleOptions()
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &LifecycleManager{
		configItems:    make(map[ConfigPath]*ConfigItem),
		refCounter:     NewReferenceCounter(),
		versionCounter: 0,
		logger:         log,
		ctx:            ctx,
		cancel:         cancel,
		running:        false,
		options:        options,
	}
}

// Start 启动生命周期管理器
func (lm *LifecycleManager) Start() error {
	lm.mu.Lock()
	defer lm.mu.Unlock()

	if lm.running {
		return nil
	}

	lm.logger.Info("启动配置项生命周期管理器")

	// 启动自动清理
	if lm.options.EnableAutoCleanup {
		lm.cleanupTicker = time.NewTicker(lm.options.CleanupInterval)
		go lm.cleanupRoutine()
	}

	lm.running = true
	lm.logger.Info("配置项生命周期管理器已启动")

	return nil
}

// Stop 停止生命周期管理器
func (lm *LifecycleManager) Stop() error {
	lm.mu.Lock()
	defer lm.mu.Unlock()

	if !lm.running {
		return nil
	}

	lm.logger.Info("停止配置项生命周期管理器")

	// 停止清理定时器
	if lm.cleanupTicker != nil {
		lm.cleanupTicker.Stop()
		lm.cleanupTicker = nil
	}

	// 取消上下文
	lm.cancel()

	// 清理所有配置项
	lm.cleanupAllItems()

	lm.running = false
	lm.logger.Info("配置项生命周期管理器已停止")

	return nil
}

// RegisterConfigItem 注册配置项
func (lm *LifecycleManager) RegisterConfigItem(item *ConfigItem) error {
	lm.mu.Lock()
	defer lm.mu.Unlock()

	if item == nil {
		return errors.NewError(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"配置项不能为空",
		)
	}

	if !item.Path.IsValid() {
		return errors.NewErrorWithDetails(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"配置项路径无效",
			fmt.Sprintf("路径: %s", item.Path),
		)
	}

	// 检查是否已存在
	if existingItem, exists := lm.configItems[item.Path]; exists {
		lm.logger.Warn(fmt.Sprintf("配置项已存在，将更新版本: %s", item.Path))
		existingItem.UpdateVersion()
		return nil
	}

	// 设置版本号
	item.Version = atomic.AddInt64(&lm.versionCounter, 1)
	item.State = LifecycleStateInactive

	// 注册配置项
	lm.configItems[item.Path] = item

	lm.logger.Debugf("注册配置项: %s, 版本: %d", item.Path, item.Version)
	return nil
}

// AcquireConfigItem 获取配置项引用
func (lm *LifecycleManager) AcquireConfigItem(path ConfigPath, owner, purpose string) (*ConfigItemReference, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	if !lm.running {
		return nil, errors.NewError(
			errors.ErrTypeSystem,
			errors.ErrCodeSystemNotRunning,
			"生命周期管理器未运行",
		)
	}

	// 查找配置项
	item, exists := lm.configItems[path]
	if !exists {
		return nil, errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigPathNotFound,
			"配置项不存在",
			fmt.Sprintf("路径: %s", path),
		)
	}

	// 检查配置项状态
	if item.State == LifecycleStateExpired {
		return nil, errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigItemExpired,
			"配置项已过期",
			fmt.Sprintf("路径: %s", path),
		)
	}

	// 增加引用计数
	refCount := item.AcquireReference()
	lm.refCounter.Acquire(path)

	// 创建引用对象
	reference := &ConfigItemReference{
		Path:       path,
		Version:    item.Version,
		AcquiredAt: time.Now(),
		Owner:      owner,
		Purpose:    purpose,
		item:       item,
		released:   0,
	}

	lm.logger.Debugf("获取配置项引用: %s, 引用计数: %d, 持有者: %s",
		path, refCount, owner)

	return reference, nil
}

// ReleaseConfigItem 释放配置项引用
func (lm *LifecycleManager) ReleaseConfigItem(reference *ConfigItemReference) error {
	if reference == nil {
		return errors.NewError(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"配置项引用不能为空",
		)
	}

	// 检查是否已释放
	if atomic.LoadInt32(&reference.released) == 1 {
		return errors.NewErrorWithDetails(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"配置项引用已释放",
			fmt.Sprintf("路径: %s", reference.Path),
		)
	}

	lm.mu.RLock()
	defer lm.mu.RUnlock()

	// 标记为已释放
	atomic.StoreInt32(&reference.released, 1)
	now := time.Now()
	reference.ReleasedAt = &now

	// 减少引用计数
	if reference.item != nil {
		refCount := reference.item.ReleaseReference()
		lm.refCounter.Release(reference.Path)

		lm.logger.Debugf("释放配置项引用: %s, 引用计数: %d, 持有者: %s",
			reference.Path, refCount, reference.Owner)
	}

	return nil
}

// GetConfigItem 获取配置项（不增加引用计数）
func (lm *LifecycleManager) GetConfigItem(path ConfigPath) (*ConfigItem, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	item, exists := lm.configItems[path]
	if !exists {
		return nil, errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigPathNotFound,
			"配置项不存在",
			fmt.Sprintf("路径: %s", path),
		)
	}

	return item, nil
}

// UpdateConfigItem 更新配置项
func (lm *LifecycleManager) UpdateConfigItem(path ConfigPath, newItem *ConfigItem) error {
	lm.mu.Lock()
	defer lm.mu.Unlock()

	existingItem, exists := lm.configItems[path]
	if !exists {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigPathNotFound,
			"配置项不存在",
			fmt.Sprintf("路径: %s", path),
		)
	}

	// 检查是否有活跃引用
	if existingItem.IsActive() {
		lm.logger.Infof("配置项有活跃引用，标记为待更新: %s", path)
		existingItem.State = LifecycleStatePending
		
		// 创建新版本的配置项
		newItem.Version = atomic.AddInt64(&lm.versionCounter, 1)
		newItem.State = LifecycleStateInactive
		
		// 暂时保留旧版本，等待引用释放
		// 这里可以实现版本管理逻辑
		return nil
	}

	// 更新配置项
	newItem.Version = atomic.AddInt64(&lm.versionCounter, 1)
	newItem.State = LifecycleStateActive
	lm.configItems[path] = newItem

	lm.logger.Infof("更新配置项: %s, 新版本: %d", path, newItem.Version)
	return nil
}

// GetActiveReferences 获取活跃引用统计
func (lm *LifecycleManager) GetActiveReferences() map[ConfigPath]int64 {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	return lm.refCounter.GetAllCounts()
}

// GetLifecycleStats 获取生命周期统计信息
func (lm *LifecycleManager) GetLifecycleStats() *LifecycleStats {
	lm.mu.RLock()
	defer lm.mu.RUnlock()

	stats := &LifecycleStats{
		TotalItems:    len(lm.configItems),
		ActiveItems:   0,
		PendingItems:  0,
		ExpiredItems:  0,
		TotalRefs:     0,
		CurrentVersion: atomic.LoadInt64(&lm.versionCounter),
	}

	// 统计各状态的配置项数量
	for _, item := range lm.configItems {
		switch item.State {
		case LifecycleStateActive:
			stats.ActiveItems++
		case LifecycleStatePending:
			stats.PendingItems++
		case LifecycleStateExpired:
			stats.ExpiredItems++
		}
		
		stats.TotalRefs += item.GetReferenceCount()
	}

	return stats
}

// LifecycleStats 生命周期统计信息
type LifecycleStats struct {
	TotalItems     int   `json:"total_items"`     // 总配置项数
	ActiveItems    int   `json:"active_items"`    // 活跃配置项数
	PendingItems   int   `json:"pending_items"`   // 待更新配置项数
	ExpiredItems   int   `json:"expired_items"`   // 过期配置项数
	TotalRefs      int64 `json:"total_refs"`      // 总引用数
	CurrentVersion int64 `json:"current_version"` // 当前版本号
}

// cleanupRoutine 清理协程
func (lm *LifecycleManager) cleanupRoutine() {
	lm.logger.Info("启动配置项清理协程")
	defer lm.logger.Info("配置项清理协程已停止")

	for {
		select {
		case <-lm.cleanupTicker.C:
			lm.performCleanup()
		case <-lm.ctx.Done():
			return
		}
	}
}

// performCleanup 执行清理操作
func (lm *LifecycleManager) performCleanup() {
	lm.mu.Lock()
	defer lm.mu.Unlock()

	cleanedCount := 0
	now := time.Now()

	for path, item := range lm.configItems {
		// 清理过期的配置项
		if item.State == LifecycleStateExpired && item.GetReferenceCount() == 0 {
			// 检查是否超过超时时间
			if now.Sub(item.AccessedAt) > lm.options.ItemTimeout {
				delete(lm.configItems, path)
				cleanedCount++
				lm.logger.Debugf("清理过期配置项: %s", path)
			}
		}
	}

	if cleanedCount > 0 {
		lm.logger.Infof("清理完成，清理了 %d 个过期配置项", cleanedCount)
	}
}

// cleanupAllItems 清理所有配置项
func (lm *LifecycleManager) cleanupAllItems() {
	for path := range lm.configItems {
		delete(lm.configItems, path)
	}
	lm.logger.Info("清理了所有配置项")
}

// IsReleased 检查引用是否已释放
func (ref *ConfigItemReference) IsReleased() bool {
	return atomic.LoadInt32(&ref.released) == 1
}

// GetUsageDuration 获取引用使用时长
func (ref *ConfigItemReference) GetUsageDuration() time.Duration {
	if ref.ReleasedAt != nil {
		return ref.ReleasedAt.Sub(ref.AcquiredAt)
	}
	return time.Since(ref.AcquiredAt)
}

// GetConfigValue 获取配置项的当前值
func (ref *ConfigItemReference) GetConfigValue() interface{} {
	if ref.item != nil && ref.item.ReflectValue.IsValid() {
		if ref.item.ReflectValue.CanInterface() {
			return ref.item.ReflectValue.Interface()
		}
	}
	return nil
}

// String 返回引用的字符串表示
func (ref *ConfigItemReference) String() string {
	status := "active"
	if ref.IsReleased() {
		status = "released"
	}
	
	return fmt.Sprintf("ConfigItemReference{Path: %s, Version: %d, Owner: %s, Status: %s, Duration: %v}",
		ref.Path, ref.Version, ref.Owner, status, ref.GetUsageDuration())
}
