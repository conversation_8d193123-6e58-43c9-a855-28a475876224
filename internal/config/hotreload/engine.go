// Package hotreload 热重载引擎主控制器实现
// 协调各个组件的工作，提供统一的热重载接口和流程控制
package hotreload

import (
	"context"
	"sync"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
)

// HotReloadEngine 热重载引擎主控制器
// 负责协调配置映射、差异检测、生命周期管理、并发控制和事件通知等组件
type HotReloadEngine struct {
	mapper            *ConfigMapper        // 配置映射器
	differ            *ConfigDiffer        // 差异检测器
	lifecycleManager  *LifecycleManager    // 生命周期管理器
	concurrentManager *ConcurrentManager   // 并发控制管理器
	eventBus          *EventBus            // 事件总线
	logger            logger.Logger        // 日志记录器
	options           *EngineOptions       // 引擎选项
	currentConfig     *config.Config       // 当前配置
	configVersion     int64                // 配置版本号
	mu                sync.RWMutex         // 并发控制锁
	running           bool                 // 是否正在运行
	ctx               context.Context      // 上下文
	cancel            context.CancelFunc   // 取消函数
	stats             *EngineStats         // 引擎统计信息
}

// EngineOptions 热重载引擎选项
type EngineOptions struct {
	EnableGranularReload bool          // 是否启用精细化重载
	EnableEventNotify    bool          // 是否启用事件通知
	EnableLifecycle      bool          // 是否启用生命周期管理
	EnableConcurrency    bool          // 是否启用并发控制
	ReloadTimeout        time.Duration // 重载超时时间
	MaxRetries           int           // 最大重试次数
	FallbackToFullReload bool          // 是否回退到全量重载
	ServiceName          string        // 服务名称
}

// DefaultEngineOptions 默认引擎选项
func DefaultEngineOptions() *EngineOptions {
	return &EngineOptions{
		EnableGranularReload: true,
		EnableEventNotify:    true,
		EnableLifecycle:      true,
		EnableConcurrency:    true,
		ReloadTimeout:        constants.DefaultConfigItemTimeout,
		MaxRetries:           3,
		FallbackToFullReload: true,
		ServiceName:          "flexproxy",
	}
}

// EngineStats 引擎统计信息
type EngineStats struct {
	TotalReloads      int64     `json:"total_reloads"`       // 总重载次数
	SuccessfulReloads int64     `json:"successful_reloads"`  // 成功重载次数
	FailedReloads     int64     `json:"failed_reloads"`      // 失败重载次数
	GranularReloads   int64     `json:"granular_reloads"`    // 精细化重载次数
	FullReloads       int64     `json:"full_reloads"`        // 全量重载次数
	LastReloadTime    time.Time `json:"last_reload_time"`    // 最后重载时间
	AverageReloadTime time.Duration `json:"average_reload_time"` // 平均重载时间
	ConfigVersion     int64     `json:"config_version"`      // 配置版本号
}

// ReloadResult 重载结果
type ReloadResult struct {
	Success       bool                 `json:"success"`        // 是否成功
	ReloadType    string               `json:"reload_type"`    // 重载类型（granular/full）
	Duration      time.Duration        `json:"duration"`       // 重载耗时
	ChangedPaths  []ConfigPath         `json:"changed_paths"`  // 变更的配置路径
	ChangeReport  *ChangeReport        `json:"change_report"`  // 变更报告
	Error         error                `json:"error,omitempty"` // 错误信息
	Timestamp     time.Time            `json:"timestamp"`      // 重载时间戳
}

// NewHotReloadEngine 创建新的热重载引擎
func NewHotReloadEngine(cfg *config.Config, log logger.Logger, options *EngineOptions) (*HotReloadEngine, error) {
	if cfg == nil {
		return nil, errors.NewError(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"配置对象不能为空",
		)
	}

	if log == nil {
		log = logger.GetLogger("hotreload_engine")
	}

	if options == nil {
		options = DefaultEngineOptions()
	}

	ctx, cancel := context.WithCancel(context.Background())

	// 创建配置映射器
	mapper := NewConfigMapper(cfg, logger.GetLogger("mapper"))
	if err := mapper.Initialize(); err != nil {
		cancel()
		return nil, errors.WrapError(
			err,
			errors.ErrTypeConfig,
			errors.ErrCodeConfigInitializationFailed,
			"配置映射器初始化失败",
		)
	}

	// 创建差异检测器
	differ := NewConfigDiffer(mapper, logger.GetLogger("differ"), nil)

	// 创建生命周期管理器
	lifecycleManager := NewLifecycleManager(logger.GetLogger("lifecycle"), nil)

	// 创建并发控制管理器
	concurrentManager := NewConcurrentManager(logger.GetLogger("concurrent"), nil)

	// 创建事件总线
	eventBus := NewEventBus(logger.GetLogger("events"), nil)

	engine := &HotReloadEngine{
		mapper:            mapper,
		differ:            differ,
		lifecycleManager:  lifecycleManager,
		concurrentManager: concurrentManager,
		eventBus:          eventBus,
		logger:            log,
		options:           options,
		currentConfig:     cfg,
		configVersion:     1,
		running:           false,
		ctx:               ctx,
		cancel:            cancel,
		stats: &EngineStats{
			ConfigVersion: 1,
		},
	}

	return engine, nil
}

// Start 启动热重载引擎
func (hre *HotReloadEngine) Start() error {
	hre.mu.Lock()
	defer hre.mu.Unlock()

	if hre.running {
		return nil
	}

	hre.logger.Info("启动配置热重载引擎")

	// 启动各个组件
	if hre.options.EnableLifecycle {
		if err := hre.lifecycleManager.Start(); err != nil {
			return errors.WrapError(err, errors.ErrTypeSystem, errors.ErrCodeSystemStartFailed, "生命周期管理器启动失败")
		}
	}

	if hre.options.EnableEventNotify {
		if err := hre.eventBus.Start(); err != nil {
			return errors.WrapError(err, errors.ErrTypeSystem, errors.ErrCodeSystemStartFailed, "事件总线启动失败")
		}
	}

	// 注册配置项到生命周期管理器
	if hre.options.EnableLifecycle {
		if err := hre.registerConfigItems(); err != nil {
			hre.logger.Warnf("注册配置项失败: %v", err)
		}
	}

	hre.running = true
	hre.logger.Info("配置热重载引擎已启动")

	return nil
}

// Stop 停止热重载引擎
func (hre *HotReloadEngine) Stop() error {
	hre.mu.Lock()
	defer hre.mu.Unlock()

	if !hre.running {
		return nil
	}

	hre.logger.Info("停止配置热重载引擎")

	// 停止各个组件
	if hre.options.EnableEventNotify {
		if err := hre.eventBus.Stop(); err != nil {
			hre.logger.Warnf("停止事件总线失败: %v", err)
		}
	}

	if hre.options.EnableLifecycle {
		if err := hre.lifecycleManager.Stop(); err != nil {
			hre.logger.Warnf("停止生命周期管理器失败: %v", err)
		}
	}

	// 取消上下文
	hre.cancel()

	hre.running = false
	hre.logger.Info("配置热重载引擎已停止")

	return nil
}

// ProcessConfigChange 处理配置变更
func (hre *HotReloadEngine) ProcessConfigChange(newConfig *config.Config) (*ReloadResult, error) {
	if newConfig == nil {
		return nil, errors.NewError(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"新配置对象不能为空",
		)
	}

	if !hre.running {
		return nil, errors.NewError(
			errors.ErrTypeSystem,
			errors.ErrCodeSystemNotRunning,
			"热重载引擎未运行",
		)
	}

	startTime := time.Now()
	hre.logger.Info("开始处理配置变更")

	// 更新统计信息
	hre.stats.TotalReloads++

	// 尝试精细化重载
	if hre.options.EnableGranularReload {
		result, err := hre.performGranularReload(newConfig)
		if err == nil {
			hre.stats.SuccessfulReloads++
			hre.stats.GranularReloads++
			hre.updateReloadStats(result, startTime)
			return result, nil
		}

		hre.logger.Warnf("精细化重载失败: %v", err)

		// 如果不允许回退到全量重载，直接返回错误
		if !hre.options.FallbackToFullReload {
			hre.stats.FailedReloads++
			return &ReloadResult{
				Success:   false,
				Error:     err,
				Timestamp: time.Now(),
				Duration:  time.Since(startTime),
			}, err
		}
	}

	// 回退到全量重载
	hre.logger.Info("回退到全量重载")
	result, err := hre.performFullReload(newConfig)
	
	if err != nil {
		hre.stats.FailedReloads++
	} else {
		hre.stats.SuccessfulReloads++
		hre.stats.FullReloads++
	}

	hre.updateReloadStats(result, startTime)
	return result, err
}

// performGranularReload 执行精细化重载
func (hre *HotReloadEngine) performGranularReload(newConfig *config.Config) (*ReloadResult, error) {
	hre.logger.Debug("执行精细化配置重载")

	// 检测配置差异
	changeReport, err := hre.differ.CompareConfigs(hre.currentConfig, newConfig)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeConfig, errors.ErrCodeConfigCompareFailed, "配置差异检测失败")
	}

	// 如果没有变更，直接返回成功
	if changeReport.ChangedPaths == 0 {
		hre.logger.Info("配置无变更，跳过重载")
		return &ReloadResult{
			Success:      true,
			ReloadType:   "granular",
			ChangedPaths: make([]ConfigPath, 0),
			ChangeReport: changeReport,
			Timestamp:    time.Now(),
		}, nil
	}

	hre.logger.Infof("检测到配置变更: %s", changeReport.GetChangesSummary())

	// 按优先级排序变更
	changeReport.SortChangesByPriority()

	// 收集需要锁定的配置路径
	changedPaths := make([]ConfigPath, len(changeReport.Changes))
	for i, change := range changeReport.Changes {
		changedPaths[i] = change.Path
	}

	// 原子性应用变更
	var applyErr error
	if hre.options.EnableConcurrency {
		applyErr = hre.concurrentManager.AtomicSwitch(changedPaths, func() error {
			return hre.applyConfigChanges(changeReport.Changes, newConfig)
		}, "hotreload_engine")
	} else {
		applyErr = hre.applyConfigChanges(changeReport.Changes, newConfig)
	}

	if applyErr != nil {
		return nil, errors.WrapError(applyErr, errors.ErrTypeConfig, errors.ErrCodeConfigApplyFailed, "应用配置变更失败")
	}

	// 发布事件通知
	if hre.options.EnableEventNotify {
		hre.publishChangeEvents(changeReport.Changes)
	}

	// 更新当前配置
	hre.currentConfig = newConfig
	hre.configVersion++
	hre.stats.ConfigVersion = hre.configVersion

	return &ReloadResult{
		Success:      true,
		ReloadType:   "granular",
		ChangedPaths: changedPaths,
		ChangeReport: changeReport,
		Timestamp:    time.Now(),
	}, nil
}

// performFullReload 执行全量重载
func (hre *HotReloadEngine) performFullReload(newConfig *config.Config) (*ReloadResult, error) {
	hre.logger.Debug("执行全量配置重载")

	// 刷新配置映射器
	if err := hre.mapper.Refresh(newConfig); err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeConfig, errors.ErrCodeConfigMappingFailed, "刷新配置映射失败")
	}

	// 更新当前配置
	hre.currentConfig = newConfig
	hre.configVersion++
	hre.stats.ConfigVersion = hre.configVersion

	// 重新注册配置项
	if hre.options.EnableLifecycle {
		if err := hre.registerConfigItems(); err != nil {
			hre.logger.Warnf("重新注册配置项失败: %v", err)
		}
	}

	// 发布全量重载事件
	if hre.options.EnableEventNotify {
		changeEvent := NewChangeEvent("*", ChangeTypeModify, nil, newConfig)
		hre.eventBus.PublishChange(changeEvent, hre.options.ServiceName, SeverityInfo)
	}

	return &ReloadResult{
		Success:    true,
		ReloadType: "full",
		Timestamp:  time.Now(),
	}, nil
}

// applyConfigChanges 应用配置变更
func (hre *HotReloadEngine) applyConfigChanges(changes []*ChangeEvent, newConfig *config.Config) error {
	for _, change := range changes {
		hre.logger.Debugf("应用配置变更: %s, 类型: %s", change.Path, change.Type.String())

		// 这里可以添加具体的配置应用逻辑
		// 例如：更新服务配置、重启组件等
		
		// 更新生命周期管理器中的配置项
		if hre.options.EnableLifecycle {
			if err := hre.updateConfigItemInLifecycle(change.Path, newConfig); err != nil {
				hre.logger.Warnf("更新配置项生命周期失败: %s, 错误: %v", change.Path, err)
			}
		}
	}

	return nil
}

// updateConfigItemInLifecycle 更新生命周期管理器中的配置项
func (hre *HotReloadEngine) updateConfigItemInLifecycle(path ConfigPath, newConfig *config.Config) error {
	// 获取字段信息
	fieldInfo, err := hre.mapper.GetFieldByPath(path)
	if err != nil {
		return err
	}

	// 创建新的配置项
	newItem := NewConfigItem(path, fieldInfo.Type, fieldInfo.Value)

	// 更新配置项
	return hre.lifecycleManager.UpdateConfigItem(path, newItem)
}

// publishChangeEvents 发布变更事件
func (hre *HotReloadEngine) publishChangeEvents(changes []*ChangeEvent) {
	for _, change := range changes {
		severity := SeverityInfo
		if change.IsSignificant() {
			severity = SeverityWarn
		}

		if err := hre.eventBus.PublishChange(change, hre.options.ServiceName, severity); err != nil {
			hre.logger.Warnf("发布变更事件失败: %s, 错误: %v", change.Path, err)
		}
	}
}

// registerConfigItems 注册配置项到生命周期管理器
func (hre *HotReloadEngine) registerConfigItems() error {
	allPaths := hre.mapper.GetAllPaths()
	
	for _, path := range allPaths {
		fieldInfo, err := hre.mapper.GetFieldByPath(path)
		if err != nil {
			hre.logger.Warnf("获取字段信息失败: %s, 错误: %v", path, err)
			continue
		}

		configItem := NewConfigItem(path, fieldInfo.Type, fieldInfo.Value)
		if err := hre.lifecycleManager.RegisterConfigItem(configItem); err != nil {
			hre.logger.Warnf("注册配置项失败: %s, 错误: %v", path, err)
		}
	}

	hre.logger.Infof("注册了 %d 个配置项到生命周期管理器", len(allPaths))
	return nil
}

// updateReloadStats 更新重载统计信息
func (hre *HotReloadEngine) updateReloadStats(result *ReloadResult, startTime time.Time) {
	if result != nil {
		result.Duration = time.Since(startTime)
		hre.stats.LastReloadTime = result.Timestamp
		
		// 更新平均重载时间
		if hre.stats.SuccessfulReloads > 0 {
			totalTime := hre.stats.AverageReloadTime * time.Duration(hre.stats.SuccessfulReloads-1)
			hre.stats.AverageReloadTime = (totalTime + result.Duration) / time.Duration(hre.stats.SuccessfulReloads)
		}
	}
}

// GetCurrentConfig 获取当前配置
func (hre *HotReloadEngine) GetCurrentConfig() *config.Config {
	hre.mu.RLock()
	defer hre.mu.RUnlock()
	return hre.currentConfig
}

// GetConfigVersion 获取配置版本号
func (hre *HotReloadEngine) GetConfigVersion() int64 {
	hre.mu.RLock()
	defer hre.mu.RUnlock()
	return hre.configVersion
}

// GetEngineStats 获取引擎统计信息
func (hre *HotReloadEngine) GetEngineStats() *EngineStats {
	hre.mu.RLock()
	defer hre.mu.RUnlock()
	
	// 返回统计信息的副本
	statsCopy := *hre.stats
	return &statsCopy
}

// IsRunning 检查引擎是否正在运行
func (hre *HotReloadEngine) IsRunning() bool {
	hre.mu.RLock()
	defer hre.mu.RUnlock()
	return hre.running
}

// SubscribeToChanges 订阅配置变更事件
func (hre *HotReloadEngine) SubscribeToChanges(subscriber *Subscriber) error {
	if !hre.options.EnableEventNotify {
		return errors.NewError(
			errors.ErrTypeSystem,
			errors.ErrCodeSystemFeatureDisabled,
			"事件通知功能未启用",
		)
	}

	return hre.eventBus.Subscribe(subscriber)
}

// UnsubscribeFromChanges 取消订阅配置变更事件
func (hre *HotReloadEngine) UnsubscribeFromChanges(subscriberID string) error {
	if !hre.options.EnableEventNotify {
		return errors.NewError(
			errors.ErrTypeSystem,
			errors.ErrCodeSystemFeatureDisabled,
			"事件通知功能未启用",
		)
	}

	return hre.eventBus.Unsubscribe(subscriberID)
}
