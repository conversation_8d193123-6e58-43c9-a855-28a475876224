// Package hotreload 并发安全机制实现
// 提供配置项级别的并发控制和原子性配置切换功能
package hotreload

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
)

// ConcurrentManager 并发控制管理器
// 负责管理配置项级别的并发访问控制和原子性操作
type ConcurrentManager struct {
	pathLocks     map[ConfigPath]*PathLock // 配置路径锁映射
	globalLock    sync.RWMutex             // 全局读写锁
	lockPool      sync.Pool                // 锁对象池
	logger        logger.Logger            // 日志记录器
	options       *ConcurrentOptions       // 并发选项
	activeReaders int64                    // 活跃读者数量
	activeWriters int64                    // 活跃写者数量
	lockTimeout   time.Duration            // 锁超时时间
}

// PathLock 配置路径锁，提供细粒度的并发控制
type PathLock struct {
	path         ConfigPath        // 配置路径
	rwMutex      sync.RWMutex      // 读写锁
	readerCount  int64             // 读者计数
	writerCount  int64             // 写者计数
	lastAccessed time.Time         // 最后访问时间
	created      time.Time         // 创建时间
	mu           sync.Mutex        // 内部锁
}

// ConcurrentOptions 并发控制选项
type ConcurrentOptions struct {
	MaxConcurrentReads  int           // 最大并发读取数
	MaxConcurrentWrites int           // 最大并发写入数
	LockTimeout         time.Duration // 锁超时时间
	EnableDeadlockDetection bool      // 是否启用死锁检测
	CleanupInterval     time.Duration // 锁清理间隔
}

// DefaultConcurrentOptions 默认并发控制选项
func DefaultConcurrentOptions() *ConcurrentOptions {
	return &ConcurrentOptions{
		MaxConcurrentReads:      constants.MaxConcurrentConfigChanges,
		MaxConcurrentWrites:     10,
		LockTimeout:             constants.DefaultConfigItemTimeout,
		EnableDeadlockDetection: true,
		CleanupInterval:         5 * time.Minute,
	}
}

// LockType 锁类型
type LockType int

const (
	LockTypeRead  LockType = iota // 读锁
	LockTypeWrite                 // 写锁
)

// String 返回锁类型的字符串表示
func (lt LockType) String() string {
	switch lt {
	case LockTypeRead:
		return "read"
	case LockTypeWrite:
		return "write"
	default:
		return "unknown"
	}
}

// LockContext 锁上下文，包含锁的详细信息
type LockContext struct {
	Path        ConfigPath    // 配置路径
	Type        LockType      // 锁类型
	AcquiredAt  time.Time     // 获取时间
	ReleasedAt  *time.Time    // 释放时间
	Owner       string        // 锁持有者
	Purpose     string        // 锁用途
	Timeout     time.Duration // 超时时间
	pathLock    *PathLock     // 路径锁引用
	released    int32         // 是否已释放（原子操作）
}

// NewConcurrentManager 创建新的并发控制管理器
func NewConcurrentManager(log logger.Logger, options *ConcurrentOptions) *ConcurrentManager {
	if log == nil {
		log = logger.GetLogger("concurrent_manager")
	}
	
	if options == nil {
		options = DefaultConcurrentOptions()
	}

	cm := &ConcurrentManager{
		pathLocks:   make(map[ConfigPath]*PathLock),
		logger:      log,
		options:     options,
		lockTimeout: options.LockTimeout,
	}

	// 初始化锁对象池
	cm.lockPool = sync.Pool{
		New: func() interface{} {
			return &PathLock{
				created: time.Now(),
			}
		},
	}

	return cm
}

// AcquireReadLock 获取读锁
func (cm *ConcurrentManager) AcquireReadLock(path ConfigPath, owner, purpose string) (*LockContext, error) {
	return cm.acquireLock(path, LockTypeRead, owner, purpose, cm.lockTimeout)
}

// AcquireWriteLock 获取写锁
func (cm *ConcurrentManager) AcquireWriteLock(path ConfigPath, owner, purpose string) (*LockContext, error) {
	return cm.acquireLock(path, LockTypeWrite, owner, purpose, cm.lockTimeout)
}

// AcquireReadLockWithTimeout 获取带超时的读锁
func (cm *ConcurrentManager) AcquireReadLockWithTimeout(path ConfigPath, owner, purpose string, timeout time.Duration) (*LockContext, error) {
	return cm.acquireLock(path, LockTypeRead, owner, purpose, timeout)
}

// AcquireWriteLockWithTimeout 获取带超时的写锁
func (cm *ConcurrentManager) AcquireWriteLockWithTimeout(path ConfigPath, owner, purpose string, timeout time.Duration) (*LockContext, error) {
	return cm.acquireLock(path, LockTypeWrite, owner, purpose, timeout)
}

// acquireLock 获取锁的内部实现
func (cm *ConcurrentManager) acquireLock(path ConfigPath, lockType LockType, owner, purpose string, timeout time.Duration) (*LockContext, error) {
	if !path.IsValid() {
		return nil, errors.NewErrorWithDetails(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"配置路径无效",
			fmt.Sprintf("路径: %s", path),
		)
	}

	// 检查并发限制
	if err := cm.checkConcurrencyLimits(lockType); err != nil {
		return nil, err
	}

	// 获取或创建路径锁
	pathLock := cm.getOrCreatePathLock(path)

	// 创建锁上下文
	lockCtx := &LockContext{
		Path:       path,
		Type:       lockType,
		AcquiredAt: time.Now(),
		Owner:      owner,
		Purpose:    purpose,
		Timeout:    timeout,
		pathLock:   pathLock,
		released:   0,
	}

	// 尝试获取锁
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	acquired := make(chan bool, 1)
	go func() {
		if lockType == LockTypeRead {
			pathLock.rwMutex.RLock()
			atomic.AddInt64(&pathLock.readerCount, 1)
			atomic.AddInt64(&cm.activeReaders, 1)
		} else {
			pathLock.rwMutex.Lock()
			atomic.AddInt64(&pathLock.writerCount, 1)
			atomic.AddInt64(&cm.activeWriters, 1)
		}
		
		pathLock.mu.Lock()
		pathLock.lastAccessed = time.Now()
		pathLock.mu.Unlock()
		
		acquired <- true
	}()

	select {
	case <-acquired:
		cm.logger.Debugf("获取锁成功: %s, 类型: %s, 持有者: %s",
			path, lockType.String(), owner)
		return lockCtx, nil
	case <-ctx.Done():
		return nil, errors.NewErrorWithDetails(
			errors.ErrTypeTimeout,
			errors.ErrCodeTimeoutExceeded,
			"获取锁超时",
			fmt.Sprintf("路径: %s, 类型: %s, 超时: %v", path, lockType.String(), timeout),
		)
	}
}

// ReleaseLock 释放锁
func (cm *ConcurrentManager) ReleaseLock(lockCtx *LockContext) error {
	if lockCtx == nil {
		return errors.NewError(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"锁上下文不能为空",
		)
	}

	// 检查是否已释放
	if atomic.LoadInt32(&lockCtx.released) == 1 {
		return errors.NewErrorWithDetails(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"锁已释放",
			fmt.Sprintf("路径: %s", lockCtx.Path),
		)
	}

	// 标记为已释放
	atomic.StoreInt32(&lockCtx.released, 1)
	now := time.Now()
	lockCtx.ReleasedAt = &now

	// 释放锁
	pathLock := lockCtx.pathLock
	if pathLock != nil {
		if lockCtx.Type == LockTypeRead {
			pathLock.rwMutex.RUnlock()
			atomic.AddInt64(&pathLock.readerCount, -1)
			atomic.AddInt64(&cm.activeReaders, -1)
		} else {
			pathLock.rwMutex.Unlock()
			atomic.AddInt64(&pathLock.writerCount, -1)
			atomic.AddInt64(&cm.activeWriters, -1)
		}

		pathLock.mu.Lock()
		pathLock.lastAccessed = time.Now()
		pathLock.mu.Unlock()
	}

	cm.logger.Debugf("释放锁成功: %s, 类型: %s, 持有者: %s, 持有时长: %v",
		lockCtx.Path, lockCtx.Type.String(), lockCtx.Owner, lockCtx.GetHoldDuration())

	return nil
}

// AtomicSwitch 原子性配置切换
// 确保配置切换过程中的一致性和原子性
func (cm *ConcurrentManager) AtomicSwitch(paths []ConfigPath, switchFunc func() error, owner string) error {
	if len(paths) == 0 {
		return errors.NewError(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"配置路径列表不能为空",
		)
	}

	cm.logger.Infof("开始原子性配置切换，涉及 %d 个配置项", len(paths))

	// 按路径排序，避免死锁
	sortedPaths := make([]ConfigPath, len(paths))
	copy(sortedPaths, paths)
	cm.sortPaths(sortedPaths)

	// 获取所有写锁
	lockContexts := make([]*LockContext, 0, len(sortedPaths))
	
	// 逐个获取锁
	for _, path := range sortedPaths {
		lockCtx, err := cm.AcquireWriteLock(path, owner, "原子性配置切换")
		if err != nil {
			// 释放已获取的锁
			cm.releaseAllLocks(lockContexts)
			return errors.WrapErrorWithDetails(
				err,
				errors.ErrTypeConcurrency,
				errors.ErrCodeConcurrencyLockFailed,
				"获取写锁失败，原子性切换中止",
				fmt.Sprintf("失败路径: %s", path),
			)
		}
		lockContexts = append(lockContexts, lockCtx)
	}

	// 执行切换函数
	switchErr := switchFunc()

	// 释放所有锁
	releaseErr := cm.releaseAllLocks(lockContexts)

	// 处理错误
	if switchErr != nil {
		cm.logger.Errorf("原子性配置切换失败: %v", switchErr)
		return errors.WrapError(
			switchErr,
			errors.ErrTypeConfig,
			errors.ErrCodeConfigSwitchFailed,
			"原子性配置切换失败",
		)
	}

	if releaseErr != nil {
		cm.logger.Warnf("释放锁时出现错误: %v", releaseErr)
	}

	cm.logger.Info("原子性配置切换完成")
	return nil
}

// getOrCreatePathLock 获取或创建路径锁
func (cm *ConcurrentManager) getOrCreatePathLock(path ConfigPath) *PathLock {
	cm.globalLock.Lock()
	defer cm.globalLock.Unlock()

	if pathLock, exists := cm.pathLocks[path]; exists {
		return pathLock
	}

	// 从对象池获取锁对象
	pathLock := cm.lockPool.Get().(*PathLock)
	pathLock.path = path
	pathLock.readerCount = 0
	pathLock.writerCount = 0
	pathLock.lastAccessed = time.Now()
	pathLock.created = time.Now()

	cm.pathLocks[path] = pathLock
	return pathLock
}

// checkConcurrencyLimits 检查并发限制
func (cm *ConcurrentManager) checkConcurrencyLimits(lockType LockType) error {
	if lockType == LockTypeRead {
		if atomic.LoadInt64(&cm.activeReaders) >= int64(cm.options.MaxConcurrentReads) {
			return errors.NewErrorWithDetails(
				errors.ErrTypeConcurrency,
				errors.ErrCodeConcurrencyLimitExceeded,
				"并发读取数量超过限制",
				fmt.Sprintf("当前: %d, 限制: %d", 
					atomic.LoadInt64(&cm.activeReaders), cm.options.MaxConcurrentReads),
			)
		}
	} else {
		if atomic.LoadInt64(&cm.activeWriters) >= int64(cm.options.MaxConcurrentWrites) {
			return errors.NewErrorWithDetails(
				errors.ErrTypeConcurrency,
				errors.ErrCodeConcurrencyLimitExceeded,
				"并发写入数量超过限制",
				fmt.Sprintf("当前: %d, 限制: %d", 
					atomic.LoadInt64(&cm.activeWriters), cm.options.MaxConcurrentWrites),
			)
		}
	}
	return nil
}

// releaseAllLocks 释放所有锁
func (cm *ConcurrentManager) releaseAllLocks(lockContexts []*LockContext) error {
	var lastErr error
	
	// 逆序释放锁
	for i := len(lockContexts) - 1; i >= 0; i-- {
		if err := cm.ReleaseLock(lockContexts[i]); err != nil {
			cm.logger.Warnf("释放锁失败: %s, 错误: %v", lockContexts[i].Path, err)
			lastErr = err
		}
	}
	
	return lastErr
}

// sortPaths 对路径进行排序，避免死锁
func (cm *ConcurrentManager) sortPaths(paths []ConfigPath) {
	// 简单的字符串排序
	for i := 0; i < len(paths)-1; i++ {
		for j := i + 1; j < len(paths); j++ {
			if paths[i] > paths[j] {
				paths[i], paths[j] = paths[j], paths[i]
			}
		}
	}
}

// GetConcurrencyStats 获取并发统计信息
func (cm *ConcurrentManager) GetConcurrencyStats() *ConcurrencyStats {
	cm.globalLock.RLock()
	defer cm.globalLock.RUnlock()

	stats := &ConcurrencyStats{
		ActiveReaders:  atomic.LoadInt64(&cm.activeReaders),
		ActiveWriters:  atomic.LoadInt64(&cm.activeWriters),
		TotalPathLocks: len(cm.pathLocks),
		PathLockStats:  make(map[string]*PathLockStats),
	}

	// 统计每个路径锁的信息
	for path, pathLock := range cm.pathLocks {
		pathLock.mu.Lock()
		stats.PathLockStats[path.String()] = &PathLockStats{
			ReaderCount:  atomic.LoadInt64(&pathLock.readerCount),
			WriterCount:  atomic.LoadInt64(&pathLock.writerCount),
			LastAccessed: pathLock.lastAccessed,
			Created:      pathLock.created,
		}
		pathLock.mu.Unlock()
	}

	return stats
}

// ConcurrencyStats 并发统计信息
type ConcurrencyStats struct {
	ActiveReaders  int64                       `json:"active_readers"`   // 活跃读者数
	ActiveWriters  int64                       `json:"active_writers"`   // 活跃写者数
	TotalPathLocks int                         `json:"total_path_locks"` // 总路径锁数
	PathLockStats  map[string]*PathLockStats   `json:"path_lock_stats"`  // 路径锁统计
}

// PathLockStats 路径锁统计信息
type PathLockStats struct {
	ReaderCount  int64     `json:"reader_count"`  // 读者计数
	WriterCount  int64     `json:"writer_count"`  // 写者计数
	LastAccessed time.Time `json:"last_accessed"` // 最后访问时间
	Created      time.Time `json:"created"`       // 创建时间
}

// IsReleased 检查锁是否已释放
func (lc *LockContext) IsReleased() bool {
	return atomic.LoadInt32(&lc.released) == 1
}

// GetHoldDuration 获取锁持有时长
func (lc *LockContext) GetHoldDuration() time.Duration {
	if lc.ReleasedAt != nil {
		return lc.ReleasedAt.Sub(lc.AcquiredAt)
	}
	return time.Since(lc.AcquiredAt)
}

// IsExpired 检查锁是否已过期
func (lc *LockContext) IsExpired() bool {
	return time.Since(lc.AcquiredAt) > lc.Timeout
}

// String 返回锁上下文的字符串表示
func (lc *LockContext) String() string {
	status := "active"
	if lc.IsReleased() {
		status = "released"
	} else if lc.IsExpired() {
		status = "expired"
	}
	
	return fmt.Sprintf("LockContext{Path: %s, Type: %s, Owner: %s, Status: %s, Duration: %v}",
		lc.Path, lc.Type.String(), lc.Owner, status, lc.GetHoldDuration())
}

// CleanupExpiredLocks 清理过期的路径锁
func (cm *ConcurrentManager) CleanupExpiredLocks() int {
	cm.globalLock.Lock()
	defer cm.globalLock.Unlock()

	cleanedCount := 0
	now := time.Now()
	
	for path, pathLock := range cm.pathLocks {
		pathLock.mu.Lock()
		
		// 检查是否无活跃引用且长时间未访问
		if atomic.LoadInt64(&pathLock.readerCount) == 0 && 
		   atomic.LoadInt64(&pathLock.writerCount) == 0 &&
		   now.Sub(pathLock.lastAccessed) > cm.options.CleanupInterval {
			
			delete(cm.pathLocks, path)
			pathLock.mu.Unlock()
			
			// 归还到对象池
			cm.lockPool.Put(pathLock)
			cleanedCount++
			
			cm.logger.Debugf("清理过期路径锁: %s", path)
		} else {
			pathLock.mu.Unlock()
		}
	}
	
	if cleanedCount > 0 {
		cm.logger.Infof("清理了 %d 个过期路径锁", cleanedCount)
	}
	
	return cleanedCount
}
