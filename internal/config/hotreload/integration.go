// Package hotreload 热重载集成适配器
// 连接新的热重载引擎与现有配置管理系统，提供平滑的集成接口
package hotreload

import (
	"context"
	"fmt"
	"sync"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
	"flexproxy/internal/interfaces"
	"github.com/fsnotify/fsnotify"
)

// HotReloadIntegration 热重载集成适配器
// 作为新热重载引擎与现有配置管理系统之间的桥梁
type HotReloadIntegration struct {
	engine           *HotReloadEngine           // 新的热重载引擎
	configManager    *config.ConfigManager      // 现有配置管理器
	configService    interfaces.ConfigService   // 现有配置服务
	fileWatcher      *fsnotify.Watcher          // 文件监控器
	logger           logger.Logger              // 日志记录器
	
	// 配置文件监控
	configFile       string                     // 配置文件路径
	watchEnabled     bool                       // 是否启用文件监控
	
	// 回调管理
	callbacks        []func(*config.Config) error // 配置变更回调列表
	callbackMu       sync.RWMutex               // 回调列表锁
	
	// 运行状态
	running          bool                       // 是否正在运行
	ctx              context.Context            // 上下文
	cancel           context.CancelFunc         // 取消函数
	mu               sync.RWMutex               // 状态锁
	
	// 集成选项
	options          *IntegrationOptions        // 集成选项
}

// IntegrationOptions 集成选项
type IntegrationOptions struct {
	EnableFileWatch     bool          `json:"enable_file_watch"`     // 是否启用文件监控
	EnableEngineReload  bool          `json:"enable_engine_reload"`  // 是否启用引擎热重载
	EnableCallbackChain bool          `json:"enable_callback_chain"` // 是否启用回调链
	WatchDebounceDelay  time.Duration `json:"watch_debounce_delay"`  // 文件监控防抖延迟
	MaxRetryAttempts    int           `json:"max_retry_attempts"`    // 最大重试次数
	RetryDelay          time.Duration `json:"retry_delay"`           // 重试延迟
}

// DefaultIntegrationOptions 默认集成选项
func DefaultIntegrationOptions() *IntegrationOptions {
	return &IntegrationOptions{
		EnableFileWatch:     true,
		EnableEngineReload:  true,
		EnableCallbackChain: true,
		WatchDebounceDelay:  constants.DefaultDebounceDelay,
		MaxRetryAttempts:    constants.DefaultMaxReloadRetries,
		RetryDelay:          constants.DefaultActionRetryDelay,
	}
}

// NewHotReloadIntegration 创建新的热重载集成适配器
func NewHotReloadIntegration(
	cfg *config.Config,
	configManager *config.ConfigManager,
	configService interfaces.ConfigService,
	log logger.Logger,
	options *IntegrationOptions,
) (*HotReloadIntegration, error) {
	if cfg == nil {
		return nil, errors.NewError(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"配置对象不能为空",
		)
	}

	if log == nil {
		log = logger.GetLogger("hotreload_integration")
	}

	if options == nil {
		options = DefaultIntegrationOptions()
	}

	// 创建热重载引擎
	engineOptions := DefaultEngineOptions()
	engineOptions.EnableGranularReload = true
	engineOptions.EnableEventNotify = true
	engineOptions.EnableLifecycle = true
	engineOptions.EnableConcurrency = true
	engineOptions.ServiceName = "integration_adapter"

	engine, err := NewHotReloadEngine(cfg, log, engineOptions)
	if err != nil {
		return nil, errors.WrapError(
			err,
			errors.ErrTypeConfig,
			errors.ErrCodeConfigInitializationFailed,
			"热重载引擎初始化失败",
		)
	}

	ctx, cancel := context.WithCancel(context.Background())

	integration := &HotReloadIntegration{
		engine:        engine,
		configManager: configManager,
		configService: configService,
		logger:        log,
		callbacks:     make([]func(*config.Config) error, 0),
		running:       false,
		ctx:           ctx,
		cancel:        cancel,
		options:       options,
	}

	log.Info("热重载集成适配器已创建")
	return integration, nil
}

// Start 启动热重载集成适配器
func (hri *HotReloadIntegration) Start() error {
	hri.mu.Lock()
	defer hri.mu.Unlock()

	if hri.running {
		return nil
	}

	hri.logger.Info("启动热重载集成适配器")

	// 启动热重载引擎
	if hri.options.EnableEngineReload {
		if err := hri.engine.Start(); err != nil {
			return errors.WrapError(
				err,
				errors.ErrTypeSystem,
				errors.ErrCodeSystemStartFailed,
				"热重载引擎启动失败",
			)
		}
	}

	// 启动文件监控
	if hri.options.EnableFileWatch && hri.configFile != "" {
		if err := hri.startFileWatch(); err != nil {
			hri.logger.Warnf("文件监控启动失败: %v", err)
		}
	}

	// 注册引擎事件订阅者
	if hri.options.EnableCallbackChain {
		hri.registerEngineSubscriber()
	}

	hri.running = true
	hri.logger.Info("热重载集成适配器已启动")

	return nil
}

// Stop 停止热重载集成适配器
func (hri *HotReloadIntegration) Stop() error {
	hri.mu.Lock()
	defer hri.mu.Unlock()

	if !hri.running {
		return nil
	}

	hri.logger.Info("停止热重载集成适配器")

	// 停止文件监控
	if hri.fileWatcher != nil {
		hri.fileWatcher.Close()
		hri.fileWatcher = nil
	}

	// 停止热重载引擎
	if err := hri.engine.Stop(); err != nil {
		hri.logger.Warnf("热重载引擎停止失败: %v", err)
	}

	// 取消上下文
	hri.cancel()

	hri.running = false
	hri.logger.Info("热重载集成适配器已停止")

	return nil
}

// SetConfigFile 设置配置文件路径
func (hri *HotReloadIntegration) SetConfigFile(configFile string) {
	hri.mu.Lock()
	defer hri.mu.Unlock()
	
	hri.configFile = configFile
	hri.logger.Infof("设置配置文件路径: %s", configFile)
}

// RegisterCallback 注册配置变更回调（兼容现有接口）
func (hri *HotReloadIntegration) RegisterCallback(callback func(interface{})) {
	hri.callbackMu.Lock()
	defer hri.callbackMu.Unlock()

	// 包装回调函数以适配类型
	wrappedCallback := func(cfg *config.Config) error {
		callback(cfg)
		return nil
	}

	hri.callbacks = append(hri.callbacks, wrappedCallback)
	hri.logger.Debugf("注册配置变更回调，当前回调数量: %d", len(hri.callbacks))
}

// RegisterReloadCallback 注册重载回调（兼容ConfigManager接口）
func (hri *HotReloadIntegration) RegisterReloadCallback(callback func(*config.Config) error) {
	hri.callbackMu.Lock()
	defer hri.callbackMu.Unlock()

	hri.callbacks = append(hri.callbacks, callback)
	hri.logger.Debugf("注册重载回调，当前回调数量: %d", len(hri.callbacks))
}

// ReloadConfig 重载配置（兼容现有接口）
func (hri *HotReloadIntegration) ReloadConfig(configPath string) error {
	hri.logger.Infof("开始重载配置: %s", configPath)

	// 加载新配置（跳过验证以简化测试）
	newConfig, err := config.LoadConfigFromYAMLWithoutValidation(configPath)
	if err != nil {
		return errors.WrapError(
			err,
			errors.ErrTypeConfig,
			errors.ErrCodeConfigLoadFailed,
			"配置文件加载失败",
		)
	}

	// 使用热重载引擎处理配置变更
	if hri.options.EnableEngineReload && hri.running {
		result, err := hri.engine.ProcessConfigChange(newConfig)
		if err != nil {
			return errors.WrapError(
				err,
				errors.ErrTypeConfig,
				errors.ErrCodeConfigReloadFailed,
				"热重载引擎配置重载失败",
			)
		}

		hri.logger.Infof("热重载完成: 成功=%v, 持续时间=%v, 变更路径数量=%d",
			result.Success, result.Duration, len(result.ChangedPaths))
	}

	// 执行回调链
	if hri.options.EnableCallbackChain {
		if err := hri.executeCallbacks(newConfig); err != nil {
			return errors.WrapError(
				err,
				errors.ErrTypeConfig,
				errors.ErrCodeConfigCallbackFailed,
				"配置变更回调执行失败",
			)
		}
	}

	return nil
}

// GetEngine 获取热重载引擎（用于高级操作）
func (hri *HotReloadIntegration) GetEngine() *HotReloadEngine {
	return hri.engine
}

// GetStats 获取集成适配器统计信息
func (hri *HotReloadIntegration) GetStats() map[string]interface{} {
	hri.mu.RLock()
	defer hri.mu.RUnlock()

	stats := map[string]interface{}{
		"running":         hri.running,
		"config_file":     hri.configFile,
		"watch_enabled":   hri.watchEnabled,
		"callback_count":  len(hri.callbacks),
		"engine_stats":    hri.engine.stats,
	}

	return stats
}

// startFileWatch 启动文件监控
func (hri *HotReloadIntegration) startFileWatch() error {
	if hri.configFile == "" {
		return errors.NewError(
			errors.ErrTypeValidation,
			errors.ErrCodeValidationFailed,
			"配置文件路径为空",
		)
	}

	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return errors.WrapError(
			err,
			errors.ErrTypeSystem,
			errors.ErrCodeInitializationFailed,
			"创建文件监控器失败",
		)
	}

	if err := watcher.Add(hri.configFile); err != nil {
		watcher.Close()
		return errors.WrapError(
			err,
			errors.ErrTypeFile,
			errors.ErrCodeFileOperationFailed,
			"添加文件监控失败",
		)
	}

	hri.fileWatcher = watcher
	hri.watchEnabled = true

	// 启动文件监控协程
	go hri.watchFileChanges()

	hri.logger.Infof("文件监控已启动: %s", hri.configFile)
	return nil
}

// watchFileChanges 监控文件变化
func (hri *HotReloadIntegration) watchFileChanges() {
	defer func() {
		if r := recover(); r != nil {
			hri.logger.Errorf("文件监控协程异常退出: %v", r)
		}
	}()

	for {
		select {
		case event, ok := <-hri.fileWatcher.Events:
			if !ok {
				hri.logger.Info("文件监控器已关闭")
				return
			}

			if event.Op&fsnotify.Write == fsnotify.Write {
				hri.logger.Infof("检测到配置文件变化: %s", event.Name)

				// 防抖处理
				time.Sleep(hri.options.WatchDebounceDelay)

				// 重载配置
				if err := hri.handleFileChange(event.Name); err != nil {
					hri.logger.Errorf("处理文件变化失败: %v", err)
				}
			}

		case err, ok := <-hri.fileWatcher.Errors:
			if !ok {
				hri.logger.Info("文件监控错误通道已关闭")
				return
			}
			hri.logger.Errorf("文件监控错误: %v", err)

		case <-hri.ctx.Done():
			hri.logger.Info("文件监控协程收到停止信号")
			return
		}
	}
}

// handleFileChange 处理文件变化
func (hri *HotReloadIntegration) handleFileChange(filePath string) error {
	var lastErr error

	for attempt := 1; attempt <= hri.options.MaxRetryAttempts; attempt++ {
		if err := hri.ReloadConfig(filePath); err != nil {
			lastErr = err
			hri.logger.Warnf("配置重载失败 (尝试 %d/%d): %v",
				attempt, hri.options.MaxRetryAttempts, err)

			if attempt < hri.options.MaxRetryAttempts {
				time.Sleep(hri.options.RetryDelay)
			}
		} else {
			hri.logger.Infof("配置重载成功 (尝试 %d/%d)", attempt, hri.options.MaxRetryAttempts)
			return nil
		}
	}

	return errors.WrapError(
		lastErr,
		errors.ErrTypeConfig,
		errors.ErrCodeConfigReloadFailed,
		fmt.Sprintf("配置重载失败，已重试 %d 次", hri.options.MaxRetryAttempts),
	)
}

// registerEngineSubscriber 注册引擎事件订阅者
func (hri *HotReloadIntegration) registerEngineSubscriber() {
	if hri.engine.eventBus == nil {
		hri.logger.Warn("事件总线未启用，跳过订阅者注册")
		return
	}

	// 订阅配置变更事件
	subscriber := &Subscriber{
		ID:       "integration_adapter",
		Name:     "热重载集成适配器",
		Priority: 100, // 高优先级
		Callback: hri.handleEngineEvent,
		Filter: func(event *ConfigChangeEvent) bool {
			// 接收所有配置变更事件
			return true
		},
	}

	hri.engine.eventBus.Subscribe(subscriber)
	// 注意：EventBus的Subscribe方法只接受一个Subscriber参数
	// 事件类型过滤通过Subscriber的Filter函数实现

	hri.logger.Info("已注册引擎事件订阅者")
}

// handleEngineEvent 处理引擎事件
func (hri *HotReloadIntegration) handleEngineEvent(event *ConfigChangeEvent) error {
	hri.logger.Debugf("收到引擎事件: %s, 路径: %s", event.Type, event.Path)

	// 如果是全量重载事件，执行回调链
	if event.Path.String() == "*" {
		if newConfig, ok := event.NewValue.(*config.Config); ok {
			return hri.executeCallbacks(newConfig)
		}
	}

	return nil
}

// executeCallbacks 执行回调链
func (hri *HotReloadIntegration) executeCallbacks(cfg *config.Config) error {
	hri.callbackMu.RLock()
	callbacks := make([]func(*config.Config) error, len(hri.callbacks))
	copy(callbacks, hri.callbacks)
	hri.callbackMu.RUnlock()

	for i, callback := range callbacks {
		if err := callback(cfg); err != nil {
			return errors.WrapError(
				err,
				errors.ErrTypeConfig,
				errors.ErrCodeConfigCallbackFailed,
				fmt.Sprintf("回调 %d 执行失败", i),
			)
		}
	}

	hri.logger.Debugf("配置变更回调链执行完成，回调数量: %d", len(callbacks))
	return nil
}

// IsRunning 检查是否正在运行
func (hri *HotReloadIntegration) IsRunning() bool {
	hri.mu.RLock()
	defer hri.mu.RUnlock()
	return hri.running
}

// GetConfigFile 获取配置文件路径
func (hri *HotReloadIntegration) GetConfigFile() string {
	hri.mu.RLock()
	defer hri.mu.RUnlock()
	return hri.configFile
}
