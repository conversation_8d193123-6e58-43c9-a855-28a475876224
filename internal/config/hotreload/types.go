// Package hotreload 提供配置项级别的精细化热重载功能
// 本包实现了基于Go反射机制的配置项路径映射、差异检测、生命周期管理和并发安全的配置切换机制
package hotreload

import (
	"reflect"
	"sync"
	"sync/atomic"
	"time"

	"flexproxy/common/constants"
)

// ConfigPath 配置项路径类型，用于唯一标识配置项
// 格式：server.host, proxy.strategy, cache.redis.host 等
type ConfigPath string

// String 返回配置路径的字符串表示
func (cp ConfigPath) String() string {
	return string(cp)
}

// IsValid 检查配置路径是否有效
func (cp ConfigPath) IsValid() bool {
	return len(cp) > 0 && cp != constants.ConfigPathSeparator
}

// Parent 获取父级配置路径
func (cp ConfigPath) Parent() ConfigPath {
	path := string(cp)
	for i := len(path) - 1; i >= 0; i-- {
		if path[i] == constants.ConfigPathSeparatorChar {
			return ConfigPath(path[:i])
		}
	}
	return ""
}

// ConfigItemType 配置项类型枚举
type ConfigItemType int

const (
	ConfigItemTypeUnknown ConfigItemType = iota
	ConfigItemTypeString                 // 字符串类型
	ConfigItemTypeInt                    // 整数类型
	ConfigItemTypeBool                   // 布尔类型
	ConfigItemTypeFloat                  // 浮点数类型
	ConfigItemTypeDuration               // 时间间隔类型
	ConfigItemTypeSlice                  // 切片类型
	ConfigItemTypeMap                    // 映射类型
	ConfigItemTypeStruct                 // 结构体类型
	ConfigItemTypePointer                // 指针类型
)

// String 返回配置项类型的字符串表示
func (cit ConfigItemType) String() string {
	switch cit {
	case ConfigItemTypeString:
		return "string"
	case ConfigItemTypeInt:
		return "int"
	case ConfigItemTypeBool:
		return "bool"
	case ConfigItemTypeFloat:
		return "float"
	case ConfigItemTypeDuration:
		return "duration"
	case ConfigItemTypeSlice:
		return "slice"
	case ConfigItemTypeMap:
		return "map"
	case ConfigItemTypeStruct:
		return "struct"
	case ConfigItemTypePointer:
		return "pointer"
	default:
		return "unknown"
	}
}

// LifecycleState 配置项生命周期状态
type LifecycleState int

const (
	LifecycleStateInactive LifecycleState = iota // 未激活状态
	LifecycleStateActive                         // 激活状态（正在使用）
	LifecycleStatePending                        // 待更新状态
	LifecycleStateExpired                        // 过期状态（待清理）
)

// String 返回生命周期状态的字符串表示
func (ls LifecycleState) String() string {
	switch ls {
	case LifecycleStateInactive:
		return "inactive"
	case LifecycleStateActive:
		return "active"
	case LifecycleStatePending:
		return "pending"
	case LifecycleStateExpired:
		return "expired"
	default:
		return "unknown"
	}
}

// ConfigItem 配置项元数据，包含配置项的完整信息
type ConfigItem struct {
	Path         ConfigPath        `json:"path"`          // 配置项路径
	Type         ConfigItemType    `json:"type"`          // 配置项类型
	ReflectType  reflect.Type      `json:"-"`             // 反射类型信息
	ReflectValue reflect.Value     `json:"-"`             // 反射值信息
	Version      int64             `json:"version"`       // 配置项版本号
	State        LifecycleState    `json:"state"`         // 生命周期状态
	RefCount     int64             `json:"ref_count"`     // 引用计数
	CreatedAt    time.Time         `json:"created_at"`    // 创建时间
	UpdatedAt    time.Time         `json:"updated_at"`    // 最后更新时间
	AccessedAt   time.Time         `json:"accessed_at"`   // 最后访问时间
	Tags         map[string]string `json:"tags"`          // 标签信息
	mu           sync.RWMutex      `json:"-"`             // 并发控制锁
}

// NewConfigItem 创建新的配置项
func NewConfigItem(path ConfigPath, reflectType reflect.Type, reflectValue reflect.Value) *ConfigItem {
	now := time.Now()
	return &ConfigItem{
		Path:         path,
		Type:         getConfigItemType(reflectType),
		ReflectType:  reflectType,
		ReflectValue: reflectValue,
		Version:      1,
		State:        LifecycleStateInactive,
		RefCount:     0,
		CreatedAt:    now,
		UpdatedAt:    now,
		AccessedAt:   now,
		Tags:         make(map[string]string),
	}
}

// AcquireReference 获取配置项引用，原子性增加引用计数
func (ci *ConfigItem) AcquireReference() int64 {
	ci.mu.Lock()
	defer ci.mu.Unlock()
	
	count := atomic.AddInt64(&ci.RefCount, 1)
	ci.AccessedAt = time.Now()
	if ci.State == LifecycleStateInactive {
		ci.State = LifecycleStateActive
	}
	return count
}

// ReleaseReference 释放配置项引用，原子性减少引用计数
func (ci *ConfigItem) ReleaseReference() int64 {
	ci.mu.Lock()
	defer ci.mu.Unlock()
	
	count := atomic.AddInt64(&ci.RefCount, -1)
	if count <= 0 {
		ci.State = LifecycleStateExpired
	}
	return count
}

// GetReferenceCount 获取当前引用计数
func (ci *ConfigItem) GetReferenceCount() int64 {
	return atomic.LoadInt64(&ci.RefCount)
}

// IsActive 检查配置项是否处于活跃状态
func (ci *ConfigItem) IsActive() bool {
	ci.mu.RLock()
	defer ci.mu.RUnlock()
	return ci.State == LifecycleStateActive && ci.GetReferenceCount() > 0
}

// UpdateVersion 更新配置项版本
func (ci *ConfigItem) UpdateVersion() {
	ci.mu.Lock()
	defer ci.mu.Unlock()
	
	ci.Version++
	ci.UpdatedAt = time.Now()
	ci.State = LifecycleStatePending
}

// SetTag 设置标签
func (ci *ConfigItem) SetTag(key, value string) {
	ci.mu.Lock()
	defer ci.mu.Unlock()
	ci.Tags[key] = value
}

// GetTag 获取标签
func (ci *ConfigItem) GetTag(key string) (string, bool) {
	ci.mu.RLock()
	defer ci.mu.RUnlock()
	value, exists := ci.Tags[key]
	return value, exists
}

// ChangeType 配置变更类型
type ChangeType int

const (
	ChangeTypeUnknown ChangeType = iota
	ChangeTypeAdd                // 新增配置项
	ChangeTypeModify             // 修改配置项
	ChangeTypeDelete             // 删除配置项
)

// String 返回变更类型的字符串表示
func (ct ChangeType) String() string {
	switch ct {
	case ChangeTypeAdd:
		return "add"
	case ChangeTypeModify:
		return "modify"
	case ChangeTypeDelete:
		return "delete"
	default:
		return "unknown"
	}
}

// ChangeEvent 配置变更事件，记录配置项的具体变更信息
type ChangeEvent struct {
	Path      ConfigPath    `json:"path"`       // 变更的配置项路径
	Type      ChangeType    `json:"type"`       // 变更类型
	OldValue  interface{}   `json:"old_value"`  // 旧值
	NewValue  interface{}   `json:"new_value"`  // 新值
	Timestamp time.Time     `json:"timestamp"`  // 变更时间戳
	Version   int64         `json:"version"`    // 变更后的版本号
	Reason    string        `json:"reason"`     // 变更原因
	Source    string        `json:"source"`     // 变更来源
}

// NewChangeEvent 创建新的配置变更事件
func NewChangeEvent(path ConfigPath, changeType ChangeType, oldValue, newValue interface{}) *ChangeEvent {
	return &ChangeEvent{
		Path:      path,
		Type:      changeType,
		OldValue:  oldValue,
		NewValue:  newValue,
		Timestamp: time.Now(),
		Version:   1,
		Reason:    "热重载配置变更",
		Source:    "hotreload_engine",
	}
}

// IsSignificant 判断变更是否重要（需要通知服务）
func (ce *ChangeEvent) IsSignificant() bool {
	// 删除和修改操作都是重要的
	if ce.Type == ChangeTypeDelete || ce.Type == ChangeTypeModify {
		return true
	}
	
	// 新增操作根据配置项路径判断重要性
	path := ce.Path.String()
	for _, criticalPath := range constants.CriticalConfigPaths {
		if path == criticalPath {
			return true
		}
	}
	
	return false
}

// ReferenceCounter 引用计数器，用于追踪配置项的使用情况
type ReferenceCounter struct {
	counters map[ConfigPath]*int64 // 配置项路径到引用计数的映射
	mu       sync.RWMutex          // 并发控制锁
}

// NewReferenceCounter 创建新的引用计数器
func NewReferenceCounter() *ReferenceCounter {
	return &ReferenceCounter{
		counters: make(map[ConfigPath]*int64),
	}
}

// Acquire 获取配置项引用
func (rc *ReferenceCounter) Acquire(path ConfigPath) int64 {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	
	if counter, exists := rc.counters[path]; exists {
		return atomic.AddInt64(counter, 1)
	}
	
	// 创建新的计数器
	counter := new(int64)
	atomic.StoreInt64(counter, 1)
	rc.counters[path] = counter
	return 1
}

// Release 释放配置项引用
func (rc *ReferenceCounter) Release(path ConfigPath) int64 {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	
	if counter, exists := rc.counters[path]; exists {
		count := atomic.AddInt64(counter, -1)
		if count <= 0 {
			delete(rc.counters, path)
		}
		return count
	}
	
	return 0
}

// GetCount 获取配置项的引用计数
func (rc *ReferenceCounter) GetCount(path ConfigPath) int64 {
	rc.mu.RLock()
	defer rc.mu.RUnlock()
	
	if counter, exists := rc.counters[path]; exists {
		return atomic.LoadInt64(counter)
	}
	
	return 0
}

// GetAllCounts 获取所有配置项的引用计数
func (rc *ReferenceCounter) GetAllCounts() map[ConfigPath]int64 {
	rc.mu.RLock()
	defer rc.mu.RUnlock()
	
	result := make(map[ConfigPath]int64)
	for path, counter := range rc.counters {
		result[path] = atomic.LoadInt64(counter)
	}
	
	return result
}

// getConfigItemType 根据反射类型确定配置项类型
func getConfigItemType(reflectType reflect.Type) ConfigItemType {
	// 处理指针类型
	if reflectType.Kind() == reflect.Ptr {
		return ConfigItemTypePointer
	}
	
	switch reflectType.Kind() {
	case reflect.String:
		return ConfigItemTypeString
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return ConfigItemTypeInt
	case reflect.Bool:
		return ConfigItemTypeBool
	case reflect.Float32, reflect.Float64:
		return ConfigItemTypeFloat
	case reflect.Slice:
		return ConfigItemTypeSlice
	case reflect.Map:
		return ConfigItemTypeMap
	case reflect.Struct:
		// 特殊处理时间间隔类型
		if reflectType == reflect.TypeOf(time.Duration(0)) {
			return ConfigItemTypeDuration
		}
		return ConfigItemTypeStruct
	default:
		return ConfigItemTypeUnknown
	}
}
