// Package hotreload 配置项路径映射系统实现
// 使用Go反射机制遍历配置结构体，建立配置项路径到字段的映射关系
package hotreload

import (
	"fmt"
	"reflect"
	"strings"
	"sync"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
)

// ConfigMapper 配置项路径映射器
// 负责建立配置项路径与结构体字段之间的双向映射关系
type ConfigMapper struct {
	pathToField   map[ConfigPath]*FieldInfo // 路径到字段信息的映射
	fieldToPath   map[string]ConfigPath     // 字段标识到路径的映射
	rootType      reflect.Type              // 根配置类型
	rootValue     reflect.Value             // 根配置值
	logger        logger.Logger             // 日志记录器
	mu            sync.RWMutex              // 并发控制锁
	initialized   bool                      // 是否已初始化
}

// FieldInfo 字段信息，包含字段的反射信息和访问路径
type FieldInfo struct {
	Field       reflect.StructField // 结构体字段信息
	Value       reflect.Value       // 字段值
	Type        reflect.Type        // 字段类型
	Path        ConfigPath          // 配置路径
	Parent      *FieldInfo          // 父字段信息
	Children    []*FieldInfo        // 子字段信息列表
	IsPointer   bool                // 是否为指针类型
	IsSlice     bool                // 是否为切片类型
	IsMap       bool                // 是否为映射类型
	YamlTag     string              // YAML标签
	ValidateTag string              // 验证标签
}

// NewConfigMapper 创建新的配置映射器
func NewConfigMapper(cfg *config.Config, log logger.Logger) *ConfigMapper {
	if log == nil {
		log = logger.GetLogger("config_mapper")
	}

	return &ConfigMapper{
		pathToField: make(map[ConfigPath]*FieldInfo),
		fieldToPath: make(map[string]ConfigPath),
		rootType:    reflect.TypeOf(cfg).Elem(), // 去除指针
		rootValue:   reflect.ValueOf(cfg).Elem(),
		logger:      log,
		initialized: false,
	}
}

// Initialize 初始化配置映射器，构建完整的路径映射
func (cm *ConfigMapper) Initialize() error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if cm.initialized {
		return nil
	}

	cm.logger.Info("开始初始化配置项路径映射系统")

	// 清空现有映射
	cm.pathToField = make(map[ConfigPath]*FieldInfo)
	cm.fieldToPath = make(map[string]ConfigPath)

	// 递归构建路径映射
	err := cm.buildPathMapping("", cm.rootType, cm.rootValue, nil)
	if err != nil {
		return errors.WrapErrorWithDetails(
			err,
			errors.ErrTypeConfig,
			errors.ErrCodeConfigMappingFailed,
			"配置项路径映射构建失败",
			fmt.Sprintf("根类型: %s", cm.rootType.Name()),
		)
	}

	cm.initialized = true
	cm.logger.Infof("配置项路径映射系统初始化完成，共映射 %d 个配置项", len(cm.pathToField))

	return nil
}

// buildPathMapping 递归构建路径映射
func (cm *ConfigMapper) buildPathMapping(basePath string, structType reflect.Type, structValue reflect.Value, parent *FieldInfo) error {
	// 处理指针类型
	if structType.Kind() == reflect.Ptr {
		if structValue.IsNil() {
			// 对于nil指针，创建零值进行映射
			structValue = reflect.New(structType.Elem())
		}
		structType = structType.Elem()
		structValue = structValue.Elem()
	}

	// 只处理结构体类型
	if structType.Kind() != reflect.Struct {
		return nil
	}

	// 遍历结构体的所有字段
	for i := 0; i < structType.NumField(); i++ {
		field := structType.Field(i)
		fieldValue := structValue.Field(i)

		// 跳过未导出的字段
		if !field.IsExported() {
			continue
		}

		// 获取YAML标签
		yamlTag := field.Tag.Get("yaml")
		if yamlTag == "-" {
			continue // 跳过标记为忽略的字段
		}

		// 解析YAML标签
		yamlName := cm.parseYamlTag(yamlTag)
		if yamlName == "" {
			yamlName = strings.ToLower(field.Name) // 默认使用小写字段名
		}

		// 构建配置路径
		var configPath string
		if basePath == "" {
			configPath = yamlName
		} else {
			configPath = basePath + constants.ConfigPathSeparator + yamlName
		}

		// 创建字段信息
		fieldInfo := &FieldInfo{
			Field:       field,
			Value:       fieldValue,
			Type:        field.Type,
			Path:        ConfigPath(configPath),
			Parent:      parent,
			Children:    make([]*FieldInfo, 0),
			IsPointer:   field.Type.Kind() == reflect.Ptr,
			IsSlice:     field.Type.Kind() == reflect.Slice,
			IsMap:       field.Type.Kind() == reflect.Map,
			YamlTag:     yamlTag,
			ValidateTag: field.Tag.Get("validate"),
		}

		// 添加到映射表
		cm.pathToField[ConfigPath(configPath)] = fieldInfo
		cm.fieldToPath[cm.getFieldIdentifier(field)] = ConfigPath(configPath)

		// 如果父字段存在，添加到父字段的子字段列表
		if parent != nil {
			parent.Children = append(parent.Children, fieldInfo)
		}

		// 递归处理嵌套结构
		err := cm.processNestedField(configPath, fieldInfo)
		if err != nil {
			return err
		}
	}

	return nil
}

// processNestedField 处理嵌套字段
func (cm *ConfigMapper) processNestedField(configPath string, fieldInfo *FieldInfo) error {
	fieldType := fieldInfo.Type
	fieldValue := fieldInfo.Value

	// 处理指针类型
	if fieldType.Kind() == reflect.Ptr {
		if !fieldValue.IsNil() {
			fieldType = fieldType.Elem()
			fieldValue = fieldValue.Elem()
		} else {
			// 为nil指针创建零值
			fieldValue = reflect.New(fieldType.Elem()).Elem()
			fieldType = fieldType.Elem()
		}
	}

	switch fieldType.Kind() {
	case reflect.Struct:
		// 递归处理结构体
		return cm.buildPathMapping(configPath, fieldType, fieldValue, fieldInfo)

	case reflect.Slice:
		// 处理切片类型
		return cm.processSliceField(configPath, fieldInfo, fieldValue)

	case reflect.Map:
		// 处理映射类型
		return cm.processMapField(configPath, fieldInfo, fieldValue)

	default:
		// 基本类型，无需进一步处理
		return nil
	}
}

// processSliceField 处理切片字段
func (cm *ConfigMapper) processSliceField(configPath string, fieldInfo *FieldInfo, sliceValue reflect.Value) error {
	if sliceValue.Len() == 0 {
		return nil // 空切片无需处理
	}

	elementType := fieldInfo.Type.Elem()

	// 为切片的每个元素创建路径映射
	for i := 0; i < sliceValue.Len(); i++ {
		elementPath := fmt.Sprintf("%s[%d]", configPath, i)
		elementValue := sliceValue.Index(i)

		// 创建元素字段信息
		elementFieldInfo := &FieldInfo{
			Field:     reflect.StructField{Name: fmt.Sprintf("[%d]", i), Type: elementType},
			Value:     elementValue,
			Type:      elementType,
			Path:      ConfigPath(elementPath),
			Parent:    fieldInfo,
			Children:  make([]*FieldInfo, 0),
			IsPointer: elementType.Kind() == reflect.Ptr,
			IsSlice:   elementType.Kind() == reflect.Slice,
			IsMap:     elementType.Kind() == reflect.Map,
		}

		// 添加到映射表
		cm.pathToField[ConfigPath(elementPath)] = elementFieldInfo
		fieldInfo.Children = append(fieldInfo.Children, elementFieldInfo)

		// 递归处理元素
		if elementType.Kind() == reflect.Struct || 
		   (elementType.Kind() == reflect.Ptr && elementType.Elem().Kind() == reflect.Struct) {
			err := cm.processNestedField(elementPath, elementFieldInfo)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// processMapField 处理映射字段
func (cm *ConfigMapper) processMapField(configPath string, fieldInfo *FieldInfo, mapValue reflect.Value) error {
	if mapValue.Len() == 0 {
		return nil // 空映射无需处理
	}

	valueType := fieldInfo.Type.Elem()

	// 为映射的每个键值对创建路径映射
	for _, key := range mapValue.MapKeys() {
		keyStr := fmt.Sprintf("%v", key.Interface())
		elementPath := configPath + constants.ConfigPathSeparator + keyStr
		elementValue := mapValue.MapIndex(key)

		// 创建元素字段信息
		elementFieldInfo := &FieldInfo{
			Field:     reflect.StructField{Name: keyStr, Type: valueType},
			Value:     elementValue,
			Type:      valueType,
			Path:      ConfigPath(elementPath),
			Parent:    fieldInfo,
			Children:  make([]*FieldInfo, 0),
			IsPointer: valueType.Kind() == reflect.Ptr,
			IsSlice:   valueType.Kind() == reflect.Slice,
			IsMap:     valueType.Kind() == reflect.Map,
		}

		// 添加到映射表
		cm.pathToField[ConfigPath(elementPath)] = elementFieldInfo
		fieldInfo.Children = append(fieldInfo.Children, elementFieldInfo)

		// 递归处理值
		if valueType.Kind() == reflect.Struct || 
		   (valueType.Kind() == reflect.Ptr && valueType.Elem().Kind() == reflect.Struct) {
			err := cm.processNestedField(elementPath, elementFieldInfo)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// GetFieldByPath 根据配置路径获取字段信息
func (cm *ConfigMapper) GetFieldByPath(path ConfigPath) (*FieldInfo, error) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	if !cm.initialized {
		return nil, errors.NewError(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigNotInitialized,
			"配置映射器未初始化",
		)
	}

	fieldInfo, exists := cm.pathToField[path]
	if !exists {
		return nil, errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigPathNotFound,
			"配置路径不存在",
			fmt.Sprintf("路径: %s", path),
		)
	}

	return fieldInfo, nil
}

// GetPathByField 根据字段获取配置路径
func (cm *ConfigMapper) GetPathByField(field reflect.StructField) (ConfigPath, error) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	if !cm.initialized {
		return "", errors.NewError(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigNotInitialized,
			"配置映射器未初始化",
		)
	}

	identifier := cm.getFieldIdentifier(field)
	path, exists := cm.fieldToPath[identifier]
	if !exists {
		return "", errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigFieldNotFound,
			"字段路径不存在",
			fmt.Sprintf("字段: %s", field.Name),
		)
	}

	return path, nil
}

// GetAllPaths 获取所有配置路径
func (cm *ConfigMapper) GetAllPaths() []ConfigPath {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	paths := make([]ConfigPath, 0, len(cm.pathToField))
	for path := range cm.pathToField {
		paths = append(paths, path)
	}

	return paths
}

// GetFieldCount 获取映射的字段总数
func (cm *ConfigMapper) GetFieldCount() int {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return len(cm.pathToField)
}

// IsInitialized 检查映射器是否已初始化
func (cm *ConfigMapper) IsInitialized() bool {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.initialized
}

// parseYamlTag 解析YAML标签，提取字段名
func (cm *ConfigMapper) parseYamlTag(tag string) string {
	if tag == "" {
		return ""
	}

	// 分割标签，获取第一部分作为字段名
	parts := strings.Split(tag, ",")
	if len(parts) > 0 && parts[0] != "" {
		return parts[0]
	}

	return ""
}

// getFieldIdentifier 获取字段的唯一标识符
func (cm *ConfigMapper) getFieldIdentifier(field reflect.StructField) string {
	return fmt.Sprintf("%s.%s", field.PkgPath, field.Name)
}

// Refresh 刷新配置映射（当配置结构发生变化时调用）
func (cm *ConfigMapper) Refresh(cfg *config.Config) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	cm.logger.Info("开始刷新配置项路径映射")

	// 更新根类型和值
	cm.rootType = reflect.TypeOf(cfg).Elem()
	cm.rootValue = reflect.ValueOf(cfg).Elem()
	cm.initialized = false

	// 重新初始化
	cm.mu.Unlock() // 临时释放锁，避免死锁
	err := cm.Initialize()
	cm.mu.Lock() // 重新获取锁

	if err != nil {
		return errors.WrapError(
			err,
			errors.ErrTypeConfig,
			errors.ErrCodeConfigMappingFailed,
			"刷新配置项路径映射失败",
		)
	}

	cm.logger.Info("配置项路径映射刷新完成")
	return nil
}
