package config

import (
	"context"
	"os"
	"time"

	"github.com/fsnotify/fsnotify"
)

// ProxyManagerInterface 代理管理器接口（避免循环导入）
type ProxyManagerInterface interface {
	GetProxy(mode string) (string, error)
	RotateProxy(domain ...string) (string, error)
	GetProxyCount() int
	Count() int
	GetProxies() []string
	UpdateConfig(config interface{}) error
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
	IsIPPermanentlyBlocked(ip string) bool
	IsIPBanned(ip, resource, scope string) bool
	MarkProxyAsFailed(proxy string)
	ClearProxyCacheIfFailed(proxy string)
	RemoveProxy(proxy string) error
	ClearProxyFailureStatus(proxy string)
	InitBanSystem(cfg interface{})
	StartBanCleaner(ctx context.Context)
	Watch() (*fsnotify.Watcher, error)
	Reload() error
}

// Options 包含所需的配置
type Options struct {
	ProxyManager ProxyManagerInterface // 使用本地接口定义
	Result       *os.File
	Timeout      time.Duration

	Address    string
	Auth       string
	CC         string
	Check      bool
	Countries  []string
	Daemon     bool
	File       string
	Goroutine  int
	Output     string
	Sync       bool
	Verbose    bool
	Watch      bool
	Type       string
	RuleConfig *Config // 使用 Config (YAML 配置)
	ConfigFile string
}
