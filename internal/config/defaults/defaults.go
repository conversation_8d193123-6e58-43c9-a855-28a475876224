package config

import (
	"fmt"
	"reflect"
	"strings"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
)

// DefaultManager 默认配置管理器
type DefaultManager struct {
	defaults map[string]interface{}
}

// NewDefaultManager 创建新的默认配置管理器
func NewDefaultManager() *DefaultManager {
	return &DefaultManager{
		defaults: make(map[string]interface{}),
	}
}

// SetDefault 设置默认值
func (dm *DefaultManager) SetDefault(key string, value interface{}) {
	dm.defaults[key] = value
}

// GetDefault 获取默认值
func (dm *DefaultManager) GetDefault(key string) (interface{}, bool) {
	value, exists := dm.defaults[key]
	return value, exists
}

// ApplyDefaults 将默认值应用到配置结构体
func (dm *DefaultManager) ApplyDefaults(config interface{}) error {
	v := reflect.ValueOf(config)
	if v.Kind() != reflect.Ptr || v.Elem().Kind() != reflect.Struct {
		return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "config must be a pointer to struct")
	}

	return dm.applyDefaultsRecursive(v.Elem(), "")
}

// applyDefaultsRecursive 递归应用默认值
func (dm *DefaultManager) applyDefaultsRecursive(v reflect.Value, prefix string) error {
	t := v.Type()

	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)

		// 跳过不可设置的字段
		if !field.CanSet() {
			continue
		}

		// 构建配置键
		key := dm.buildConfigKey(prefix, fieldType)

		// 处理嵌套结构体
		if field.Kind() == reflect.Struct {
			if err := dm.applyDefaultsRecursive(field, key); err != nil {
				return err
			}
			continue
		}

		// 处理指针类型
		if field.Kind() == reflect.Ptr {
			if field.IsNil() {
				// 创建新实例
				newValue := reflect.New(field.Type().Elem())
				field.Set(newValue)
			}
			if field.Elem().Kind() == reflect.Struct {
				if err := dm.applyDefaultsRecursive(field.Elem(), key); err != nil {
					return err
				}
			}
			continue
		}

		// 应用默认值（只对零值字段）
		if defaultValue, exists := dm.GetDefault(key); exists {
			// 检查字段是否为零值
			if dm.isZeroValue(field) {
				if err := dm.setFieldValue(field, defaultValue); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

// buildConfigKey 构建配置键
func (dm *DefaultManager) buildConfigKey(prefix string, field reflect.StructField) string {
	// 优先使用 mapstructure 标签
	if tag := field.Tag.Get("mapstructure"); tag != "" && tag != "-" {
		if prefix != "" {
			return prefix + "." + tag
		}
		return tag
	}

	// 使用 yaml 标签
	if tag := field.Tag.Get("yaml"); tag != "" && tag != "-" {
		// 处理 yaml 标签中的选项（如 omitempty）
		if commaIndex := strings.Index(tag, ","); commaIndex != -1 {
			tag = tag[:commaIndex]
		}

		if prefix != "" {
			return prefix + "." + tag
		}
		return tag
	}

	// 使用 json 标签
	if tag := field.Tag.Get("json"); tag != "" && tag != "-" {
		if prefix != "" {
			return prefix + "." + tag
		}
		return tag
	}

	// 使用字段名（转换为小写）
	fieldName := strings.ToLower(field.Name)
	if prefix != "" {
		return prefix + "." + fieldName
	}
	return fieldName
}

// setFieldValue 设置字段值
func (dm *DefaultManager) setFieldValue(field reflect.Value, value interface{}) error {
	valueReflect := reflect.ValueOf(value)

	// 类型完全匹配
	if field.Type() == valueReflect.Type() {
		field.Set(valueReflect)
		return nil
	}

	// 类型转换
	if valueReflect.Type().ConvertibleTo(field.Type()) {
		field.Set(valueReflect.Convert(field.Type()))
		return nil
	}

	// 特殊处理 time.Duration
	if field.Type() == reflect.TypeOf(time.Duration(0)) {
		switch v := value.(type) {
		case string:
			if duration, err := time.ParseDuration(v); err == nil {
				field.Set(reflect.ValueOf(duration))
				return nil
			}
		case int64:
			field.Set(reflect.ValueOf(time.Duration(v)))
			return nil
		case float64:
			field.Set(reflect.ValueOf(time.Duration(int64(v))))
			return nil
		}
	}

	return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "cannot convert value type", fmt.Sprintf("value_type: %T, target_type: %s", value, field.Type().String()))
}

// isZeroValue 检查字段是否为零值
func (dm *DefaultManager) isZeroValue(field reflect.Value) bool {
	// 处理不同类型的零值检查
	switch field.Kind() {
	case reflect.Slice, reflect.Map, reflect.Chan, reflect.Func:
		return field.IsNil() || field.Len() == 0
	case reflect.Ptr, reflect.Interface:
		return field.IsNil()
	case reflect.String:
		return field.String() == ""
	case reflect.Bool:
		return !field.Bool()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return field.Int() == 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return field.Uint() == 0
	case reflect.Float32, reflect.Float64:
		return field.Float() == 0.0
	default:
		// 对于其他类型，使用反射比较
		zeroValue := reflect.Zero(field.Type())
		return reflect.DeepEqual(field.Interface(), zeroValue.Interface())
	}
}

// LoadDefaults 加载预定义的默认值
func (dm *DefaultManager) LoadDefaults() {
	// 从常量包加载默认配置
	for key, value := range constants.DefaultConfig {
		dm.SetDefault(key, value)
	}
}

// GetAllDefaults 获取所有默认值
func (dm *DefaultManager) GetAllDefaults() map[string]interface{} {
	result := make(map[string]interface{})
	for k, v := range dm.defaults {
		result[k] = v
	}
	return result
}

// MergeDefaults 合并默认值
func (dm *DefaultManager) MergeDefaults(other map[string]interface{}) {
	for key, value := range other {
		dm.SetDefault(key, value)
	}
}

// ValidateDefaults 验证默认值的有效性
func (dm *DefaultManager) ValidateDefaults() error {
	for key, value := range dm.defaults {
		if err := dm.validateDefaultValue(key, value); err != nil {
			return errors.WrapErrorWithDetails(err, errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "invalid default value", fmt.Sprintf("key: %s", key))
		}
	}
	return nil
}

// validateDefaultValue 验证单个默认值
func (dm *DefaultManager) validateDefaultValue(key string, value interface{}) error {
	// 验证端口号
	if strings.Contains(key, "port") {
		if port, ok := value.(int); ok {
			if port < 1 || port > 65535 {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "port must be between 1 and 65535", fmt.Sprintf("port: %d, min: 1, max: 65535", port))
			}
		}
	}

	// 验证超时时间
	if strings.Contains(key, "timeout") || strings.Contains(key, "interval") {
		if duration, ok := value.(time.Duration); ok {
			if duration < 0 {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "duration cannot be negative")
			}
		}
	}

	// 验证大小限制
	if strings.Contains(key, "size") || strings.Contains(key, "max") {
		if size, ok := value.(int); ok {
			if size < 0 {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "size cannot be negative")
			}
		}
	}

	return nil
}

// 全局默认配置管理器实例
var GlobalDefaultManager = NewDefaultManager()

// 初始化函数
func init() {
	GlobalDefaultManager.LoadDefaults()
}

// 便捷函数

// SetDefault 设置全局默认值
func SetDefault(key string, value interface{}) {
	GlobalDefaultManager.SetDefault(key, value)
}

// GetDefault 获取全局默认值
func GetDefault(key string) (interface{}, bool) {
	return GlobalDefaultManager.GetDefault(key)
}

// ApplyDefaults 应用全局默认值到配置
func ApplyDefaults(config interface{}) error {
	return GlobalDefaultManager.ApplyDefaults(config)
}

// GetDefaultString 获取字符串类型的默认值
func GetDefaultString(key string, fallback string) string {
	if value, exists := GetDefault(key); exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return fallback
}

// GetDefaultInt 获取整数类型的默认值
func GetDefaultInt(key string, fallback int) int {
	if value, exists := GetDefault(key); exists {
		if i, ok := value.(int); ok {
			return i
		}
	}
	return fallback
}

// GetDefaultDuration 获取时间间隔类型的默认值
func GetDefaultDuration(key string, fallback time.Duration) time.Duration {
	if value, exists := GetDefault(key); exists {
		if duration, ok := value.(time.Duration); ok {
			return duration
		}
	}
	return fallback
}

// GetDefaultBool 获取布尔类型的默认值
func GetDefaultBool(key string, fallback bool) bool {
	if value, exists := GetDefault(key); exists {
		if b, ok := value.(bool); ok {
			return b
		}
	}
	return fallback
}
