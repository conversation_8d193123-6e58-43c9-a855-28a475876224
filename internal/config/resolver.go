package config

import (
	"net"
	"strconv"

	"flexproxy/common/constants"
	"flexproxy/common/logger"
)

// AddressResolver 配置地址解析器
type AddressResolver struct {
	config *Config
	logger logger.Logger
}

// NewAddressResolver 创建新的地址解析器
func NewAddressResolver(config *Config) *AddressResolver {
	return &AddressResolver{
		config: config,
		logger: logger.GetLogger("address-resolver"),
	}
}

// ResolveListenAddress 解析监听地址，处理配置优先级
// 优先级：命令行参数 > server.listen_address > ports配置+server.host > 默认值
func (ar *AddressResolver) ResolveListenAddress(cmdAddress string, serverType string) (string, error) {
	// 如果命令行已指定地址，直接使用
	if cmdAddress != "" {
		ar.logger.Debug("使用命令行指定的监听地址: " + cmdAddress)
		return cmdAddress, nil
	}

	// 优先级1: server.listen_address (完整地址配置)
	if ar.config.Server != nil && ar.config.Server.ListenAddress != "" {
		ar.logger.Infof("使用配置的完整监听地址: %s (来源: server.listen_address)",
			ar.config.Server.ListenAddress)
		return ar.config.Server.ListenAddress, nil
	}

	// 优先级2: 组合配置 (host + port)
	host := ar.resolveHost()
	port, source := ar.resolvePort(serverType)

	// 构建完整地址
	address := net.JoinHostPort(host, strconv.Itoa(port))

	ar.logger.Infof("解析监听地址: %s (主机: %s, 端口: %d, 来源: %s)",
		address, host, port, source)

	return address, nil
}

// resolveHost 解析主机地址
func (ar *AddressResolver) resolveHost() string {
	// 优先使用 server.host
	if ar.config.Server != nil && ar.config.Server.Host != "" {
		return ar.config.Server.Host
	}
	
	// 使用默认值
	return constants.DefaultServerHost
}

// resolvePort 解析端口，返回端口号和来源说明
func (ar *AddressResolver) resolvePort(serverType string) (int, string) {
	switch serverType {
	case "http":
		return ar.resolveHTTPPort()
	case "socks5":
		return ar.resolveSOCKSPort()
	default:
		return ar.resolveHTTPPort() // 默认使用HTTP端口
	}
}

// resolveHTTPPort 解析HTTP端口
func (ar *AddressResolver) resolveHTTPPort() (int, string) {
	// 优先级1: ports.http
	if ar.config.Ports != nil && ar.config.Ports.HTTP > 0 {
		return ar.config.Ports.HTTP, "ports.http"
	}

	// 优先级2: 默认值
	return constants.DefaultHTTPPort, "默认值"
}

// resolveSOCKSPort 解析SOCKS端口
func (ar *AddressResolver) resolveSOCKSPort() (int, string) {
	// 优先级1: ports.socks
	if ar.config.Ports != nil && ar.config.Ports.SOCKS > 0 {
		return ar.config.Ports.SOCKS, "ports.socks"
	}

	// 优先级2: 默认值
	return constants.DefaultSOCKSPort, "默认值"
}

// resolveHTTPSPort 解析HTTPS端口
func (ar *AddressResolver) resolveHTTPSPort() (int, string) {
	// 优先级1: ports.https
	if ar.config.Ports != nil && ar.config.Ports.HTTPS > 0 {
		return ar.config.Ports.HTTPS, "ports.https"
	}

	// 优先级2: 默认值
	return constants.DefaultHTTPSPort, "默认值"
}

// GetAllResolvedPorts 获取所有解析后的端口信息，用于调试和日志
func (ar *AddressResolver) GetAllResolvedPorts() map[string]PortInfo {
	httpPort, httpSource := ar.resolveHTTPPort()
	httpsPort, httpsSource := ar.resolveHTTPSPort()
	socksPort, socksSource := ar.resolveSOCKSPort()
	
	return map[string]PortInfo{
		"http": {
			Port:   httpPort,
			Source: httpSource,
		},
		"https": {
			Port:   httpsPort,
			Source: httpsSource,
		},
		"socks": {
			Port:   socksPort,
			Source: socksSource,
		},
	}
}

// PortInfo 端口信息
type PortInfo struct {
	Port   int    // 端口号
	Source string // 配置来源
}

// ValidatePortConfiguration 验证端口配置
func (ar *AddressResolver) ValidatePortConfiguration() []string {
	var warnings []string

	// 检查是否缺少端口配置
	if ar.config.Ports == nil {
		warnings = append(warnings, "建议配置 ports 配置节以明确指定服务端口")
	} else {
		if ar.config.Ports.HTTP <= 0 {
			warnings = append(warnings, "ports.http 未配置，将使用默认端口")
		}
		if ar.config.Ports.HTTPS <= 0 {
			warnings = append(warnings, "ports.https 未配置，将使用默认端口")
		}
		if ar.config.Ports.SOCKS <= 0 {
			warnings = append(warnings, "ports.socks 未配置，将使用默认端口")
		}
	}

	return warnings
}

// LogPortConfiguration 记录端口配置信息
func (ar *AddressResolver) LogPortConfiguration() {
	ar.logger.Info("=== 端口配置解析结果 ===")
	
	allPorts := ar.GetAllResolvedPorts()
	for protocol, info := range allPorts {
		ar.logger.Infof("%s 端口: %d (来源: %s)",
			protocol, info.Port, info.Source)
	}

	// 显示配置验证警告
	warnings := ar.ValidatePortConfiguration()
	for _, warning := range warnings {
		ar.logger.Warnf("配置警告: %s", warning)
	}
	
	ar.logger.Info("========================")
}
