package config

import (
	"time"
)

// 注意：处理阶段常量和动作类型常量已迁移到 common/constants/constants.go 中统一管理
// 请使用 constants.PreProcess, constants.ActionRetrySame 等常量

// 触发器类型常量
const (
	TriggerStatus         = "status"           // 状态码触发
	TriggerBody           = "body"             // 响应内容触发
	TriggerMaxRequestTime = "max_request_time" // 请求时间长于触发
	TriggerConnTimeOut    = "conn_time_out"    // 连接超时触发
	TriggerMinRequestTime = "min_request_time" // 请求时间短于触发
	TriggerURL            = "url"              // URL匹配触发
	TriggerDomain         = "domain"           // 域名匹配触发
	TriggerCombined       = "combined"         // 组合条件触发
	TriggerJavaScript     = "javascript"       // JavaScript触发器
	TriggerRequestBody    = "request_body"     // 请求内容触发器
	TriggerRequestHeader  = "request_header"   // 请求头触发器
	TriggerResponseHeader = "response_header"  // 响应头触发器
)

// Config 是整个配置文件的顶层结构
type Config struct {
	Global       GlobalConfig                  `yaml:"global" validate:"required"`
	Server       *ServerConfig                 `yaml:"server,omitempty"`
	Proxy        *ProxyConfig                  `yaml:"proxy,omitempty"`
	Cache        *CacheConfig                  `yaml:"cache,omitempty"`
	Logging      *LoggingConfig                `yaml:"logging,omitempty"`
	Monitoring   *MonitoringConfig             `yaml:"monitoring,omitempty"`
	Security     *SecurityConfig               `yaml:"security,omitempty"`
	RateLimiting *RateLimitingConfig           `yaml:"rate_limiting,omitempty"`
	DNSService   *DNSServiceConfig             `yaml:"dns_service,omitempty"`
	Timeouts     *TimeoutsConfig               `yaml:"timeouts,omitempty"`
	Ports        *PortsConfig                  `yaml:"ports,omitempty"`
	Modules      *ModulesConfig                `yaml:"modules,omitempty"`
	ProxyCheck   *ProxyCheckConfig             `yaml:"proxy_check,omitempty"`
	Actions      map[string]ActionSequenceName `yaml:"actions" validate:"dive"`
	Events       []EventConfig                 `yaml:"events" validate:"dive"`
	Advanced     *AdvancedConfig               `yaml:"advanced,omitempty"`
	Paths        *PathsConfig                  `yaml:"paths,omitempty"`
	System       *SystemConfig                 `yaml:"system,omitempty"`
	Protocols        *ProtocolsConfig              `yaml:"protocols,omitempty"`
	Plugins          *PluginsConfig                `yaml:"plugins,omitempty"`
	Development      *DevelopmentConfig            `yaml:"development,omitempty"`
	ConfigManagement *ConfigManagementConfig       `yaml:"config_management,omitempty"`
}

// GlobalConfig 对应 YAML 中的 'global' 部分
type GlobalConfig struct {
	Enable                   bool                 `yaml:"enable"`
	ProxyFile                string               `yaml:"proxy_file"` // 代理文件路径
	GlobalBannedIPs          []BanIPConfig        `yaml:"global_banned_ips" validate:"dive"`
	BannedDomains            []BannedDomainConfig `yaml:"banned_domains" validate:"dive"`
	BlockedIPs               []string             `yaml:"blocked_ips" validate:"dive,ip"`
	TrustedIPs               []string             `yaml:"trusted_ips" validate:"dive,ip"`
	ExcludedPatterns         []string             `yaml:"excluded_patterns"`
	ExcludedScope            string               `yaml:"excluded_scope"`
	RulePriority             int                  `yaml:"rule_priority" validate:"min=0,max=100"`
	DefaultProcessStage      string               `yaml:"default_process_stage" validate:"oneof=pre post_header post_body"`
	IPRotationMode           string               `yaml:"ip_rotation_mode" validate:"oneof=random sequential quality smart"`
	MinProxyPoolSize         int                  `yaml:"min_proxy_pool_size" validate:"min=1"`
	MaxProxyFetchAttempts    int                  `yaml:"max_proxy_fetch_attempts" validate:"min=1,max=10"`

	// 代理连接失败重试配置
	ProxyConnectionRetryEnabled bool             `yaml:"proxy_connection_retry_enabled"`
	ProxyConnectionMaxRetries   int              `yaml:"proxy_connection_max_retries" validate:"min=0,max=10"`
	ProxyConnectionRetryDelay   string           `yaml:"proxy_connection_retry_delay"`
	ProxyConnectionRetryBackoff float64          `yaml:"proxy_connection_retry_backoff" validate:"min=1"`
	ProxyConnectionMaxDelay     string           `yaml:"proxy_connection_max_delay"`

	RetryProxyReusePolicy    string               `yaml:"retry_proxy_reuse_policy" validate:"oneof=allow deny cooldown"`
	RetryProxyCooldownTime   int                  `yaml:"retry_proxy_cooldown_time" validate:"min=0"`
	RetryProxyGlobalTracking bool                 `yaml:"retry_proxy_global_tracking"`

	// 命令行参数对应的配置字段
	Verbose    bool   `yaml:"verbose"`           // 详细模式，对应 -v, --verbose
	Daemon     bool   `yaml:"daemon"`            // 守护进程模式，对应 -d, --daemon
	Sync       bool   `yaml:"sync"`              // 同步模式，对应 -s, --sync
	Watch      bool   `yaml:"watch"`             // 监控文件变化，对应 -w, --watch
}

// BanIPConfig 代表全局IP封禁配置项
type BanIPConfig struct {
	IP       string      `yaml:"ip" validate:"required,ip"`
	Duration interface{} `yaml:"duration" validate:"required"` // int 或 string "reboot"
}

// BannedDomainConfig 代表全局域名封禁配置项
type BannedDomainConfig struct {
	Domain   string      `yaml:"domain" validate:"required,fqdn"`
	Duration interface{} `yaml:"duration" validate:"required"` // int 或 string "reboot"
}

// CustomDNSServer 代表自定义DNS服务器配置
type CustomDNSServer struct {
	Server   string   `yaml:"server" validate:"required,ip_or_ip_port"`
	Protocol string   `yaml:"protocol" validate:"required,oneof=udp tcp doh dot"`
	Timeout  int      `yaml:"timeout,omitempty" validate:"min=1000,max=30000"` // omitempty 如果允许默认值
	Priority int      `yaml:"priority,omitempty" validate:"min=0,max=100"`
	Tags     []string `yaml:"tags,omitempty"`
}

// DNSServerConfig 新增DNS服务器配置结构
type DNSServerConfig struct {
	Address  string            // 服务器地址（IP:端口）
	Protocol string            // 协议（udp/tcp/doh等）
	Timeout  int               // 超时时间（毫秒）
	Priority int               // 优先级
	Tags     []string          // 标签
	Options  map[string]string // 其他选项
}

// ActionConfig 代表单个动作的配置
// 使用 Params map[string]interface{} 来捕获所有未知或特定于类型的参数
type ActionConfig struct {
	Type   string                 `yaml:"type"`
	Params map[string]interface{} `yaml:",inline"`
}

// ActionSequenceName 代表一个动作序列，包含主序列和可选的备用序列
type ActionSequenceName struct {
	Sequence []ActionConfig `yaml:"sequence"`
	Relation []ActionConfig `yaml:"relation,omitempty"` // 关联动作序列
}

// EventConfig 代表单个事件的配置
type EventConfig struct {
	Name               string                    `yaml:"name" validate:"required,min=1,max=100"`
	Enable             bool                      `yaml:"enable"`
	TriggerType        string                    `yaml:"trigger_type" validate:"omitempty,trigger_type"`
	Conditions         []ConditionConfig         `yaml:"conditions" validate:"dive"`                    // 条件定义列表
	Matches            []MatchConfig             `yaml:"matches" validate:"dive"`                       // 匹配规则列表
	ConditionalActions []ConditionalActionConfig `yaml:"conditional_actions,omitempty" validate:"dive"` // 向后兼容（已废弃）
	ProcessStage       string                    `yaml:"process_stage" validate:"omitempty,process_stage"`
	Priority           int                       `yaml:"priority" validate:"priority_range"`
}

// ConditionalActionConfig 条件-动作配置（保留向后兼容）
type ConditionalActionConfig struct {
	ConditionName          string                       `yaml:"condition_name"` // 条件名称
	StatusCodes            *StatusCodesConfig           `yaml:"status_codes,omitempty"`
	BodyPatterns           *EnhancedPatternConfig       `yaml:"body_patterns,omitempty"`
	MaxRequestTime         int                          `yaml:"max_request_time,omitempty"`
	ConnectionTimeout      int                          `yaml:"connection_timeout,omitempty"`
	MinRequestTime         int                          `yaml:"min_request_time,omitempty"`
	URLPatterns            *EnhancedPatternConfig       `yaml:"url_patterns,omitempty"`
	DomainPatterns         *EnhancedPatternConfig       `yaml:"domain_patterns,omitempty"`
	RequestBodyPatterns    *EnhancedPatternConfig       `yaml:"request_body_patterns,omitempty"`
	RequestHeaderPatterns  *EnhancedHeaderPatternConfig `yaml:"request_header_patterns,omitempty"`
	ResponseHeaderPatterns *EnhancedHeaderPatternConfig `yaml:"response_header_patterns,omitempty"`
	// JavaScript 触发器配置
	JavaScriptCode         string                       `yaml:"javascript_code,omitempty"`    // JavaScript 代码
	JavaScriptTimeout      int                          `yaml:"javascript_timeout,omitempty"` // JavaScript 执行超时时间（毫秒）
	// 组合条件触发器配置
	CombinedConditions     []CombinedConditionConfig    `yaml:"combined_conditions,omitempty"` // 组合条件列表
	CombinedLogic          string                       `yaml:"combined_logic,omitempty"`      // 组合逻辑：AND, OR
	ConditionRelation      string                       `yaml:"condition_relation,omitempty"`  // 当前条件内部的逻辑关系
	TriggerID              string                       `yaml:"trigger_id,omitempty"`
	ActionSequenceName     string                       `yaml:"action_sequence_name"` // 该条件匹配时执行的动作序列
	Enable                 bool                         `yaml:"enable"`               // 是否启用该条件
}

// ConditionConfig 新的条件配置结构
type ConditionConfig struct {
	Name                   string                       `yaml:"name"`   // 条件名称
	Enable                 bool                         `yaml:"enable"` // 是否启用该条件
	StatusCodes            *StatusCodesConfig           `yaml:"status_codes,omitempty"`
	BodyPatterns           *EnhancedPatternConfig       `yaml:"body_patterns,omitempty"`
	MaxRequestTime         int                          `yaml:"max_request_time,omitempty"`
	ConnectionTimeout      int                          `yaml:"connection_timeout,omitempty"`
	MinRequestTime         int                          `yaml:"min_request_time,omitempty"`
	URLPatterns            *EnhancedPatternConfig       `yaml:"url_patterns,omitempty"`
	DomainPatterns         *EnhancedPatternConfig       `yaml:"domain_patterns,omitempty"`
	RequestBodyPatterns    *EnhancedPatternConfig       `yaml:"request_body_patterns,omitempty"`
	RequestHeaderPatterns  *EnhancedHeaderPatternConfig `yaml:"request_header_patterns,omitempty"`
	ResponseHeaderPatterns *EnhancedHeaderPatternConfig `yaml:"response_header_patterns,omitempty"`
	// JavaScript 触发器配置
	JavaScriptCode         string                       `yaml:"javascript_code,omitempty"`    // JavaScript 代码
	JavaScriptTimeout      int                          `yaml:"javascript_timeout,omitempty"` // JavaScript 执行超时时间（毫秒）
	// 组合条件触发器配置
	CombinedConditions     []CombinedConditionConfig    `yaml:"combined_conditions,omitempty"` // 组合条件列表
	CombinedLogic          string                       `yaml:"combined_logic,omitempty"`      // 组合逻辑：AND, OR
	ConditionRelation      string                       `yaml:"condition_relation,omitempty"`  // 条件内部的逻辑关系：AND, OR
	TriggerID              string                       `yaml:"trigger_id,omitempty"`
}

// CombinedConditionConfig 组合条件配置
type CombinedConditionConfig struct {
	Type          string                    `yaml:"type"`                    // 条件类型：status, body, url, header, time, custom
	Field         string                    `yaml:"field,omitempty"`         // 字段名（如header名称）
	Operator      string                    `yaml:"operator"`                // 操作符：equals, contains, regex, gt, lt, in, not_in
	Value         interface{}               `yaml:"value"`                   // 比较值
	Weight        int                       `yaml:"weight,omitempty"`        // 权重（用于复杂逻辑）
	SubConditions []CombinedConditionConfig `yaml:"sub_conditions,omitempty"` // 嵌套子条件
}

// MatchConfig 匹配规则配置，支持多层嵌套
type MatchConfig struct {
	Name       string         `yaml:"name,omitempty"`        // 匹配规则名称
	Conditions []string       `yaml:"conditions,omitempty"`  // 条件名称列表
	SubMatches []MatchConfig  `yaml:"sub_matches,omitempty"` // 嵌套的子匹配规则
	Logic      string         `yaml:"logic,omitempty"`       // 条件组合逻辑：AND, OR, NOT
	Actions    []ActionConfig `yaml:"actions"`               // 独立的动作列表
	Enable     bool           `yaml:"enable"`                // 是否启用该匹配规则
}

// StatusCodesConfig 状态码配置
type StatusCodesConfig struct {
	Codes    []int  `yaml:"codes"`    // 状态码列表
	Relation string `yaml:"relation"` // 关系："and" 或 "or"
}

// HeaderPatternConfig 头部模式配置
type HeaderPatternConfig struct {
	HeaderName string   `yaml:"header_name"` // 头部名称
	Patterns   []string `yaml:"patterns"`    // 模式列表
	Relation   string   `yaml:"relation"`    // 关系："and" 或 "or"
}

// EnhancedPatternConfig 增强模式配置
type EnhancedPatternConfig struct {
	Patterns      []PatternExpression `yaml:"patterns"`              // 模式表达式列表
	Relation      string              `yaml:"relation"`              // 模式间关系："and" 或 "or"
	Chained       []ChainedPattern    `yaml:"chained,omitempty"`     // 链式模式
	Negation      bool                `yaml:"negation,omitempty"`    // 是否取反
	CaseSensitive bool                `yaml:"case_sensitive"`        // 是否大小写敏感
	MultiLine     bool                `yaml:"multiline,omitempty"`   // 是否多行模式
	DotAll        bool                `yaml:"dotall,omitempty"`      // 是否点匹配所有字符
	Unicode       bool                `yaml:"unicode,omitempty"`     // 是否启用Unicode支持
	Timeout       int                 `yaml:"timeout,omitempty"`     // 匹配超时时间（毫秒）
	MaxMatches    int                 `yaml:"max_matches,omitempty"` // 最大匹配数量
}

// PatternExpression 模式表达式
type PatternExpression struct {
	Pattern  string                 `yaml:"pattern"` // 正则表达式或字符串模式
	Type     string                 `yaml:"type"`    // 模式类型："regex", "string", "glob"
	Weight   int                    `yaml:"weight"`  // 权重
	Chain    []ChainedPattern       `yaml:"chain,omitempty"`
	Logic    string                 `yaml:"logic,omitempty"` // AND, OR, XOR, NOT
	Negate   bool                   `yaml:"negate,omitempty"`
	SubGroup *EnhancedPatternConfig `yaml:"subgroup,omitempty"`
}

// ChainedPattern 链式模式
type ChainedPattern struct {
	Order    int    `yaml:"order"`    // 执行顺序
	Pattern  string `yaml:"pattern"`  // 模式
	Operator string `yaml:"operator"` // 操作符："and", "or", "not"
	Negate   bool   `yaml:"negate,omitempty"`
}

// EnhancedHeaderPatternConfig 增强头部模式配置
type EnhancedHeaderPatternConfig struct {
	Headers map[string]*EnhancedPatternConfig `yaml:"headers"` // 头部名称到模式配置的映射
}

// HeaderPatternExpression 头部模式表达式
type HeaderPatternExpression struct {
	Header   string                 `yaml:"header"`
	Patterns *EnhancedPatternConfig `yaml:"patterns"`
	Negate   bool                   `yaml:"negate,omitempty"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host                string             `yaml:"host" validate:"required"`

	// 完整监听地址配置 (可选，优先级最高)
	ListenAddress       string             `yaml:"listen_address,omitempty"`               // 完整监听地址，如 "0.0.0.0:8080"

	MaxIdleConns        int                `yaml:"max_idle_conns" validate:"min=0"`
	MaxIdleConnsPerHost int                `yaml:"max_idle_conns_per_host" validate:"min=0"`
	MaxConnsPerHost     int                `yaml:"max_conns_per_host" validate:"min=0"`
	BufferSize          int                `yaml:"buffer_size" validate:"min=1024"`
	MaxHeaderBytes      int                `yaml:"max_header_bytes" validate:"min=1024"`
	DebounceDelay       string             `yaml:"debounce_delay"`
	Compression         *CompressionConfig `yaml:"compression,omitempty"`
	HTTP2               *HTTP2Config       `yaml:"http2,omitempty"`
	Profiling           *ProfilingConfig   `yaml:"profiling,omitempty"`

	// 命令行参数对应的配置字段
	Auth string `yaml:"auth"`  // 认证配置 (username:password)，对应 -A, --auth
	Type string `yaml:"type" validate:"oneof=http socks5"`  // 代理服务器类型，对应 --type
}

// ProxyConfig 代理配置
type ProxyConfig struct {
	Strategy         string              `yaml:"strategy" validate:"oneof=random sequential quality custom"`
	LoadBalancer     string              `yaml:"load_balancer" validate:"oneof=round_robin weighted_round_robin least_connections response_time ip_hash"`
	MaxRetries       int                 `yaml:"max_retries" validate:"min=0"`

	HealthCheck      *HealthCheckConfig  `yaml:"health_check,omitempty"`
	PoolSize         int                 `yaml:"pool_size" validate:"min=1"`
	RotationInterval int                 `yaml:"rotation_interval" validate:"min=0"`
	QualityScore     *QualityScoreConfig `yaml:"quality_score,omitempty"`
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Enabled                 bool   `yaml:"enabled"`

	Path                    string `yaml:"path"`
	MaxConsecutiveFailures  int    `yaml:"max_consecutive_failures" validate:"min=1"`
	MaxConsecutiveSuccesses int    `yaml:"max_consecutive_successes" validate:"min=1"`
}

// QualityScoreConfig 质量评分配置
type QualityScoreConfig struct {
	Default              float64 `yaml:"default" validate:"min=0,max=1"`
	SuccessRateWeight    float64 `yaml:"success_rate_weight" validate:"min=0,max=1"`
	ResponseTimeWeight   float64 `yaml:"response_time_weight" validate:"min=0,max=1"`
	MaxFailureRate       float64 `yaml:"max_failure_rate" validate:"min=0,max=1"`
	TopProxyRatio        float64 `yaml:"top_proxy_ratio" validate:"min=0,max=1"`
	ResponseTimeBaseline int     `yaml:"response_time_baseline" validate:"min=0"`
	SmoothingFactor      float64 `yaml:"smoothing_factor" validate:"min=0,max=1"`
}

// CacheConfig 层次化缓存配置
type CacheConfig struct {
	// 全局缓存配置
	Global *GlobalCacheConfig `yaml:"global,omitempty"`

	// 专用缓存配置
	Modules *ModuleCacheConfig `yaml:"modules,omitempty"`

	// 缓存键前缀配置
	KeyPrefixes *CacheKeyPrefixConfig `yaml:"key_prefixes,omitempty"`

	// 缓存策略配置
	Policies *CachePolicyConfig `yaml:"policies,omitempty"`

	// 缓存存储配置
	Storage *CacheStorageConfig `yaml:"storage,omitempty"`
}

// GlobalCacheConfig 全局缓存配置
type GlobalCacheConfig struct {
	Enabled         bool   `yaml:"enabled" default:"true"`
	DefaultTTL      string `yaml:"default_ttl"`
	DefaultSize     int    `yaml:"default_size" validate:"min=1"`
	CleanupInterval string `yaml:"cleanup_interval"`
	MaxMemoryUsage  string `yaml:"max_memory_usage"`
	EvictionPolicy  string `yaml:"eviction_policy" validate:"oneof=lru lfu fifo random"`
}

// ModuleCacheConfig 模块缓存配置
type ModuleCacheConfig struct {
	DNS     *ModuleCacheEntry `yaml:"dns,omitempty"`
	Proxy   *ModuleCacheEntry `yaml:"proxy,omitempty"`
	Session *ModuleCacheEntry `yaml:"session,omitempty"`
	Regex   *ModuleCacheEntry `yaml:"regex,omitempty"`
	Auth    *ModuleCacheEntry `yaml:"auth,omitempty"`
	Rate    *ModuleCacheEntry `yaml:"rate,omitempty"`
}

// ModuleCacheEntry 模块缓存条目配置
type ModuleCacheEntry struct {
	Enabled         bool   `yaml:"enabled" default:"true"`
	TTL             string `yaml:"ttl"`
	Size            int    `yaml:"size" validate:"min=1"`
	CleanupInterval string `yaml:"cleanup_interval"`
	EvictionPolicy  string `yaml:"eviction_policy" validate:"oneof=lru lfu fifo random"`
	Compression     bool   `yaml:"compression"`
}

// CacheKeyPrefixConfig 缓存键前缀配置
type CacheKeyPrefixConfig struct {
	ProxyList   string `yaml:"proxy_list"`
	ProxyStatus string `yaml:"proxy_status"`
	UserSession string `yaml:"user_session"`
	RateLimit   string `yaml:"rate_limit"`
	DNSCache    string `yaml:"dns_cache"`
	RegexCache  string `yaml:"regex_cache"`
	AuthCache   string `yaml:"auth_cache"`
}

// CachePolicyConfig 缓存策略配置
type CachePolicyConfig struct {
	// 缓存预热策略
	Warmup *CacheWarmupConfig `yaml:"warmup,omitempty"`

	// 缓存失效策略
	Invalidation *CacheInvalidationConfig `yaml:"invalidation,omitempty"`

	// 缓存同步策略
	Sync *CacheSyncConfig `yaml:"sync,omitempty"`

	// 缓存压缩策略
	Compression *CacheCompressionConfig `yaml:"compression,omitempty"`
}

// CacheWarmupConfig 缓存预热配置
type CacheWarmupConfig struct {
	Enabled   bool     `yaml:"enabled"`
	OnStartup bool     `yaml:"on_startup"`
	Sources   []string `yaml:"sources"`
	BatchSize int      `yaml:"batch_size" validate:"min=1"`
}

// CacheInvalidationConfig 缓存失效配置
type CacheInvalidationConfig struct {
	Strategy string   `yaml:"strategy" validate:"oneof=ttl manual event"`
	Events   []string `yaml:"events"`
	Patterns []string `yaml:"patterns"`
}

// CacheSyncConfig 缓存同步配置
type CacheSyncConfig struct {
	Enabled  bool   `yaml:"enabled"`
	Mode     string `yaml:"mode" validate:"oneof=master slave cluster"`
	Interval string `yaml:"interval"`
}

// CacheCompressionConfig 缓存压缩配置
type CacheCompressionConfig struct {
	Enabled   bool   `yaml:"enabled"`
	Algorithm string `yaml:"algorithm" validate:"oneof=gzip zlib lz4"`
	Level     int    `yaml:"level" validate:"min=1,max=9"`
	MinSize   int    `yaml:"min_size" validate:"min=1"`
}

// CacheStorageConfig 缓存存储配置
type CacheStorageConfig struct {
	Type   string                 `yaml:"type" validate:"oneof=memory redis file hybrid"`
	Memory *MemoryCacheConfig     `yaml:"memory,omitempty"`
	Redis  *RedisCacheConfig      `yaml:"redis,omitempty"`
	File   *FileCacheConfig       `yaml:"file,omitempty"`
	Hybrid *HybridCacheConfig     `yaml:"hybrid,omitempty"`
}

// MemoryCacheConfig 内存缓存配置
type MemoryCacheConfig struct {
	MaxSize        int    `yaml:"max_size" validate:"min=1"`
	MaxMemory      string `yaml:"max_memory"`
	EvictionPolicy string `yaml:"eviction_policy" validate:"oneof=lru lfu fifo random"`
	Shards         int    `yaml:"shards" validate:"min=1"`
}

// RedisCacheConfig Redis缓存配置
type RedisCacheConfig struct {
	Address     string `yaml:"address"`
	Password    string `yaml:"password"`
	Database    int    `yaml:"database" validate:"min=0,max=15"`
	PoolSize    int    `yaml:"pool_size" validate:"min=1"`
	MaxRetries  int    `yaml:"max_retries" validate:"min=0"`
	DialTimeout string `yaml:"dial_timeout"`
}

// FileCacheConfig 文件缓存配置
type FileCacheConfig struct {
	Directory   string `yaml:"directory"`
	MaxFileSize string `yaml:"max_file_size"`
	MaxFiles    int    `yaml:"max_files" validate:"min=1"`
	Compression bool   `yaml:"compression"`
}

// HybridCacheConfig 混合缓存配置
type HybridCacheConfig struct {
	L1 string `yaml:"l1" validate:"oneof=memory"`
	L2 string `yaml:"l2" validate:"oneof=redis file"`
	L1Size int `yaml:"l1_size" validate:"min=1"`
	L2Size int `yaml:"l2_size" validate:"min=1"`
}

// ModulesConfig 统一模块管理配置
type ModulesConfig struct {
	// 启用的模块列表
	Enabled []string `yaml:"enabled,omitempty"`

	// 禁用的模块列表
	Disabled []string `yaml:"disabled,omitempty"`

	// 模块依赖关系
	Dependencies *ModuleDependenciesConfig `yaml:"dependencies,omitempty"`

	// 模块启动顺序
	StartupOrder *ModuleStartupOrderConfig `yaml:"startup_order,omitempty"`

	// 模块生命周期配置
	Lifecycle *ModuleLifecycleConfig `yaml:"lifecycle,omitempty"`

	// 模块健康检查配置
	HealthCheck *ModuleHealthCheckConfig `yaml:"health_check,omitempty"`
}

// ModuleDependenciesConfig 模块依赖关系配置
type ModuleDependenciesConfig struct {
	// 核心依赖 - 这些模块必须启用
	Core []string `yaml:"core,omitempty"`

	// 模块依赖映射
	Dependencies map[string][]string `yaml:"dependencies,omitempty"`

	// 可选依赖
	Optional map[string][]string `yaml:"optional,omitempty"`

	// 冲突模块
	Conflicts map[string][]string `yaml:"conflicts,omitempty"`
}

// ModuleStartupOrderConfig 模块启动顺序配置
type ModuleStartupOrderConfig struct {
	// 启动阶段定义
	Phases []ModulePhase `yaml:"phases,omitempty"`

	// 阶段间延迟
	PhaseDelay string `yaml:"phase_delay"`

	// 并行启动配置
	Parallel *ModuleParallelConfig `yaml:"parallel,omitempty"`
}

// ModulePhase 模块启动阶段
type ModulePhase struct {
	Name     string   `yaml:"name"`
	Modules  []string `yaml:"modules"`
	Timeout  string   `yaml:"timeout"`
	Required bool     `yaml:"required"`
}

// ModuleParallelConfig 模块并行启动配置
type ModuleParallelConfig struct {
	Enabled     bool `yaml:"enabled"`
	MaxWorkers  int  `yaml:"max_workers" validate:"min=1"`
	Timeout     string `yaml:"timeout"`
}

// ModuleLifecycleConfig 模块生命周期配置
type ModuleLifecycleConfig struct {
	// 启动超时
	StartupTimeout string `yaml:"startup_timeout"`

	// 停止超时
	ShutdownTimeout string `yaml:"shutdown_timeout"`

	// 重启策略
	RestartPolicy *ModuleRestartPolicy `yaml:"restart_policy,omitempty"`

	// 优雅停止配置
	GracefulShutdown *ModuleGracefulShutdownConfig `yaml:"graceful_shutdown,omitempty"`
}

// ModuleRestartPolicy 模块重启策略
type ModuleRestartPolicy struct {
	Enabled      bool   `yaml:"enabled"`
	MaxRetries   int    `yaml:"max_retries" validate:"min=0"`
	RetryDelay   string `yaml:"retry_delay"`
	BackoffMultiplier float64 `yaml:"backoff_multiplier" validate:"min=1"`
	MaxDelay     string `yaml:"max_delay"`
}

// ModuleGracefulShutdownConfig 模块优雅停止配置
type ModuleGracefulShutdownConfig struct {
	Enabled       bool   `yaml:"enabled"`
	Timeout       string `yaml:"timeout"`
	ForceAfter    string `yaml:"force_after"`
	WaitForConnections bool `yaml:"wait_for_connections"`
}

// ModuleHealthCheckConfig 模块健康检查配置
type ModuleHealthCheckConfig struct {
	Enabled  bool   `yaml:"enabled"`
	Interval string `yaml:"interval"`
	Timeout  string `yaml:"timeout"`

	// 健康检查端点
	Endpoints map[string]string `yaml:"endpoints,omitempty"`

	// 失败阈值
	FailureThreshold int `yaml:"failure_threshold" validate:"min=1"`

	// 成功阈值
	SuccessThreshold int `yaml:"success_threshold" validate:"min=1"`

	// 自动重启
	AutoRestart bool `yaml:"auto_restart"`
}

// DNSCacheConfig DNS缓存配置
type DNSCacheConfig struct {
	TTL             string `yaml:"ttl"`
	CleanupInterval string `yaml:"cleanup_interval"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level      string `yaml:"level" validate:"oneof=debug info warn error fatal"`
	Format     string `yaml:"format" validate:"oneof=json text"`
	File       string `yaml:"file"`
	MaxSize    int    `yaml:"max_size" validate:"min=1"`
	MaxAge     int    `yaml:"max_age" validate:"min=1"`


	MaxBackups int    `yaml:"max_backups" validate:"min=0"`
	TimeFormat string `yaml:"time_format"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Path    string            `yaml:"path"`
	Metrics map[string]string `yaml:"metrics,omitempty"`
	Labels  map[string]string `yaml:"labels,omitempty"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	Auth       *AuthConfig       `yaml:"auth,omitempty"`
	Encryption *EncryptionConfig `yaml:"encryption,omitempty"`
	TLS        *TLSConfig        `yaml:"tls,omitempty"`
}

// AuthConfig 认证配置
type AuthConfig struct {
	Type        string `yaml:"type" validate:"oneof=none basic bearer apikey"`
	TokenExpiry string `yaml:"token_expiry"`
}

// EncryptionConfig 加密配置
type EncryptionConfig struct {
	Algorithm string `yaml:"algorithm" validate:"oneof=aes256 rsa"`
	KeyLength int    `yaml:"key_length" validate:"min=16"`
}

// TLSConfig TLS配置
type TLSConfig struct {
	Enabled    bool   `yaml:"enabled"`
	CertFile   string `yaml:"cert_file"`
	KeyFile    string `yaml:"key_file"`
	MinVersion string `yaml:"min_version"`
	MaxVersion string `yaml:"max_version"`
}

// RateLimitingConfig 限流配置
type RateLimitingConfig struct {
	Algorithm     string `yaml:"algorithm" validate:"oneof=token_bucket leaky_bucket fixed_window sliding_window"`
	Rate          int    `yaml:"rate" validate:"min=1"`
	Burst         int    `yaml:"burst" validate:"min=1"`
	Window        string `yaml:"window"`
	CleanupPeriod string `yaml:"cleanup_period"`
}

// DNSServiceConfig DNS服务配置 - 统一的DNS配置
type DNSServiceConfig struct {
	// 查询配置
	LookupMode string `yaml:"lookup_mode" validate:"oneof=system custom hybrid"`
	Timeout    string `yaml:"timeout"`
	Retries    int    `yaml:"retries" validate:"min=0"`

	// 缓存配置
	Cache *DNSServiceCacheConfig `yaml:"cache,omitempty"`

	// 兼容性字段 - 从GlobalConfig迁移的DNS相关字段
	DNSLookupMode     string   `yaml:"dns_lookup_mode,omitempty"`     // DNS查询模式：local, remote
	DNSCacheTTL       int      `yaml:"dns_cache_ttl,omitempty"`       // DNS缓存TTL（秒）
	DNSNoCache        bool     `yaml:"dns_no_cache,omitempty"`        // 禁用DNS缓存
	HTTPProxyDNS      bool     `yaml:"http_proxy_dns,omitempty"`      // 是否通过HTTP代理进行DNS查询
	HTTPProxyDNSConfig string  `yaml:"http_proxy_dns_config,omitempty"` // HTTP代理DNS配置：映射格式或DNS服务器
	CustomDNSServers  []string `yaml:"custom_dns_servers,omitempty"`  // 自定义DNS服务器列表
	DefaultDNSTimeout int      `yaml:"default_dns_timeout,omitempty"` // 默认DNS超时时间（毫秒）

	// 服务器配置
	Servers *DNSServersConfig `yaml:"servers,omitempty"`

	// IP版本优先级
	IPVersionPriority string `yaml:"ip_version_priority" validate:"oneof=ipv4 ipv6 dual"`

	// 反向DNS查询配置
	ReverseLookup *DNSReverseLookupConfig `yaml:"reverse_lookup,omitempty"`
}

// DNSServiceCacheConfig DNS服务缓存配置
type DNSServiceCacheConfig struct {
	Enabled         bool   `yaml:"enabled"`
	TTL             string `yaml:"ttl"`
	CleanupInterval string `yaml:"cleanup_interval"`
	MaxSize         int    `yaml:"max_size" validate:"min=1"`
}

// DNSServersConfig DNS服务器配置
type DNSServersConfig struct {
	Primary   []DNSServerEntry `yaml:"primary,omitempty"`
	Secondary []DNSServerEntry `yaml:"secondary,omitempty"`
	Fallback  string           `yaml:"fallback" validate:"oneof=system none"`
}

// DNSServerEntry DNS服务器条目
type DNSServerEntry struct {
	Server   string   `yaml:"server" validate:"required"`
	Protocol string   `yaml:"protocol" validate:"oneof=udp tcp doh dot"`
	Timeout  int      `yaml:"timeout" validate:"min=1000,max=30000"`
	Priority int      `yaml:"priority" validate:"min=0,max=100"`
	Tags     []string `yaml:"tags,omitempty"`
}

// DNSReverseLookupConfig DNS反向查询配置
type DNSReverseLookupConfig struct {
	Enabled bool   `yaml:"enabled"`
	Mode    string `yaml:"mode" validate:"oneof=no dns file"`
	Source  string `yaml:"source,omitempty"` // 当mode为file时的文件路径
}

// TimeoutsConfig 统一超时配置
type TimeoutsConfig struct {
	// 网络超时
	Network *NetworkTimeoutsConfig `yaml:"network,omitempty"`

	// DNS超时
	DNS *DNSTimeoutsConfig `yaml:"dns,omitempty"`

	// 代理超时
	Proxy *ProxyTimeoutsConfig `yaml:"proxy,omitempty"`

	// 监控超时
	Monitoring *MonitoringTimeoutsConfig `yaml:"monitoring,omitempty"`

	// 缓存超时
	Cache *CacheTimeoutsConfig `yaml:"cache,omitempty"`

	// 动作超时
	Action *ActionTimeoutsConfig `yaml:"action,omitempty"`
}

// NetworkTimeoutsConfig 网络超时配置
type NetworkTimeoutsConfig struct {
	Connect string `yaml:"connect"`     // 连接超时
	Read    string `yaml:"read"`        // 读取超时
	Write   string `yaml:"write"`       // 写入超时
	Idle    string `yaml:"idle"`        // 空闲超时


}

// DNSTimeoutsConfig DNS超时配置
type DNSTimeoutsConfig struct {
	Query   string `yaml:"query"`   // DNS查询超时
	Resolve string `yaml:"resolve"` // DNS解析超时
}

// ProxyTimeoutsConfig 代理超时配置
type ProxyTimeoutsConfig struct {
	Retry        string `yaml:"retry"`         // 重试间隔
	MaxRetry     string `yaml:"max_retry"`     // 最大重试间隔
	HealthCheck  string `yaml:"health_check"`  // 健康检查超时
	Cooldown     string `yaml:"cooldown"`      // 代理冷却时间
}

// MonitoringTimeoutsConfig 监控超时配置
type MonitoringTimeoutsConfig struct {
	Interval   string `yaml:"interval"`    // 监控间隔
	Collection string `yaml:"collection"`  // 数据收集超时
}

// CacheTimeoutsConfig 缓存超时配置
type CacheTimeoutsConfig struct {
	Cleanup string `yaml:"cleanup"` // 缓存清理间隔
}

// ActionTimeoutsConfig 动作超时配置
type ActionTimeoutsConfig struct {
	Default string `yaml:"default"` // 默认动作超时
	Request string `yaml:"request"` // 请求动作超时
	Bypass  string `yaml:"bypass"`  // 绕过代理超时
}

// PortsConfig 统一端口配置
type PortsConfig struct {
	// 主要服务端口
	HTTP  int `yaml:"http" validate:"min=1,max=65535"`   // HTTP代理端口
	HTTPS int `yaml:"https" validate:"min=1,max=65535"` // HTTPS代理端口
	SOCKS int `yaml:"socks" validate:"min=1,max=65535"` // SOCKS代理端口

	// 管理和监控端口
	Monitoring int `yaml:"monitoring" validate:"min=1,max=65535"` // 监控端口
	Debug      int `yaml:"debug" validate:"min=1,max=65535"`      // 调试端口
	Admin      int `yaml:"admin" validate:"min=1,max=65535"`      // 管理端口

	// 协议特定端口
	DNS  int `yaml:"dns" validate:"min=1,max=65535"`  // DNS服务端口
	DHCP int `yaml:"dhcp" validate:"min=1,max=65535"` // DHCP服务端口

	// 端口范围配置
	Ranges *PortRangesConfig `yaml:"ranges,omitempty"`

	// 端口冲突检测
	ConflictDetection *PortConflictConfig `yaml:"conflict_detection,omitempty"`
}

// PortRangesConfig 端口范围配置
type PortRangesConfig struct {
	// 动态端口分配范围
	DynamicStart int `yaml:"dynamic_start" validate:"min=1024,max=65535"` // 动态端口起始
	DynamicEnd   int `yaml:"dynamic_end" validate:"min=1024,max=65535"`   // 动态端口结束

	// 保留端口范围
	ReservedStart int `yaml:"reserved_start" validate:"min=1,max=1023"`   // 保留端口起始
	ReservedEnd   int `yaml:"reserved_end" validate:"min=1,max=1023"`     // 保留端口结束

	// 用户端口范围
	UserStart int `yaml:"user_start" validate:"min=1024,max=65535"` // 用户端口起始
	UserEnd   int `yaml:"user_end" validate:"min=1024,max=65535"`   // 用户端口结束
}

// PortConflictConfig 端口冲突检测配置
type PortConflictConfig struct {
	Enabled    bool     `yaml:"enabled"`              // 是否启用冲突检测
	CheckLocal bool     `yaml:"check_local"`          // 检查本地端口占用
	ExcludePorts []int  `yaml:"exclude_ports"`        // 排除检测的端口
	AutoResolve  bool   `yaml:"auto_resolve"`         // 自动解决冲突
	FallbackPorts []int `yaml:"fallback_ports"`       // 备用端口列表
}

// AdvancedConfig 高级配置
type AdvancedConfig struct {
	Enabled       bool                 `yaml:"enabled" default:"false"`
	ErrorRecovery *ErrorRecoveryConfig `yaml:"error_recovery,omitempty"`
	Tracing       *TracingConfig       `yaml:"tracing,omitempty"`
	Performance   *PerformanceConfig   `yaml:"performance,omitempty"`
	Debug         *DebugConfig         `yaml:"debug,omitempty"`
}

// ErrorRecoveryConfig 错误恢复配置
type ErrorRecoveryConfig struct {
	MaxRetryAttempts    int     `yaml:"max_retry_attempts" validate:"min=0"`
	InitialRetryDelay   string  `yaml:"initial_retry_delay"`
	MaxRetryDelay       string  `yaml:"max_retry_delay"`
	RetryMultiplier     float64 `yaml:"retry_multiplier" validate:"min=1"`
	FailureThreshold    int     `yaml:"failure_threshold" validate:"min=1"`
	SuccessThreshold    int     `yaml:"success_threshold" validate:"min=1"`
	CircuitTimeout      string  `yaml:"circuit_timeout"`
	CircuitResetTimeout string  `yaml:"circuit_reset_timeout"`
}

// TracingConfig 追踪配置
type TracingConfig struct {
	Enabled            bool `yaml:"enabled"`
	HexGeneratorLength int  `yaml:"hex_generator_length" validate:"min=8"`
	SequenceModulus    int  `yaml:"sequence_modulus" validate:"min=1000"`
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	WorkerPoolSize int    `yaml:"worker_pool_size" validate:"min=1"`
	QueueSize      int    `yaml:"queue_size" validate:"min=1"`
	BatchSize      int    `yaml:"batch_size" validate:"min=1"`
	FlushInterval  string `yaml:"flush_interval"`
}

// DebugConfig 调试配置
type DebugConfig struct {
	Enabled        bool `yaml:"enabled"`
	VerboseLogging bool `yaml:"verbose_logging"`
	DumpRequests   bool `yaml:"dump_requests"`
	DumpResponses  bool `yaml:"dump_responses"`
	ProfileEnabled bool `yaml:"profile_enabled"`
}

// PathsConfig 路径配置
type PathsConfig struct {
	BaseDir        string            `yaml:"base_dir"`
	ConfigDir      string            `yaml:"config_dir"`
	LogsDir        string            `yaml:"logs_dir"`
	DataDir        string            `yaml:"data_dir"`
	TempDir        string            `yaml:"temp_dir"`
	BackupDir      string            `yaml:"backup_dir"`
	FilePermission int               `yaml:"file_permission"`
	DirPermission  int               `yaml:"dir_permission"`
	Extensions     map[string]string `yaml:"extensions,omitempty"`
}

// SystemConfig 系统配置
type SystemConfig struct {
	OSDetection    bool                  `yaml:"os_detection"`
	ArchDetection  bool                  `yaml:"arch_detection"`
	SignalHandling *SignalHandlingConfig `yaml:"signal_handling,omitempty"`
	Limits         *ResourceLimitsConfig `yaml:"limits,omitempty"`
}

// SignalHandlingConfig 信号处理配置
type SignalHandlingConfig struct {
	GracefulShutdown bool     `yaml:"graceful_shutdown"`
	ShutdownTimeout  string   `yaml:"shutdown_timeout"`
	Signals          []string `yaml:"signals,omitempty"`
}

// ResourceLimitsConfig 资源限制配置
type ResourceLimitsConfig struct {
	MaxMemory          string `yaml:"max_memory"`
	MaxCPUPercent      int    `yaml:"max_cpu_percent" validate:"min=1,max=100"`
	MaxFileDescriptors int    `yaml:"max_file_descriptors" validate:"min=1"`
	MaxGoroutines      int    `yaml:"max_goroutines" validate:"min=1"`
}

// ProtocolsConfig 协议配置
type ProtocolsConfig struct {
	HTTP   *HTTPConfig        `yaml:"http,omitempty"`
	HTTPS  *HTTPSConfig       `yaml:"https,omitempty"`
	SOCKS4 *SOCKS4Config      `yaml:"socks4,omitempty"`
	SOCKS5 *SOCKS5Config      `yaml:"socks5,omitempty"`
	DNS    *DNSProtocolConfig `yaml:"dns,omitempty"`
}

// HTTPConfig HTTP协议配置
type HTTPConfig struct {
	Enabled     bool   `yaml:"enabled"`
	Version     string `yaml:"version" validate:"oneof=1.0 1.1 2.0"`
	KeepAlive   bool   `yaml:"keep_alive"`
	Compression bool   `yaml:"compression"`
}

// HTTPSConfig HTTPS协议配置
type HTTPSConfig struct {
	Enabled     bool   `yaml:"enabled"`
	Version     string `yaml:"version" validate:"oneof=1.0 1.1 2.0"`
	KeepAlive   bool   `yaml:"keep_alive"`
	Compression bool   `yaml:"compression"`
	VerifySSL   bool   `yaml:"verify_ssl"`
}

// SOCKS4Config SOCKS4协议配置
type SOCKS4Config struct {
	Enabled bool `yaml:"enabled"`
}

// SOCKS5Config SOCKS5协议配置
type SOCKS5Config struct {
	Enabled      bool `yaml:"enabled"`
	AuthRequired bool `yaml:"auth_required"`
}

// DNSProtocolConfig DNS协议配置
type DNSProtocolConfig struct {
	UDP   bool `yaml:"udp"`
	TCP   bool `yaml:"tcp"`
	TLS   bool `yaml:"tls"`
	HTTPS bool `yaml:"https"`
	DOH   bool `yaml:"doh"`
}

// PluginsConfig 插件配置
type PluginsConfig struct {
	Enabled   bool           `yaml:"enabled"`
	Directory string         `yaml:"directory"`
	AutoLoad  bool           `yaml:"auto_load"`
	Available []PluginConfig `yaml:"available,omitempty"`
}

// PluginConfig 单个插件配置
type PluginConfig struct {
	Name    string                 `yaml:"name"`
	Enabled bool                   `yaml:"enabled"`
	Config  map[string]interface{} `yaml:"config,omitempty"`
}

// DevelopmentConfig 开发配置
type DevelopmentConfig struct {
	Enabled   bool             `yaml:"enabled" default:"false"`
	Mode      string           `yaml:"mode" validate:"oneof=development testing production"`
	HotReload bool             `yaml:"hot_reload"`
	Testing   *TestingConfig   `yaml:"testing,omitempty"`
	Profiling *ProfilingConfig `yaml:"profiling,omitempty"`
}

// TestingConfig 测试配置
type TestingConfig struct {
	Enabled       bool   `yaml:"enabled"`
	MockResponses bool   `yaml:"mock_responses"`
	TestDataDir   string `yaml:"test_data_dir"`
}

// ProfilingConfig 性能分析配置
type ProfilingConfig struct {
	Enabled       bool `yaml:"enabled"`
	CPUProfile    bool `yaml:"cpu_profile"`
	MemoryProfile bool `yaml:"memory_profile"`
	BlockProfile  bool `yaml:"block_profile"`
	MutexProfile  bool `yaml:"mutex_profile"`
}

// CompressionConfig 压缩配置
type CompressionConfig struct {
	Enabled    bool     `yaml:"enabled"`
	Algorithms []string `yaml:"algorithms,omitempty" validate:"dive,oneof=gzip deflate br"`
	Level      int      `yaml:"level" validate:"min=-1,max=9"`
	MinSize    int      `yaml:"min_size" validate:"min=0"`
}

// ProxyQualityConfig 代理质量配置
type ProxyQualityConfig struct {
	Enable                bool          `yaml:"enable" json:"enable"`
	MinSuccessRate        float64       `yaml:"min_success_rate" json:"min_success_rate"`           // 最小成功率
	MaxResponseTime       time.Duration `yaml:"max_response_time" json:"max_response_time"`         // 最大响应时间
	BlacklistThreshold    int           `yaml:"blacklist_threshold" json:"blacklist_threshold"`     // 黑名单阈值
	BlacklistDuration     time.Duration `yaml:"blacklist_duration" json:"blacklist_duration"`       // 黑名单持续时间
	QualityCheckInterval  time.Duration `yaml:"quality_check_interval" json:"quality_check_interval"` // 质量检查间隔
	StatsRetentionPeriod  time.Duration `yaml:"stats_retention_period" json:"stats_retention_period"` // 统计数据保留期
	AutoRemoveBadProxies  bool          `yaml:"auto_remove_bad_proxies" json:"auto_remove_bad_proxies"` // 自动移除劣质代理
}

// StatisticsConfig 统计配置
type StatisticsConfig struct {
	Enable              bool          `yaml:"enable" json:"enable"`
	CollectionInterval  time.Duration `yaml:"collection_interval" json:"collection_interval"`   // 统计收集间隔
	RetentionPeriod     time.Duration `yaml:"retention_period" json:"retention_period"`         // 数据保留期
	MaxHistoryEntries   int           `yaml:"max_history_entries" json:"max_history_entries"`   // 最大历史条目数
	CompressOldData     bool          `yaml:"compress_old_data" json:"compress_old_data"`       // 压缩旧数据
	EnableRequestStats  bool          `yaml:"enable_request_stats" json:"enable_request_stats"` // 启用请求统计
	EnableProxyStats    bool          `yaml:"enable_proxy_stats" json:"enable_proxy_stats"`     // 启用代理统计
	EnablePerformanceStats bool       `yaml:"enable_performance_stats" json:"enable_performance_stats"` // 启用性能统计
	EnableErrorStats    bool          `yaml:"enable_error_stats" json:"enable_error_stats"`     // 启用错误统计
}

// HTTP2Config HTTP/2配置
type HTTP2Config struct {
	Enabled              bool   `yaml:"enabled"`
	MaxConcurrentStreams uint32 `yaml:"max_concurrent_streams" validate:"min=1"`
	MaxFrameSize         uint32 `yaml:"max_frame_size" validate:"min=16384,max=16777215"`
	InitialWindowSize    uint32 `yaml:"initial_window_size" validate:"min=65535"`
	ServerPush           bool   `yaml:"server_push"`
}

// ConfigManagementConfig 配置管理配置
type ConfigManagementConfig struct {
	HotReload  *HotReloadConfig  `yaml:"hot_reload,omitempty"`
	Validation *ValidationConfig `yaml:"validation,omitempty"`
	Backup     *BackupConfig     `yaml:"backup,omitempty"`
}

// HotReloadConfig 热重载配置
type HotReloadConfig struct {
	Enabled          bool                 `yaml:"enabled"`
	WatchFiles       []string             `yaml:"watch_files,omitempty"`
	ReloadInterval   string               `yaml:"reload_interval" validate:"required"`
	BackupOnReload   bool                 `yaml:"backup_on_reload"`
	RollbackOnError  bool                 `yaml:"rollback_on_error"`
	DebounceDelay    string               `yaml:"debounce_delay"`
	MaxReloadRetries int                  `yaml:"max_reload_retries"`
	RiskAssessment   *RiskAssessmentConfig `yaml:"risk_assessment,omitempty"`
	UnattendedMode   *UnattendedModeConfig `yaml:"unattended_mode,omitempty"`
}

// ValidationConfig 验证配置
type ValidationConfig struct {
	StrictMode        bool `yaml:"strict_mode"`
	FailFast          bool `yaml:"fail_fast"`
	ValidateOnStartup bool `yaml:"validate_on_startup"`
	ValidateOnReload  bool `yaml:"validate_on_reload"`
	ValidateOnSave    bool `yaml:"validate_on_save"`
}

// BackupConfig 备份配置
type BackupConfig struct {
	Enabled       bool   `yaml:"enabled"`
	BackupDir     string `yaml:"backup_dir"`
	MaxBackups    int    `yaml:"max_backups" validate:"min=1"`
	Compress      bool   `yaml:"compress"`
	AutoCleanup   bool   `yaml:"auto_cleanup"`
	RetentionDays int    `yaml:"retention_days"`
}

// RiskAssessmentConfig 风险评估配置
type RiskAssessmentConfig struct {
	// 数量变更阈值
	EventCountMultiplier  float64 `yaml:"event_count_multiplier" default:"5.0"`   // 事件数量倍数阈值，默认5倍
	ActionCountMultiplier float64 `yaml:"action_count_multiplier" default:"5.0"` // 动作数量倍数阈值，默认5倍

	// 数据大小阈值
	DataSizeThreshold string `yaml:"data_size_threshold" default:"1GB"` // 数据大小阈值，默认1GB

	// 服务影响阈值
	MaxDirectlyAffectedServices   int `yaml:"max_directly_affected_services" default:"3"`   // 最大直接影响服务数
	MaxIndirectlyAffectedServices int `yaml:"max_indirectly_affected_services" default:"5"` // 最大间接影响服务数

	// 安全评分阈值
	MinSecurityScore        int `yaml:"min_security_score" default:"60"`        // 最低安全评分
	SecurityWarningScore    int `yaml:"security_warning_score" default:"80"`    // 安全警告评分

	// 性能影响阈值
	MaxPerformanceImpact    int `yaml:"max_performance_impact" default:"30"`    // 最大性能影响百分比

	// 自定义风险规则
	CustomRiskRules []CustomRiskRule `yaml:"custom_risk_rules,omitempty"` // 自定义风险规则列表
}

// CustomRiskRule 自定义风险规则
type CustomRiskRule struct {
	RuleID      string   `yaml:"rule_id"`                                    // 规则ID
	Name        string   `yaml:"name"`                                       // 规则名称
	Pattern     string   `yaml:"pattern"`                                    // 匹配模式
	RiskLevel   string   `yaml:"risk_level" validate:"oneof=low medium high"` // 风险等级
	AutoReload  bool     `yaml:"auto_reload"`                                // 是否允许自动重载
	Conditions  []string `yaml:"conditions,omitempty"`                       // 触发条件
	Description string   `yaml:"description,omitempty"`                      // 规则描述
}

// UnattendedModeConfig 无人值守模式配置
type UnattendedModeConfig struct {
	Enabled                bool   `yaml:"enabled" default:"false"`                           // 是否启用无人值守模式
	AutoApproveThreshold   string `yaml:"auto_approve_threshold" default:"medium"`           // 自动批准阈值 (low/medium/high)
	ManualConfirmTimeout   string `yaml:"manual_confirm_timeout" default:"300s"`             // 手动确认超时时间
	FallbackStrategy       string `yaml:"fallback_strategy" default:"auto_approve"`          // 降级策略
	MaxWaitTime            string `yaml:"max_wait_time" default:"5m"`                        // 最大等待时间
	EmergencyRollback      bool   `yaml:"emergency_rollback" default:"true"`                 // 紧急回滚开关
	BusinessHoursOnly      bool   `yaml:"business_hours_only" default:"false"`               // 仅在业务时间启用
	BusinessHoursStart     string `yaml:"business_hours_start" default:"09:00"`              // 业务时间开始
	BusinessHoursEnd       string `yaml:"business_hours_end" default:"18:00"`                // 业务时间结束
	BusinessHoursTimezone  string `yaml:"business_hours_timezone" default:"Local"`           // 业务时间时区
	NotificationWebhook    string `yaml:"notification_webhook,omitempty"`                    // 通知webhook地址
	NotificationRetries    int    `yaml:"notification_retries" default:"3"`                  // 通知重试次数
}

// ProxyCheckConfig 代理检查配置
type ProxyCheckConfig struct {
	Enabled       bool     `yaml:"enabled"`        // 是否启用代理检查，对应 -c, --check
	MaxGoroutines int      `yaml:"max_goroutines" validate:"min=1"` // 最大协程数，对应 -g, --goroutine
	CountryCodes  []string `yaml:"country_codes"`  // 仅检查特定国家代码，对应 --only-cc
}
