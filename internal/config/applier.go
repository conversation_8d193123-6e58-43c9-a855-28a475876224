package config

import (
	"fmt"
	"flexproxy/common/logger"
	"flexproxy/internal/config/defaults"
)

// ConfigApplier 配置应用器，根据配置创建相应的服务实例
type ConfigApplier struct {
	config  *Config
	checker *ConfigChecker
	logger  logger.Logger
}

// NewConfigApplier 创建配置应用器
func NewConfigApplier(config *Config) *ConfigApplier {
	return &ConfigApplier{
		config:  config,
		checker: NewConfigChecker(config),
		logger:  logger.GetLogger("config-applier"),
	}
}

// ServiceCreationConfig 服务创建配置
type ServiceCreationConfig struct {
	CacheEnabled       bool
	LoggingEnabled     bool
	SecurityEnabled    bool
	AdvancedEnabled    bool
	DevelopmentEnabled bool
}

// GetServiceCreationConfig 获取服务创建配置
func (ca *ConfigApplier) GetServiceCreationConfig() *ServiceCreationConfig {
	return &ServiceCreationConfig{
		CacheEnabled:       ca.checker.IsCacheEnabled(),
		LoggingEnabled:     ca.checker.IsLoggingEnabled(),
		SecurityEnabled:    ca.checker.IsSecurityEnabled(),
		AdvancedEnabled:    ca.checker.IsAdvancedEnabled(),
		DevelopmentEnabled: ca.checker.IsDevelopmentEnabled(),
	}
}

// GetSecurityConfig 获取安全配置
func (ca *ConfigApplier) GetSecurityConfig() *SecurityConfig {
	if ca.config.Security != nil {
		return ca.config.Security
	}

	// 创建默认安全配置并应用默认值
	defaultConfig := &SecurityConfig{
		Auth: &AuthConfig{
			Type:        config.GetDefaultString("security.auth.type", "none"),
			TokenExpiry: config.GetDefaultString("security.auth.token_expiry", "24h"),
		},
		Encryption: &EncryptionConfig{
			Algorithm: config.GetDefaultString("security.encryption.algorithm", "aes256"),
			KeyLength: config.GetDefaultInt("security.encryption.key_length", 32),
		},
	}
	return defaultConfig
}

// GetAdvancedConfig 获取高级配置
func (ca *ConfigApplier) GetAdvancedConfig() *AdvancedConfig {
	if ca.config.Advanced != nil {
		return ca.config.Advanced
	}

	// 创建默认高级配置并应用默认值
	defaultConfig := &AdvancedConfig{
		Enabled: config.GetDefaultBool("advanced.enabled", false),
		ErrorRecovery: &ErrorRecoveryConfig{
			MaxRetryAttempts:    config.GetDefaultInt("advanced.error_recovery.max_retry_attempts", 3),
			InitialRetryDelay:   config.GetDefaultString("advanced.error_recovery.initial_retry_delay", "1s"),
			MaxRetryDelay:       config.GetDefaultString("advanced.error_recovery.max_retry_delay", "30s"),
			RetryMultiplier:     2.0, // 这个值在constants中是float64类型，暂时保持硬编码
			FailureThreshold:    config.GetDefaultInt("advanced.error_recovery.failure_threshold", 5),
			SuccessThreshold:    config.GetDefaultInt("advanced.error_recovery.success_threshold", 3),
			CircuitTimeout:      config.GetDefaultString("advanced.error_recovery.circuit_timeout", "60s"),
			CircuitResetTimeout: config.GetDefaultString("advanced.error_recovery.circuit_reset_timeout", "300s"),
		},
	}
	return defaultConfig
}

// GetDevelopmentConfig 获取开发配置
func (ca *ConfigApplier) GetDevelopmentConfig() *DevelopmentConfig {
	if ca.config.Development != nil {
		return ca.config.Development
	}

	// 创建默认开发配置并应用默认值
	defaultConfig := &DevelopmentConfig{
		Enabled:   config.GetDefaultBool("development.enabled", false),
		Mode:      config.GetDefaultString("development.mode", "production"),
		HotReload: config.GetDefaultBool("development.hot_reload", false),
		Testing: &TestingConfig{
			Enabled:       config.GetDefaultBool("development.testing.enabled", false),
			MockResponses: config.GetDefaultBool("development.testing.mock_responses", false),
			TestDataDir:   config.GetDefaultString("development.testing.test_data_dir", "./testdata"),
		},
		Profiling: &ProfilingConfig{
			Enabled:       config.GetDefaultBool("development.profiling.enabled", false),
			CPUProfile:    config.GetDefaultBool("development.profiling.cpu_profile", false),
			MemoryProfile: config.GetDefaultBool("development.profiling.memory_profile", false),
			BlockProfile:  config.GetDefaultBool("development.profiling.block_profile", false),
			MutexProfile:  config.GetDefaultBool("development.profiling.mutex_profile", false),
		},
	}
	return defaultConfig
}

// GetProxyConfig 获取代理配置
func (ca *ConfigApplier) GetProxyConfig() *ProxyConfig {
	if ca.config.Proxy != nil {
		return ca.config.Proxy
	}

	// 创建默认代理配置并应用默认值
	defaultConfig := &ProxyConfig{
		Strategy:   config.GetDefaultString("proxy.strategy", "random"),
		MaxRetries: config.GetDefaultInt("proxy.max_retries", 3),
		PoolSize:   config.GetDefaultInt("proxy.pool_size", 10),
	}
	return defaultConfig
}

// ValidateProxyConfiguration 验证代理配置
func (ca *ConfigApplier) ValidateProxyConfiguration() error {
	if !ca.checker.IsProxyEnabled() {
		ca.logger.Warn("代理功能已禁用 - 这可能影响核心功能")
		return fmt.Errorf("代理功能已禁用")
	}

	ca.logger.Info("代理配置验证通过")
	return nil
}

// GetEnabledFeaturesSummary 获取启用功能的摘要
func (ca *ConfigApplier) GetEnabledFeaturesSummary() map[string]bool {
	return map[string]bool{
		"cache":        ca.checker.IsCacheEnabled(),
		"logging":      ca.checker.IsLoggingEnabled(),
		"security":     ca.checker.IsSecurityEnabled(),
		"proxy":        ca.checker.IsProxyEnabled(),
		"advanced":     ca.checker.IsAdvancedEnabled(),
		"development":  ca.checker.IsDevelopmentEnabled(),
		"monitoring":   ca.checker.IsMonitoringEnabled(),
		"rate_limiting": ca.checker.IsRateLimitingEnabled(),
		"dns_service":  ca.checker.IsDNSServiceEnabled(),
	}
}
