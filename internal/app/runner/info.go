package runner

import (
	"fmt"
	"os"

	"flexproxy/common"
)

// 向stderr显示横幅
func showBanner() {
	fmt.Fprintf(os.Stderr, "%s\n\n", common.Banner)
}

// 向stderr显示使用说明
func showUsage() {
	fmt.Fprint(os.Stderr, "Usage:", common.Usage)
}

// 显示版本并退出
func showVersion() {
	version := common.Version
	if version == "" {
		version = "unknown (go-get)"
	}

	fmt.Println(common.App, "version", version)
	os.Exit(1)
}
