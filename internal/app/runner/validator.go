package runner

import (
	"os"
	"path/filepath"
	"strings"

	"flexproxy/pkg/flexproxy"

	"flexproxy/common/errors"
	"flexproxy/internal/config"
	"flexproxy/internal/core/proxy/manager"
)

// validate user-supplied option values before Runner.
func validate(opt *config.Options) error {
	var err error

	// if hasStdin() {
	// 	tmp, err := os.CreateTemp("", "flexproxy-stdin-*")
	// 	if err != nil {
	// 		return err
	// 	}
	// 	defer tmp.Close()

	// 	data, err := io.ReadAll(os.Stdin)
	// 	if err != nil {
	// 		return err
	// 	}

	// 	if _, err := tmp.Write(data); err != nil {
	// 		return err
	// 	}

	// 	opt.File = tmp.Name()

	// 	defer os.Remove(opt.File)
	// }

	if opt.File == "" {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
			errors.ErrConfigFieldRequired.Message, "未提供代理文件")
	}

	// 服务器模式下的配置要求
	if opt.Address != "" || opt.Check == false {
		// 如果指定了监听地址但没有配置文件，要求配置文件
		if opt.ConfigFile == "" && opt.Address != "" {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
				errors.ErrConfigFieldRequired.Message, "服务器模式下未指定配置文件")
		}

		// 如果没有指定监听地址，必须有配置文件来解析默认地址
		if opt.Address == "" && opt.ConfigFile == "" && !opt.Check {
			return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
				errors.ErrConfigFieldRequired.Message, "未指定监听地址且未提供配置文件")
		}
	}
	// if opt.RuleConfig == nil {
	// 	return errors.New("配置文件未加载成功")
	// }

	opt.File, err = filepath.Abs(opt.File)
	if err != nil {
		return err
	}

	// 获取服务器类型
	flexproxy.ServerType = opt.Type
	opt.ProxyManager, err = manager.New(opt.File, opt.RuleConfig)
	if err != nil {
		return err
	}

	if opt.Address != "" && !opt.Check {
		if opt.Auth != "" {
			auth := strings.SplitN(opt.Auth, ":", 2)
			if len(auth) != 2 {
				return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeConfigAuthIncomplete,
					errors.ErrConfigAuthIncomplete.Message, "代理认证格式应为 username:password")
			}
		}
	}

	if opt.CC != "" {
		opt.Countries = strings.Split(opt.CC, ",")
	}

	if opt.Output != "" {
		opt.Output, err = filepath.Abs(opt.Output)
		if err != nil {
			return err
		}

		opt.Result, err = os.OpenFile(opt.Output, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0644)
		if err != nil {
			return err
		}
	}

	return nil
}
