package runner

import (
	"strings"
	"time"

	"flexproxy/common/errors"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
	"flexproxy/internal/config/hotreload"
	"flexproxy/internal/checker"
	"flexproxy/internal/app/daemon"
	"flexproxy/internal/core/server"
)

// 模块级别的日志器
var runnerLogger = logger.GetRunnerLogger()

// 全局热重载集成适配器
var hotReloadIntegration *hotreload.HotReloadIntegration

// New 切换操作，决定是检查还是运行代理服务器。
func New(opt *config.Options) error {
	// 加载配置文件（如果指定）
	if opt.ConfigFile != "" {
		runnerLogger.GetRawLogger().Infof("开始验证配置文件: %s", opt.ConfigFile)

		// 加载配置文件（内部包含验证）
		cfg, err := config.LoadConfigFromYAML(opt.ConfigFile)
		if err != nil {
			runnerLogger.GetRawLogger().Errorf("配置文件验证失败: %v", err)
			runnerLogger.GetRawLogger().Errorf("程序无法启动，请检查配置文件并修复以上错误")
			return err // 立即返回错误，不继续执行
		}

		// 创建配置检查器并记录启用的功能
		configChecker := config.NewConfigChecker(cfg)
		configChecker.LogEnabledFeatures()

		// 验证启用的功能配置
		if err := configChecker.ValidateEnabledFeatures(); err != nil {
			runnerLogger.GetRawLogger().Warnf("配置验证警告: %v", err)
		}

		runnerLogger.GetRawLogger().Infof("配置文件验证通过")

		opt.RuleConfig = cfg
		runnerLogger.GetRawLogger().Infof("已从 %s 加载规则配置", opt.ConfigFile)

		// 初始化热重载集成适配器
		if err := initializeHotReload(cfg, opt.ConfigFile); err != nil {
			runnerLogger.GetRawLogger().Warnf("热重载系统初始化失败: %v", err)
			// 不阻止程序启动，热重载是可选功能
		}

		// 合并命令行参数与配置文件，确保命令行参数优先级更高
		if opt.RuleConfig != nil {
			runnerLogger.GetRawLogger().Debugf("配置文件加载后 opt.RuleConfig.Global.Enable 的值为: %v", opt.RuleConfig.Global.Enable)

			// 执行配置优先级合并
			mergeCommandLineWithConfig(opt, opt.RuleConfig)

			runnerLogger.GetRawLogger().Infof("配置合并完成，最终的代理文件路径: %s", opt.File)
		} else {
			runnerLogger.Debug("配置文件加载后 opt.RuleConfig 为 nil")
		}
	} else {
		runnerLogger.Info("未指定配置文件，将使用默认行为。")
	}

	// 无论是否有配置文件，都需要进行基本验证以初始化 ProxyManager
	if err := validate(opt); err != nil {
		return err
	}

	// 解析监听地址（处理配置文件端口配置优先级）
	if opt.RuleConfig != nil {
		addressResolver := config.NewAddressResolver(opt.RuleConfig)

		// 记录端口配置信息
		addressResolver.LogPortConfiguration()

		// 解析监听地址
		resolvedAddress, err := addressResolver.ResolveListenAddress(opt.Address, opt.Type)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeConfig, errors.ErrCodeConfigValidationFailed, "监听地址解析失败")
		}

		// 更新 opt.Address
		if opt.Address == "" {
			opt.Address = resolvedAddress
			runnerLogger.GetRawLogger().Infof("从配置文件解析监听地址: %s", opt.Address)
		}
	}

	// 在传递给 server.Run 之前再次检查
	if opt.RuleConfig != nil {
		runnerLogger.GetRawLogger().Debugf("传递给 server.Run 之前 opt.RuleConfig.Global.Enable 的值为: %v", opt.RuleConfig.Global.Enable)
	} else {
		runnerLogger.Debug("传递给 server.Run 之前 opt.RuleConfig 为 nil")
	}

	if opt.Address != "" {
		if opt.Daemon {
			return daemon.New(opt)
		}

		server.Run(opt)
	} else if opt.Check {
		checker.Do(opt)

		if opt.Output != "" {
			defer opt.Result.Close()
		}
	} else {
		return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "没有可执行的操作")
	}

	return nil
}

// initializeHotReload 初始化热重载集成适配器
func initializeHotReload(cfg *config.Config, configFile string) error {
	runnerLogger.GetRawLogger().Info("初始化热重载集成适配器")

	// 创建配置管理器（如果不存在）
	configManagerOptions := config.ConfigManagerOptions{
		MaxBackups:     10,
		BackupDir:      "./config_backups",
		EnableAutoSave: true,
		SaveInterval:   300, // 5分钟
	}
	configManager := config.NewConfigManager(configManagerOptions)

	// 创建配置服务（可选，用于与现有配置系统集成）
	// configService := basic.NewConfigService(nil)

	// 创建热重载集成适配器
	integration, err := hotreload.NewHotReloadIntegration(
		cfg,
		configManager,
		nil, // 配置服务暂时为nil，如需集成现有ConfigService可传入configService
		nil, // 使用默认logger
		nil, // 使用默认选项
	)
	if err != nil {
		return errors.WrapError(
			err,
			errors.ErrTypeConfig,
			errors.ErrCodeConfigInitializationFailed,
			"热重载集成适配器创建失败",
		)
	}

	// 设置配置文件路径
	integration.SetConfigFile(configFile)

	// 启动热重载系统
	if err := integration.Start(); err != nil {
		return errors.WrapError(
			err,
			errors.ErrTypeSystem,
			errors.ErrCodeSystemStartFailed,
			"热重载集成适配器启动失败",
		)
	}

	// 保存全局引用
	hotReloadIntegration = integration

	runnerLogger.GetRawLogger().Info("热重载集成适配器初始化成功")
	return nil
}

// GetHotReloadIntegration 获取热重载集成适配器（供其他模块使用）
func GetHotReloadIntegration() *hotreload.HotReloadIntegration {
	return hotReloadIntegration
}

// StopHotReload 停止热重载系统（程序退出时调用）
func StopHotReload() {
	if hotReloadIntegration != nil {
		if err := hotReloadIntegration.Stop(); err != nil {
			runnerLogger.GetRawLogger().Warnf("热重载系统停止失败: %v", err)
		}
		hotReloadIntegration = nil
		runnerLogger.GetRawLogger().Info("热重载系统已停止")
	}
}

// mergeCommandLineWithConfig 合并命令行参数与配置文件，确保命令行参数优先级更高
func mergeCommandLineWithConfig(opt *config.Options, cfg *config.Config) {
	runnerLogger.GetRawLogger().Debug("开始合并命令行参数与配置文件")

	// 1. 代理文件路径：命令行 > 配置文件
	if opt.File == "" && cfg.Global.ProxyFile != "" {
		opt.File = cfg.Global.ProxyFile
		runnerLogger.GetRawLogger().Infof("使用配置文件中的代理文件: %s", opt.File)
	}

	// 2. 详细模式：命令行 > 配置文件
	if !opt.Verbose && cfg.Global.Verbose {
		opt.Verbose = cfg.Global.Verbose
		runnerLogger.GetRawLogger().Debug("使用配置文件中的详细模式设置")
	}

	// 3. 最大重试次数：命令行 > 配置文件
	if opt.MaxRetries == 0 && cfg.Global.MaxRetries > 0 {
		opt.MaxRetries = cfg.Global.MaxRetries
		runnerLogger.GetRawLogger().Infof("使用配置文件中的最大重试次数: %d", opt.MaxRetries)
	}

	// 4. 守护进程模式：命令行 > 配置文件
	if !opt.Daemon && cfg.Global.Daemon {
		opt.Daemon = cfg.Global.Daemon
		runnerLogger.GetRawLogger().Debug("使用配置文件中的守护进程模式设置")
	}

	// 5. 同步模式：命令行 > 配置文件
	if !opt.Sync && cfg.Global.Sync {
		opt.Sync = cfg.Global.Sync
		runnerLogger.GetRawLogger().Debug("使用配置文件中的同步模式设置")
	}

	// 6. 文件监控：命令行 > 配置文件
	if !opt.Watch && cfg.Global.Watch {
		opt.Watch = cfg.Global.Watch
		runnerLogger.GetRawLogger().Debug("使用配置文件中的文件监控设置")
	}

	// 7. 服务器认证：命令行 > 配置文件
	if opt.Auth == "" && cfg.Server != nil && cfg.Server.Auth != "" {
		opt.Auth = cfg.Server.Auth
		runnerLogger.GetRawLogger().Debug("使用配置文件中的服务器认证设置")
	}

	// 8. 代理服务器类型：命令行 > 配置文件
	if opt.Type == "http" && cfg.Server != nil && cfg.Server.Type != "" {
		opt.Type = cfg.Server.Type
		runnerLogger.GetRawLogger().Infof("使用配置文件中的代理服务器类型: %s", opt.Type)
	}

	// 9. 日志输出文件：命令行 > 配置文件
	if opt.Output == "" && cfg.Logging != nil && cfg.Logging.OutputFile != "" {
		opt.Output = cfg.Logging.OutputFile
		runnerLogger.GetRawLogger().Infof("使用配置文件中的日志输出文件: %s", opt.Output)
	}

	// 10. 连接超时：命令行 > 配置文件
	if opt.Timeout == 30*time.Second && cfg.Timeouts != nil && cfg.Timeouts.Network != nil && cfg.Timeouts.Network.ConnectionTimeout != "" {
		if timeout, err := time.ParseDuration(cfg.Timeouts.Network.ConnectionTimeout); err == nil {
			opt.Timeout = timeout
			runnerLogger.GetRawLogger().Infof("使用配置文件中的连接超时: %s", opt.Timeout)
		} else {
			runnerLogger.GetRawLogger().Warnf("配置文件中的连接超时格式无效: %s", cfg.Timeouts.Network.ConnectionTimeout)
		}
	}

	// 11. 代理检查相关：命令行 > 配置文件
	if !opt.Check && cfg.ProxyCheck != nil && cfg.ProxyCheck.Enabled {
		opt.Check = cfg.ProxyCheck.Enabled
		runnerLogger.GetRawLogger().Debug("使用配置文件中的代理检查设置")
	}

	if opt.Goroutine == 50 && cfg.ProxyCheck != nil && cfg.ProxyCheck.MaxGoroutines > 0 {
		opt.Goroutine = cfg.ProxyCheck.MaxGoroutines
		runnerLogger.GetRawLogger().Infof("使用配置文件中的最大协程数: %d", opt.Goroutine)
	}

	if opt.CC == "" && cfg.ProxyCheck != nil && len(cfg.ProxyCheck.CountryCodes) > 0 {
		opt.CC = strings.Join(cfg.ProxyCheck.CountryCodes, ",")
		runnerLogger.GetRawLogger().Infof("使用配置文件中的国家代码: %s", opt.CC)
	}

	runnerLogger.GetRawLogger().Debug("命令行参数与配置文件合并完成")
}
