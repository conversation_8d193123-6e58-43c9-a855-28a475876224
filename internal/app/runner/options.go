package runner

import (
	"flag"
	"time"

	"flexproxy/internal/config"
	// "flexproxy/common/logger" // 暂时注释掉，因为验证被移到了 runner.New() 中
)

// Options 定义执行 Runner 所需的值。
func Options() *config.Options {
	opt := &config.Options{}

	flag.StringVar(&opt.File, "f", "", "")
	flag.StringVar(&opt.File, "file", "", "")

	flag.StringVar(&opt.Address, "a", "", "")
	flag.StringVar(&opt.Address, "address", "", "")

	flag.StringVar(&opt.Auth, "A", "", "")
	flag.StringVar(&opt.Auth, "auth", "", "")

	flag.BoolVar(&opt.Check, "c", false, "")
	flag.BoolVar(&opt.Check, "check", false, "")

	flag.StringVar(&opt.CC, "only-cc", "", "")

	flag.DurationVar(&opt.Timeout, "t", 30*time.Second, "")
	flag.DurationVar(&opt.Timeout, "timeout", 30*time.Second, "")

	flag.BoolVar(&opt.Sync, "s", false, "")
	flag.BoolVar(&opt.Sync, "sync", false, "")

	flag.BoolVar(&opt.Verbose, "v", false, "")
	flag.BoolVar(&opt.Verbose, "verbose", false, "")

	flag.BoolVar(&opt.Daemon, "d", false, "")
	flag.BoolVar(&opt.Daemon, "daemon", false, "")

	flag.StringVar(&opt.Output, "o", "", "")
	flag.StringVar(&opt.Output, "output", "", "")

	flag.StringVar(&opt.Type, "type", "http", "http | socks5")

	flag.StringVar(&opt.ConfigFile, "config", "", "规则配置文件路径")

	flag.BoolVar(&version, "V", false, "")
	flag.BoolVar(&version, "version", false, "")

	flag.BoolVar(&opt.Watch, "w", false, "")
	flag.BoolVar(&opt.Watch, "watch", false, "")

	flag.IntVar(&opt.Goroutine, "g", 50, "")
	flag.IntVar(&opt.Goroutine, "goroutine", 50, "")

	flag.Usage = func() {
		showBanner()
		showUsage()
	}
	flag.Parse()

	if version {
		showVersion()
	}
	showBanner()

	// 注释掉这里的验证，将验证移到 runner.New() 中配置文件加载之后
	// if err := validate(opt); err != nil {
	// 	logger.GetRunnerLogger().GetRawLogger().Fatalf("Error! %v.", err)
	// }

	return opt
}
