// Package services 提供统一的服务构造函数导出
package services

import (
	"flexproxy/common/logger"
	"flexproxy/internal/config"
	"flexproxy/internal/interfaces"
	"flexproxy/internal/services/advanced"
	"flexproxy/internal/services/basic"
	"flexproxy/internal/services/monitoring"
	"github.com/mbndr/logo"
)

// 基础服务构造函数

// NewLogService 创建日志服务
func NewLogService(logFile string) interfaces.LogService {
	return basic.NewLogService(logFile)
}

// NewNullLogService 创建空日志服务
func NewNullLogService() interfaces.LogService {
	return basic.NewNullLogService()
}

// NewConfigService 创建配置服务
func NewConfigService(logger *logo.Logger) interfaces.ConfigService {
	return basic.NewConfigService(logger)
}

// NewCacheService 创建缓存服务
func NewCacheService(log logger.Logger) interfaces.CacheService {
	return basic.NewCacheService(log)
}

// NewNullCacheService 创建空缓存服务
func NewNullCacheService(log logger.Logger) interfaces.CacheService {
	return basic.NewNullCacheService(log)
}

// NewDNSService 创建DNS服务
func NewDNSService(cacheService interfaces.CacheService, logService interfaces.LogService) interfaces.DNSService {
	return basic.NewDNSService(cacheService, logService)
}

// NewProxyService 创建代理服务
func NewProxyService(cacheService interfaces.CacheService, log logger.Logger) interfaces.ProxyService {
	return basic.NewProxyService(cacheService, log)
}

// 高级服务构造函数

// NewActionService 创建动作服务
func NewActionService(
	log *logger.LoggerAdapter,
	cacheService interfaces.CacheService,
	proxyService interfaces.ProxyService,
	dnsService interfaces.DNSService,
) interfaces.ActionService {
	return advanced.NewActionService(log, cacheService, proxyService, dnsService)
}

// NewTriggerService 创建触发器服务
func NewTriggerService(cacheService interfaces.CacheService, log logger.Logger) interfaces.TriggerService {
	return advanced.NewTriggerService(cacheService, log)
}

// NewRateLimitingService 创建限流服务
func NewRateLimitingService(config *config.RateLimitingConfig, globalConfig *config.Config, log logger.Logger) interfaces.RateLimitingService {
	return advanced.NewRateLimitingService(config, globalConfig, log)
}

// NewSecurityService 创建安全服务
func NewSecurityService(config *config.SecurityConfig, log logger.Logger) interfaces.SecurityService {
	return advanced.NewSecurityService(config, log)
}

// NewNullSecurityService 创建空安全服务
func NewNullSecurityService(log logger.Logger) interfaces.SecurityService {
	return basic.NewNullSecurityService(log)
}

// NewPluginService 创建插件服务
func NewPluginService(config *config.PluginsConfig, log logger.Logger) interfaces.PluginService {
	return advanced.NewPluginService(config, log)
}

// NewAdvancedConfigService 创建高级配置服务
func NewAdvancedConfigService(config *config.AdvancedConfig, logger *logo.Logger) interfaces.AdvancedConfigService {
	return advanced.NewAdvancedConfigService(config, logger)
}

// NewNullAdvancedConfigService 创建空高级配置服务
func NewNullAdvancedConfigService(logger *logo.Logger) interfaces.AdvancedConfigService {
	return advanced.NewNullAdvancedConfigService(logger)
}

// 监控服务构造函数

// NewMonitoringService 创建监控服务
func NewMonitoringService(config *config.MonitoringConfig, portsConfig *config.PortsConfig, globalConfig *config.Config, log logger.Logger) interfaces.MonitoringService {
	return monitoring.NewMonitoringService(config, portsConfig, globalConfig, log)
}

// NewTracingService 创建追踪服务
func NewTracingService(config *config.TracingConfig, logger *logo.Logger) interfaces.TracingService {
	return monitoring.NewTracingService(config, logger)
}

// NewPerformanceService 创建性能服务
func NewPerformanceService(config *config.PerformanceConfig, logger *logo.Logger) interfaces.PerformanceService {
	return monitoring.NewPerformanceService(config, logger)
}

// NewDebugService 创建调试服务
func NewDebugService(config *config.DebugConfig, portsConfig *config.PortsConfig, logger *logo.Logger) interfaces.DebugService {
	return monitoring.NewDebugService(config, portsConfig, logger)
}

// NewNullDebugService 创建空调试服务
func NewNullDebugService(logger *logo.Logger) interfaces.DebugService {
	return basic.NewNullDebugService(logger)
}

// NewProfilingService 创建性能分析服务
func NewProfilingService(config *config.ProfilingConfig, logger *logo.Logger) interfaces.ProfilingService {
	return monitoring.NewProfilingService(config, logger)
}

// NewNullProfilingService 创建空性能分析服务
func NewNullProfilingService(logger *logo.Logger) interfaces.ProfilingService {
	return basic.NewNullProfilingService(logger)
}
