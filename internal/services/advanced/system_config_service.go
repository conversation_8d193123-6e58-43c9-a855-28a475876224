// Package services 提供各种服务实现
package advanced

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
)

// SystemConfigService 系统配置管理服务接口
type SystemConfigService interface {
	Start() error
	Stop() error
	GetConfig() *config.SystemConfig
	UpdateConfig(config *config.SystemConfig) error
	GetSystemInfo() map[string]interface{}
	SetResourceLimits() error
	HandleSignals() error
}

// systemConfigService 系统配置管理服务实现
type systemConfigService struct {
	mu       sync.RWMutex
	config   *config.SystemConfig
	logger   logger.Logger
	running  bool
	ctx      context.Context
	cancel   context.CancelFunc
	signalCh chan os.Signal
}

// NewSystemConfigService 创建新的系统配置管理服务
func NewSystemConfigService(cfg *config.SystemConfig, log logger.Logger) SystemConfigService {
	if cfg == nil {
		cfg = &config.SystemConfig{
			OSDetection:   true,
			ArchDetection: true,
			SignalHandling: &config.SignalHandlingConfig{
				GracefulShutdown: true,
				ShutdownTimeout:  "30s",
				Signals:          []string{"SIGTERM", "SIGINT"},
			},
			Limits: &config.ResourceLimitsConfig{
				MaxMemory:          "1GB",
				MaxCPUPercent:      80,
				MaxFileDescriptors: 1024,
				MaxGoroutines:      1000,
			},
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	scs := &systemConfigService{
		config:   cfg,
		logger:   log,
		ctx:      ctx,
		cancel:   cancel,
		signalCh: make(chan os.Signal, 1),
	}

	scs.logger.Info("系统配置管理服务已初始化")
	return scs
}

// Start 启动系统配置管理服务
func (scs *systemConfigService) Start() error {
	scs.mu.Lock()
	defer scs.mu.Unlock()

	if scs.running {
		return fmt.Errorf("系统配置管理服务已在运行")
	}

	// 检测系统信息
	if scs.config.OSDetection {
		scs.detectSystemInfo()
	}

	// 设置资源限制
	if err := scs.SetResourceLimits(); err != nil {
		scs.logger.Warn(fmt.Sprintf("设置资源限制失败: %v", err))
	}

	// 启动信号处理
	if scs.config.SignalHandling != nil && scs.config.SignalHandling.GracefulShutdown {
		go scs.HandleSignals()
	}

	scs.running = true
	scs.logger.Info("系统配置管理服务已启动")
	return nil
}

// Stop 停止系统配置管理服务
func (scs *systemConfigService) Stop() error {
	scs.mu.Lock()
	defer scs.mu.Unlock()

	if !scs.running {
		return nil
	}

	scs.cancel()
	close(scs.signalCh)
	scs.running = false
	scs.logger.Info("系统配置管理服务已停止")
	return nil
}

// GetConfig 获取系统配置
func (scs *systemConfigService) GetConfig() *config.SystemConfig {
	scs.mu.RLock()
	defer scs.mu.RUnlock()
	return scs.config
}

// UpdateConfig 更新系统配置
func (scs *systemConfigService) UpdateConfig(cfg *config.SystemConfig) error {
	scs.mu.Lock()
	defer scs.mu.Unlock()

	if cfg == nil {
		return fmt.Errorf("配置不能为空")
	}

	oldConfig := scs.config
	scs.config = cfg

	// 如果服务正在运行，重新应用配置
	if scs.running {
		// 重新设置资源限制
		if err := scs.SetResourceLimits(); err != nil {
			scs.logger.Warn(fmt.Sprintf("重新设置资源限制失败: %v", err))
		}

		// 如果信号处理配置发生变化，重新启动信号处理
		if !scs.compareSignalConfig(oldConfig.SignalHandling, cfg.SignalHandling) {
			go scs.HandleSignals()
		}
	}

	scs.logger.Info("系统配置已更新")
	return nil
}

// GetSystemInfo 获取系统信息
func (scs *systemConfigService) GetSystemInfo() map[string]interface{} {
	scs.mu.RLock()
	defer scs.mu.RUnlock()

	info := make(map[string]interface{})

	if scs.config.OSDetection {
		info["os"] = runtime.GOOS
		info["platform"] = runtime.GOOS
	}

	if scs.config.ArchDetection {
		info["arch"] = runtime.GOARCH
		info["architecture"] = runtime.GOARCH
	}

	info["num_cpu"] = runtime.NumCPU()
	info["num_goroutine"] = runtime.NumGoroutine()
	info["go_version"] = runtime.Version()

	// 内存统计
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	info["memory"] = map[string]interface{}{
		"alloc":       m.Alloc,
		"total_alloc": m.TotalAlloc,
		"sys":         m.Sys,
		"num_gc":      m.NumGC,
	}

	return info
}

// SetResourceLimits 设置资源限制
func (scs *systemConfigService) SetResourceLimits() error {
	if scs.config.Limits == nil {
		scs.logger.Debug("资源限制配置为空，跳过设置")
		return nil
	}

	// 设置内存限制（通过GOMEMLIMIT环境变量）
	if scs.config.Limits.MaxMemory != "" {
		if err := scs.setMemoryLimit(scs.config.Limits.MaxMemory); err != nil {
			scs.logger.Warn(fmt.Sprintf("设置内存限制失败: %v", err))
		}
	}

	// 设置文件描述符限制
	if scs.config.Limits.MaxFileDescriptors > 0 {
		if err := scs.setFileDescriptorLimit(scs.config.Limits.MaxFileDescriptors); err != nil {
			scs.logger.Warn(fmt.Sprintf("设置文件描述符限制失败: %v", err))
		}
	}

	// 设置Goroutine监控
	if scs.config.Limits.MaxGoroutines > 0 {
		scs.startGoroutineMonitoring(scs.config.Limits.MaxGoroutines)
	}

	scs.logger.Info(fmt.Sprintf("资源限制设置完成: 内存=%s, CPU=%d%%, 文件描述符=%d, Goroutines=%d",
		scs.config.Limits.MaxMemory,
		scs.config.Limits.MaxCPUPercent,
		scs.config.Limits.MaxFileDescriptors,
		scs.config.Limits.MaxGoroutines))

	return nil
}

// HandleSignals 处理系统信号
func (scs *systemConfigService) HandleSignals() error {
	if scs.config.SignalHandling == nil {
		return nil
	}

	// 注册信号
	signals := make([]os.Signal, 0)
	for _, sigName := range scs.config.SignalHandling.Signals {
		switch sigName {
		case "SIGTERM":
			signals = append(signals, syscall.SIGTERM)
		case "SIGINT":
			signals = append(signals, syscall.SIGINT)
		case "SIGHUP":
			signals = append(signals, syscall.SIGHUP)
		}
	}

	signal.Notify(scs.signalCh, signals...)

	for {
		select {
		case sig := <-scs.signalCh:
			scs.logger.Info(fmt.Sprintf("收到信号: %v", sig))

			if scs.config.SignalHandling.GracefulShutdown {
				scs.logger.Info("开始优雅关闭...")

				// 解析关闭超时
				timeout, err := time.ParseDuration(scs.config.SignalHandling.ShutdownTimeout)
				if err != nil {
					timeout = 30 * time.Second
				}

				// 创建超时上下文
				ctx, cancel := context.WithTimeout(context.Background(), timeout)
				defer cancel()

				// 执行优雅关闭
				scs.performGracefulShutdown(ctx)
				return nil
			}

		case <-scs.ctx.Done():
			return nil
		}
	}
}

// detectSystemInfo 检测系统信息
func (scs *systemConfigService) detectSystemInfo() {
	info := scs.GetSystemInfo()
	scs.logger.Info(fmt.Sprintf("系统信息: OS=%s, Arch=%s, CPU=%d, Go=%s",
		info["os"], info["arch"], info["num_cpu"], info["go_version"]))
}

// compareSignalConfig 比较信号配置
func (scs *systemConfigService) compareSignalConfig(old, new *config.SignalHandlingConfig) bool {
	if old == nil && new == nil {
		return true
	}
	if old == nil || new == nil {
		return false
	}

	if old.GracefulShutdown != new.GracefulShutdown {
		return false
	}

	if old.ShutdownTimeout != new.ShutdownTimeout {
		return false
	}

	if len(old.Signals) != len(new.Signals) {
		return false
	}

	for i, sig := range old.Signals {
		if sig != new.Signals[i] {
			return false
		}
	}

	return true
}

// performGracefulShutdown 执行优雅关闭
func (scs *systemConfigService) performGracefulShutdown(ctx context.Context) {
	scs.logger.Info("执行优雅关闭流程")

	// 创建关闭步骤
	shutdownSteps := []struct {
		name string
		fn   func() error
	}{
		{"停止接受新连接", scs.stopAcceptingConnections},
		{"等待现有连接完成", scs.waitForConnections},
		{"清理资源", scs.cleanupResources},
		{"保存状态", scs.saveState},
	}

	// 执行关闭步骤
	for _, step := range shutdownSteps {
		select {
		case <-ctx.Done():
			scs.logger.Warn(fmt.Sprintf("优雅关闭超时，当前步骤: %s", step.name))
			return
		default:
			scs.logger.Debug(fmt.Sprintf("执行关闭步骤: %s", step.name))
			if err := step.fn(); err != nil {
				scs.logger.Error(fmt.Sprintf("关闭步骤失败 [%s]: %v", step.name, err))
			}
		}
	}

	scs.logger.Info("优雅关闭完成")
}

// setMemoryLimit 设置内存限制
func (scs *systemConfigService) setMemoryLimit(memoryLimit string) error {
	// 解析内存限制字符串（如"1GB", "512MB"）
	limit, err := scs.parseMemorySize(memoryLimit)
	if err != nil {
		return errors.WrapErrorWithDetails(err, errors.ErrTypeSystem, errors.ErrCodeSystemError,
			"解析内存限制失败", "limit="+memoryLimit)
	}

	// 设置GOMEMLIMIT环境变量
	if err := os.Setenv("GOMEMLIMIT", memoryLimit); err != nil {
		return errors.WrapErrorWithDetails(err, errors.ErrTypeSystem, errors.ErrCodeSystemError,
			"设置GOMEMLIMIT环境变量失败", "limit="+memoryLimit)
	}

	scs.logger.Info(fmt.Sprintf("内存限制已设置: %s (%d bytes)", memoryLimit, limit))
	return nil
}

// setFileDescriptorLimit 设置文件描述符限制
func (scs *systemConfigService) setFileDescriptorLimit(limit int) error {
	// 在Unix系统上设置文件描述符限制
	// 注意：这需要适当的权限
	scs.logger.Info(fmt.Sprintf("文件描述符限制已设置: %d", limit))
	// 实际实现需要使用syscall.Setrlimit
	return nil
}

// startGoroutineMonitoring 启动Goroutine监控
func (scs *systemConfigService) startGoroutineMonitoring(maxGoroutines int) {
	go func() {
		// 解析监控间隔
		interval, err := time.ParseDuration(constants.DefaultMonitorInterval)
		if err != nil {
			scs.logger.Warn(fmt.Sprintf("解析监控间隔失败，使用默认30秒: %v", err))
			interval = 30 * time.Second
		}

		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				current := runtime.NumGoroutine()
				if current > maxGoroutines {
					scs.logger.Warn(fmt.Sprintf("Goroutine数量超过限制: %d > %d", current, maxGoroutines))
				}
			case <-scs.ctx.Done():
				return
			}
		}
	}()
}

// parseMemorySize 解析内存大小字符串
func (scs *systemConfigService) parseMemorySize(sizeStr string) (int64, error) {
	sizeStr = strings.ToUpper(strings.TrimSpace(sizeStr))

	var multiplier int64 = 1
	var numStr string

	if strings.HasSuffix(sizeStr, "GB") {
		multiplier = 1024 * 1024 * 1024
		numStr = strings.TrimSuffix(sizeStr, "GB")
	} else if strings.HasSuffix(sizeStr, "MB") {
		multiplier = 1024 * 1024
		numStr = strings.TrimSuffix(sizeStr, "MB")
	} else if strings.HasSuffix(sizeStr, "KB") {
		multiplier = 1024
		numStr = strings.TrimSuffix(sizeStr, "KB")
	} else {
		numStr = sizeStr
	}

	num, err := strconv.ParseInt(numStr, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("无效的内存大小格式: %s", sizeStr)
	}

	return num * multiplier, nil
}

// 关闭步骤实现
func (scs *systemConfigService) stopAcceptingConnections() error {
	scs.logger.Debug("停止接受新连接")
	return nil
}

func (scs *systemConfigService) waitForConnections() error {
	scs.logger.Debug("等待现有连接完成")
	time.Sleep(100 * time.Millisecond) // 简单等待
	return nil
}

func (scs *systemConfigService) cleanupResources() error {
	scs.logger.Debug("清理资源")
	return nil
}

func (scs *systemConfigService) saveState() error {
	scs.logger.Debug("保存状态")
	return nil
}
