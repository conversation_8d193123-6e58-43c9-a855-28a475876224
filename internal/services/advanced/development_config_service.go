// Package services 提供各种服务实现
package advanced

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	_ "net/http/pprof"
	"os"
	"path/filepath"
	"runtime"
	"runtime/pprof"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
)

// DevelopmentConfigService 开发配置管理服务接口
type DevelopmentConfigService interface {
	Start() error
	Stop() error
	GetConfig() *config.DevelopmentConfig
	UpdateConfig(config *config.DevelopmentConfig) error
	IsEnabled() bool
	GetMode() string
	EnableProfiling() error
	DisableProfiling() error
	GetMockResponse(path string) (interface{}, error)
	SetMockResponse(path string, response interface{}) error
	StartHotReload() error
	StopHotReload() error
}

// developmentConfigService 开发配置管理服务实现
type developmentConfigService struct {
	mu           sync.RWMutex
	config       *config.DevelopmentConfig
	logger       logger.Logger
	running      bool
	profilingOn  bool
	mockData     map[string]interface{}
	hotReloadOn  bool
	profileServer *http.Server

	// 热重载相关字段
	watcher      *fsnotify.Watcher
	watchedFiles []string
	ctx          context.Context
	cancel       context.CancelFunc
}

// NewDevelopmentConfigService 创建新的开发配置管理服务
func NewDevelopmentConfigService(cfg *config.DevelopmentConfig, log logger.Logger) DevelopmentConfigService {
	if cfg == nil {
		cfg = &config.DevelopmentConfig{
			Enabled:   false,
			Mode:      "production",
			HotReload: false,
			Testing: &config.TestingConfig{
				Enabled:       false,
				MockResponses: false,
				TestDataDir:   "./testdata",
			},
			Profiling: &config.ProfilingConfig{
				Enabled:       false,
				CPUProfile:    false,
				MemoryProfile: false,
				BlockProfile:  false,
				MutexProfile:  false,
			},
		}
	}

	dcs := &developmentConfigService{
		config:   cfg,
		logger:   log,
		mockData: make(map[string]interface{}),
	}

	dcs.logger.Info("开发配置管理服务已初始化")
	return dcs
}

// Start 启动开发配置管理服务
func (dcs *developmentConfigService) Start() error {
	dcs.mu.Lock()
	defer dcs.mu.Unlock()

	if dcs.running {
		return fmt.Errorf("开发配置管理服务已在运行")
	}

	// 如果开发模式未启用，直接返回
	if !dcs.config.Enabled {
		dcs.logger.Info("开发模式未启用，跳过开发配置服务启动")
		dcs.running = true
		return nil
	}

	// 启用性能分析
	if dcs.config.Profiling != nil && dcs.config.Profiling.Enabled {
		if err := dcs.EnableProfiling(); err != nil {
			dcs.logger.Warn(fmt.Sprintf("启用性能分析失败: %v", err))
		}
	}

	// 加载模拟数据
	if dcs.config.Testing != nil && dcs.config.Testing.MockResponses {
		if err := dcs.loadMockData(); err != nil {
			dcs.logger.Warn(fmt.Sprintf("加载模拟数据失败: %v", err))
		}
	}

	// 启动热重载
	if dcs.config.HotReload {
		if err := dcs.startHotReloadInternal(); err != nil {
			dcs.logger.Warn(fmt.Sprintf("启动热重载失败: %v", err))
		}
	}

	dcs.running = true
	dcs.logger.Info(fmt.Sprintf("开发配置管理服务已启动，模式: %s", dcs.config.Mode))
	return nil
}

// Stop 停止开发配置管理服务
func (dcs *developmentConfigService) Stop() error {
	dcs.mu.Lock()
	defer dcs.mu.Unlock()

	if !dcs.running {
		return nil
	}

	// 停止性能分析
	if dcs.profilingOn {
		if err := dcs.DisableProfiling(); err != nil {
			dcs.logger.Warn(fmt.Sprintf("停止性能分析失败: %v", err))
		}
	}

	// 停止热重载
	if dcs.hotReloadOn {
		if err := dcs.stopHotReloadInternal(); err != nil {
			dcs.logger.Warn(fmt.Sprintf("停止热重载失败: %v", err))
		}
	}

	dcs.running = false
	dcs.logger.Info("开发配置管理服务已停止")
	return nil
}

// GetConfig 获取开发配置
func (dcs *developmentConfigService) GetConfig() *config.DevelopmentConfig {
	dcs.mu.RLock()
	defer dcs.mu.RUnlock()
	return dcs.config
}

// UpdateConfig 更新开发配置
func (dcs *developmentConfigService) UpdateConfig(cfg *config.DevelopmentConfig) error {
	dcs.mu.Lock()
	defer dcs.mu.Unlock()

	if cfg == nil {
		return fmt.Errorf("配置不能为空")
	}

	oldConfig := dcs.config
	dcs.config = cfg

	// 如果服务正在运行，应用新配置
	if dcs.running {
		// 处理性能分析配置变化
		if dcs.shouldToggleProfiling(oldConfig.Profiling, cfg.Profiling) {
			if cfg.Profiling != nil && cfg.Profiling.Enabled {
				dcs.EnableProfiling()
			} else {
				dcs.DisableProfiling()
			}
		}

		// 处理热重载配置变化
		if oldConfig.HotReload != cfg.HotReload {
			if cfg.HotReload {
				dcs.StartHotReload()
			} else {
				dcs.StopHotReload()
			}
		}

		// 重新加载模拟数据
		if cfg.Testing != nil && cfg.Testing.MockResponses {
			dcs.loadMockData()
		}
	}

	dcs.logger.Info("开发配置已更新")
	return nil
}

// IsEnabled 检查开发模式是否启用
func (dcs *developmentConfigService) IsEnabled() bool {
	dcs.mu.RLock()
	defer dcs.mu.RUnlock()
	return dcs.config.Enabled
}

// GetMode 获取开发模式
func (dcs *developmentConfigService) GetMode() string {
	dcs.mu.RLock()
	defer dcs.mu.RUnlock()
	return dcs.config.Mode
}

// EnableProfiling 启用性能分析
func (dcs *developmentConfigService) EnableProfiling() error {
	if dcs.profilingOn {
		return nil
	}

	if dcs.config.Profiling == nil {
		return fmt.Errorf("性能分析配置为空")
	}

	// 启动pprof服务器
	mux := http.NewServeMux()
	mux.HandleFunc("/debug/pprof/", http.DefaultServeMux.ServeHTTP)
	
	dcs.profileServer = &http.Server{
		Addr:    ":6060",
		Handler: mux,
	}

	go func() {
		if err := dcs.profileServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			dcs.logger.Error(fmt.Sprintf("性能分析服务器启动失败: %v", err))
		}
	}()

	// 启用CPU性能分析
	if dcs.config.Profiling.CPUProfile {
		f, err := os.Create("cpu.prof")
		if err != nil {
			return fmt.Errorf("创建CPU性能分析文件失败: %v", err)
		}
		pprof.StartCPUProfile(f)
	}

	// 启用阻塞性能分析
	if dcs.config.Profiling.BlockProfile {
		runtime.SetBlockProfileRate(1)
	}

	// 启用互斥锁性能分析
	if dcs.config.Profiling.MutexProfile {
		runtime.SetMutexProfileFraction(1)
	}

	dcs.profilingOn = true
	dcs.logger.Info("性能分析已启用")
	return nil
}

// DisableProfiling 禁用性能分析
func (dcs *developmentConfigService) DisableProfiling() error {
	if !dcs.profilingOn {
		return nil
	}

	// 停止CPU性能分析
	if dcs.config.Profiling.CPUProfile {
		pprof.StopCPUProfile()
	}

	// 生成内存性能分析
	if dcs.config.Profiling.MemoryProfile {
		f, err := os.Create("mem.prof")
		if err == nil {
			runtime.GC()
			pprof.WriteHeapProfile(f)
			f.Close()
		}
	}

	// 停止性能分析服务器
	if dcs.profileServer != nil {
		dcs.profileServer.Close()
		dcs.profileServer = nil
	}

	dcs.profilingOn = false
	dcs.logger.Info("性能分析已禁用")
	return nil
}

// GetMockResponse 获取模拟响应
func (dcs *developmentConfigService) GetMockResponse(path string) (interface{}, error) {
	dcs.mu.RLock()
	defer dcs.mu.RUnlock()

	if !dcs.config.Testing.MockResponses {
		return nil, fmt.Errorf("模拟响应未启用")
	}

	response, exists := dcs.mockData[path]
	if !exists {
		return nil, fmt.Errorf("路径 %s 的模拟响应不存在", path)
	}

	return response, nil
}

// SetMockResponse 设置模拟响应
func (dcs *developmentConfigService) SetMockResponse(path string, response interface{}) error {
	dcs.mu.Lock()
	defer dcs.mu.Unlock()

	if !dcs.config.Testing.MockResponses {
		return fmt.Errorf("模拟响应未启用")
	}

	dcs.mockData[path] = response
	dcs.logger.Info(fmt.Sprintf("已设置路径 %s 的模拟响应", path))
	return nil
}

// StartHotReload 启动热重载
func (dcs *developmentConfigService) StartHotReload() error {
	dcs.mu.Lock()
	defer dcs.mu.Unlock()

	return dcs.startHotReloadInternal()
}

// startHotReloadInternal 内部启动热重载（不获取锁）
func (dcs *developmentConfigService) startHotReloadInternal() error {
	if dcs.hotReloadOn {
		return nil
	}

	// 在测试环境中，跳过文件监控启动
	if dcs.isTestEnvironment() {
		dcs.hotReloadOn = true
		dcs.logger.Info("测试环境：跳过文件监控启动")
		return nil
	}

	// 创建文件监控器
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return fmt.Errorf("创建文件监控器失败: %v", err)
	}

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())

	dcs.watcher = watcher
	dcs.ctx = ctx
	dcs.cancel = cancel

	// 添加要监控的文件
	if err := dcs.addWatchedFiles(); err != nil {
		dcs.cleanup()
		return fmt.Errorf("添加监控文件失败: %v", err)
	}

	// 启动文件监控协程
	go dcs.watchFiles()

	dcs.hotReloadOn = true
	dcs.logger.Info(fmt.Sprintf("开发配置热重载已启动，监控文件数: %d", len(dcs.watchedFiles)))
	return nil
}

// isTestEnvironment 检查是否在测试环境中
func (dcs *developmentConfigService) isTestEnvironment() bool {
	// 检查是否在测试环境中运行
	return os.Getenv("GO_ENV") == "test" || strings.Contains(os.Args[0], ".test")
}

// StopHotReload 停止热重载
func (dcs *developmentConfigService) StopHotReload() error {
	dcs.mu.Lock()
	defer dcs.mu.Unlock()

	return dcs.stopHotReloadInternal()
}

// stopHotReloadInternal 内部停止热重载（不获取锁）
func (dcs *developmentConfigService) stopHotReloadInternal() error {
	if !dcs.hotReloadOn {
		return nil
	}

	// 停止文件监控
	dcs.cleanup()

	dcs.hotReloadOn = false
	dcs.logger.Info("开发配置热重载已停止")
	return nil
}

// addWatchedFiles 添加要监控的文件
func (dcs *developmentConfigService) addWatchedFiles() error {
	// 清空现有监控文件列表
	dcs.watchedFiles = make([]string, 0)

	// 监控测试数据目录
	if dcs.config.Testing != nil && dcs.config.Testing.TestDataDir != "" {
		testDataDir := dcs.config.Testing.TestDataDir
		if _, err := os.Stat(testDataDir); err == nil {
			if err := dcs.watcher.Add(testDataDir); err != nil {
				dcs.logger.Warn(fmt.Sprintf("添加测试数据目录监控失败: %v", err))
			} else {
				dcs.watchedFiles = append(dcs.watchedFiles, testDataDir)
				dcs.logger.Debug(fmt.Sprintf("已添加测试数据目录监控: %s", testDataDir))
			}
		}
	}

	// 可以根据需要添加更多文件监控
	// 例如：配置文件、模板文件等

	return nil
}

// watchFiles 文件监控协程
func (dcs *developmentConfigService) watchFiles() {
	defer func() {
		if r := recover(); r != nil {
			dcs.logger.Error(fmt.Sprintf("文件监控协程异常退出: %v", r))
		}
	}()

	for {
		select {
		case event, ok := <-dcs.watcher.Events:
			if !ok {
				return
			}
			dcs.handleFileEvent(event)

		case err, ok := <-dcs.watcher.Errors:
			if !ok {
				return
			}
			dcs.logger.Error(fmt.Sprintf("文件监控错误: %v", err))

		case <-dcs.ctx.Done():
			dcs.logger.Debug("文件监控协程收到停止信号")
			return
		}
	}
}

// handleFileEvent 处理文件变更事件
func (dcs *developmentConfigService) handleFileEvent(event fsnotify.Event) {
	dcs.logger.Debug(fmt.Sprintf("检测到文件变更: %s, 操作: %s", event.Name, event.Op.String()))

	// 防抖处理：短时间内的多次变更只处理一次
	time.Sleep(100 * time.Millisecond)

	// 根据文件类型处理不同的变更
	if dcs.isTestDataFile(event.Name) {
		dcs.handleTestDataChange(event)
	}
}

// isTestDataFile 检查是否为测试数据文件
func (dcs *developmentConfigService) isTestDataFile(filename string) bool {
	if dcs.config.Testing == nil || dcs.config.Testing.TestDataDir == "" {
		return false
	}

	testDataDir := dcs.config.Testing.TestDataDir
	rel, err := filepath.Rel(testDataDir, filename)
	if err != nil {
		return false
	}

	// 检查文件是否在测试数据目录内
	return !filepath.IsAbs(rel) && !filepath.HasPrefix(rel, "..")
}

// handleTestDataChange 处理测试数据变更
func (dcs *developmentConfigService) handleTestDataChange(event fsnotify.Event) {
	if event.Op&fsnotify.Write == fsnotify.Write || event.Op&fsnotify.Create == fsnotify.Create {
		dcs.logger.Info(fmt.Sprintf("检测到测试数据文件变更，重新加载模拟数据: %s", event.Name))

		// 重新加载模拟数据
		if err := dcs.loadMockData(); err != nil {
			dcs.logger.Error(fmt.Sprintf("重新加载模拟数据失败: %v", err))
		} else {
			dcs.logger.Info("模拟数据重新加载完成")
		}
	}
}

// cleanup 清理资源
func (dcs *developmentConfigService) cleanup() {
	if dcs.cancel != nil {
		dcs.cancel()
	}

	if dcs.watcher != nil {
		dcs.watcher.Close()
		dcs.watcher = nil
	}

	dcs.watchedFiles = nil
	dcs.ctx = nil
	dcs.cancel = nil
}

// loadMockData 加载模拟数据
func (dcs *developmentConfigService) loadMockData() error {
	if dcs.config.Testing == nil || dcs.config.Testing.TestDataDir == "" {
		return nil
	}

	dataDir := dcs.config.Testing.TestDataDir
	if _, err := os.Stat(dataDir); os.IsNotExist(err) {
		// 创建测试数据目录
		if err := os.MkdirAll(dataDir, 0755); err != nil {
			return fmt.Errorf("创建测试数据目录失败: %v", err)
		}
		dcs.logger.Info(fmt.Sprintf("已创建测试数据目录: %s", dataDir))
		return nil
	}

	// 加载JSON文件
	files, err := filepath.Glob(filepath.Join(dataDir, "*.json"))
	if err != nil {
		return fmt.Errorf("搜索模拟数据文件失败: %v", err)
	}

	for _, file := range files {
		data, err := ioutil.ReadFile(file)
		if err != nil {
			dcs.logger.Warn(fmt.Sprintf("读取模拟数据文件失败: %s, 错误: %v", file, err))
			continue
		}

		var mockData map[string]interface{}
		if err := json.Unmarshal(data, &mockData); err != nil {
			dcs.logger.Warn(fmt.Sprintf("解析模拟数据文件失败: %s, 错误: %v", file, err))
			continue
		}

		// 将数据添加到mockData中
		for path, response := range mockData {
			dcs.mockData[path] = response
		}

		dcs.logger.Info(fmt.Sprintf("已加载模拟数据文件: %s", file))
	}

	return nil
}

// shouldToggleProfiling 检查是否需要切换性能分析状态
func (dcs *developmentConfigService) shouldToggleProfiling(old, new *config.ProfilingConfig) bool {
	if old == nil && new == nil {
		return false
	}
	if old == nil || new == nil {
		return true
	}
	return old.Enabled != new.Enabled
}
