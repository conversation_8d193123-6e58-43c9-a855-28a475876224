// Package services 提供各种服务实现
package advanced

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"

	"flexproxy/common/logger"
	"flexproxy/internal/config"
)

// PathsConfigService 路径配置管理服务接口
type PathsConfigService interface {
	Start() error
	Stop() error
	GetConfig() *config.PathsConfig
	UpdateConfig(config *config.PathsConfig) error
	CreateDirectories() error
	ValidatePaths() error
	GetAbsolutePath(relativePath string) string
	SetPermissions() error
	CleanupTempFiles() error
}

// pathsConfigService 路径配置管理服务实现
type pathsConfigService struct {
	mu      sync.RWMutex
	config  *config.PathsConfig
	logger  logger.Logger
	running bool
}

// NewPathsConfigService 创建新的路径配置管理服务
func NewPathsConfigService(cfg *config.PathsConfig, log logger.Logger) PathsConfigService {
	if cfg == nil {
		cfg = &config.PathsConfig{
			BaseDir:        "./",
			ConfigDir:      "./config",
			LogsDir:        "./logs",
			DataDir:        "./data",
			TempDir:        "./temp",
			BackupDir:      "./backup",
			FilePermission: 0644,
			DirPermission:  0755,
			Extensions: map[string]string{
				"config": ".yaml",
				"log":    ".log",
				"data":   ".db",
				"temp":   ".tmp",
				"backup": ".bak",
			},
		}
	}

	pcs := &pathsConfigService{
		config: cfg,
		logger: log,
	}

	pcs.logger.Info("路径配置管理服务已初始化")
	return pcs
}

// Start 启动路径配置管理服务
func (pcs *pathsConfigService) Start() error {
	pcs.mu.Lock()
	defer pcs.mu.Unlock()

	if pcs.running {
		return fmt.Errorf("路径配置管理服务已在运行")
	}

	// 验证路径配置
	if err := pcs.ValidatePaths(); err != nil {
		return fmt.Errorf("路径配置验证失败: %v", err)
	}

	// 创建必要的目录
	if err := pcs.CreateDirectories(); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	// 设置权限
	if err := pcs.SetPermissions(); err != nil {
		pcs.logger.Warn(fmt.Sprintf("设置权限失败: %v", err))
	}

	pcs.running = true
	pcs.logger.Info("路径配置管理服务已启动")
	return nil
}

// Stop 停止路径配置管理服务
func (pcs *pathsConfigService) Stop() error {
	pcs.mu.Lock()
	defer pcs.mu.Unlock()

	if !pcs.running {
		return nil
	}

	// 清理临时文件
	if err := pcs.CleanupTempFiles(); err != nil {
		pcs.logger.Warn(fmt.Sprintf("清理临时文件失败: %v", err))
	}

	pcs.running = false
	pcs.logger.Info("路径配置管理服务已停止")
	return nil
}

// GetConfig 获取路径配置
func (pcs *pathsConfigService) GetConfig() *config.PathsConfig {
	pcs.mu.RLock()
	defer pcs.mu.RUnlock()
	return pcs.config
}

// UpdateConfig 更新路径配置
func (pcs *pathsConfigService) UpdateConfig(cfg *config.PathsConfig) error {
	pcs.mu.Lock()
	defer pcs.mu.Unlock()

	if cfg == nil {
		return fmt.Errorf("配置不能为空")
	}

	// 验证新配置
	oldConfig := pcs.config
	pcs.config = cfg

	if err := pcs.ValidatePaths(); err != nil {
		pcs.config = oldConfig // 回滚
		return fmt.Errorf("新配置验证失败: %v", err)
	}

	// 如果服务正在运行，重新创建目录和设置权限
	if pcs.running {
		if err := pcs.CreateDirectories(); err != nil {
			pcs.logger.Warn(fmt.Sprintf("重新创建目录失败: %v", err))
		}
		if err := pcs.SetPermissions(); err != nil {
			pcs.logger.Warn(fmt.Sprintf("重新设置权限失败: %v", err))
		}
	}

	pcs.logger.Info("路径配置已更新")
	return nil
}

// CreateDirectories 创建必要的目录
func (pcs *pathsConfigService) CreateDirectories() error {
	directories := []string{
		pcs.config.BaseDir,
		pcs.config.ConfigDir,
		pcs.config.LogsDir,
		pcs.config.DataDir,
		pcs.config.TempDir,
		pcs.config.BackupDir,
	}

	for _, dir := range directories {
		if dir == "" {
			continue
		}

		absPath := pcs.GetAbsolutePath(dir)
		if err := os.MkdirAll(absPath, os.FileMode(pcs.config.DirPermission)); err != nil {
			return fmt.Errorf("创建目录失败 %s: %v", absPath, err)
		}

		pcs.logger.Info(fmt.Sprintf("已创建目录: %s", absPath))
	}

	return nil
}

// ValidatePaths 验证路径配置
func (pcs *pathsConfigService) ValidatePaths() error {
	// 验证基础目录
	if pcs.config.BaseDir == "" {
		return fmt.Errorf("基础目录不能为空")
	}

	// 验证权限值
	if pcs.config.FilePermission < 0 || pcs.config.FilePermission > 0777 {
		return fmt.Errorf("文件权限值无效: %o", pcs.config.FilePermission)
	}

	if pcs.config.DirPermission < 0 || pcs.config.DirPermission > 0777 {
		return fmt.Errorf("目录权限值无效: %o", pcs.config.DirPermission)
	}

	// 验证路径是否可写
	testDirs := []string{
		pcs.config.BaseDir,
		pcs.config.LogsDir,
		pcs.config.DataDir,
		pcs.config.TempDir,
		pcs.config.BackupDir,
	}

	for _, dir := range testDirs {
		if dir == "" {
			continue
		}

		absPath := pcs.GetAbsolutePath(dir)
		parentDir := filepath.Dir(absPath)

		// 检查父目录是否存在且可写
		if info, err := os.Stat(parentDir); err != nil {
			if os.IsNotExist(err) {
				// 父目录不存在，尝试创建
				if err := os.MkdirAll(parentDir, 0755); err != nil {
					return fmt.Errorf("无法创建父目录 %s: %v", parentDir, err)
				}
			} else {
				return fmt.Errorf("无法访问父目录 %s: %v", parentDir, err)
			}
		} else if !info.IsDir() {
			return fmt.Errorf("路径不是目录: %s", parentDir)
		}
	}

	return nil
}

// GetAbsolutePath 获取绝对路径
func (pcs *pathsConfigService) GetAbsolutePath(relativePath string) string {
	if filepath.IsAbs(relativePath) {
		return relativePath
	}

	if pcs.config.BaseDir == "" {
		return relativePath
	}

	return filepath.Join(pcs.config.BaseDir, relativePath)
}

// SetPermissions 设置目录权限
func (pcs *pathsConfigService) SetPermissions() error {
	directories := []string{
		pcs.config.ConfigDir,
		pcs.config.LogsDir,
		pcs.config.DataDir,
		pcs.config.TempDir,
		pcs.config.BackupDir,
	}

	for _, dir := range directories {
		if dir == "" {
			continue
		}

		absPath := pcs.GetAbsolutePath(dir)
		if _, err := os.Stat(absPath); err != nil {
			continue // 目录不存在，跳过
		}

		if err := os.Chmod(absPath, os.FileMode(pcs.config.DirPermission)); err != nil {
			return fmt.Errorf("设置目录权限失败 %s: %v", absPath, err)
		}

		pcs.logger.Info(fmt.Sprintf("已设置目录权限: %s (%o)", absPath, pcs.config.DirPermission))
	}

	return nil
}

// CleanupTempFiles 清理临时文件
func (pcs *pathsConfigService) CleanupTempFiles() error {
	if pcs.config.TempDir == "" {
		return nil
	}

	tempPath := pcs.GetAbsolutePath(pcs.config.TempDir)
	if _, err := os.Stat(tempPath); os.IsNotExist(err) {
		return nil // 临时目录不存在
	}

	// 读取临时目录内容
	files, err := os.ReadDir(tempPath)
	if err != nil {
		return fmt.Errorf("读取临时目录失败: %v", err)
	}

	// 删除临时文件
	for _, file := range files {
		filePath := filepath.Join(tempPath, file.Name())
		
		// 只删除临时文件（以.tmp结尾或在Extensions中定义的temp扩展名）
		if pcs.isTempFile(file.Name()) {
			if err := os.RemoveAll(filePath); err != nil {
				pcs.logger.Warn(fmt.Sprintf("删除临时文件失败: %s, 错误: %v", filePath, err))
			} else {
				pcs.logger.Info(fmt.Sprintf("已删除临时文件: %s", filePath))
			}
		}
	}

	return nil
}

// isTempFile 判断是否为临时文件
func (pcs *pathsConfigService) isTempFile(filename string) bool {
	// 检查是否以.tmp结尾
	if filepath.Ext(filename) == ".tmp" {
		return true
	}

	// 检查是否匹配配置中的临时文件扩展名
	if pcs.config.Extensions != nil {
		if tempExt, exists := pcs.config.Extensions["temp"]; exists {
			return filepath.Ext(filename) == tempExt
		}
	}

	return false
}
