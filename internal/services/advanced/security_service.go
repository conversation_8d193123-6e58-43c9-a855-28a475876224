// Package services 提供各种服务实现
package advanced

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/tls"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"

	"flexproxy/common/logger"
	"flexproxy/internal/config"
	"flexproxy/internal/interfaces"
)

// SecurityService 提供安全功能
type securityService struct {
	mu            sync.RWMutex
	config        *config.SecurityConfig
	authTokens    map[string]*AuthToken
	encryptionKey []byte
	tlsConfig     *tls.Config
	logger        logger.Logger
}

// AuthResult 认证结果
type AuthResult struct {
	Authenticated bool        `json:"authenticated"`
	UserID        string      `json:"user_id,omitempty"`
	Token         string      `json:"token,omitempty"`
	Scopes        []string    `json:"scopes,omitempty"`
	Error         error       `json:"error,omitempty"`
	ExpiresAt     *time.Time  `json:"expires_at,omitempty"`
}

// EncryptionResult 加密结果
type EncryptionResult struct {
	Ciphertext string `json:"ciphertext"`
	Nonce      string `json:"nonce"`
	Algorithm  string `json:"algorithm,omitempty"`
}

// AuthToken 认证令牌
type AuthToken struct {
	Token     string    `json:"token"`
	UserID    string    `json:"user_id"`
	ExpiresAt time.Time `json:"expires_at"`
	Scopes    []string  `json:"scopes"`
	CreatedAt time.Time `json:"created_at"`
	LastUsed  time.Time `json:"last_used"`
}

// NewSecurityService 创建安全服务
func NewSecurityService(config *config.SecurityConfig, log logger.Logger) interfaces.SecurityService {
	if log == nil {
		log = logger.GetLogger("security")
	}

	ss := &securityService{
		config:     config,
		authTokens: make(map[string]*AuthToken),
		logger:     log,
	}

	// 初始化加密密钥
	ss.initEncryptionKey()

	// 初始化TLS配置
	ss.initTLSConfig()

	ss.logger.Info("安全服务已初始化")
	return ss
}

// GetConfig 获取当前安全配置
func (ss *securityService) GetConfig() interface{} {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	return ss.config
}

// UpdateConfig 更新安全服务配置
// 支持热重载认证配置、加密配置、TLS配置等
func (ss *securityService) UpdateConfig(configInterface interface{}) error {
	ss.mu.Lock()
	defer ss.mu.Unlock()

	// 类型断言
	cfg, ok := configInterface.(*config.SecurityConfig)
	if !ok {
		return fmt.Errorf("无效的配置类型，期望 *config.SecurityConfig")
	}

	if cfg == nil {
		return fmt.Errorf("配置不能为空")
	}

	oldConfig := ss.config
	ss.config = cfg

	// 记录配置更新
	ss.logger.Info("开始更新安全服务配置")

	// 1. 更新认证配置
	if err := ss.updateAuthConfig(oldConfig, cfg); err != nil {
		ss.logger.Warn(fmt.Sprintf("更新认证配置失败: %v", err))
	}

	// 2. 更新加密配置
	if err := ss.updateEncryptionConfig(oldConfig, cfg); err != nil {
		ss.logger.Warn(fmt.Sprintf("更新加密配置失败: %v", err))
	}

	// 3. 更新TLS配置
	if err := ss.updateTLSConfig(oldConfig, cfg); err != nil {
		ss.logger.Warn(fmt.Sprintf("更新TLS配置失败: %v", err))
	}

	// 4. 清理过期令牌（内部调用，不获取锁）
	cleanedCount := ss.cleanupExpiredTokensInternal()
	if cleanedCount > 0 {
		ss.logger.Info(fmt.Sprintf("清理了 %d 个过期认证令牌", cleanedCount))
	}

	ss.logger.Info("安全服务配置更新完成")
	return nil
}

// Authenticate 认证请求
func (ss *securityService) Authenticate(r *http.Request) interface{} {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	if ss.config.Auth == nil || ss.config.Auth.Type == "none" {
		return AuthResult{Authenticated: true}
	}

	// 从请求头获取认证信息
	auth := r.Header.Get("Authorization")
	if auth == "" {
		return AuthResult{
			Authenticated: false,
			Error:         fmt.Errorf("缺少Authorization头"),
		}
	}

	// 解析Bearer token
	if !strings.HasPrefix(auth, "Bearer ") {
		return AuthResult{
			Authenticated: false,
			Error:         fmt.Errorf("无效的认证格式"),
		}
	}

	token := strings.TrimPrefix(auth, "Bearer ")
	authToken, exists := ss.authTokens[token]
	if !exists {
		return AuthResult{
			Authenticated: false,
			Error:         fmt.Errorf("无效的认证令牌"),
		}
	}

	// 检查令牌是否过期
	if time.Now().After(authToken.ExpiresAt) {
		delete(ss.authTokens, token)
		return AuthResult{
			Authenticated: false,
			Error:         fmt.Errorf("认证令牌已过期"),
		}
	}

	// 更新最后使用时间
	authToken.LastUsed = time.Now()

	return AuthResult{
		Authenticated: true,
		UserID:        authToken.UserID,
		Scopes:        authToken.Scopes,
	}
}

// GenerateToken 生成认证令牌
func (ss *securityService) GenerateToken(userID string, scopes []string, duration time.Duration) (string, error) {
	ss.mu.Lock()
	defer ss.mu.Unlock()

	// 生成随机令牌
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return "", fmt.Errorf("生成令牌失败: %v", err)
	}

	token := base64.URLEncoding.EncodeToString(tokenBytes)

	// 创建认证令牌
	authToken := &AuthToken{
		Token:     token,
		UserID:    userID,
		ExpiresAt: time.Now().Add(duration),
		Scopes:    scopes,
		CreatedAt: time.Now(),
		LastUsed:  time.Now(),
	}

	ss.authTokens[token] = authToken
	ss.logger.Debug(fmt.Sprintf("生成认证令牌: user_id=%s, scopes=%v", userID, scopes))

	return token, nil
}

// RevokeToken 撤销认证令牌
func (ss *securityService) RevokeToken(token string) bool {
	ss.mu.Lock()
	defer ss.mu.Unlock()

	if _, exists := ss.authTokens[token]; exists {
		delete(ss.authTokens, token)
		ss.logger.Debug(fmt.Sprintf("撤销认证令牌: %s", token))
		return true
	}

	return false
}

// ValidateScope 验证权限范围
func (ss *securityService) ValidateScope(token string, requiredScope string) bool {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	authToken, exists := ss.authTokens[token]
	if !exists {
		return false
	}

	for _, scope := range authToken.Scopes {
		if scope == requiredScope || scope == "*" {
			return true
		}
	}

	return false
}

// Encrypt 加密数据
func (ss *securityService) Encrypt(plaintext string) (interface{}, error) {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	if ss.config.Encryption == nil {
		return EncryptionResult{}, fmt.Errorf("加密功能未启用")
	}

	block, err := aes.NewCipher(ss.encryptionKey)
	if err != nil {
		return EncryptionResult{}, fmt.Errorf("创建加密器失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return EncryptionResult{}, fmt.Errorf("创建GCM失败: %v", err)
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return EncryptionResult{}, fmt.Errorf("生成nonce失败: %v", err)
	}

	ciphertext := gcm.Seal(nil, nonce, []byte(plaintext), nil)

	return EncryptionResult{
		Ciphertext: base64.StdEncoding.EncodeToString(ciphertext),
		Nonce:      base64.StdEncoding.EncodeToString(nonce),
	}, nil
}

// Decrypt 解密数据
func (ss *securityService) Decrypt(ciphertext, nonce string) (string, error) {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	if ss.config.Encryption == nil {
		return "", fmt.Errorf("加密功能未启用")
	}

	block, err := aes.NewCipher(ss.encryptionKey)
	if err != nil {
		return "", fmt.Errorf("创建解密器失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建GCM失败: %v", err)
	}

	ciphertextBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("解码密文失败: %v", err)
	}

	nonceBytes, err := base64.StdEncoding.DecodeString(nonce)
	if err != nil {
		return "", fmt.Errorf("解码nonce失败: %v", err)
	}

	plaintext, err := gcm.Open(nil, nonceBytes, ciphertextBytes, nil)
	if err != nil {
		return "", fmt.Errorf("解密失败: %v", err)
	}

	return string(plaintext), nil
}

// GetTLSConfig 获取TLS配置
func (ss *securityService) GetTLSConfig() *tls.Config {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	return ss.tlsConfig
}

// ValidateRequest 验证请求安全性
func (ss *securityService) ValidateRequest(r *http.Request) error {
	// 检查请求头
	if err := ss.validateHeaders(r); err != nil {
		return err
	}

	// 检查请求大小
	if err := ss.validateRequestSize(r); err != nil {
		return err
	}

	// 检查内容类型
	if err := ss.validateContentType(r); err != nil {
		return err
	}

	return nil
}

// GetAuthStats 获取认证统计
func (ss *securityService) GetAuthStats() map[string]interface{} {
	ss.mu.RLock()
	defer ss.mu.RUnlock()

	activeTokens := 0
	expiredTokens := 0
	now := time.Now()

	for _, token := range ss.authTokens {
		if now.After(token.ExpiresAt) {
			expiredTokens++
		} else {
			activeTokens++
		}
	}

	return map[string]interface{}{
		"active_tokens":      activeTokens,
		"expired_tokens":     expiredTokens,
		"total_tokens":       len(ss.authTokens),
		"auth_enabled":       ss.config.Auth != nil && ss.config.Auth.Type != "none",
		"encryption_enabled": ss.config.Encryption != nil,
		"tls_enabled":        ss.config.TLS != nil,
	}
}

// CleanupExpiredTokens 清理过期令牌
func (ss *securityService) CleanupExpiredTokens() int {
	ss.mu.Lock()
	defer ss.mu.Unlock()

	return ss.cleanupExpiredTokensInternal()
}

// cleanupExpiredTokensInternal 内部清理过期令牌（不获取锁）
func (ss *securityService) cleanupExpiredTokensInternal() int {
	now := time.Now()
	expiredCount := 0

	for token, authToken := range ss.authTokens {
		if now.After(authToken.ExpiresAt) {
			delete(ss.authTokens, token)
			expiredCount++
		}
	}

	if expiredCount > 0 {
		ss.logger.Debug(fmt.Sprintf("清理过期令牌: %d个", expiredCount))
	}

	return expiredCount
}

// initEncryptionKey 初始化加密密钥
func (ss *securityService) initEncryptionKey() {
	if ss.config.Encryption == nil {
		return
	}

	// 生成随机密钥
	key := make([]byte, 32)
	if _, err := rand.Read(key); err != nil {
		ss.logger.Error(fmt.Sprintf("生成加密密钥失败: %v", err))
		return
	}
	ss.encryptionKey = key
	ss.logger.Info("加密功能已启用")
}

// initTLSConfig 初始化TLS配置
func (ss *securityService) initTLSConfig() {
	if ss.config.TLS == nil {
		return
	}

	ss.tlsConfig = &tls.Config{
		MinVersion: ss.getTLSVersion(ss.config.TLS.MinVersion),
		MaxVersion: ss.getTLSVersion(ss.config.TLS.MaxVersion),
	}

	// 加载证书
	if ss.config.TLS.CertFile != "" && ss.config.TLS.KeyFile != "" {
		cert, err := tls.LoadX509KeyPair(ss.config.TLS.CertFile, ss.config.TLS.KeyFile)
		if err != nil {
			ss.logger.Error(fmt.Sprintf("加载TLS证书失败: %v", err))
			return
		}
		ss.tlsConfig.Certificates = []tls.Certificate{cert}
	}

	ss.logger.Info("TLS配置已初始化")
}

// getTLSVersion 获取TLS版本
func (ss *securityService) getTLSVersion(version string) uint16 {
	switch version {
	case "1.0":
		return tls.VersionTLS10
	case "1.1":
		return tls.VersionTLS11
	case "1.2":
		return tls.VersionTLS12
	case "1.3":
		return tls.VersionTLS13
	default:
		return tls.VersionTLS12
	}
}

// validateHeaders 验证请求头
func (ss *securityService) validateHeaders(r *http.Request) error {
	// 检查危险的请求头
	dangerousHeaders := []string{
		"X-Forwarded-For",
		"X-Real-IP",
		"X-Originating-IP",
	}

	for _, header := range dangerousHeaders {
		if value := r.Header.Get(header); value != "" {
			// 这里可以添加更严格的验证逻辑
			ss.logger.Debug(fmt.Sprintf("检测到潜在危险请求头: %s=%s", header, value))
		}
	}

	return nil
}

// validateRequestSize 验证请求大小
func (ss *securityService) validateRequestSize(r *http.Request) error {
	maxSize := int64(10 * 1024 * 1024) // 10MB
	if r.ContentLength > maxSize {
		return fmt.Errorf("请求体过大: %d bytes (最大: %d bytes)", r.ContentLength, maxSize)
	}
	return nil
}

// validateContentType 验证内容类型
func (ss *securityService) validateContentType(r *http.Request) error {
	contentType := r.Header.Get("Content-Type")
	if contentType == "" {
		return nil // 允许空内容类型
	}

	// 检查是否为允许的内容类型
	allowedTypes := []string{
		"application/json",
		"application/x-www-form-urlencoded",
		"multipart/form-data",
		"text/plain",
		"text/html",
	}

	for _, allowedType := range allowedTypes {
		if strings.HasPrefix(contentType, allowedType) {
			return nil
		}
	}

	ss.logger.Warn(fmt.Sprintf("未知内容类型: %s", contentType))
	return nil // 警告但不阻止
}

// updateAuthConfig 更新认证配置
func (ss *securityService) updateAuthConfig(oldConfig, newConfig *config.SecurityConfig) error {
	// 检查认证配置变化
	if oldConfig != nil && newConfig != nil {
		// 这里可以添加具体的认证配置更新逻辑
		// 例如：更新令牌过期时间、认证方式等
		ss.logger.Debug("认证配置已更新")
	}
	return nil
}

// updateEncryptionConfig 更新加密配置
func (ss *securityService) updateEncryptionConfig(oldConfig, newConfig *config.SecurityConfig) error {
	// 检查是否需要重新初始化加密密钥
	if ss.needsEncryptionKeyUpdate(oldConfig, newConfig) {
		ss.logger.Info("检测到加密配置变化，重新初始化加密密钥")
		ss.initEncryptionKey()
	}
	return nil
}

// updateTLSConfig 更新TLS配置
func (ss *securityService) updateTLSConfig(oldConfig, newConfig *config.SecurityConfig) error {
	// 检查是否需要重新初始化TLS配置
	if ss.needsTLSConfigUpdate(oldConfig, newConfig) {
		ss.logger.Info("检测到TLS配置变化，重新初始化TLS配置")
		ss.initTLSConfig()
	}
	return nil
}

// needsEncryptionKeyUpdate 检查是否需要更新加密密钥
func (ss *securityService) needsEncryptionKeyUpdate(oldConfig, newConfig *config.SecurityConfig) bool {
	// 这里可以添加具体的检查逻辑
	// 例如：检查加密算法、密钥长度等是否发生变化
	return false // 暂时返回false，避免频繁重新初始化
}

// needsTLSConfigUpdate 检查是否需要更新TLS配置
func (ss *securityService) needsTLSConfigUpdate(oldConfig, newConfig *config.SecurityConfig) bool {
	// 这里可以添加具体的检查逻辑
	// 例如：检查证书路径、TLS版本等是否发生变化
	return false // 暂时返回false，避免频繁重新初始化
}
