// Package services 提供各种服务实现
package advanced

import (
	"fmt"
	"net/http"
	"sync"
	"time"

	"flexproxy/internal/config"
	"flexproxy/common/logger"
)

// ProtocolsConfigService 协议配置管理服务接口
type ProtocolsConfigService interface {
	Start() error
	Stop() error
	GetConfig() *config.ProtocolsConfig
	UpdateConfig(config *config.ProtocolsConfig) error
	IsProtocolEnabled(protocol string) bool
	GetProtocolConfig(protocol string) interface{}
	ValidateProtocolConfig() error
	GetSupportedProtocols() []string
}

// protocolsConfigService 协议配置管理服务实现
type protocolsConfigService struct {
	mu      sync.RWMutex
	config  *config.ProtocolsConfig
	logger  logger.Logger
	running bool
	clients map[string]interface{} // 存储各协议的客户端
}

// NewProtocolsConfigService 创建新的协议配置管理服务
func NewProtocolsConfigService(cfg *config.ProtocolsConfig, log logger.Logger) ProtocolsConfigService {
	if cfg == nil {
		cfg = &config.ProtocolsConfig{
			HTTP: &config.HTTPConfig{
				Enabled:     true,
				Version:     "1.1",
				KeepAlive:   true,
				Compression: true,
			},
			HTTPS: &config.HTTPSConfig{
				Enabled:     true,
				Version:     "1.1",
				KeepAlive:   true,
				Compression: true,
				VerifySSL:   true,
			},
			SOCKS4: &config.SOCKS4Config{
				Enabled: false,
			},
			SOCKS5: &config.SOCKS5Config{
				Enabled:      true,
				AuthRequired: false,
			},
			DNS: &config.DNSProtocolConfig{
				UDP:   true,
				TCP:   false,
				TLS:   false,
				HTTPS: false,
				DOH:   false,
			},
		}
	}

	pcs := &protocolsConfigService{
		config:  cfg,
		logger:  log,
		clients: make(map[string]interface{}),
	}

	pcs.logger.Info("协议配置管理服务已初始化")
	return pcs
}

// Start 启动协议配置管理服务
func (pcs *protocolsConfigService) Start() error {
	pcs.mu.Lock()
	defer pcs.mu.Unlock()

	if pcs.running {
		return fmt.Errorf("协议配置管理服务已在运行")
	}

	// 验证配置
	if err := pcs.ValidateProtocolConfig(); err != nil {
		return fmt.Errorf("协议配置验证失败: %v", err)
	}

	// 初始化协议客户端
	if err := pcs.initializeClients(); err != nil {
		return fmt.Errorf("初始化协议客户端失败: %v", err)
	}

	pcs.running = true
	pcs.logger.Info("协议配置管理服务已启动")
	return nil
}

// Stop 停止协议配置管理服务
func (pcs *protocolsConfigService) Stop() error {
	pcs.mu.Lock()
	defer pcs.mu.Unlock()

	if !pcs.running {
		return nil
	}

	// 清理客户端
	pcs.cleanupClients()

	pcs.running = false
	pcs.logger.Info("协议配置管理服务已停止")
	return nil
}

// GetConfig 获取协议配置
func (pcs *protocolsConfigService) GetConfig() *config.ProtocolsConfig {
	pcs.mu.RLock()
	defer pcs.mu.RUnlock()
	return pcs.config
}

// UpdateConfig 更新协议配置
func (pcs *protocolsConfigService) UpdateConfig(cfg *config.ProtocolsConfig) error {
	pcs.mu.Lock()
	defer pcs.mu.Unlock()

	if cfg == nil {
		return fmt.Errorf("配置不能为空")
	}

	// 验证新配置
	oldConfig := pcs.config
	pcs.config = cfg

	if err := pcs.ValidateProtocolConfig(); err != nil {
		pcs.config = oldConfig // 回滚
		return fmt.Errorf("新配置验证失败: %v", err)
	}

	// 如果服务正在运行，重新初始化客户端
	if pcs.running {
		pcs.cleanupClients()
		if err := pcs.initializeClients(); err != nil {
			pcs.logger.Warn(fmt.Sprintf("重新初始化客户端失败: %v", err))
		}
	}

	pcs.logger.Info("协议配置已更新")
	return nil
}

// IsProtocolEnabled 检查协议是否启用
func (pcs *protocolsConfigService) IsProtocolEnabled(protocol string) bool {
	pcs.mu.RLock()
	defer pcs.mu.RUnlock()

	switch protocol {
	case "http":
		return pcs.config.HTTP != nil && pcs.config.HTTP.Enabled
	case "https":
		return pcs.config.HTTPS != nil && pcs.config.HTTPS.Enabled
	case "socks4":
		return pcs.config.SOCKS4 != nil && pcs.config.SOCKS4.Enabled
	case "socks5":
		return pcs.config.SOCKS5 != nil && pcs.config.SOCKS5.Enabled
	case "dns":
		return pcs.config.DNS != nil && (pcs.config.DNS.UDP || pcs.config.DNS.TCP || pcs.config.DNS.TLS || pcs.config.DNS.HTTPS || pcs.config.DNS.DOH)
	default:
		return false
	}
}

// GetProtocolConfig 获取指定协议的配置
func (pcs *protocolsConfigService) GetProtocolConfig(protocol string) interface{} {
	pcs.mu.RLock()
	defer pcs.mu.RUnlock()

	switch protocol {
	case "http":
		return pcs.config.HTTP
	case "https":
		return pcs.config.HTTPS
	case "socks4":
		return pcs.config.SOCKS4
	case "socks5":
		return pcs.config.SOCKS5
	case "dns":
		return pcs.config.DNS
	default:
		return nil
	}
}

// ValidateProtocolConfig 验证协议配置
func (pcs *protocolsConfigService) ValidateProtocolConfig() error {
	// 验证HTTP配置
	if pcs.config.HTTP != nil && pcs.config.HTTP.Enabled {
		if err := pcs.validateHTTPConfig(pcs.config.HTTP); err != nil {
			return fmt.Errorf("HTTP配置无效: %v", err)
		}
	}

	// 验证HTTPS配置
	if pcs.config.HTTPS != nil && pcs.config.HTTPS.Enabled {
		if err := pcs.validateHTTPSConfig(pcs.config.HTTPS); err != nil {
			return fmt.Errorf("HTTPS配置无效: %v", err)
		}
	}

	// 验证SOCKS4配置
	if pcs.config.SOCKS4 != nil && pcs.config.SOCKS4.Enabled {
		if err := pcs.validateSOCKS4Config(pcs.config.SOCKS4); err != nil {
			return fmt.Errorf("SOCKS4配置无效: %v", err)
		}
	}

	// 验证SOCKS5配置
	if pcs.config.SOCKS5 != nil && pcs.config.SOCKS5.Enabled {
		if err := pcs.validateSOCKS5Config(pcs.config.SOCKS5); err != nil {
			return fmt.Errorf("SOCKS5配置无效: %v", err)
		}
	}

	// 验证DNS配置
	if pcs.config.DNS != nil {
		if err := pcs.validateDNSConfig(pcs.config.DNS); err != nil {
			return fmt.Errorf("DNS配置无效: %v", err)
		}
	}

	return nil
}

// GetSupportedProtocols 获取支持的协议列表
func (pcs *protocolsConfigService) GetSupportedProtocols() []string {
	protocols := make([]string, 0)

	if pcs.IsProtocolEnabled("http") {
		protocols = append(protocols, "http")
	}
	if pcs.IsProtocolEnabled("https") {
		protocols = append(protocols, "https")
	}
	if pcs.IsProtocolEnabled("socks4") {
		protocols = append(protocols, "socks4")
	}
	if pcs.IsProtocolEnabled("socks5") {
		protocols = append(protocols, "socks5")
	}
	if pcs.IsProtocolEnabled("dns") {
		protocols = append(protocols, "dns")
	}

	return protocols
}

// initializeClients 初始化协议客户端
func (pcs *protocolsConfigService) initializeClients() error {
	// 初始化HTTP客户端
	if pcs.config.HTTP != nil && pcs.config.HTTP.Enabled {
		client, err := pcs.createHTTPClient(pcs.config.HTTP)
		if err != nil {
			return fmt.Errorf("创建HTTP客户端失败: %v", err)
		}
		pcs.clients["http"] = client
		pcs.logger.Info("HTTP客户端已初始化")
	}

	// 初始化HTTPS客户端
	if pcs.config.HTTPS != nil && pcs.config.HTTPS.Enabled {
		client, err := pcs.createHTTPSClient(pcs.config.HTTPS)
		if err != nil {
			return fmt.Errorf("创建HTTPS客户端失败: %v", err)
		}
		pcs.clients["https"] = client
		pcs.logger.Info("HTTPS客户端已初始化")
	}

	// 其他协议的客户端初始化...
	// 这里可以添加SOCKS4、SOCKS5、DNS等协议的客户端初始化

	return nil
}

// cleanupClients 清理客户端
func (pcs *protocolsConfigService) cleanupClients() {
	for protocol := range pcs.clients {
		delete(pcs.clients, protocol)
		pcs.logger.Info(fmt.Sprintf("%s客户端已清理", protocol))
	}
}

// createHTTPClient 创建HTTP客户端
func (pcs *protocolsConfigService) createHTTPClient(cfg *config.HTTPConfig) (*http.Client, error) {
	// 使用默认超时时间
	timeout := 30 * time.Second

	transport := &http.Transport{
		DisableKeepAlives: !cfg.KeepAlive,
		DisableCompression: !cfg.Compression,
	}

	client := &http.Client{
		Timeout:   timeout,
		Transport: transport,
	}

	return client, nil
}

// createHTTPSClient 创建HTTPS客户端
func (pcs *protocolsConfigService) createHTTPSClient(cfg *config.HTTPSConfig) (*http.Client, error) {
	// 使用默认超时时间
	timeout := 30 * time.Second

	transport := &http.Transport{
		DisableKeepAlives:  !cfg.KeepAlive,
		DisableCompression: !cfg.Compression,
	}

	client := &http.Client{
		Timeout:   timeout,
		Transport: transport,
	}

	return client, nil
}

// 验证方法
func (pcs *protocolsConfigService) validateHTTPConfig(cfg *config.HTTPConfig) error {
	if cfg.Version != "1.0" && cfg.Version != "1.1" && cfg.Version != "2.0" {
		return fmt.Errorf("不支持的HTTP版本: %s", cfg.Version)
	}

	return nil
}

func (pcs *protocolsConfigService) validateHTTPSConfig(cfg *config.HTTPSConfig) error {
	if cfg.Version != "1.0" && cfg.Version != "1.1" && cfg.Version != "2.0" {
		return fmt.Errorf("不支持的HTTPS版本: %s", cfg.Version)
	}

	return nil
}

func (pcs *protocolsConfigService) validateSOCKS4Config(config *config.SOCKS4Config) error {
	// SOCKS4配置验证
	return nil
}

func (pcs *protocolsConfigService) validateSOCKS5Config(config *config.SOCKS5Config) error {
	// SOCKS5配置验证
	return nil
}

func (pcs *protocolsConfigService) validateDNSConfig(cfg *config.DNSProtocolConfig) error {
	// 至少要启用一种DNS协议
	if !cfg.UDP && !cfg.TCP && !cfg.TLS && !cfg.HTTPS && !cfg.DOH {
		return fmt.Errorf("至少需要启用一种DNS协议")
	}
	return nil
}
