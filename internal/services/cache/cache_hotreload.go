package cache

import (
	"fmt"
	"sync"
	"time"

	"flexproxy/common/logger"
	"flexproxy/internal/config"
	"flexproxy/internal/interfaces"
)

// CacheEntry 缓存条目
type CacheEntry struct {
	Value     interface{} `json:"value"`
	ExpiresAt time.Time   `json:"expires_at"`
	CreatedAt time.Time   `json:"created_at"`
	HitCount  int64       `json:"hit_count"`
}

// CacheHotReloadManager 缓存热重载管理器
type CacheHotReloadManager struct {
	mu                sync.RWMutex
	cacheService      interfaces.CacheService
	logger            *logger.LoggerAdapter
	migrationHistory  []CacheMigrationRecord
	
	// 数据迁移配置
	migrationConfig   *CacheMigrationConfig
}

// CacheMigrationConfig 缓存迁移配置
type CacheMigrationConfig struct {
	Enable                bool          `yaml:"enable" json:"enable"`
	PreserveDNSCache      bool          `yaml:"preserve_dns_cache" json:"preserve_dns_cache"`
	PreserveRegexCache    bool          `yaml:"preserve_regex_cache" json:"preserve_regex_cache"`
	PreserveResponseCache bool          `yaml:"preserve_response_cache" json:"preserve_response_cache"`
	MigrationTimeout      time.Duration `yaml:"migration_timeout" json:"migration_timeout"`
	BackupBeforeMigration bool          `yaml:"backup_before_migration" json:"backup_before_migration"`
	MaxMigrationRetries   int           `yaml:"max_migration_retries" json:"max_migration_retries"`
}

// CacheMigrationRecord 缓存迁移记录
type CacheMigrationRecord struct {
	Timestamp       time.Time `json:"timestamp"`
	FromVersion     string    `json:"from_version"`
	ToVersion       string    `json:"to_version"`
	MigratedEntries int       `json:"migrated_entries"`
	Success         bool      `json:"success"`
	ErrorMessage    string    `json:"error_message,omitempty"`
	Duration        time.Duration `json:"duration"`
}

// CacheSnapshot 缓存快照
type CacheSnapshot struct {
	Timestamp     time.Time                `json:"timestamp"`
	Version       string                   `json:"version"`
	DNSCache      map[string]CacheEntry    `json:"dns_cache"`
	RegexCache    map[string]CacheEntry    `json:"regex_cache"`
	ResponseCache map[string]CacheEntry    `json:"response_cache"`
	TotalEntries  int                      `json:"total_entries"`
}

// NewCacheHotReloadManager 创建缓存热重载管理器
func NewCacheHotReloadManager(cacheService interfaces.CacheService) *CacheHotReloadManager {
	return &CacheHotReloadManager{
		cacheService:     cacheService,
		logger:           logger.GetLoggerAdapter(logger.ModuleCache),
		migrationHistory: make([]CacheMigrationRecord, 0),
		migrationConfig: &CacheMigrationConfig{
			Enable:                true,
			PreserveDNSCache:      true,
			PreserveRegexCache:    true,
			PreserveResponseCache: false, // 响应缓存通常不需要保留
			MigrationTimeout:      30 * time.Second,
			BackupBeforeMigration: true,
			MaxMigrationRetries:   3,
		},
	}
}

// UpdateConfig 更新缓存配置并执行数据迁移
func (chrm *CacheHotReloadManager) UpdateConfig(oldConfig, newConfig *config.CacheConfig) error {
	chrm.mu.Lock()
	defer chrm.mu.Unlock()

	chrm.logger.Info("开始缓存系统热重载和数据迁移")
	startTime := time.Now()

	// 1. 创建缓存快照
	snapshot, err := chrm.createCacheSnapshot("before_migration")
	if err != nil {
		chrm.logger.Error(fmt.Sprintf("创建缓存快照失败: %v", err))
		return fmt.Errorf("创建缓存快照失败: %v", err)
	}

	// 2. 备份当前缓存数据（如果启用）
	var backupSnapshot *CacheSnapshot
	if chrm.migrationConfig.BackupBeforeMigration {
		backupSnapshot = snapshot
		chrm.logger.Info(fmt.Sprintf("已备份 %d 个缓存条目", snapshot.TotalEntries))
	}

	// 3. 分析配置变更
	migrationPlan := chrm.analyzeCacheConfigChanges(oldConfig, newConfig)
	chrm.logger.Info(fmt.Sprintf("缓存迁移计划: %s", migrationPlan.Description))

	// 4. 执行数据迁移
	migrationResult, err := chrm.executeCacheMigration(migrationPlan, snapshot)
	if err != nil {
		chrm.logger.Error(fmt.Sprintf("缓存数据迁移失败: %v", err))
		
		// 尝试恢复备份
		if backupSnapshot != nil {
			chrm.logger.Info("尝试从备份恢复缓存数据")
			if restoreErr := chrm.restoreFromSnapshot(backupSnapshot); restoreErr != nil {
				chrm.logger.Error(fmt.Sprintf("从备份恢复失败: %v", restoreErr))
			}
		}
		
		return fmt.Errorf("缓存数据迁移失败: %v", err)
	}

	// 5. 记录迁移历史
	migrationRecord := CacheMigrationRecord{
		Timestamp:       startTime,
		FromVersion:     chrm.getConfigVersion(oldConfig),
		ToVersion:       chrm.getConfigVersion(newConfig),
		MigratedEntries: migrationResult.MigratedEntries,
		Success:         true,
		Duration:        time.Since(startTime),
	}
	chrm.migrationHistory = append(chrm.migrationHistory, migrationRecord)

	chrm.logger.Info(fmt.Sprintf("缓存系统热重载完成，迁移了 %d 个条目，耗时: %v", 
		migrationResult.MigratedEntries, migrationRecord.Duration))

	return nil
}

// CacheMigrationPlan 缓存迁移计划
type CacheMigrationPlan struct {
	Description       string
	PreserveDNS       bool
	PreserveRegex     bool
	PreserveResponse  bool
	ClearExpired      bool
	ResizeCache       bool
	NewDNSCacheSize   int
	NewRegexCacheSize int
}

// CacheMigrationResult 缓存迁移结果
type CacheMigrationResult struct {
	MigratedEntries int
	DroppedEntries  int
	ErrorCount      int
}

// analyzeCacheConfigChanges 分析缓存配置变更
func (chrm *CacheHotReloadManager) analyzeCacheConfigChanges(oldConfig, newConfig *config.CacheConfig) *CacheMigrationPlan {
	plan := &CacheMigrationPlan{
		PreserveDNS:      chrm.migrationConfig.PreserveDNSCache,
		PreserveRegex:    chrm.migrationConfig.PreserveRegexCache,
		PreserveResponse: chrm.migrationConfig.PreserveResponseCache,
		ClearExpired:     true, // 总是清理过期条目
	}

	// 检查DNS缓存大小变化
	if oldConfig != nil && newConfig != nil {
		// 由于 CacheConfig 结构复杂，我们简化处理
		// 实际项目中应该根据具体的配置结构来访问字段
		plan.Description += "缓存配置已更新; "
	}

	if plan.Description == "" {
		plan.Description = "配置无重大变更，执行常规清理"
	}

	return plan
}

// executeCacheMigration 执行缓存迁移
func (chrm *CacheHotReloadManager) executeCacheMigration(plan *CacheMigrationPlan, snapshot *CacheSnapshot) (*CacheMigrationResult, error) {
	result := &CacheMigrationResult{}

	// 1. 清理过期条目
	if plan.ClearExpired {
		expiredCount := chrm.clearExpiredEntries()
		result.DroppedEntries += expiredCount
		chrm.logger.Debug(fmt.Sprintf("清理了 %d 个过期缓存条目", expiredCount))
	}

	// 2. 迁移DNS缓存
	if plan.PreserveDNS && len(snapshot.DNSCache) > 0 {
		migratedDNS := chrm.migrateDNSCache(snapshot.DNSCache, plan)
		result.MigratedEntries += migratedDNS
		chrm.logger.Debug(fmt.Sprintf("迁移了 %d 个DNS缓存条目", migratedDNS))
	}

	// 3. 迁移正则缓存
	if plan.PreserveRegex && len(snapshot.RegexCache) > 0 {
		migratedRegex := chrm.migrateRegexCache(snapshot.RegexCache, plan)
		result.MigratedEntries += migratedRegex
		chrm.logger.Debug(fmt.Sprintf("迁移了 %d 个正则缓存条目", migratedRegex))
	}

	// 4. 迁移响应缓存（如果需要）
	if plan.PreserveResponse && len(snapshot.ResponseCache) > 0 {
		migratedResponse := chrm.migrateResponseCache(snapshot.ResponseCache, plan)
		result.MigratedEntries += migratedResponse
		chrm.logger.Debug(fmt.Sprintf("迁移了 %d 个响应缓存条目", migratedResponse))
	}

	return result, nil
}

// createCacheSnapshot 创建缓存快照
func (chrm *CacheHotReloadManager) createCacheSnapshot(version string) (*CacheSnapshot, error) {
	snapshot := &CacheSnapshot{
		Timestamp:     time.Now(),
		Version:       version,
		DNSCache:      make(map[string]CacheEntry),
		RegexCache:    make(map[string]CacheEntry),
		ResponseCache: make(map[string]CacheEntry),
	}

	// 获取所有DNS缓存条目
	allDNSCache := chrm.cacheService.GetAllDNSCache()
	for key, cacheData := range allDNSCache {
		if cacheDataMap, ok := cacheData.(map[string]interface{}); ok {
			entry := CacheEntry{
				Value:     cacheDataMap["value"],
				CreatedAt: time.Now(), // 默认创建时间
				HitCount:  1,          // 默认命中次数
			}

			// 解析过期时间
			if expiresAt, ok := cacheDataMap["expires_at"].(time.Time); ok {
				entry.ExpiresAt = expiresAt
			} else {
				entry.ExpiresAt = time.Now().Add(5 * time.Minute) // 默认5分钟过期
			}

			// 解析创建时间
			if createdAt, ok := cacheDataMap["created_at"].(time.Time); ok {
				entry.CreatedAt = createdAt
			}

			snapshot.DNSCache[key] = entry
		}
	}

	// 获取所有正则缓存条目
	allRegexCache := chrm.cacheService.GetAllRegexCache()
	for pattern, cacheData := range allRegexCache {
		if cacheDataMap, ok := cacheData.(map[string]interface{}); ok {
			entry := CacheEntry{
				Value:     cacheDataMap["value"],
				CreatedAt: time.Now(), // 默认创建时间
				HitCount:  1,          // 默认命中次数
			}

			// 解析过期时间
			if expiresAt, ok := cacheDataMap["expires_at"].(time.Time); ok {
				entry.ExpiresAt = expiresAt
			} else {
				entry.ExpiresAt = time.Now().Add(24 * time.Hour) // 默认24小时过期
			}

			// 解析创建时间
			if createdAt, ok := cacheDataMap["created_at"].(time.Time); ok {
				entry.CreatedAt = createdAt
			}

			snapshot.RegexCache[pattern] = entry
		}
	}

	// 计算总条目数
	snapshot.TotalEntries = len(snapshot.DNSCache) + len(snapshot.RegexCache) + len(snapshot.ResponseCache)

	chrm.logger.Debug(fmt.Sprintf("创建缓存快照完成，包含 %d 个条目 (DNS: %d, Regex: %d, Response: %d)",
		snapshot.TotalEntries, len(snapshot.DNSCache), len(snapshot.RegexCache), len(snapshot.ResponseCache)))
	return snapshot, nil
}

// clearExpiredEntries 清理过期条目
func (chrm *CacheHotReloadManager) clearExpiredEntries() int {
	// 获取清理前的缓存统计
	statsBefore := chrm.cacheService.GetCacheStats()
	beforeDNSSize := 0
	beforeRegexSize := 0

	if statsBefore != nil {
		if size, ok := statsBefore["dns_cache_size"].(int); ok {
			beforeDNSSize = size
		}
		if size, ok := statsBefore["regex_cache_size"].(int); ok {
			beforeRegexSize = size
		}
	}

	// 触发缓存清理 - 由于缓存服务内部有自动清理机制，我们可以通过获取统计信息来触发清理检查
	// 这里我们通过多次调用统计信息来间接触发内部的过期检查
	for i := 0; i < 3; i++ {
		chrm.cacheService.GetCacheStats()
		time.Sleep(10 * time.Millisecond) // 短暂等待，让内部清理机制有机会运行
	}

	// 获取清理后的缓存统计
	statsAfter := chrm.cacheService.GetCacheStats()
	afterDNSSize := 0
	afterRegexSize := 0

	if statsAfter != nil {
		if size, ok := statsAfter["dns_cache_size"].(int); ok {
			afterDNSSize = size
		}
		if size, ok := statsAfter["regex_cache_size"].(int); ok {
			afterRegexSize = size
		}
	}

	// 计算清理的条目数
	expiredCount := (beforeDNSSize - afterDNSSize) + (beforeRegexSize - afterRegexSize)

	if expiredCount > 0 {
		chrm.logger.Info(fmt.Sprintf("清理了 %d 个过期缓存条目 (DNS: %d->%d, Regex: %d->%d)",
			expiredCount, beforeDNSSize, afterDNSSize, beforeRegexSize, afterRegexSize))
	} else {
		chrm.logger.Debug("没有发现过期的缓存条目")
	}

	return expiredCount
}

// migrateDNSCache 迁移DNS缓存
func (chrm *CacheHotReloadManager) migrateDNSCache(dnsCache map[string]CacheEntry, plan *CacheMigrationPlan) int {
	migratedCount := 0
	now := time.Now()

	chrm.logger.Debug(fmt.Sprintf("开始迁移 %d 个DNS缓存条目", len(dnsCache)))

	for key, entry := range dnsCache {
		// 跳过过期条目
		if now.After(entry.ExpiresAt) {
			chrm.logger.Debug(fmt.Sprintf("跳过过期的DNS缓存条目: %s", key))
			continue
		}

		// 检查缓存大小限制
		if plan.NewDNSCacheSize > 0 {
			currentStats := chrm.cacheService.GetCacheStats()
			if currentStats != nil {
				if currentSize, ok := currentStats["dns_cache_size"].(int); ok && currentSize >= plan.NewDNSCacheSize {
					chrm.logger.Debug(fmt.Sprintf("DNS缓存已达到大小限制 %d，停止迁移", plan.NewDNSCacheSize))
					break
				}
			}
		}

		// 迁移条目 - 通过缓存服务的公共接口设置
		if dnsValue, ok := entry.Value.([]string); ok {
			// 计算剩余TTL
			remainingTTL := int(entry.ExpiresAt.Sub(now).Seconds())
			if remainingTTL > 0 {
				chrm.cacheService.SetDNSCache(key, dnsValue, remainingTTL)
				migratedCount++
				chrm.logger.Debug(fmt.Sprintf("迁移DNS缓存条目: %s, TTL: %d秒", key, remainingTTL))
			}
		} else if stringValue, ok := entry.Value.(string); ok {
			// 处理字符串类型的DNS值（转换为字符串数组）
			remainingTTL := int(entry.ExpiresAt.Sub(now).Seconds())
			if remainingTTL > 0 {
				chrm.cacheService.SetDNSCache(key, []string{stringValue}, remainingTTL)
				migratedCount++
				chrm.logger.Debug(fmt.Sprintf("迁移DNS缓存条目（字符串转数组）: %s, TTL: %d秒", key, remainingTTL))
			}
		} else {
			chrm.logger.Warn(fmt.Sprintf("DNS缓存条目类型不匹配，跳过: %s, 类型: %T", key, entry.Value))
		}
	}

	chrm.logger.Info(fmt.Sprintf("DNS缓存迁移完成，成功迁移 %d 个条目", migratedCount))
	return migratedCount
}

// migrateRegexCache 迁移正则缓存
func (chrm *CacheHotReloadManager) migrateRegexCache(regexCache map[string]CacheEntry, plan *CacheMigrationPlan) int {
	migratedCount := 0
	now := time.Now()

	chrm.logger.Debug(fmt.Sprintf("开始迁移 %d 个正则缓存条目", len(regexCache)))

	for pattern, entry := range regexCache {
		// 跳过过期条目
		if now.After(entry.ExpiresAt) {
			chrm.logger.Debug(fmt.Sprintf("跳过过期的正则缓存条目: %s", pattern))
			continue
		}

		// 检查缓存大小限制
		if plan.NewRegexCacheSize > 0 {
			currentStats := chrm.cacheService.GetCacheStats()
			if currentStats != nil {
				if currentSize, ok := currentStats["regex_cache_size"].(int); ok && currentSize >= plan.NewRegexCacheSize {
					chrm.logger.Debug(fmt.Sprintf("正则缓存已达到大小限制 %d，停止迁移", plan.NewRegexCacheSize))
					break
				}
			}
		}

		// 迁移条目 - 通过缓存服务的公共接口设置
		// 正则缓存通常存储编译后的正则表达式对象
		chrm.cacheService.SetRegexCache(pattern, entry.Value)
		migratedCount++
		chrm.logger.Debug(fmt.Sprintf("迁移正则缓存条目: %s", pattern))
	}

	chrm.logger.Info(fmt.Sprintf("正则缓存迁移完成，成功迁移 %d 个条目", migratedCount))
	return migratedCount
}

// migrateResponseCache 迁移响应缓存
func (chrm *CacheHotReloadManager) migrateResponseCache(responseCache map[string]CacheEntry, plan *CacheMigrationPlan) int {
	// 响应缓存迁移逻辑
	// 由于响应缓存通常不需要保留，这里返回0
	return 0
}

// restoreFromSnapshot 从快照恢复缓存
func (chrm *CacheHotReloadManager) restoreFromSnapshot(snapshot *CacheSnapshot) error {
	chrm.logger.Info(fmt.Sprintf("开始从快照恢复缓存数据，快照时间: %s", snapshot.Timestamp.Format("2006-01-02 15:04:05")))

	restoredCount := 0
	now := time.Now()

	// 首先清空现有缓存
	chrm.logger.Debug("清空现有缓存以准备恢复")
	chrm.cacheService.ClearAllCache()

	// 恢复DNS缓存
	if len(snapshot.DNSCache) > 0 {
		chrm.logger.Debug(fmt.Sprintf("恢复 %d 个DNS缓存条目", len(snapshot.DNSCache)))
		for key, entry := range snapshot.DNSCache {
			// 检查条目是否仍然有效
			if now.After(entry.ExpiresAt) {
				chrm.logger.Debug(fmt.Sprintf("跳过过期的DNS缓存条目: %s", key))
				continue
			}

			// 恢复DNS缓存条目
			if dnsValue, ok := entry.Value.([]string); ok {
				remainingTTL := int(entry.ExpiresAt.Sub(now).Seconds())
				if remainingTTL > 0 {
					chrm.cacheService.SetDNSCache(key, dnsValue, remainingTTL)
					restoredCount++
					chrm.logger.Debug(fmt.Sprintf("恢复DNS缓存条目: %s, TTL: %d秒", key, remainingTTL))
				}
			} else if stringValue, ok := entry.Value.(string); ok {
				remainingTTL := int(entry.ExpiresAt.Sub(now).Seconds())
				if remainingTTL > 0 {
					chrm.cacheService.SetDNSCache(key, []string{stringValue}, remainingTTL)
					restoredCount++
					chrm.logger.Debug(fmt.Sprintf("恢复DNS缓存条目（字符串转数组）: %s, TTL: %d秒", key, remainingTTL))
				}
			} else {
				chrm.logger.Warn(fmt.Sprintf("DNS缓存条目类型不匹配，跳过恢复: %s, 类型: %T", key, entry.Value))
			}
		}
	}

	// 恢复正则缓存
	if len(snapshot.RegexCache) > 0 {
		chrm.logger.Debug(fmt.Sprintf("恢复 %d 个正则缓存条目", len(snapshot.RegexCache)))
		for pattern, entry := range snapshot.RegexCache {
			// 检查条目是否仍然有效
			if now.After(entry.ExpiresAt) {
				chrm.logger.Debug(fmt.Sprintf("跳过过期的正则缓存条目: %s", pattern))
				continue
			}

			// 恢复正则缓存条目
			chrm.cacheService.SetRegexCache(pattern, entry.Value)
			restoredCount++
			chrm.logger.Debug(fmt.Sprintf("恢复正则缓存条目: %s", pattern))
		}
	}

	// 验证恢复结果
	finalStats := chrm.cacheService.GetCacheStats()
	if finalStats != nil {
		chrm.logger.Info(fmt.Sprintf("缓存恢复完成，恢复了 %d 个条目，当前缓存状态: %+v", restoredCount, finalStats))
	} else {
		chrm.logger.Info(fmt.Sprintf("缓存恢复完成，恢复了 %d 个条目", restoredCount))
	}

	return nil
}

// getConfigVersion 获取配置版本标识
func (chrm *CacheHotReloadManager) getConfigVersion(config *config.CacheConfig) string {
	if config == nil {
		return "unknown"
	}

	// 基于配置的主要特征生成版本标识
	version := "cache_config"

	// 添加全局配置信息
	if config.Global != nil {
		version += fmt.Sprintf("_global_enabled:%t", config.Global.Enabled)
		if config.Global.DefaultTTL != "" {
			version += fmt.Sprintf("_ttl:%s", config.Global.DefaultTTL)
		}
		if config.Global.DefaultSize > 0 {
			version += fmt.Sprintf("_size:%d", config.Global.DefaultSize)
		}
	}

	// 添加模块配置信息
	if config.Modules != nil {
		if config.Modules.DNS != nil {
			version += fmt.Sprintf("_dns_enabled:%t", config.Modules.DNS.Enabled)
			if config.Modules.DNS.Size > 0 {
				version += fmt.Sprintf("_dns_size:%d", config.Modules.DNS.Size)
			}
		}
		if config.Modules.Regex != nil {
			version += fmt.Sprintf("_regex_enabled:%t", config.Modules.Regex.Enabled)
			if config.Modules.Regex.Size > 0 {
				version += fmt.Sprintf("_regex_size:%d", config.Modules.Regex.Size)
			}
		}
	}

	return version
}

// GetMigrationHistory 获取迁移历史
func (chrm *CacheHotReloadManager) GetMigrationHistory() []CacheMigrationRecord {
	chrm.mu.RLock()
	defer chrm.mu.RUnlock()

	// 返回副本
	history := make([]CacheMigrationRecord, len(chrm.migrationHistory))
	copy(history, chrm.migrationHistory)
	return history
}
