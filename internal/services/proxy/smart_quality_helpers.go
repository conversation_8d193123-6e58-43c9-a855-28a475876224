package proxy

import (
	"math"
	"time"
)

// ========== 一致性计算相关辅助方法 ==========

// calculateResponseTimeConsistency 计算响应时间一致性
func (sqa *SmartQualityAlgorithm) calculateResponseTimeConsistency(stats *AdvancedProxyStats) float64 {
	if len(stats.HourlyStats) < 2 {
		return 0.5 // 默认中等一致性
	}
	
	// 计算响应时间的变异系数
	var responseTimes []float64
	for _, hourlyStats := range stats.HourlyStats {
		if hourlyStats != nil {
			responseTimes = append(responseTimes, hourlyStats.AvgResponseTime)
		}
	}
	
	if len(responseTimes) < 2 {
		return 0.5
	}
	
	mean := sqa.calculateMean(responseTimes)
	stdDev := sqa.calculateStandardDeviation(responseTimes, mean)
	
	// 变异系数越小，一致性越高
	coefficientOfVariation := stdDev / mean
	consistency := math.Max(0.0, 1.0 - coefficientOfVariation)
	
	return math.Min(1.0, consistency)
}

// calculateSuccessRateConsistency 计算成功率一致性
func (sqa *SmartQualityAlgorithm) calculateSuccessRateConsistency(stats *AdvancedProxyStats) float64 {
	if len(stats.HourlyStats) < 2 {
		return 0.5
	}

	var successRates []float64
	for _, hourlyStats := range stats.HourlyStats {
		if hourlyStats != nil && hourlyStats.RequestCount > 0 {
			// 使用SuccessRate字段
			successRates = append(successRates, hourlyStats.SuccessRate)
		}
	}

	if len(successRates) < 2 {
		return 0.5
	}

	// 计算成功率的标准差
	mean := sqa.calculateMean(successRates)
	stdDev := sqa.calculateStandardDeviation(successRates, mean)

	// 标准差越小，一致性越高
	consistency := math.Max(0.0, 1.0 - stdDev*2) // 乘以2来放大差异

	return math.Min(1.0, consistency)
}

// calculateUpdateFrequencyConsistency 计算更新频率一致性
func (sqa *SmartQualityAlgorithm) calculateUpdateFrequencyConsistency(stats *AdvancedProxyStats) float64 {
	// 基于更新次数评估一致性
	if stats.UpdateCount < 5 {
		return 0.3 // 更新次数少，一致性较低
	} else if stats.UpdateCount > 100 {
		return 0.9 // 更新次数多，一致性较高
	} else {
		// 线性插值
		return 0.3 + (float64(stats.UpdateCount-5)/95.0)*0.6
	}
}

// ========== 恢复能力计算相关辅助方法 ==========

// analyzeRecoveryPatterns 分析恢复模式
func (sqa *SmartQualityAlgorithm) analyzeRecoveryPatterns(errors []ErrorRecord) float64 {
	if len(errors) < 2 {
		return 0.8 // 错误少，恢复模式良好
	}
	
	// 分析错误间隔时间
	var intervals []float64
	for i := 1; i < len(errors); i++ {
		interval := errors[i].Timestamp.Sub(errors[i-1].Timestamp).Hours()
		intervals = append(intervals, interval)
	}
	
	if len(intervals) == 0 {
		return 0.8
	}
	
	// 间隔时间越长，恢复模式越好
	avgInterval := sqa.calculateMean(intervals)
	
	// 将平均间隔转换为0-1分数
	// 假设24小时间隔为满分
	patternScore := math.Min(1.0, avgInterval/24.0)
	
	return patternScore
}

// calculateAverageRecoveryTime 计算平均恢复时间
func (sqa *SmartQualityAlgorithm) calculateAverageRecoveryTime(errors []ErrorRecord) float64 {
	if len(errors) == 0 {
		return 0.0 // 无错误，恢复时间为0
	}
	
	// 简化计算：假设每个错误的恢复时间为1小时
	// 实际实现中应该基于错误类型和历史数据
	totalRecoveryTime := 0.0
	for _, error := range errors {
		switch error.ErrorType {
		case "connection_timeout":
			totalRecoveryTime += 0.5 // 30分钟
		case "connection_refused":
			totalRecoveryTime += 1.0 // 1小时
		case "ssl_error":
			totalRecoveryTime += 2.0 // 2小时
		default:
			totalRecoveryTime += 1.0 // 默认1小时
		}
	}
	
	return totalRecoveryTime / float64(len(errors))
}

// calculateRecoverySuccessRate 计算恢复成功率
func (sqa *SmartQualityAlgorithm) calculateRecoverySuccessRate(stats *AdvancedProxyStats) float64 {
	if stats.TotalRequests == 0 {
		return 0.5 // 默认中等成功率
	}
	
	// 基于整体成功率评估恢复成功率
	overallSuccessRate := float64(stats.SuccessRequests) / float64(stats.TotalRequests)
	
	// 如果有错误记录，调整恢复成功率
	if len(stats.LastErrors) > 0 {
		errorRate := float64(len(stats.LastErrors)) / float64(stats.TotalRequests)
		// 错误率越低，恢复成功率越高
		recoveryRate := math.Max(0.0, 1.0 - errorRate*2)
		return (overallSuccessRate + recoveryRate) / 2.0
	}
	
	return overallSuccessRate
}

// calculateRecoveryStability 计算恢复稳定性
func (sqa *SmartQualityAlgorithm) calculateRecoveryStability(errors []ErrorRecord) float64 {
	if len(errors) < 3 {
		return 0.8 // 错误少，稳定性较高
	}
	
	// 分析错误类型的多样性
	errorTypes := make(map[string]int)
	for _, error := range errors {
		errorTypes[error.ErrorType]++
	}
	
	// 错误类型越集中，恢复稳定性越高
	maxCount := 0
	for _, count := range errorTypes {
		if count > maxCount {
			maxCount = count
		}
	}
	
	concentration := float64(maxCount) / float64(len(errors))
	stability := concentration * 0.8 + 0.2 // 基础稳定性20%
	
	return math.Min(1.0, stability)
}

// normalizeRecoveryTime 标准化恢复时间
func (sqa *SmartQualityAlgorithm) normalizeRecoveryTime(avgRecoveryTime float64) float64 {
	// 将恢复时间转换为0-1分数
	// 假设4小时为最差恢复时间
	maxRecoveryTime := 4.0
	normalized := math.Max(0.0, 1.0 - avgRecoveryTime/maxRecoveryTime)
	return normalized
}

// ========== 趋势计算相关辅助方法 ==========

// collectRecentHourlyStats 收集最近的小时统计
func (sqa *SmartQualityAlgorithm) collectRecentHourlyStats(hourlyStats [24]*HourlyQualityStats, hours int) []*HourlyQualityStats {
	var recentStats []*HourlyQualityStats
	cutoffTime := time.Now().Add(-time.Duration(hours) * time.Hour)

	for _, stats := range hourlyStats {
		if stats != nil && stats.Timestamp.After(cutoffTime) {
			recentStats = append(recentStats, stats)
		}
	}

	return recentStats
}

// calculateLinearTrend 计算线性趋势
func (sqa *SmartQualityAlgorithm) calculateLinearTrend(stats []*HourlyQualityStats) float64 {
	if len(stats) < 2 {
		return 0.0
	}
	
	// 使用最小二乘法计算线性回归斜率
	n := float64(len(stats))
	var sumX, sumY, sumXY, sumX2 float64
	
	for i, stat := range stats {
		x := float64(i) // 时间索引
		y := stat.AvgResponseTime // 响应时间
		
		sumX += x
		sumY += y
		sumXY += x * y
		sumX2 += x * x
	}
	
	// 计算斜率
	slope := (n*sumXY - sumX*sumY) / (n*sumX2 - sumX*sumX)
	return slope
}

// calculateTrendStrength 计算趋势强度
func (sqa *SmartQualityAlgorithm) calculateTrendStrength(stats []*HourlyQualityStats, slope float64) float64 {
	if len(stats) < 2 {
		return 0.0
	}
	
	// 计算R²（决定系数）来衡量趋势强度
	var responseTimes []float64
	for _, stat := range stats {
		responseTimes = append(responseTimes, stat.AvgResponseTime)
	}
	
	mean := sqa.calculateMean(responseTimes)
	
	var ssRes, ssTot float64
	for i, responseTime := range responseTimes {
		predicted := slope*float64(i) + mean // 简化的预测值
		ssRes += math.Pow(responseTime-predicted, 2)
		ssTot += math.Pow(responseTime-mean, 2)
	}
	
	if ssTot == 0 {
		return 0.0
	}
	
	rSquared := 1.0 - ssRes/ssTot
	return math.Max(0.0, rSquared)
}

// calculateTrendStability 计算趋势稳定性
func (sqa *SmartQualityAlgorithm) calculateTrendStability(stats []*HourlyQualityStats) float64 {
	if len(stats) < 3 {
		return 0.5
	}
	
	// 计算相邻数据点的变化率
	var changeRates []float64
	for i := 1; i < len(stats); i++ {
		if stats[i-1].AvgResponseTime > 0 {
			changeRate := math.Abs(stats[i].AvgResponseTime-stats[i-1].AvgResponseTime) / stats[i-1].AvgResponseTime
			changeRates = append(changeRates, changeRate)
		}
	}
	
	if len(changeRates) == 0 {
		return 0.5
	}
	
	// 变化率的标准差越小，稳定性越高
	mean := sqa.calculateMean(changeRates)
	stdDev := sqa.calculateStandardDeviation(changeRates, mean)
	
	stability := math.Max(0.0, 1.0 - stdDev*5) // 乘以5来放大差异
	return math.Min(1.0, stability)
}

// ========== 数学计算辅助方法 ==========

// calculateMean 计算平均值
func (sqa *SmartQualityAlgorithm) calculateMean(values []float64) float64 {
	if len(values) == 0 {
		return 0.0
	}
	
	sum := 0.0
	for _, value := range values {
		sum += value
	}
	
	return sum / float64(len(values))
}

// calculateStandardDeviation 计算标准差
func (sqa *SmartQualityAlgorithm) calculateStandardDeviation(values []float64, mean float64) float64 {
	if len(values) <= 1 {
		return 0.0
	}
	
	sumSquaredDiff := 0.0
	for _, value := range values {
		diff := value - mean
		sumSquaredDiff += diff * diff
	}
	
	variance := sumSquaredDiff / float64(len(values)-1)
	return math.Sqrt(variance)
}
