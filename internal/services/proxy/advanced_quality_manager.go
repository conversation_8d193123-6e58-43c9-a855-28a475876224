package proxy

import (
	"fmt"
	"math"
	"os"
	"strings"
	"sync"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/logger"
)

// AdvancedProxyQualityManager 高级代理质量管理器
// 提供更智能的代理质量评估和热重载支持
type AdvancedProxyQualityManager struct {
	// 基础配置
	config *ProxyQualityConfig
	mu     sync.RWMutex
	logger *logger.LoggerAdapter

	// 高级质量评估
	qualityAlgorithm QualityAlgorithm
	adaptiveWeights  *AdaptiveWeights
	
	// 代理统计数据
	proxyStats       map[string]*AdvancedProxyStats
	globalStats      *GlobalQualityStats
	
	// 热重载支持
	migrationHistory []QualityMigrationRecord
	maxHistorySize   int
	
	// 性能优化
	cacheManager     *QualityCacheManager
	batchProcessor   *BatchQualityProcessor
}

// AdvancedProxyStats 高级代理统计信息
type AdvancedProxyStats struct {
	// 基础统计
	TotalRequests    int64     `json:"total_requests"`
	SuccessRequests  int64     `json:"success_requests"`
	FailedRequests   int64     `json:"failed_requests"`
	TotalResponseTime time.Duration `json:"total_response_time"`
	
	// 高级统计
	QualityScore     float64   `json:"quality_score"`
	ReliabilityScore float64   `json:"reliability_score"`
	SpeedScore       float64   `json:"speed_score"`
	StabilityScore   float64   `json:"stability_score"`
	
	// 时间序列数据
	HourlyStats      [24]*HourlyQualityStats `json:"hourly_stats"`
	DailyTrend       []float64               `json:"daily_trend"`
	
	// 错误分析
	ErrorCategories  map[string]int64        `json:"error_categories"`
	LastErrors       []ErrorRecord           `json:"last_errors"`
	
	// 地理位置和网络信息
	GeographicInfo   *GeographicInfo         `json:"geographic_info"`
	NetworkInfo      *NetworkInfo            `json:"network_info"`
	
	// 元数据
	FirstSeen        time.Time               `json:"first_seen"`
	LastUpdated      time.Time               `json:"last_updated"`
	UpdateCount      int64                   `json:"update_count"`
}

// QualityAlgorithm 质量评估算法接口
type QualityAlgorithm interface {
	CalculateQualityScore(stats *AdvancedProxyStats, weights *AdaptiveWeights) float64
	UpdateWeights(globalStats *GlobalQualityStats) *AdaptiveWeights
	GetAlgorithmName() string
	GetVersion() string
}

// AdaptiveWeights 自适应权重配置
type AdaptiveWeights struct {
	ReliabilityWeight float64   `json:"reliability_weight"`
	SpeedWeight       float64   `json:"speed_weight"`
	StabilityWeight   float64   `json:"stability_weight"`
	GeographicWeight  float64   `json:"geographic_weight"`
	NetworkWeight     float64   `json:"network_weight"`
	LastUpdated       time.Time `json:"last_updated"`
	UpdateReason      string    `json:"update_reason"`
}

// HourlyQualityStats 小时质量统计
type HourlyQualityStats struct {
	Hour             int       `json:"hour"`
	RequestCount     int64     `json:"request_count"`
	SuccessRate      float64   `json:"success_rate"`
	AvgResponseTime  float64   `json:"avg_response_time"`
	QualityScore     float64   `json:"quality_score"`
	Timestamp        time.Time `json:"timestamp"`
}

// ErrorRecord 错误记录
type ErrorRecord struct {
	Timestamp   time.Time `json:"timestamp"`
	ErrorType   string    `json:"error_type"`
	ErrorCode   int       `json:"error_code"`
	ErrorMsg    string    `json:"error_msg"`
	Duration    time.Duration `json:"duration"`
	Severity    string    `json:"severity"`
}

// GeographicInfo 地理位置信息
type GeographicInfo struct {
	Country     string  `json:"country"`
	Region      string  `json:"region"`
	City        string  `json:"city"`
	Latitude    float64 `json:"latitude"`
	Longitude   float64 `json:"longitude"`
	Timezone    string  `json:"timezone"`
	ISP         string  `json:"isp"`
	LastUpdated time.Time `json:"last_updated"`
}

// NetworkInfo 网络信息
type NetworkInfo struct {
	Protocol      string    `json:"protocol"`
	Port          int       `json:"port"`
	Encryption    string    `json:"encryption"`
	Bandwidth     int64     `json:"bandwidth"`
	Latency       float64   `json:"latency"`
	PacketLoss    float64   `json:"packet_loss"`
	LastTested    time.Time `json:"last_tested"`
}

// GlobalQualityStats 全局质量统计
type GlobalQualityStats struct {
	TotalProxies        int       `json:"total_proxies"`
	ActiveProxies       int       `json:"active_proxies"`
	AvgQualityScore     float64   `json:"avg_quality_score"`
	AvgResponseTime     float64   `json:"avg_response_time"`
	GlobalSuccessRate   float64   `json:"global_success_rate"`
	TopPerformingRegion string    `json:"top_performing_region"`
	LastUpdated         time.Time `json:"last_updated"`
}

// QualityMigrationRecord 质量数据迁移记录
type QualityMigrationRecord struct {
	MigrationID     string                 `json:"migration_id"`
	Timestamp       time.Time              `json:"timestamp"`
	OldConfig       *ProxyQualityConfig    `json:"old_config"`
	NewConfig       *ProxyQualityConfig    `json:"new_config"`
	MigratedProxies int                    `json:"migrated_proxies"`
	DataLoss        float64                `json:"data_loss"`
	Duration        time.Duration          `json:"duration"`
	Success         bool                   `json:"success"`
	ErrorMsg        string                 `json:"error_msg"`
	Details         map[string]interface{} `json:"details"`
}

// QualityCacheManager 质量缓存管理器
type QualityCacheManager struct {
	cache           map[string]*CachedQualityResult
	cacheTimeout    time.Duration
	maxCacheSize    int
	hitCount        int64
	missCount       int64
	mu              sync.RWMutex
}

// CachedQualityResult 缓存的质量结果
type CachedQualityResult struct {
	QualityScore float64   `json:"quality_score"`
	Timestamp    time.Time `json:"timestamp"`
	HitCount     int64     `json:"hit_count"`
}

// BatchQualityProcessor 批量质量处理器
type BatchQualityProcessor struct {
	batchSize       int
	processingQueue chan *QualityUpdateRequest
	workers         int
	stopChan        chan struct{}
	wg              sync.WaitGroup
}

// QualityUpdateRequest 质量更新请求
type QualityUpdateRequest struct {
	ProxyURL     string        `json:"proxy_url"`
	Success      bool          `json:"success"`
	ResponseTime time.Duration `json:"response_time"`
	ErrorInfo    *ErrorRecord  `json:"error_info"`
	Timestamp    time.Time     `json:"timestamp"`
	Priority     int           `json:"priority"`
}

// NewAdvancedProxyQualityManager 创建高级代理质量管理器
func NewAdvancedProxyQualityManager(config *ProxyQualityConfig) *AdvancedProxyQualityManager {
	manager := &AdvancedProxyQualityManager{
		config:           config,
		logger:           logger.GetLoggerAdapter(logger.ModuleProxy),
		proxyStats:       make(map[string]*AdvancedProxyStats),
		migrationHistory: make([]QualityMigrationRecord, 0),
		maxHistorySize:   constants.DefaultMaxMigrationHistory,
		globalStats:      &GlobalQualityStats{},
	}

	// 初始化质量算法
	manager.qualityAlgorithm = NewSmartQualityAlgorithm()
	
	// 初始化自适应权重
	manager.adaptiveWeights = &AdaptiveWeights{
		ReliabilityWeight: 0.4,
		SpeedWeight:       0.3,
		StabilityWeight:   0.2,
		GeographicWeight:  0.05,
		NetworkWeight:     0.05,
		LastUpdated:       time.Now(),
		UpdateReason:      "初始化默认权重",
	}

	// 初始化缓存管理器
	manager.cacheManager = &QualityCacheManager{
		cache:        make(map[string]*CachedQualityResult),
		cacheTimeout: 5 * time.Minute,
		maxCacheSize: 1000,
	}

	// 初始化批量处理器
	manager.batchProcessor = &BatchQualityProcessor{
		batchSize:       constants.DefaultBatchSize,
		processingQueue: make(chan *QualityUpdateRequest, 1000),
		workers:         4, // 默认工作协程数
		stopChan:        make(chan struct{}),
	}

	// 在测试环境中，不启动批量处理器
	if !manager.isTestEnvironment() {
		// 启动批量处理器
		manager.startBatchProcessor()
	} else {
		manager.logger.Info("测试环境：跳过批量处理器启动")
	}

	manager.logger.Info("高级代理质量管理器初始化完成")
	return manager
}

// isTestEnvironment 检查是否在测试环境中
func (aqm *AdvancedProxyQualityManager) isTestEnvironment() bool {
	// 检查是否在测试环境中运行
	return os.Getenv("GO_ENV") == "test" || strings.Contains(os.Args[0], ".test")
}

// UpdateConfig 更新配置（支持热重载）
func (aqm *AdvancedProxyQualityManager) UpdateConfig(newConfig *ProxyQualityConfig) error {
	aqm.mu.Lock()
	defer aqm.mu.Unlock()

	startTime := time.Now()
	migrationID := fmt.Sprintf("migration_%d", time.Now().Unix())
	
	aqm.logger.Info("开始高级代理质量管理器配置热重载，迁移ID: %s", migrationID)

	// 创建迁移记录
	migrationRecord := QualityMigrationRecord{
		MigrationID: migrationID,
		Timestamp:   startTime,
		OldConfig:   aqm.config,
		NewConfig:   newConfig,
		Details:     make(map[string]interface{}),
	}

	// 1. 验证新配置
	if err := aqm.validateConfig(newConfig); err != nil {
		migrationRecord.Success = false
		migrationRecord.ErrorMsg = fmt.Sprintf("配置验证失败: %v", err)
		aqm.addMigrationRecord(migrationRecord)
		return fmt.Errorf("配置验证失败: %v", err)
	}

	// 2. 创建数据快照
	snapshot, err := aqm.createDataSnapshot()
	if err != nil {
		migrationRecord.Success = false
		migrationRecord.ErrorMsg = fmt.Sprintf("创建数据快照失败: %v", err)
		aqm.addMigrationRecord(migrationRecord)
		return fmt.Errorf("创建数据快照失败: %v", err)
	}

	// 3. 执行配置迁移
	migratedCount, dataLoss, err := aqm.migrateQualityData(aqm.config, newConfig, snapshot)
	if err != nil {
		migrationRecord.Success = false
		migrationRecord.ErrorMsg = fmt.Sprintf("数据迁移失败: %v", err)
		aqm.addMigrationRecord(migrationRecord)
		return fmt.Errorf("数据迁移失败: %v", err)
	}

	// 4. 更新配置
	aqm.config = newConfig

	// 5. 更新质量算法和权重
	if err := aqm.updateQualityAlgorithm(newConfig); err != nil {
		aqm.logger.Warn("更新质量算法失败: %v", err)
	}

	// 6. 重新计算所有代理的质量分数
	aqm.recalculateAllQualityScores()

	// 7. 记录迁移成功
	migrationRecord.Success = true
	migrationRecord.MigratedProxies = migratedCount
	migrationRecord.DataLoss = dataLoss
	migrationRecord.Duration = time.Since(startTime)
	migrationRecord.Details["snapshot_size"] = len(snapshot)
	migrationRecord.Details["cache_cleared"] = aqm.clearQualityCache()
	
	aqm.addMigrationRecord(migrationRecord)

	aqm.logger.Info("高级代理质量管理器配置热重载完成，迁移ID: %s，耗时: %v，迁移代理: %d，数据损失: %.2f%%", 
		migrationID, migrationRecord.Duration, migratedCount, dataLoss*100)

	return nil
}

// GetConfig 获取当前配置
func (aqm *AdvancedProxyQualityManager) GetConfig() *ProxyQualityConfig {
	aqm.mu.RLock()
	defer aqm.mu.RUnlock()
	return aqm.config
}

// GetMigrationHistory 获取迁移历史
func (aqm *AdvancedProxyQualityManager) GetMigrationHistory() []QualityMigrationRecord {
	aqm.mu.RLock()
	defer aqm.mu.RUnlock()
	
	// 返回副本以避免并发修改
	history := make([]QualityMigrationRecord, len(aqm.migrationHistory))
	copy(history, aqm.migrationHistory)
	return history
}

// GetAdvancedStats 获取高级统计信息
func (aqm *AdvancedProxyQualityManager) GetAdvancedStats(proxyURL string) (*AdvancedProxyStats, bool) {
	aqm.mu.RLock()
	defer aqm.mu.RUnlock()
	
	stats, exists := aqm.proxyStats[proxyURL]
	if !exists {
		return nil, false
	}
	
	// 返回副本以避免并发修改
	statsCopy := *stats
	return &statsCopy, true
}

// GetGlobalStats 获取全局统计信息
func (aqm *AdvancedProxyQualityManager) GetGlobalStats() *GlobalQualityStats {
	aqm.mu.RLock()
	defer aqm.mu.RUnlock()
	
	// 返回副本
	globalStatsCopy := *aqm.globalStats
	return &globalStatsCopy
}

// RecordAdvancedRequest 记录高级请求信息
func (aqm *AdvancedProxyQualityManager) RecordAdvancedRequest(proxyURL string, success bool, responseTime time.Duration, errorInfo *ErrorRecord) {
	// 创建更新请求
	request := &QualityUpdateRequest{
		ProxyURL:     proxyURL,
		Success:      success,
		ResponseTime: responseTime,
		ErrorInfo:    errorInfo,
		Timestamp:    time.Now(),
		Priority:     1, // 默认优先级
	}

	// 在测试环境中，直接处理请求
	if aqm.isTestEnvironment() {
		aqm.processQualityUpdate(request)
	} else {
		// 发送到批量处理队列
		select {
		case aqm.batchProcessor.processingQueue <- request:
			// 成功发送到队列
		default:
			// 队列满了，直接处理
			aqm.processQualityUpdate(request)
		}
	}

	// 立即更新全局统计（用于测试）
	aqm.updateGlobalStatsImmediate()
}

// validateConfig 验证配置
func (aqm *AdvancedProxyQualityManager) validateConfig(config *ProxyQualityConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}

	if config.MinSuccessRate < 0 || config.MinSuccessRate > 1 {
		return fmt.Errorf("最小成功率必须在 0-1 之间")
	}

	if config.MaxResponseTime <= 0 {
		return fmt.Errorf("最大响应时间必须大于 0")
	}

	return nil
}

// createDataSnapshot 创建数据快照
func (aqm *AdvancedProxyQualityManager) createDataSnapshot() (map[string]*AdvancedProxyStats, error) {
	snapshot := make(map[string]*AdvancedProxyStats)

	for proxyURL, stats := range aqm.proxyStats {
		// 创建深拷贝
		statsCopy := *stats
		snapshot[proxyURL] = &statsCopy
	}

	aqm.logger.Debug("创建数据快照完成，包含 %d 个代理的统计数据", len(snapshot))
	return snapshot, nil
}

// migrateQualityData 迁移质量数据
func (aqm *AdvancedProxyQualityManager) migrateQualityData(oldConfig, newConfig *ProxyQualityConfig, snapshot map[string]*AdvancedProxyStats) (int, float64, error) {
	migratedCount := 0
	totalDataPoints := 0
	lostDataPoints := 0

	for proxyURL, oldStats := range snapshot {
		totalDataPoints++

		// 检查是否需要重新计算质量分数
		if aqm.shouldRecalculateQuality(oldConfig, newConfig) {
			newStats := aqm.recalculateProxyQuality(oldStats, newConfig)
			aqm.proxyStats[proxyURL] = newStats
			migratedCount++
		} else {
			// 直接保留现有数据
			aqm.proxyStats[proxyURL] = oldStats
			migratedCount++
		}
	}

	dataLoss := float64(lostDataPoints) / float64(totalDataPoints)
	return migratedCount, dataLoss, nil
}

// shouldRecalculateQuality 判断是否需要重新计算质量
func (aqm *AdvancedProxyQualityManager) shouldRecalculateQuality(oldConfig, newConfig *ProxyQualityConfig) bool {
	return oldConfig.MinSuccessRate != newConfig.MinSuccessRate ||
		   oldConfig.MaxResponseTime != newConfig.MaxResponseTime
}

// recalculateProxyQuality 重新计算代理质量
func (aqm *AdvancedProxyQualityManager) recalculateProxyQuality(oldStats *AdvancedProxyStats, newConfig *ProxyQualityConfig) *AdvancedProxyStats {
	newStats := *oldStats

	// 重新计算质量分数
	newStats.QualityScore = aqm.qualityAlgorithm.CalculateQualityScore(&newStats, aqm.adaptiveWeights)
	newStats.LastUpdated = time.Now()
	newStats.UpdateCount++

	return &newStats
}

// updateQualityAlgorithm 更新质量算法
func (aqm *AdvancedProxyQualityManager) updateQualityAlgorithm(config *ProxyQualityConfig) error {
	// 根据配置更新自适应权重
	newWeights := aqm.qualityAlgorithm.UpdateWeights(aqm.globalStats)
	if newWeights != nil {
		aqm.adaptiveWeights = newWeights
		aqm.logger.Info("自适应权重已更新")
	}

	return nil
}

// recalculateAllQualityScores 重新计算所有质量分数
func (aqm *AdvancedProxyQualityManager) recalculateAllQualityScores() {
	recalculatedCount := 0

	for _, stats := range aqm.proxyStats {
		oldScore := stats.QualityScore
		newScore := aqm.qualityAlgorithm.CalculateQualityScore(stats, aqm.adaptiveWeights)

		if math.Abs(oldScore-newScore) > 0.01 { // 只有显著变化才更新
			stats.QualityScore = newScore
			stats.LastUpdated = time.Now()
			stats.UpdateCount++
			recalculatedCount++
		}
	}

	aqm.logger.Info("重新计算质量分数完成，更新了 %d 个代理", recalculatedCount)
}

// updateGlobalStatsImmediate 立即更新全局统计（用于测试）
func (aqm *AdvancedProxyQualityManager) updateGlobalStatsImmediate() {
	aqm.mu.Lock()
	defer aqm.mu.Unlock()

	// 更新代理总数
	aqm.globalStats.TotalProxies = len(aqm.proxyStats)

	// 计算其他统计信息
	totalRequests := int64(0)
	successRequests := int64(0)
	totalResponseTime := time.Duration(0)
	totalQualityScore := 0.0
	activeProxies := 0

	for _, stats := range aqm.proxyStats {
		totalRequests += stats.TotalRequests
		successRequests += stats.SuccessRequests
		totalResponseTime += stats.TotalResponseTime
		totalQualityScore += stats.QualityScore
		if stats.TotalRequests > 0 {
			activeProxies++
		}
	}

	aqm.globalStats.ActiveProxies = activeProxies

	if len(aqm.proxyStats) > 0 {
		aqm.globalStats.AvgResponseTime = float64(totalResponseTime) / float64(len(aqm.proxyStats)) / float64(time.Millisecond)
		aqm.globalStats.AvgQualityScore = totalQualityScore / float64(len(aqm.proxyStats))
	}

	if totalRequests > 0 {
		aqm.globalStats.GlobalSuccessRate = float64(successRequests) / float64(totalRequests)
	}

	aqm.globalStats.LastUpdated = time.Now()
}

// clearQualityCache 清理质量缓存
func (aqm *AdvancedProxyQualityManager) clearQualityCache() int {
	aqm.cacheManager.mu.Lock()
	defer aqm.cacheManager.mu.Unlock()

	clearedCount := len(aqm.cacheManager.cache)
	aqm.cacheManager.cache = make(map[string]*CachedQualityResult)

	return clearedCount
}

// addMigrationRecord 添加迁移记录
func (aqm *AdvancedProxyQualityManager) addMigrationRecord(record QualityMigrationRecord) {
	aqm.migrationHistory = append(aqm.migrationHistory, record)

	// 限制历史记录数量
	if len(aqm.migrationHistory) > aqm.maxHistorySize {
		aqm.migrationHistory = aqm.migrationHistory[1:]
	}
}

// startBatchProcessor 启动批量处理器
func (aqm *AdvancedProxyQualityManager) startBatchProcessor() {
	for i := 0; i < aqm.batchProcessor.workers; i++ {
		aqm.batchProcessor.wg.Add(1)
		go aqm.qualityUpdateWorker()
	}
}

// qualityUpdateWorker 质量更新工作协程
func (aqm *AdvancedProxyQualityManager) qualityUpdateWorker() {
	defer aqm.batchProcessor.wg.Done()

	for {
		select {
		case request := <-aqm.batchProcessor.processingQueue:
			aqm.processQualityUpdate(request)
		case <-aqm.batchProcessor.stopChan:
			return
		}
	}
}

// processQualityUpdate 处理质量更新
func (aqm *AdvancedProxyQualityManager) processQualityUpdate(request *QualityUpdateRequest) {
	aqm.mu.Lock()
	defer aqm.mu.Unlock()

	stats, exists := aqm.proxyStats[request.ProxyURL]
	if !exists {
		stats = aqm.createNewProxyStats(request.ProxyURL)
		aqm.proxyStats[request.ProxyURL] = stats
	}

	// 更新基础统计
	stats.TotalRequests++
	if request.Success {
		stats.SuccessRequests++
	} else {
		stats.FailedRequests++
		if request.ErrorInfo != nil {
			aqm.recordError(stats, request.ErrorInfo)
		}
	}

	stats.TotalResponseTime += request.ResponseTime
	stats.LastUpdated = request.Timestamp
	stats.UpdateCount++

	// 重新计算质量分数
	stats.QualityScore = aqm.qualityAlgorithm.CalculateQualityScore(stats, aqm.adaptiveWeights)

	// 更新小时统计
	aqm.updateHourlyStats(stats, request)
}

// createNewProxyStats 创建新的代理统计
func (aqm *AdvancedProxyQualityManager) createNewProxyStats(proxyURL string) *AdvancedProxyStats {
	return &AdvancedProxyStats{
		TotalRequests:   0,
		SuccessRequests: 0,
		FailedRequests:  0,
		QualityScore:    0.5, // 默认分数
		ErrorCategories: make(map[string]int64),
		LastErrors:      make([]ErrorRecord, 0),
		FirstSeen:       time.Now(),
		LastUpdated:     time.Now(),
		UpdateCount:     0,
	}
}

// recordError 记录错误
func (aqm *AdvancedProxyQualityManager) recordError(stats *AdvancedProxyStats, errorInfo *ErrorRecord) {
	// 更新错误分类统计
	stats.ErrorCategories[errorInfo.ErrorType]++

	// 添加到最近错误列表
	stats.LastErrors = append(stats.LastErrors, *errorInfo)

	// 限制错误记录数量
	if len(stats.LastErrors) > constants.MaxErrorRecords {
		stats.LastErrors = stats.LastErrors[1:]
	}
}

// updateHourlyStats 更新小时统计
func (aqm *AdvancedProxyQualityManager) updateHourlyStats(stats *AdvancedProxyStats, request *QualityUpdateRequest) {
	hour := request.Timestamp.Hour()

	if stats.HourlyStats[hour] == nil {
		stats.HourlyStats[hour] = &HourlyQualityStats{
			Hour:        hour,
			Timestamp:   request.Timestamp,
		}
	}

	hourlyStats := stats.HourlyStats[hour]
	hourlyStats.RequestCount++

	if request.Success {
		hourlyStats.SuccessRate = float64(stats.SuccessRequests) / float64(stats.TotalRequests)
	}

	hourlyStats.AvgResponseTime = float64(stats.TotalResponseTime) / float64(stats.TotalRequests) / float64(time.Millisecond)
	hourlyStats.QualityScore = stats.QualityScore
}
