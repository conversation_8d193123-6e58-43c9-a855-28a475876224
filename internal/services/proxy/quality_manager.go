package proxy

import (
	"fmt"
	"sync"
	"time"

	"flexproxy/common/logger"
)

// ProxyQualityStats 代理质量统计
type ProxyQualityStats struct {
	ProxyURL        string        `json:"proxy_url"`
	TotalRequests   int64         `json:"total_requests"`
	SuccessRequests int64         `json:"success_requests"`
	FailedRequests  int64         `json:"failed_requests"`
	AvgResponseTime time.Duration `json:"avg_response_time"`
	LastUsed        time.Time     `json:"last_used"`
	QualityScore    float64       `json:"quality_score"`    // 0.0-1.0
	IsBlacklisted   bool          `json:"is_blacklisted"`
	BlacklistUntil  time.Time     `json:"blacklist_until"`
	CreatedAt       time.Time     `json:"created_at"`
	UpdatedAt       time.Time     `json:"updated_at"`
}

// ProxyQualityManager 代理质量管理器
type ProxyQualityManager struct {
	mu           sync.RWMutex
	stats        map[string]*ProxyQualityStats
	config       *ProxyQualityConfig
	logger       *logger.LoggerAdapter

	// 热重载支持
	configCallbacks []func(*ProxyQualityConfig) error
}

// ProxyQualityConfig 代理质量配置
type ProxyQualityConfig struct {
	Enable                bool          `yaml:"enable" json:"enable"`
	MinSuccessRate        float64       `yaml:"min_success_rate" json:"min_success_rate"`           // 最小成功率
	MaxResponseTime       time.Duration `yaml:"max_response_time" json:"max_response_time"`         // 最大响应时间
	BlacklistThreshold    int           `yaml:"blacklist_threshold" json:"blacklist_threshold"`     // 黑名单阈值
	BlacklistDuration     time.Duration `yaml:"blacklist_duration" json:"blacklist_duration"`       // 黑名单持续时间
	QualityCheckInterval  time.Duration `yaml:"quality_check_interval" json:"quality_check_interval"` // 质量检查间隔
	StatsRetentionPeriod  time.Duration `yaml:"stats_retention_period" json:"stats_retention_period"` // 统计数据保留期
	AutoRemoveBadProxies  bool          `yaml:"auto_remove_bad_proxies" json:"auto_remove_bad_proxies"` // 自动移除劣质代理
}

// NewProxyQualityManager 创建代理质量管理器
func NewProxyQualityManager(cfg *ProxyQualityConfig) *ProxyQualityManager {
	pqm := &ProxyQualityManager{
		stats:           make(map[string]*ProxyQualityStats),
		config:          cfg,
		logger:          logger.GetLoggerAdapter(logger.ModuleProxy),
		configCallbacks: make([]func(*ProxyQualityConfig) error, 0),
	}

	pqm.logger.Info("代理质量管理器已初始化")
	return pqm
}

// GetConfig 获取当前代理质量配置
func (pqm *ProxyQualityManager) GetConfig() *ProxyQualityConfig {
	pqm.mu.RLock()
	defer pqm.mu.RUnlock()

	return pqm.config
}

// UpdateConfig 更新代理质量配置（热重载支持）
func (pqm *ProxyQualityManager) UpdateConfig(configInterface interface{}) error {
	pqm.mu.Lock()
	defer pqm.mu.Unlock()

	// 类型断言
	cfg, ok := configInterface.(*ProxyQualityConfig)
	if !ok {
		return fmt.Errorf("无效的配置类型，期望 *ProxyQualityConfig")
	}

	if cfg == nil {
		return fmt.Errorf("配置不能为空")
	}

	oldConfig := pqm.config
	pqm.config = cfg

	pqm.logger.Info("开始更新代理质量管理器配置")

	// 1. 更新质量评估参数
	if err := pqm.updateQualityParameters(oldConfig, cfg); err != nil {
		pqm.logger.Warn(fmt.Sprintf("更新质量评估参数失败: %v", err))
	}

	// 2. 更新黑名单配置
	if err := pqm.updateBlacklistConfig(oldConfig, cfg); err != nil {
		pqm.logger.Warn(fmt.Sprintf("更新黑名单配置失败: %v", err))
	}

	// 3. 清理过期统计数据
	if err := pqm.cleanupExpiredStats(); err != nil {
		pqm.logger.Warn(fmt.Sprintf("清理过期统计数据失败: %v", err))
	}

	// 4. 重新计算所有代理的质量分数
	if err := pqm.recalculateQualityScores(); err != nil {
		pqm.logger.Warn(fmt.Sprintf("重新计算质量分数失败: %v", err))
	}

	// 5. 执行配置回调
	for _, callback := range pqm.configCallbacks {
		if err := callback(cfg); err != nil {
			pqm.logger.Warn(fmt.Sprintf("执行配置回调失败: %v", err))
		}
	}

	pqm.logger.Info("代理质量管理器配置更新完成")
	return nil
}

// RegisterConfigCallback 注册配置更新回调
func (pqm *ProxyQualityManager) RegisterConfigCallback(callback func(*ProxyQualityConfig) error) {
	pqm.mu.Lock()
	defer pqm.mu.Unlock()
	
	pqm.configCallbacks = append(pqm.configCallbacks, callback)
}

// RecordRequest 记录请求统计
func (pqm *ProxyQualityManager) RecordRequest(proxyURL string, success bool, responseTime time.Duration) {
	if !pqm.config.Enable {
		return
	}

	pqm.mu.Lock()
	defer pqm.mu.Unlock()

	stats, exists := pqm.stats[proxyURL]
	if !exists {
		stats = &ProxyQualityStats{
			ProxyURL:  proxyURL,
			CreatedAt: time.Now(),
		}
		pqm.stats[proxyURL] = stats
	}

	// 更新统计数据
	stats.TotalRequests++
	stats.LastUsed = time.Now()
	stats.UpdatedAt = time.Now()

	if success {
		stats.SuccessRequests++
	} else {
		stats.FailedRequests++
	}

	// 更新平均响应时间
	if stats.TotalRequests == 1 {
		stats.AvgResponseTime = responseTime
	} else {
		// 使用指数移动平均
		alpha := 0.1
		stats.AvgResponseTime = time.Duration(float64(stats.AvgResponseTime)*(1-alpha) + float64(responseTime)*alpha)
	}

	// 重新计算质量分数
	stats.QualityScore = pqm.calculateQualityScore(stats)

	// 检查是否需要加入黑名单
	if pqm.shouldBlacklist(stats) {
		pqm.blacklistProxy(stats)
	}
}

// GetProxyStats 获取代理统计信息
func (pqm *ProxyQualityManager) GetProxyStats(proxyURL string) (*ProxyQualityStats, bool) {
	pqm.mu.RLock()
	defer pqm.mu.RUnlock()

	stats, exists := pqm.stats[proxyURL]
	if !exists {
		return nil, false
	}

	// 返回副本以避免并发修改
	statsCopy := *stats
	return &statsCopy, true
}

// GetAllStats 获取所有代理统计信息
func (pqm *ProxyQualityManager) GetAllStats() map[string]*ProxyQualityStats {
	pqm.mu.RLock()
	defer pqm.mu.RUnlock()

	result := make(map[string]*ProxyQualityStats)
	for url, stats := range pqm.stats {
		statsCopy := *stats
		result[url] = &statsCopy
	}

	return result
}

// IsBlacklisted 检查代理是否在黑名单中
func (pqm *ProxyQualityManager) IsBlacklisted(proxyURL string) bool {
	pqm.mu.RLock()
	defer pqm.mu.RUnlock()

	stats, exists := pqm.stats[proxyURL]
	if !exists {
		return false
	}

	if !stats.IsBlacklisted {
		return false
	}

	// 检查黑名单是否已过期
	if time.Now().After(stats.BlacklistUntil) {
		// 异步移除过期的黑名单状态
		go func() {
			pqm.mu.Lock()
			defer pqm.mu.Unlock()
			
			if stats, exists := pqm.stats[proxyURL]; exists {
				stats.IsBlacklisted = false
				stats.BlacklistUntil = time.Time{}
				pqm.logger.Info(fmt.Sprintf("代理 %s 已从黑名单中移除（过期）", proxyURL))
			}
		}()
		return false
	}

	return true
}

// GetBestProxies 获取质量最好的代理列表
func (pqm *ProxyQualityManager) GetBestProxies(count int) []string {
	pqm.mu.RLock()
	defer pqm.mu.RUnlock()

	type proxyScore struct {
		url   string
		score float64
	}

	var proxies []proxyScore
	for url, stats := range pqm.stats {
		if !stats.IsBlacklisted {
			proxies = append(proxies, proxyScore{
				url:   url,
				score: stats.QualityScore,
			})
		}
	}

	// 按质量分数排序
	for i := 0; i < len(proxies)-1; i++ {
		for j := i + 1; j < len(proxies); j++ {
			if proxies[i].score < proxies[j].score {
				proxies[i], proxies[j] = proxies[j], proxies[i]
			}
		}
	}

	// 返回前count个
	result := make([]string, 0, count)
	for i := 0; i < len(proxies) && i < count; i++ {
		result = append(result, proxies[i].url)
	}

	return result
}

// updateQualityParameters 更新质量评估参数
func (pqm *ProxyQualityManager) updateQualityParameters(oldConfig, newConfig *ProxyQualityConfig) error {
	if oldConfig == nil {
		return nil
	}

	// 检查关键参数是否发生变化
	if oldConfig.MinSuccessRate != newConfig.MinSuccessRate ||
		oldConfig.MaxResponseTime != newConfig.MaxResponseTime {
		pqm.logger.Info("质量评估参数已更新，将重新计算所有代理的质量分数")
	}

	return nil
}

// updateBlacklistConfig 更新黑名单配置
func (pqm *ProxyQualityManager) updateBlacklistConfig(oldConfig, newConfig *ProxyQualityConfig) error {
	if oldConfig == nil {
		return nil
	}

	// 检查黑名单配置是否发生变化
	if oldConfig.BlacklistThreshold != newConfig.BlacklistThreshold ||
		oldConfig.BlacklistDuration != newConfig.BlacklistDuration {
		pqm.logger.Info("黑名单配置已更新")
	}

	return nil
}

// cleanupExpiredStats 清理过期的统计数据
func (pqm *ProxyQualityManager) cleanupExpiredStats() error {
	if pqm.config.StatsRetentionPeriod <= 0 {
		return nil
	}

	cutoffTime := time.Now().Add(-pqm.config.StatsRetentionPeriod)
	cleanedCount := 0

	for url, stats := range pqm.stats {
		if stats.UpdatedAt.Before(cutoffTime) {
			delete(pqm.stats, url)
			cleanedCount++
		}
	}

	if cleanedCount > 0 {
		pqm.logger.Info(fmt.Sprintf("清理了 %d 个过期的代理统计数据", cleanedCount))
	}

	return nil
}

// recalculateQualityScores 重新计算所有代理的质量分数
func (pqm *ProxyQualityManager) recalculateQualityScores() error {
	recalculatedCount := 0

	for _, stats := range pqm.stats {
		oldScore := stats.QualityScore
		newScore := pqm.calculateQualityScore(stats)
		
		if oldScore != newScore {
			stats.QualityScore = newScore
			recalculatedCount++
		}
	}

	if recalculatedCount > 0 {
		pqm.logger.Info(fmt.Sprintf("重新计算了 %d 个代理的质量分数", recalculatedCount))
	}

	return nil
}

// calculateQualityScore 计算代理质量分数
func (pqm *ProxyQualityManager) calculateQualityScore(stats *ProxyQualityStats) float64 {
	if stats.TotalRequests == 0 {
		return 0.5 // 默认分数
	}

	// 成功率权重 (0.6)
	successRate := float64(stats.SuccessRequests) / float64(stats.TotalRequests)
	successScore := successRate * 0.6

	// 响应时间权重 (0.3)
	responseTimeScore := 0.0
	if pqm.config.MaxResponseTime > 0 {
		if stats.AvgResponseTime <= pqm.config.MaxResponseTime {
			responseTimeScore = (1.0 - float64(stats.AvgResponseTime)/float64(pqm.config.MaxResponseTime)) * 0.3
		}
	} else {
		responseTimeScore = 0.3 // 如果没有配置最大响应时间，给予满分
	}

	// 使用频率权重 (0.1)
	usageScore := 0.0
	if time.Since(stats.LastUsed) < 24*time.Hour {
		usageScore = 0.1
	}

	totalScore := successScore + responseTimeScore + usageScore

	// 确保分数在 0.0-1.0 范围内
	if totalScore > 1.0 {
		totalScore = 1.0
	}
	if totalScore < 0.0 {
		totalScore = 0.0
	}

	return totalScore
}

// shouldBlacklist 检查是否应该将代理加入黑名单
func (pqm *ProxyQualityManager) shouldBlacklist(stats *ProxyQualityStats) bool {
	if stats.IsBlacklisted || stats.TotalRequests < int64(pqm.config.BlacklistThreshold) {
		return false
	}

	successRate := float64(stats.SuccessRequests) / float64(stats.TotalRequests)
	return successRate < pqm.config.MinSuccessRate
}

// blacklistProxy 将代理加入黑名单
func (pqm *ProxyQualityManager) blacklistProxy(stats *ProxyQualityStats) {
	stats.IsBlacklisted = true
	stats.BlacklistUntil = time.Now().Add(pqm.config.BlacklistDuration)
	
	pqm.logger.Warn(fmt.Sprintf("代理 %s 已加入黑名单，成功率: %.2f%%, 将持续到: %s", 
		stats.ProxyURL, 
		float64(stats.SuccessRequests)/float64(stats.TotalRequests)*100,
		stats.BlacklistUntil.Format("2006-01-02 15:04:05")))
}
