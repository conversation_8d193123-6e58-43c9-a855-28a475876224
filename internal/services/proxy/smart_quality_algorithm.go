package proxy

import (
	"math"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/logger"
)

// SmartQualityAlgorithm 智能质量评估算法
// 使用机器学习和统计分析来评估代理质量
type SmartQualityAlgorithm struct {
	algorithmName    string
	version          string
	logger           *logger.LoggerAdapter
	
	// 算法参数
	learningRate     float64
	decayFactor      float64
	stabilityWindow  int
	
	// 统计模型
	responseTimeModel *ResponseTimeModel
	reliabilityModel  *ReliabilityModel
	stabilityModel    *StabilityModel
}

// ResponseTimeModel 响应时间模型
type ResponseTimeModel struct {
	ExpectedMean     float64   `json:"expected_mean"`
	StandardDev      float64   `json:"standard_dev"`
	Percentiles      []float64 `json:"percentiles"`
	TrendCoefficient float64   `json:"trend_coefficient"`
	LastUpdated      time.Time `json:"last_updated"`
}

// ReliabilityModel 可靠性模型
type ReliabilityModel struct {
	BaseReliability    float64            `json:"base_reliability"`
	ErrorWeights       map[string]float64 `json:"error_weights"`
	RecoveryFactor     float64            `json:"recovery_factor"`
	ConsistencyScore   float64            `json:"consistency_score"`
	LastUpdated        time.Time          `json:"last_updated"`
}

// StabilityModel 稳定性模型
type StabilityModel struct {
	VarianceThreshold  float64   `json:"variance_threshold"`
	TrendStability     float64   `json:"trend_stability"`
	SeasonalFactors    []float64 `json:"seasonal_factors"`
	AnomalyDetection   float64   `json:"anomaly_detection"`
	LastUpdated        time.Time `json:"last_updated"`
}

// NewSmartQualityAlgorithm 创建智能质量算法
func NewSmartQualityAlgorithm() *SmartQualityAlgorithm {
	return &SmartQualityAlgorithm{
		algorithmName:   "SmartQualityAlgorithm",
		version:         "v2.1.0",
		logger:          logger.GetLoggerAdapter(logger.ModuleProxy),
		learningRate:    constants.DefaultLearningRate,
		decayFactor:     constants.DefaultDecayFactor,
		stabilityWindow: constants.DefaultStabilityWindow,
		
		responseTimeModel: &ResponseTimeModel{
			ExpectedMean:     1000.0, // 1秒默认期望
			StandardDev:      500.0,  // 0.5秒标准差
			Percentiles:      make([]float64, 100),
			TrendCoefficient: 0.0,
			LastUpdated:      time.Now(),
		},
		
		reliabilityModel: &ReliabilityModel{
			BaseReliability:  0.95,
			ErrorWeights:     make(map[string]float64),
			RecoveryFactor:   0.1,
			ConsistencyScore: 0.8,
			LastUpdated:      time.Now(),
		},
		
		stabilityModel: &StabilityModel{
			VarianceThreshold: 0.2,
			TrendStability:    0.8,
			SeasonalFactors:   make([]float64, 24), // 24小时
			AnomalyDetection:  0.05,
			LastUpdated:       time.Now(),
		},
	}
}

// CalculateQualityScore 计算质量分数
func (sqa *SmartQualityAlgorithm) CalculateQualityScore(stats *AdvancedProxyStats, weights *AdaptiveWeights) float64 {
	if stats.TotalRequests == 0 {
		return 0.5 // 默认中等分数
	}

	// 1. 计算可靠性分数
	reliabilityScore := sqa.calculateReliabilityScore(stats)
	
	// 2. 计算速度分数
	speedScore := sqa.calculateSpeedScore(stats)
	
	// 3. 计算稳定性分数
	stabilityScore := sqa.calculateStabilityScore(stats)
	
	// 4. 计算地理位置分数
	geographicScore := sqa.calculateGeographicScore(stats)
	
	// 5. 计算网络质量分数
	networkScore := sqa.calculateNetworkScore(stats)
	
	// 6. 加权计算最终分数
	finalScore := reliabilityScore*weights.ReliabilityWeight +
		speedScore*weights.SpeedWeight +
		stabilityScore*weights.StabilityWeight +
		geographicScore*weights.GeographicWeight +
		networkScore*weights.NetworkWeight
	
	// 7. 应用时间衰减因子
	finalScore = sqa.applyTimeDecay(finalScore, stats.LastUpdated)
	
	// 8. 应用学习调整
	finalScore = sqa.applyLearningAdjustment(finalScore, stats)
	
	// 确保分数在 0-1 范围内
	finalScore = math.Max(0.0, math.Min(1.0, finalScore))
	
	sqa.logger.Debug("代理质量分数计算完成: 可靠性=%.3f, 速度=%.3f, 稳定性=%.3f, 地理=%.3f, 网络=%.3f, 最终=%.3f",
		reliabilityScore, speedScore, stabilityScore, geographicScore, networkScore, finalScore)
	
	return finalScore
}

// calculateReliabilityScore 计算可靠性分数
func (sqa *SmartQualityAlgorithm) calculateReliabilityScore(stats *AdvancedProxyStats) float64 {
	if stats.TotalRequests == 0 {
		return sqa.reliabilityModel.BaseReliability
	}
	
	// 基础成功率
	successRate := float64(stats.SuccessRequests) / float64(stats.TotalRequests)
	
	// 错误类型权重调整
	errorPenalty := 0.0
	for errorType, count := range stats.ErrorCategories {
		weight := sqa.getErrorWeight(errorType)
		errorPenalty += weight * float64(count) / float64(stats.TotalRequests)
	}
	
	// 一致性调整
	consistencyBonus := sqa.calculateConsistencyBonus(stats)
	
	// 恢复能力调整
	recoveryBonus := sqa.calculateRecoveryBonus(stats)
	
	reliabilityScore := successRate - errorPenalty + consistencyBonus + recoveryBonus
	return math.Max(0.0, math.Min(1.0, reliabilityScore))
}

// calculateSpeedScore 计算速度分数
func (sqa *SmartQualityAlgorithm) calculateSpeedScore(stats *AdvancedProxyStats) float64 {
	if stats.TotalRequests == 0 {
		return 0.5
	}
	
	avgResponseTime := float64(stats.TotalResponseTime) / float64(stats.TotalRequests) / float64(time.Millisecond)
	
	// 使用正态分布模型评估速度
	expectedMean := sqa.responseTimeModel.ExpectedMean
	standardDev := sqa.responseTimeModel.StandardDev
	
	// Z-score 计算
	zScore := (avgResponseTime - expectedMean) / standardDev
	
	// 转换为 0-1 分数（越快分数越高）
	speedScore := 1.0 - math.Max(0.0, math.Min(1.0, (zScore+3.0)/6.0))
	
	// 趋势调整
	trendAdjustment := sqa.calculateSpeedTrend(stats)
	speedScore += trendAdjustment
	
	return math.Max(0.0, math.Min(1.0, speedScore))
}

// calculateStabilityScore 计算稳定性分数
func (sqa *SmartQualityAlgorithm) calculateStabilityScore(stats *AdvancedProxyStats) float64 {
	if len(stats.HourlyStats) < 2 {
		return sqa.stabilityModel.TrendStability
	}
	
	// 计算响应时间方差
	responseTimeVariance := sqa.calculateResponseTimeVariance(stats)
	
	// 计算成功率稳定性
	successRateStability := sqa.calculateSuccessRateStability(stats)
	
	// 异常检测
	anomalyScore := sqa.detectAnomalies(stats)
	
	// 季节性调整
	seasonalAdjustment := sqa.calculateSeasonalAdjustment(stats)
	
	stabilityScore := (1.0 - responseTimeVariance) * 0.4 +
		successRateStability * 0.4 +
		(1.0 - anomalyScore) * 0.1 +
		seasonalAdjustment * 0.1
	
	return math.Max(0.0, math.Min(1.0, stabilityScore))
}

// calculateGeographicScore 计算地理位置分数
func (sqa *SmartQualityAlgorithm) calculateGeographicScore(stats *AdvancedProxyStats) float64 {
	if stats.GeographicInfo == nil {
		return 0.5 // 默认中等分数
	}
	
	// 基于地理位置的质量评估
	// 这里可以根据实际需求实现复杂的地理位置评分逻辑
	geographicScore := 0.7 // 简化实现
	
	// ISP 质量调整
	if stats.GeographicInfo.ISP != "" {
		ispBonus := sqa.getISPQualityBonus(stats.GeographicInfo.ISP)
		geographicScore += ispBonus
	}
	
	return math.Max(0.0, math.Min(1.0, geographicScore))
}

// calculateNetworkScore 计算网络质量分数
func (sqa *SmartQualityAlgorithm) calculateNetworkScore(stats *AdvancedProxyStats) float64 {
	if stats.NetworkInfo == nil {
		return 0.5 // 默认中等分数
	}
	
	networkScore := 0.5
	
	// 延迟评分
	if stats.NetworkInfo.Latency > 0 {
		latencyScore := 1.0 - math.Min(1.0, stats.NetworkInfo.Latency/1000.0) // 1秒为满分界限
		networkScore += latencyScore * 0.4
	}
	
	// 丢包率评分
	if stats.NetworkInfo.PacketLoss >= 0 {
		packetLossScore := 1.0 - stats.NetworkInfo.PacketLoss
		networkScore += packetLossScore * 0.3
	}
	
	// 带宽评分
	if stats.NetworkInfo.Bandwidth > 0 {
		bandwidthScore := math.Min(1.0, float64(stats.NetworkInfo.Bandwidth)/1000000.0) // 1Mbps为满分界限
		networkScore += bandwidthScore * 0.3
	}
	
	return math.Max(0.0, math.Min(1.0, networkScore))
}

// UpdateWeights 更新自适应权重
func (sqa *SmartQualityAlgorithm) UpdateWeights(globalStats *GlobalQualityStats) *AdaptiveWeights {
	if globalStats == nil {
		return nil
	}
	
	// 基于全局统计动态调整权重
	newWeights := &AdaptiveWeights{
		LastUpdated:  time.Now(),
		UpdateReason: "基于全局统计自动调整",
	}
	
	// 根据全局成功率调整可靠性权重
	if globalStats.GlobalSuccessRate < 0.8 {
		newWeights.ReliabilityWeight = 0.5 // 提高可靠性权重
		newWeights.SpeedWeight = 0.25
		newWeights.StabilityWeight = 0.15
	} else if globalStats.AvgResponseTime > 2000 { // 2秒
		newWeights.SpeedWeight = 0.4 // 提高速度权重
		newWeights.ReliabilityWeight = 0.35
		newWeights.StabilityWeight = 0.15
	} else {
		// 平衡权重
		newWeights.ReliabilityWeight = 0.4
		newWeights.SpeedWeight = 0.3
		newWeights.StabilityWeight = 0.2
	}
	
	newWeights.GeographicWeight = 0.05
	newWeights.NetworkWeight = 0.05
	
	sqa.logger.Info("自适应权重已更新: 可靠性=%.2f, 速度=%.2f, 稳定性=%.2f",
		newWeights.ReliabilityWeight, newWeights.SpeedWeight, newWeights.StabilityWeight)
	
	return newWeights
}

// GetAlgorithmName 获取算法名称
func (sqa *SmartQualityAlgorithm) GetAlgorithmName() string {
	return sqa.algorithmName
}

// GetVersion 获取算法版本
func (sqa *SmartQualityAlgorithm) GetVersion() string {
	return sqa.version
}

// 辅助方法实现...

// getErrorWeight 获取错误权重
func (sqa *SmartQualityAlgorithm) getErrorWeight(errorType string) float64 {
	weights := map[string]float64{
		"connection_timeout": 0.3,
		"connection_refused": 0.4,
		"dns_resolution":     0.2,
		"ssl_error":         0.35,
		"http_error":        0.25,
		"proxy_error":       0.5,
		"unknown":           0.1,
	}
	
	if weight, exists := weights[errorType]; exists {
		return weight
	}
	return 0.1 // 默认权重
}

// calculateConsistencyBonus 计算一致性奖励
func (sqa *SmartQualityAlgorithm) calculateConsistencyBonus(stats *AdvancedProxyStats) float64 {
	sqa.logger.Debug("计算一致性奖励")

	if stats.TotalRequests < 10 {
		return 0.0 // 数据不足，无奖励
	}

	// 1. 计算响应时间一致性
	responseTimeConsistency := sqa.calculateResponseTimeConsistency(stats)

	// 2. 计算成功率一致性
	successRateConsistency := sqa.calculateSuccessRateConsistency(stats)

	// 3. 计算更新频率一致性
	updateFrequencyConsistency := sqa.calculateUpdateFrequencyConsistency(stats)

	// 4. 综合一致性评分
	overallConsistency := (responseTimeConsistency * 0.4 +
						   successRateConsistency * 0.4 +
						   updateFrequencyConsistency * 0.2)

	// 5. 转换为奖励分数（0-0.1之间）
	consistencyBonus := overallConsistency * 0.1

	sqa.logger.Debug("一致性奖励计算完成: %.4f", consistencyBonus)
	return consistencyBonus
}

// calculateRecoveryBonus 计算恢复能力奖励
func (sqa *SmartQualityAlgorithm) calculateRecoveryBonus(stats *AdvancedProxyStats) float64 {
	sqa.logger.Debug("计算恢复能力奖励")

	if len(stats.LastErrors) == 0 {
		return 0.05 // 无错误记录，给予基础奖励
	}

	// 1. 分析错误恢复模式
	recoveryPatterns := sqa.analyzeRecoveryPatterns(stats.LastErrors)

	// 2. 计算平均恢复时间
	avgRecoveryTime := sqa.calculateAverageRecoveryTime(stats.LastErrors)

	// 3. 评估恢复成功率
	recoverySuccessRate := sqa.calculateRecoverySuccessRate(stats)

	// 4. 计算恢复稳定性
	recoveryStability := sqa.calculateRecoveryStability(stats.LastErrors)

	// 5. 综合恢复能力评分
	recoveryScore := (recoveryPatterns * 0.3 +
					  sqa.normalizeRecoveryTime(avgRecoveryTime) * 0.3 +
					  recoverySuccessRate * 0.3 +
					  recoveryStability * 0.1)

	// 6. 转换为奖励分数（0-0.08之间）
	recoveryBonus := recoveryScore * 0.08

	sqa.logger.Debug("恢复能力奖励计算完成: %.4f", recoveryBonus)
	return recoveryBonus
}

// calculateSpeedTrend 计算速度趋势
func (sqa *SmartQualityAlgorithm) calculateSpeedTrend(stats *AdvancedProxyStats) float64 {
	sqa.logger.Debug("计算速度趋势")

	if len(stats.HourlyStats) < 3 {
		return 0.0 // 数据不足
	}

	// 1. 收集最近时间窗口内的数据点
	recentStats := sqa.collectRecentHourlyStats(stats.HourlyStats, 6) // 最近6小时
	if len(recentStats) < 3 {
		return 0.0
	}

	// 2. 计算线性回归趋势
	trendSlope := sqa.calculateLinearTrend(recentStats)

	// 3. 计算趋势强度
	trendStrength := sqa.calculateTrendStrength(recentStats, trendSlope)

	// 4. 计算趋势稳定性
	trendStability := sqa.calculateTrendStability(recentStats)

	// 5. 综合趋势评分
	// 负斜率（速度提升）为正向趋势
	normalizedSlope := -trendSlope / sqa.responseTimeModel.ExpectedMean
	trendScore := normalizedSlope * trendStrength * trendStability

	// 6. 限制趋势调整范围（-0.05到+0.05）
	trendAdjustment := math.Max(-0.05, math.Min(0.05, trendScore))

	sqa.logger.Debug("速度趋势计算完成: 斜率=%.4f, 强度=%.4f, 稳定性=%.4f, 调整=%.4f",
		trendSlope, trendStrength, trendStability, trendAdjustment)

	return trendAdjustment
}

// calculateResponseTimeVariance 计算响应时间方差
func (sqa *SmartQualityAlgorithm) calculateResponseTimeVariance(stats *AdvancedProxyStats) float64 {
	if len(stats.HourlyStats) < 2 {
		return 0.0
	}
	
	var responseTimes []float64
	for _, hourlyStats := range stats.HourlyStats {
		if hourlyStats != nil && hourlyStats.AvgResponseTime > 0 {
			responseTimes = append(responseTimes, hourlyStats.AvgResponseTime)
		}
	}
	
	if len(responseTimes) < 2 {
		return 0.0
	}
	
	// 计算均值
	mean := 0.0
	for _, rt := range responseTimes {
		mean += rt
	}
	mean /= float64(len(responseTimes))
	
	// 计算方差
	variance := 0.0
	for _, rt := range responseTimes {
		variance += math.Pow(rt-mean, 2)
	}
	variance /= float64(len(responseTimes))
	
	// 标准化方差（相对于均值）
	if mean > 0 {
		return math.Min(1.0, math.Sqrt(variance)/mean)
	}
	return 0.0
}

// calculateSuccessRateStability 计算成功率稳定性
func (sqa *SmartQualityAlgorithm) calculateSuccessRateStability(stats *AdvancedProxyStats) float64 {
	if len(stats.HourlyStats) < 2 {
		return 0.8 // 默认稳定性
	}
	
	var successRates []float64
	for _, hourlyStats := range stats.HourlyStats {
		if hourlyStats != nil && hourlyStats.RequestCount > 0 {
			successRates = append(successRates, hourlyStats.SuccessRate)
		}
	}
	
	if len(successRates) < 2 {
		return 0.8
	}
	
	// 计算成功率的标准差
	mean := 0.0
	for _, sr := range successRates {
		mean += sr
	}
	mean /= float64(len(successRates))
	
	variance := 0.0
	for _, sr := range successRates {
		variance += math.Pow(sr-mean, 2)
	}
	variance /= float64(len(successRates))
	
	// 稳定性分数（方差越小越稳定）
	stability := 1.0 - math.Min(1.0, math.Sqrt(variance))
	return stability
}

// detectAnomalies 检测异常
func (sqa *SmartQualityAlgorithm) detectAnomalies(stats *AdvancedProxyStats) float64 {
	// 简化实现：基于最近错误的异常检测
	if len(stats.LastErrors) == 0 {
		return 0.0
	}
	
	recentErrors := 0
	for _, error := range stats.LastErrors {
		if time.Since(error.Timestamp) < time.Hour {
			recentErrors++
		}
	}
	
	// 异常分数（最近错误越多异常分数越高）
	anomalyScore := math.Min(1.0, float64(recentErrors)/10.0)
	return anomalyScore
}

// calculateSeasonalAdjustment 计算季节性调整
func (sqa *SmartQualityAlgorithm) calculateSeasonalAdjustment(stats *AdvancedProxyStats) float64 {
	// 简化实现：基于当前小时的季节性因子
	currentHour := time.Now().Hour()
	if currentHour < len(sqa.stabilityModel.SeasonalFactors) {
		return sqa.stabilityModel.SeasonalFactors[currentHour]
	}
	return 0.0
}

// getISPQualityBonus 获取ISP质量奖励
func (sqa *SmartQualityAlgorithm) getISPQualityBonus(isp string) float64 {
	// 简化实现：基于ISP的质量奖励
	ispBonuses := map[string]float64{
		"Google":    0.1,
		"Cloudflare": 0.08,
		"Amazon":    0.07,
		"Microsoft": 0.06,
	}
	
	if bonus, exists := ispBonuses[isp]; exists {
		return bonus
	}
	return 0.0
}

// applyTimeDecay 应用时间衰减
func (sqa *SmartQualityAlgorithm) applyTimeDecay(score float64, lastUpdated time.Time) float64 {
	timeSinceUpdate := time.Since(lastUpdated)
	decayHours := timeSinceUpdate.Hours()
	
	// 每小时衰减 1%
	decayFactor := math.Pow(sqa.decayFactor, decayHours)
	return score * decayFactor
}

// applyLearningAdjustment 应用学习调整
func (sqa *SmartQualityAlgorithm) applyLearningAdjustment(score float64, stats *AdvancedProxyStats) float64 {
	// 简化实现：基于历史表现的学习调整
	if stats.UpdateCount > 100 {
		// 对于有足够历史数据的代理，给予小幅奖励
		return score + 0.02
	}
	return score
}
