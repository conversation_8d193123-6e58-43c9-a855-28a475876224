// Package services 提供各种服务实现
package monitoring

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"sync"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
	"flexproxy/internal/interfaces"
)

// MonitoringService 提供监控功能
type monitoringService struct {
	mu           sync.RWMutex
	config       *config.MonitoringConfig
	portsConfig  *config.PortsConfig
	globalConfig *config.Config
	metrics      map[string]interface{}
	healthChecks map[string]HealthCheck
	server       *http.Server
	logger       logger.Logger
	running      bool
	ctx          context.Context
	cancel       context.CancelFunc
}

// HealthCheck 健康检查接口
type HealthCheck interface {
	Check() error
	Name() string
}

// MetricType 指标类型
type MetricType string

const (
	MetricTypeCounter   MetricType = "counter"
	MetricTypeGauge     MetricType = "gauge"
	MetricTypeHistogram MetricType = "histogram"
	MetricTypeSummary   MetricType = "summary"
)

// Metric 指标结构
type Metric struct {
	Name      string                 `json:"name"`
	Type      MetricType             `json:"type"`
	Value     interface{}            `json:"value"`
	Labels    map[string]string      `json:"labels,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	CPUUsage    float64 `json:"cpu_usage"`
	MemoryUsage float64 `json:"memory_usage"`
	Goroutines  int     `json:"goroutines"`
	HeapSize    uint64  `json:"heap_size"`
	HeapInUse   uint64  `json:"heap_in_use"`
	GCCount     uint32  `json:"gc_count"`
	Uptime      string  `json:"uptime"`
}

// ProxyMetrics 代理指标
type ProxyMetrics struct {
	TotalRequests   int64   `json:"total_requests"`
	SuccessRequests int64   `json:"success_requests"`
	FailedRequests  int64   `json:"failed_requests"`
	AvgResponseTime float64 `json:"avg_response_time"`
	ActiveProxies   int     `json:"active_proxies"`
	BannedIPs       int     `json:"banned_ips"`
	BannedDomains   int     `json:"banned_domains"`
}

// NewMonitoringService 创建监控服务
func NewMonitoringService(config *config.MonitoringConfig, portsConfig *config.PortsConfig, globalConfig *config.Config, log logger.Logger) interfaces.MonitoringService {
	if log == nil {
		log = logger.GetLogger("monitoring")
	}

	ctx, cancel := context.WithCancel(context.Background())

	ms := &monitoringService{
		config:       config,
		portsConfig:  portsConfig,
		globalConfig: globalConfig,
		metrics:      make(map[string]interface{}),
		healthChecks: make(map[string]HealthCheck),
		logger:       log,
		ctx:          ctx,
		cancel:       cancel,
	}

	// 注册默认健康检查
	ms.registerDefaultHealthChecks()

	ms.logger.Info("监控服务已初始化")
	return ms
}

// Start 启动监控服务
func (ms *monitoringService) Start() error {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	// 检查监控服务是否启用
	checker := config.NewConfigChecker(ms.globalConfig)
	if !checker.IsMonitoringEnabled() {
		ms.logger.Info("监控服务已禁用")
		return nil
	}

	if ms.running {
		ms.logger.Warn("监控服务已在运行")
		return nil
	}

	// 获取监控端口
	monitoringPort := constants.DefaultMonitoringPort
	if ms.portsConfig != nil && ms.portsConfig.Monitoring > 0 {
		monitoringPort = ms.portsConfig.Monitoring
	}

	// 创建HTTP服务器
	mux := http.NewServeMux()
	mux.HandleFunc(ms.config.Path+"/metrics", ms.handleMetrics)
	mux.HandleFunc(ms.config.Path+"/health", ms.handleHealth)
	mux.HandleFunc(ms.config.Path+"/status", ms.handleStatus)

	ms.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", monitoringPort),
		Handler: mux,
	}

	// 启动指标收集
	go ms.collectMetrics()

	// 启动HTTP服务器
	go func() {
		ms.logger.Info(fmt.Sprintf("监控服务启动在端口 %d", monitoringPort))
		if err := ms.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			ms.logger.Error(fmt.Sprintf("监控服务启动失败: %v", err))
		}
	}()

	ms.running = true
	ms.logger.Info("监控服务已启动")
	return nil
}

// Stop 停止监控服务
func (ms *monitoringService) Stop() error {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	if !ms.running {
		return nil
	}

	ms.cancel()

	if ms.server != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := ms.server.Shutdown(ctx); err != nil {
			ms.logger.Error(fmt.Sprintf("监控服务停止失败: %v", err))
			return err
		}
	}

	ms.running = false
	ms.logger.Info("监控服务已停止")
	return nil
}

// RecordMetric 记录指标
func (ms *monitoringService) RecordMetric(name string, value interface{}, metricType interface{}, labels map[string]string) {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	var mType MetricType
	if mt, ok := metricType.(MetricType); ok {
		mType = mt
	} else {
		mType = MetricTypeGauge // 默认类型
	}

	metric := Metric{
		Name:      name,
		Type:      mType,
		Value:     value,
		Labels:    labels,
		Timestamp: time.Now(),
	}

	ms.metrics[name] = metric
	ms.logger.Debug(fmt.Sprintf("记录指标: %s = %v", name, value))
}

// UpdateProxyMetrics 更新代理指标
func (ms *monitoringService) UpdateProxyMetrics(totalReq, successReq, failedReq int64, avgRespTime float64, activeProxies, bannedIPs, bannedDomains int) {
	proxyMetrics := ProxyMetrics{
		TotalRequests:   totalReq,
		SuccessRequests: successReq,
		FailedRequests:  failedReq,
		AvgResponseTime: avgRespTime,
		ActiveProxies:   activeProxies,
		BannedIPs:       bannedIPs,
		BannedDomains:   bannedDomains,
	}

	ms.RecordMetric("proxy_metrics", proxyMetrics, MetricTypeGauge, nil)
}

// GetMetrics 获取所有指标
func (ms *monitoringService) GetMetrics() map[string]interface{} {
	ms.mu.RLock()
	defer ms.mu.RUnlock()

	result := make(map[string]interface{})
	for k, v := range ms.metrics {
		result[k] = v
	}

	return result
}

// GetConfig 获取当前监控配置
func (ms *monitoringService) GetConfig() interface{} {
	ms.mu.RLock()
	defer ms.mu.RUnlock()

	return ms.config
}

// UpdateConfig 更新监控服务配置
// 支持热重载监控端口、路径、指标收集间隔等配置
func (ms *monitoringService) UpdateConfig(configInterface interface{}) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	// 类型断言
	cfg, ok := configInterface.(*config.MonitoringConfig)
	if !ok {
		return fmt.Errorf("无效的配置类型，期望 *config.MonitoringConfig")
	}

	if cfg == nil {
		return fmt.Errorf("配置不能为空")
	}

	oldConfig := ms.config
	ms.config = cfg

	// 记录配置更新
	ms.logger.Info("开始更新监控服务配置")

	// 1. 更新监控路径
	if err := ms.updateMonitoringPath(oldConfig, cfg); err != nil {
		ms.logger.Warn(fmt.Sprintf("更新监控路径失败: %v", err))
	}

	// 2. 更新指标收集配置
	if err := ms.updateMetricsConfig(oldConfig, cfg); err != nil {
		ms.logger.Warn(fmt.Sprintf("更新指标收集配置失败: %v", err))
	}

	// 3. 更新健康检查配置
	if err := ms.updateHealthCheckConfig(oldConfig, cfg); err != nil {
		ms.logger.Warn(fmt.Sprintf("更新健康检查配置失败: %v", err))
	}

	// 4. 如果HTTP服务器正在运行且端口配置发生变化，需要重启服务器
	if ms.server != nil && ms.needsServerRestart(oldConfig, cfg) {
		if err := ms.restartHTTPServer(); err != nil {
			ms.logger.Error(fmt.Sprintf("重启HTTP服务器失败: %v", err))
			return fmt.Errorf("重启HTTP服务器失败: %v", err)
		}
	}

	ms.logger.Info("监控服务配置更新完成")
	return nil
}

// RegisterHealthCheck 注册健康检查
func (ms *monitoringService) RegisterHealthCheck(name string, check interface{}) {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	if hc, ok := check.(HealthCheck); ok {
		ms.healthChecks[name] = hc
	}
	ms.logger.Debug(fmt.Sprintf("注册健康检查: %s", name))
}

// updateMonitoringPath 更新监控路径
func (ms *monitoringService) updateMonitoringPath(oldConfig, newConfig *config.MonitoringConfig) error {
	newPath := "/metrics" // 默认路径
	if newConfig.Path != "" {
		newPath = newConfig.Path
	}

	// 检查是否有变化
	oldPath := "/metrics"
	if oldConfig != nil && oldConfig.Path != "" {
		oldPath = oldConfig.Path
	}

	if newPath != oldPath {
		ms.logger.Info(fmt.Sprintf("监控路径已更新: %s -> %s", oldPath, newPath))
		// 路径更新需要重启HTTP服务器才能生效
	}

	return nil
}

// updateMetricsConfig 更新指标收集配置
func (ms *monitoringService) updateMetricsConfig(oldConfig, newConfig *config.MonitoringConfig) error {
	// 这里可以添加指标收集间隔、指标类型等配置的更新逻辑
	ms.logger.Debug("指标收集配置已更新")
	return nil
}

// updateHealthCheckConfig 更新健康检查配置
func (ms *monitoringService) updateHealthCheckConfig(oldConfig, newConfig *config.MonitoringConfig) error {
	// 这里可以添加健康检查间隔、超时等配置的更新逻辑
	ms.logger.Debug("健康检查配置已更新")
	return nil
}

// needsServerRestart 检查是否需要重启HTTP服务器
func (ms *monitoringService) needsServerRestart(oldConfig, newConfig *config.MonitoringConfig) bool {
	// 检查路径是否发生变化
	oldPath := "/metrics"
	if oldConfig != nil && oldConfig.Path != "" {
		oldPath = oldConfig.Path
	}

	newPath := "/metrics"
	if newConfig.Path != "" {
		newPath = newConfig.Path
	}

	return oldPath != newPath
}

// restartHTTPServer 重启HTTP服务器
func (ms *monitoringService) restartHTTPServer() error {
	ms.logger.Info("正在重启监控HTTP服务器以应用新配置")

	// 停止当前服务器
	if ms.server != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := ms.server.Shutdown(ctx); err != nil {
			ms.logger.Warn(fmt.Sprintf("优雅关闭HTTP服务器失败: %v", err))
		}
	}

	// 获取监控端口
	monitoringPort := constants.DefaultMonitoringPort
	if ms.portsConfig != nil && ms.portsConfig.Monitoring > 0 {
		monitoringPort = ms.portsConfig.Monitoring
	}

	// 创建新的HTTP服务器
	mux := http.NewServeMux()
	mux.HandleFunc(ms.config.Path+"/metrics", ms.handleMetrics)
	mux.HandleFunc(ms.config.Path+"/health", ms.handleHealth)
	mux.HandleFunc(ms.config.Path+"/status", ms.handleStatus)

	ms.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", monitoringPort),
		Handler: mux,
	}

	// 启动新服务器
	go func() {
		if err := ms.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			ms.logger.Error(fmt.Sprintf("监控HTTP服务器启动失败: %v", err))
		}
	}()

	ms.logger.Info(fmt.Sprintf("监控HTTP服务器已重启，监听端口: %d", monitoringPort))
	return nil
}

// GetHealthStatus 获取健康状态
func (ms *monitoringService) GetHealthStatus() map[string]interface{} {
	ms.mu.RLock()
	defer ms.mu.RUnlock()

	status := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now().Format(constants.TimeFormatDefault),
		"checks":    make(map[string]interface{}),
	}

	allHealthy := true
	checks := status["checks"].(map[string]interface{})

	for name, check := range ms.healthChecks {
		checkResult := map[string]interface{}{
			"name":   check.Name(),
			"status": "healthy",
		}

		if err := check.Check(); err != nil {
			checkResult["status"] = "unhealthy"
			checkResult["error"] = err.Error()
			allHealthy = false
		}

		checks[name] = checkResult
	}

	if !allHealthy {
		status["status"] = "unhealthy"
	}

	return status
}

// GetProxyMetrics 获取代理指标
func (ms *monitoringService) GetProxyMetrics() map[string]interface{} {
	ms.mu.RLock()
	defer ms.mu.RUnlock()

	// 从指标中获取代理相关数据
	proxyMetrics := ProxyMetrics{
		TotalRequests:   0,
		SuccessRequests: 0,
		FailedRequests:  0,
		AvgResponseTime: 0.0,
		ActiveProxies:   0,
		BannedIPs:       0,
		BannedDomains:   0,
	}

	// 从存储的指标中提取代理相关数据
	if metric, exists := ms.metrics["proxy_metrics"]; exists {
		if pm, ok := metric.(ProxyMetrics); ok {
			proxyMetrics = pm
		}
	}

	return map[string]interface{}{
		"total_requests":    proxyMetrics.TotalRequests,
		"success_requests":  proxyMetrics.SuccessRequests,
		"failed_requests":   proxyMetrics.FailedRequests,
		"avg_response_time": proxyMetrics.AvgResponseTime,
		"active_proxies":    proxyMetrics.ActiveProxies,
		"banned_ips":        proxyMetrics.BannedIPs,
		"banned_domains":    proxyMetrics.BannedDomains,
		"timestamp":         time.Now().Format(constants.TimeFormatDefault),
	}
}

// GetSystemMetrics 获取系统指标
func (ms *monitoringService) GetSystemMetrics() map[string]interface{} {
	ms.mu.RLock()
	defer ms.mu.RUnlock()

	// 从指标中获取系统相关数据
	if metric, exists := ms.metrics["system_metrics"]; exists {
		if sm, ok := metric.(SystemMetrics); ok {
			return map[string]interface{}{
				"cpu_usage":    sm.CPUUsage,
				"memory_usage": sm.MemoryUsage,
				"goroutines":   sm.Goroutines,
				"heap_size":    sm.HeapSize,
				"heap_in_use":  sm.HeapInUse,
				"gc_count":     sm.GCCount,
				"uptime":       sm.Uptime,
				"timestamp":    time.Now().Format(constants.TimeFormatDefault),
			}
		}
	}

	return map[string]interface{}{
		"timestamp": time.Now().Format(constants.TimeFormatDefault),
	}
}

// StartHTTPServer 启动HTTP服务器
func (ms *monitoringService) StartHTTPServer(addr string) error {
	return ms.Start()
}

// StopHTTPServer 停止HTTP服务器
func (ms *monitoringService) StopHTTPServer() error {
	return ms.Stop()
}

// RecordMetricFloat 记录浮点数指标（匹配接口）
func (ms *monitoringService) RecordMetricFloat(name string, value float64, tags map[string]string) error {
	ms.RecordMetric(name, value, MetricTypeGauge, tags)
	return nil
}

// RegisterHealthCheckFunc 注册函数健康检查（匹配接口）
func (ms *monitoringService) RegisterHealthCheckFunc(name string, check func() error) error {
	healthCheck := &FunctionHealthCheck{
		name:      name,
		checkFunc: check,
	}
	ms.RegisterHealthCheck(name, healthCheck)
	return nil
}

// collectMetrics 收集系统指标
func (ms *monitoringService) collectMetrics() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ms.ctx.Done():
			return
		case <-ticker.C:
			ms.collectSystemMetrics()
		}
	}
}

// collectSystemMetrics 收集系统指标
func (ms *monitoringService) collectSystemMetrics() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	sysMetrics := SystemMetrics{
		Goroutines: runtime.NumGoroutine(),
		HeapSize:   m.HeapSys,
		HeapInUse:  m.HeapInuse,
		GCCount:    m.NumGC,
		Uptime:     time.Since(time.Now()).String(), // 这里需要实际的启动时间
	}

	ms.RecordMetric("system_metrics", sysMetrics, MetricTypeGauge, nil)
}

// registerDefaultHealthChecks 注册默认健康检查
func (ms *monitoringService) registerDefaultHealthChecks() {
	// 内存健康检查
	ms.RegisterHealthCheck("memory", &MemoryHealthCheck{})
	// Goroutine健康检查
	ms.RegisterHealthCheck("goroutines", &GoroutineHealthCheck{})
}

// handleMetrics 处理指标请求
func (ms *monitoringService) handleMetrics(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	metrics := ms.GetMetrics()
	if err := json.NewEncoder(w).Encode(metrics); err != nil {
		ms.logger.Error(fmt.Sprintf("编码指标失败: %v", err))
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

// handleHealth 处理健康检查请求
func (ms *monitoringService) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	health := ms.GetHealthStatus()
	status := http.StatusOK
	if health["status"] == "unhealthy" {
		status = http.StatusServiceUnavailable
	}

	w.WriteHeader(status)
	if err := json.NewEncoder(w).Encode(health); err != nil {
		ms.logger.Error(fmt.Sprintf("编码健康状态失败: %v", err))
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

// handleStatus 处理状态请求
func (ms *monitoringService) handleStatus(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	status := map[string]interface{}{
		"service": "FlexProxy",
		"version": "1.0.0",
		"status":  "running",
		"uptime":  time.Since(time.Now()).String(),
	}

	if err := json.NewEncoder(w).Encode(status); err != nil {
		ms.logger.Error(fmt.Sprintf("编码状态失败: %v", err))
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

// MemoryHealthCheck 内存健康检查
type MemoryHealthCheck struct{}

func (m *MemoryHealthCheck) Name() string {
	return "memory"
}

func (m *MemoryHealthCheck) Check() error {
	var mem runtime.MemStats
	runtime.ReadMemStats(&mem)

	// 检查内存使用是否超过阈值
	if mem.HeapInuse > constants.MemoryThresholdBytes {
		return errors.NewErrorWithDetails(errors.ErrTypeMonitoring, errors.ErrCodeMonitoringMemoryHigh,
			errors.ErrMonitoringMemoryHigh.Message, fmt.Sprintf("内存使用: %d bytes", mem.HeapInuse))
	}

	return nil
}

// GoroutineHealthCheck Goroutine健康检查
type GoroutineHealthCheck struct{}

func (g *GoroutineHealthCheck) Name() string {
	return "goroutines"
}

func (g *GoroutineHealthCheck) Check() error {
	count := runtime.NumGoroutine()

	// 检查Goroutine数量是否超过阈值
	if count > constants.GoroutineThresholdCount {
		return errors.NewErrorWithDetails(errors.ErrTypeMonitoring, errors.ErrCodeMonitoringGoroutineHigh,
			errors.ErrMonitoringGoroutineHigh.Message, fmt.Sprintf("Goroutine数量: %d", count))
	}

	return nil
}

// FunctionHealthCheck 函数健康检查
type FunctionHealthCheck struct {
	name      string
	checkFunc func() error
}

func (f *FunctionHealthCheck) Name() string {
	return f.name
}

func (f *FunctionHealthCheck) Check() error {
	return f.checkFunc()
}
