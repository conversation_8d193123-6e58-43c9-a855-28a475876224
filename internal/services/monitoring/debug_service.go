// Package services 提供各种服务实现
package monitoring

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"net/http/pprof"
	"regexp"
	"runtime"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"

	"flexproxy/common/errors"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
	"flexproxy/internal/interfaces"
)

// DebugService 调试服务实现
type debugService struct {
	mu          sync.RWMutex
	config      *config.DebugConfig
	portsConfig *config.PortsConfig
	logger      logger.Logger
	running     bool
	server      *http.Server
	breakpoints map[string]*Breakpoint
	watchers    map[string]*Watcher
	profiler    *Profiler
	ctx         context.Context
	cancel      context.CancelFunc
}

// Breakpoint 断点
type Breakpoint struct {
	ID        string                       `json:"id"`
	Location  string                       `json:"location"`
	Condition string                       `json:"condition"`
	Enabled   bool                         `json:"enabled"`
	HitCount  int64                        `json:"hit_count"`
	CreatedAt time.Time                    `json:"created_at"`
	Callback  func(map[string]interface{}) `json:"-"`
}

// Watcher 监视器
type Watcher struct {
	ID          string                         `json:"id"`
	Expression  string                         `json:"expression"`
	Enabled     bool                           `json:"enabled"`
	LastValue   interface{}                    `json:"last_value"`
	ChangeCount int64                          `json:"change_count"`
	CreatedAt   time.Time                      `json:"created_at"`
	Callback    func(interface{}, interface{}) `json:"-"`
}

// Profiler 性能分析器
type Profiler struct {
	mu       sync.RWMutex
	enabled  bool
	profiles map[string]*ProfileData
}

// ProfileData 性能分析数据
type ProfileData struct {
	Name      string                 `json:"name"`
	StartTime time.Time              `json:"start_time"`
	EndTime   time.Time              `json:"end_time"`
	Duration  time.Duration          `json:"duration"`
	Data      map[string]interface{} `json:"data"`
}

// DebugInfo 调试信息
type DebugInfo struct {
	Timestamp   time.Time              `json:"timestamp"`
	Goroutines  int                    `json:"goroutines"`
	MemStats    runtime.MemStats       `json:"mem_stats"`
	GCStats     debug.GCStats          `json:"gc_stats"`
	BuildInfo   *debug.BuildInfo       `json:"build_info"`
	Breakpoints []*Breakpoint          `json:"breakpoints"`
	Watchers    []*Watcher             `json:"watchers"`
	CustomData  map[string]interface{} `json:"custom_data"`
}

// NewDebugService 创建新的调试服务
func NewDebugService(cfg *config.DebugConfig, portsConfig *config.PortsConfig, log logger.Logger) interfaces.DebugService {
	if log == nil {
		log = logger.GetLogger("debug")
	}

	if cfg == nil {
		cfg = &config.DebugConfig{
			Enabled:        false,
			VerboseLogging: false,
			DumpRequests:   false,
			DumpResponses:  false,
			ProfileEnabled: false,
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	ds := &debugService{
		config:      cfg,
		portsConfig: portsConfig,
		logger:      log,
		breakpoints: make(map[string]*Breakpoint),
		watchers:    make(map[string]*Watcher),
		profiler:    &Profiler{profiles: make(map[string]*ProfileData)},
		ctx:         ctx,
		cancel:      cancel,
	}

	ds.logger.Info("调试服务已初始化")
	return ds
}

// Start 启动调试服务
func (ds *debugService) Start() error {
	ds.mu.Lock()
	defer ds.mu.Unlock()

	if ds.running {
		return fmt.Errorf("调试服务已在运行")
	}

	if !ds.config.Enabled {
		ds.logger.Info("调试服务已禁用")
		return nil
	}

	// 启动调试HTTP服务器
	if err := ds.startDebugServer(); err != nil {
		return fmt.Errorf("启动调试服务器失败: %v", err)
	}

	// 启动性能分析器
	ds.profiler.enabled = true

	ds.running = true
	// 获取调试端口
	debugPort := 6060 // 默认调试端口
	if ds.portsConfig != nil && ds.portsConfig.Debug > 0 {
		debugPort = ds.portsConfig.Debug
	}
	ds.logger.Info(fmt.Sprintf("调试服务已启动，端口: %d", debugPort))
	return nil
}

// Stop 停止调试服务
func (ds *debugService) Stop() error {
	ds.mu.Lock()
	defer ds.mu.Unlock()

	if !ds.running {
		return nil
	}

	ds.cancel()

	// 停止HTTP服务器
	if ds.server != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := ds.server.Shutdown(ctx); err != nil {
			ds.logger.Error(fmt.Sprintf("停止调试服务器失败: %v", err))
		}
	}

	// 停止性能分析器
	ds.profiler.enabled = false

	ds.running = false
	ds.logger.Info("调试服务已停止")
	return nil
}

// IsEnabled 检查调试服务是否启用
func (ds *debugService) IsEnabled() bool {
	ds.mu.RLock()
	defer ds.mu.RUnlock()
	return ds.config.Enabled && ds.running
}

// SetBreakpoint 设置断点
func (ds *debugService) SetBreakpoint(id, location string, callback func()) error {
	if id == "" || location == "" {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeDebugBreakpointEmpty,
			errors.ErrDebugBreakpointEmpty.Message, "断点ID和位置不能为空")
	}

	ds.mu.Lock()
	defer ds.mu.Unlock()

	if _, exists := ds.breakpoints[id]; exists {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeDebugBreakpointExists,
			errors.ErrDebugBreakpointExists.Message, fmt.Sprintf("断点ID: %s", id))
	}

	// 包装回调函数
	wrappedCallback := func(context map[string]interface{}) {
		if callback != nil {
			callback()
		}
	}

	bp := &Breakpoint{
		ID:        id,
		Location:  location,
		Condition: "", // 默认无条件
		Enabled:   true,
		CreatedAt: time.Now(),
		Callback:  wrappedCallback,
	}

	ds.breakpoints[id] = bp
	ds.logger.Debug(fmt.Sprintf("设置断点: %s at %s", id, location))
	return nil
}

// RemoveBreakpoint 移除断点
func (ds *debugService) RemoveBreakpoint(id string) error {
	ds.mu.Lock()
	defer ds.mu.Unlock()

	if _, exists := ds.breakpoints[id]; !exists {
		return fmt.Errorf("断点不存在: %s", id)
	}

	delete(ds.breakpoints, id)
	ds.logger.Debug(fmt.Sprintf("移除断点: %s", id))
	return nil
}

// EnableBreakpoint 启用断点
func (ds *debugService) EnableBreakpoint(id string) error {
	ds.mu.Lock()
	defer ds.mu.Unlock()

	bp, exists := ds.breakpoints[id]
	if !exists {
		return fmt.Errorf("断点不存在: %s", id)
	}

	bp.Enabled = true
	ds.logger.Debug(fmt.Sprintf("启用断点: %s", id))
	return nil
}

// DisableBreakpoint 禁用断点
func (ds *debugService) DisableBreakpoint(id string) error {
	ds.mu.Lock()
	defer ds.mu.Unlock()

	bp, exists := ds.breakpoints[id]
	if !exists {
		return fmt.Errorf("断点不存在: %s", id)
	}

	bp.Enabled = false
	ds.logger.Debug(fmt.Sprintf("禁用断点: %s", id))
	return nil
}

// HitBreakpoint 触发断点
func (ds *debugService) HitBreakpoint(id string, context map[string]interface{}) bool {
	ds.mu.RLock()
	bp, exists := ds.breakpoints[id]
	ds.mu.RUnlock()

	if !exists || !bp.Enabled {
		return false
	}

	// 检查条件
	if bp.Condition != "" && !ds.evaluateCondition(bp.Condition, context) {
		return false
	}

	ds.mu.Lock()
	bp.HitCount++
	ds.mu.Unlock()

	ds.logger.Debug(fmt.Sprintf("断点触发: %s (第%d次)", id, bp.HitCount))

	// 执行回调
	if bp.Callback != nil {
		go bp.Callback(context)
	}

	return true
}

// SetWatcher 设置监视器
func (ds *debugService) SetWatcher(id, expression string, callback func(interface{})) error {
	if id == "" || expression == "" {
		return fmt.Errorf("监视器ID和表达式不能为空")
	}

	ds.mu.Lock()
	defer ds.mu.Unlock()

	if _, exists := ds.watchers[id]; exists {
		return fmt.Errorf("监视器已存在: %s", id)
	}

	// 包装回调函数
	wrappedCallback := func(oldValue, newValue interface{}) {
		if callback != nil {
			callback(newValue)
		}
	}

	watcher := &Watcher{
		ID:         id,
		Expression: expression,
		Enabled:    true,
		CreatedAt:  time.Now(),
		Callback:   wrappedCallback,
	}

	ds.watchers[id] = watcher
	ds.logger.Debug(fmt.Sprintf("设置监视器: %s for %s", id, expression))
	return nil
}

// RemoveWatcher 移除监视器
func (ds *debugService) RemoveWatcher(id string) error {
	ds.mu.Lock()
	defer ds.mu.Unlock()

	if _, exists := ds.watchers[id]; !exists {
		return fmt.Errorf("监视器不存在: %s", id)
	}

	delete(ds.watchers, id)
	ds.logger.Debug(fmt.Sprintf("移除监视器: %s", id))
	return nil
}

// UpdateWatcher 更新监视器值
func (ds *debugService) UpdateWatcher(id string, value interface{}) error {
	ds.mu.RLock()
	watcher, exists := ds.watchers[id]
	ds.mu.RUnlock()

	if !exists {
		return fmt.Errorf("监视器不存在: %s", id)
	}

	if !watcher.Enabled {
		return nil
	}

	oldValue := watcher.LastValue

	ds.mu.Lock()
	watcher.LastValue = value
	watcher.ChangeCount++
	ds.mu.Unlock()

	// 如果值发生变化，执行回调
	if oldValue != value && watcher.Callback != nil {
		go watcher.Callback(oldValue, value)
	}

	return nil
}

// StartProfiling 开始性能分析
func (ds *debugService) StartProfiling(name string, duration time.Duration) error {
	if duration <= 0 {
		return fmt.Errorf("分析持续时间必须大于0")
	}

	if !ds.profiler.enabled {
		return fmt.Errorf("性能分析器未启用")
	}

	ds.profiler.mu.Lock()
	defer ds.profiler.mu.Unlock()

	if _, exists := ds.profiler.profiles[name]; exists {
		return fmt.Errorf("性能分析已存在: %s", name)
	}

	profile := &ProfileData{
		Name:      name,
		StartTime: time.Now(),
		Data:      make(map[string]interface{}),
	}

	ds.profiler.profiles[name] = profile
	ds.logger.Debug(fmt.Sprintf("开始性能分析: %s (持续时间: %v)", name, duration))
	return nil
}

// StopProfiling 停止性能分析
func (ds *debugService) StopProfiling(name string) ([]byte, error) {
	ds.profiler.mu.Lock()
	defer ds.profiler.mu.Unlock()

	profile, exists := ds.profiler.profiles[name]
	if !exists {
		return nil, fmt.Errorf("性能分析不存在: %s", name)
	}

	profile.EndTime = time.Now()
	profile.Duration = profile.EndTime.Sub(profile.StartTime)

	// 收集性能数据
	ds.collectProfileData(profile)

	delete(ds.profiler.profiles, name)
	ds.logger.Debug(fmt.Sprintf("停止性能分析: %s (耗时: %v)", name, profile.Duration))

	// 将profile数据序列化为JSON
	data, err := json.Marshal(profile)
	if err != nil {
		return nil, fmt.Errorf("序列化性能分析数据失败: %v", err)
	}

	return data, nil
}

// GetDebugInfo 获取调试信息
func (ds *debugService) GetDebugInfo() map[string]interface{} {
	ds.mu.RLock()
	defer ds.mu.RUnlock()

	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	var gcStats debug.GCStats
	debug.ReadGCStats(&gcStats)

	buildInfo, _ := debug.ReadBuildInfo()

	// 复制断点
	breakpoints := make([]*Breakpoint, 0, len(ds.breakpoints))
	for _, bp := range ds.breakpoints {
		bpCopy := *bp
		bpCopy.Callback = nil // 不序列化回调函数
		breakpoints = append(breakpoints, &bpCopy)
	}

	// 复制监视器
	watchers := make([]*Watcher, 0, len(ds.watchers))
	for _, w := range ds.watchers {
		wCopy := *w
		wCopy.Callback = nil // 不序列化回调函数
		watchers = append(watchers, &wCopy)
	}

	return map[string]interface{}{
		"timestamp":   time.Now(),
		"goroutines":  runtime.NumGoroutine(),
		"mem_stats":   memStats,
		"gc_stats":    gcStats,
		"build_info":  buildInfo,
		"breakpoints": breakpoints,
		"watchers":    watchers,
		"custom_data": make(map[string]interface{}),
	}
}

// DumpStack 获取堆栈信息
func (ds *debugService) DumpStack() string {
	buf := make([]byte, 1024*1024) // 1MB buffer
	n := runtime.Stack(buf, true)
	return string(buf[:n])
}

// ForceGC 强制垃圾回收
func (ds *debugService) ForceGC() error {
	runtime.GC()
	ds.logger.Info("强制垃圾回收完成")
	return nil
}

// SetLogLevel 设置日志级别
func (ds *debugService) SetLogLevel(level string) error {
	ds.mu.Lock()
	defer ds.mu.Unlock()

	// 注意：DebugConfig中没有LogLevel字段，这里可以通过其他方式处理
	ds.logger.Info(fmt.Sprintf("日志级别已设置为: %s", level))
	return nil
}

// startDebugServer 启动调试HTTP服务器
func (ds *debugService) startDebugServer() error {
	mux := http.NewServeMux()

	// 注册pprof处理器
	pprofPath := "/debug/pprof"
	mux.HandleFunc(pprofPath+"/", pprof.Index)
	mux.HandleFunc(pprofPath+"/cmdline", pprof.Cmdline)
	mux.HandleFunc(pprofPath+"/profile", pprof.Profile)
	mux.HandleFunc(pprofPath+"/symbol", pprof.Symbol)
	mux.HandleFunc(pprofPath+"/trace", pprof.Trace)

	// 注册自定义调试端点
	mux.HandleFunc("/debug/info", ds.handleDebugInfo)
	mux.HandleFunc("/debug/breakpoints", ds.handleBreakpoints)
	mux.HandleFunc("/debug/watchers", ds.handleWatchers)
	mux.HandleFunc("/debug/stack", ds.handleStack)
	mux.HandleFunc("/debug/gc", ds.handleGC)

	// 获取调试端口
	debugPort := 6060 // 默认调试端口
	if ds.portsConfig != nil && ds.portsConfig.Debug > 0 {
		debugPort = ds.portsConfig.Debug
	}

	ds.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", debugPort),
		Handler: mux,
	}

	go func() {
		if err := ds.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			ds.logger.Error(fmt.Sprintf("调试服务器错误: %v", err))
		}
	}()

	return nil
}

// handleDebugInfo 处理调试信息请求
func (ds *debugService) handleDebugInfo(w http.ResponseWriter, r *http.Request) {
	info := ds.GetDebugInfo()
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(info)
}

// handleBreakpoints 处理断点请求
func (ds *debugService) handleBreakpoints(w http.ResponseWriter, r *http.Request) {
	ds.mu.RLock()
	breakpoints := make([]*Breakpoint, 0, len(ds.breakpoints))
	for _, bp := range ds.breakpoints {
		bpCopy := *bp
		bpCopy.Callback = nil
		breakpoints = append(breakpoints, &bpCopy)
	}
	ds.mu.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(breakpoints)
}

// handleWatchers 处理监视器请求
func (ds *debugService) handleWatchers(w http.ResponseWriter, r *http.Request) {
	ds.mu.RLock()
	watchers := make([]*Watcher, 0, len(ds.watchers))
	for _, w := range ds.watchers {
		wCopy := *w
		wCopy.Callback = nil
		watchers = append(watchers, &wCopy)
	}
	ds.mu.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(watchers)
}

// handleStack 处理堆栈请求
func (ds *debugService) handleStack(w http.ResponseWriter, r *http.Request) {
	stack := ds.DumpStack()
	w.Header().Set("Content-Type", "text/plain")
	w.Write([]byte(stack))
}

// handleGC 处理垃圾回收请求
func (ds *debugService) handleGC(w http.ResponseWriter, r *http.Request) {
	if r.Method == http.MethodPost {
		ds.ForceGC()
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("GC executed"))
	} else {
		w.WriteHeader(http.StatusMethodNotAllowed)
	}
}

// evaluateCondition 评估断点条件
// 实现完整的表达式解析器，支持多种运算符、逻辑操作、函数调用等
func (ds *debugService) evaluateCondition(condition string, context map[string]interface{}) bool {
	if condition == "" {
		return true
	}

	// 预处理条件表达式
	condition = strings.TrimSpace(condition)

	// 创建表达式解析器
	parser := &ExpressionParser{
		context: context,
		logger:  ds.logger,
	}

	// 解析并评估表达式
	result, err := parser.Evaluate(condition)
	if err != nil {
		ds.logger.Error(fmt.Sprintf("条件表达式评估失败: %s, 错误: %v", condition, err))
		return false
	}

	// 转换结果为布尔值
	return parser.ToBool(result)
}

// collectProfileData 收集性能分析数据
func (ds *debugService) collectProfileData(profile *ProfileData) {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	profile.Data["goroutines"] = runtime.NumGoroutine()
	profile.Data["heap_alloc"] = memStats.HeapAlloc
	profile.Data["heap_sys"] = memStats.HeapSys
	profile.Data["heap_inuse"] = memStats.HeapInuse
	profile.Data["stack_inuse"] = memStats.StackInuse
	profile.Data["num_gc"] = memStats.NumGC
	profile.Data["gc_cpu_fraction"] = memStats.GCCPUFraction
}

// ExpressionParser 表达式解析器
type ExpressionParser struct {
	context map[string]interface{}
	logger  logger.Logger
}

// Token 表达式标记
type Token struct {
	Type  TokenType
	Value string
	Pos   int
}

// TokenType 标记类型
type TokenType int

const (
	TokenEOF TokenType = iota
	TokenNumber
	TokenString
	TokenIdentifier
	TokenOperator
	TokenLeftParen
	TokenRightParen
	TokenComma
	TokenLogical
)

// Evaluate 评估表达式
func (ep *ExpressionParser) Evaluate(expression string) (interface{}, error) {
	// 词法分析
	tokens, err := ep.tokenize(expression)
	if err != nil {
		return nil, fmt.Errorf("词法分析失败: %v", err)
	}

	// 语法分析和求值
	parser := &Parser{
		tokens:  tokens,
		pos:     0,
		context: ep.context,
		logger:  ep.logger,
	}

	return parser.parseExpression()
}

// ToBool 将结果转换为布尔值
func (ep *ExpressionParser) ToBool(value interface{}) bool {
	if value == nil {
		return false
	}

	switch v := value.(type) {
	case bool:
		return v
	case int:
		return v != 0
	case int64:
		return v != 0
	case float64:
		return v != 0.0
	case string:
		return v != "" && v != "false" && v != "0"
	default:
		return true
	}
}

// tokenize 词法分析
func (ep *ExpressionParser) tokenize(expression string) ([]Token, error) {
	var tokens []Token
	pos := 0

	for pos < len(expression) {
		// 跳过空白字符
		if isWhitespace(expression[pos]) {
			pos++
			continue
		}

		// 数字
		if isDigit(expression[pos]) {
			start := pos
			for pos < len(expression) && (isDigit(expression[pos]) || expression[pos] == '.') {
				pos++
			}
			tokens = append(tokens, Token{TokenNumber, expression[start:pos], start})
			continue
		}

		// 字符串
		if expression[pos] == '"' || expression[pos] == '\'' {
			quote := expression[pos]
			start := pos
			pos++ // 跳过开始引号
			for pos < len(expression) && expression[pos] != quote {
				if expression[pos] == '\\' {
					pos++ // 跳过转义字符
				}
				pos++
			}
			if pos >= len(expression) {
				return nil, fmt.Errorf("未闭合的字符串，位置: %d", start)
			}
			pos++ // 跳过结束引号
			tokens = append(tokens, Token{TokenString, expression[start+1:pos-1], start})
			continue
		}

		// 标识符
		if isLetter(expression[pos]) || expression[pos] == '_' {
			start := pos
			for pos < len(expression) && (isAlphaNumeric(expression[pos]) || expression[pos] == '_' || expression[pos] == '.') {
				pos++
			}
			tokens = append(tokens, Token{TokenIdentifier, expression[start:pos], start})
			continue
		}

		// 运算符和特殊字符
		switch expression[pos] {
		case '(':
			tokens = append(tokens, Token{TokenLeftParen, "(", pos})
			pos++
		case ')':
			tokens = append(tokens, Token{TokenRightParen, ")", pos})
			pos++
		case ',':
			tokens = append(tokens, Token{TokenComma, ",", pos})
			pos++
		case '=':
			if pos+1 < len(expression) && expression[pos+1] == '=' {
				tokens = append(tokens, Token{TokenOperator, "==", pos})
				pos += 2
			} else {
				tokens = append(tokens, Token{TokenOperator, "=", pos})
				pos++
			}
		case '!':
			if pos+1 < len(expression) && expression[pos+1] == '=' {
				tokens = append(tokens, Token{TokenOperator, "!=", pos})
				pos += 2
			} else {
				tokens = append(tokens, Token{TokenOperator, "!", pos})
				pos++
			}
		case '<':
			if pos+1 < len(expression) && expression[pos+1] == '=' {
				tokens = append(tokens, Token{TokenOperator, "<=", pos})
				pos += 2
			} else {
				tokens = append(tokens, Token{TokenOperator, "<", pos})
				pos++
			}
		case '>':
			if pos+1 < len(expression) && expression[pos+1] == '=' {
				tokens = append(tokens, Token{TokenOperator, ">=", pos})
				pos += 2
			} else {
				tokens = append(tokens, Token{TokenOperator, ">", pos})
				pos++
			}
		case '&':
			if pos+1 < len(expression) && expression[pos+1] == '&' {
				tokens = append(tokens, Token{TokenLogical, "&&", pos})
				pos += 2
			} else {
				return nil, fmt.Errorf("无效字符 '&'，位置: %d", pos)
			}
		case '|':
			if pos+1 < len(expression) && expression[pos+1] == '|' {
				tokens = append(tokens, Token{TokenLogical, "||", pos})
				pos += 2
			} else {
				return nil, fmt.Errorf("无效字符 '|'，位置: %d", pos)
			}
		case '+', '-', '*', '/', '%':
			tokens = append(tokens, Token{TokenOperator, string(expression[pos]), pos})
			pos++
		default:
			return nil, fmt.Errorf("无效字符 '%c'，位置: %d", expression[pos], pos)
		}
	}

	tokens = append(tokens, Token{TokenEOF, "", pos})
	return tokens, nil
}

// Parser 语法分析器
type Parser struct {
	tokens  []Token
	pos     int
	context map[string]interface{}
	logger  logger.Logger
}

// parseExpression 解析表达式
func (p *Parser) parseExpression() (interface{}, error) {
	return p.parseLogicalOr()
}

// parseLogicalOr 解析逻辑或表达式
func (p *Parser) parseLogicalOr() (interface{}, error) {
	left, err := p.parseLogicalAnd()
	if err != nil {
		return nil, err
	}

	for p.currentToken().Type == TokenLogical && p.currentToken().Value == "||" {
		p.advance()
		right, err := p.parseLogicalAnd()
		if err != nil {
			return nil, err
		}

		leftBool := p.toBool(left)
		rightBool := p.toBool(right)
		left = leftBool || rightBool
	}

	return left, nil
}

// parseLogicalAnd 解析逻辑与表达式
func (p *Parser) parseLogicalAnd() (interface{}, error) {
	left, err := p.parseEquality()
	if err != nil {
		return nil, err
	}

	for p.currentToken().Type == TokenLogical && p.currentToken().Value == "&&" {
		p.advance()
		right, err := p.parseEquality()
		if err != nil {
			return nil, err
		}

		leftBool := p.toBool(left)
		rightBool := p.toBool(right)
		left = leftBool && rightBool
	}

	return left, nil
}

// parseEquality 解析相等性表达式
func (p *Parser) parseEquality() (interface{}, error) {
	left, err := p.parseComparison()
	if err != nil {
		return nil, err
	}

	for p.currentToken().Type == TokenOperator &&
		(p.currentToken().Value == "==" || p.currentToken().Value == "!=") {

		operator := p.currentToken().Value
		p.advance()

		right, err := p.parseComparison()
		if err != nil {
			return nil, err
		}

		switch operator {
		case "==":
			left = p.isEqual(left, right)
		case "!=":
			left = !p.isEqual(left, right)
		}
	}

	return left, nil
}

// parseComparison 解析比较表达式
func (p *Parser) parseComparison() (interface{}, error) {
	left, err := p.parseAddition()
	if err != nil {
		return nil, err
	}

	for p.currentToken().Type == TokenOperator &&
		(p.currentToken().Value == "<" || p.currentToken().Value == "<=" ||
		 p.currentToken().Value == ">" || p.currentToken().Value == ">=") {

		operator := p.currentToken().Value
		p.advance()

		right, err := p.parseAddition()
		if err != nil {
			return nil, err
		}

		result, err := p.compare(left, right, operator)
		if err != nil {
			return nil, err
		}
		left = result
	}

	return left, nil
}

// parseAddition 解析加减表达式
func (p *Parser) parseAddition() (interface{}, error) {
	left, err := p.parseMultiplication()
	if err != nil {
		return nil, err
	}

	for p.currentToken().Type == TokenOperator &&
		(p.currentToken().Value == "+" || p.currentToken().Value == "-") {

		operator := p.currentToken().Value
		p.advance()

		right, err := p.parseMultiplication()
		if err != nil {
			return nil, err
		}

		result, err := p.arithmetic(left, right, operator)
		if err != nil {
			return nil, err
		}
		left = result
	}

	return left, nil
}

// parseMultiplication 解析乘除表达式
func (p *Parser) parseMultiplication() (interface{}, error) {
	left, err := p.parseUnary()
	if err != nil {
		return nil, err
	}

	for p.currentToken().Type == TokenOperator &&
		(p.currentToken().Value == "*" || p.currentToken().Value == "/" || p.currentToken().Value == "%") {

		operator := p.currentToken().Value
		p.advance()

		right, err := p.parseUnary()
		if err != nil {
			return nil, err
		}

		result, err := p.arithmetic(left, right, operator)
		if err != nil {
			return nil, err
		}
		left = result
	}

	return left, nil
}

// parseUnary 解析一元表达式
func (p *Parser) parseUnary() (interface{}, error) {
	if p.currentToken().Type == TokenOperator && p.currentToken().Value == "!" {
		p.advance()
		expr, err := p.parseUnary()
		if err != nil {
			return nil, err
		}
		return !p.toBool(expr), nil
	}

	if p.currentToken().Type == TokenOperator &&
		(p.currentToken().Value == "+" || p.currentToken().Value == "-") {

		operator := p.currentToken().Value
		p.advance()

		expr, err := p.parseUnary()
		if err != nil {
			return nil, err
		}

		if operator == "-" {
			if num, ok := p.toNumber(expr); ok {
				return -num, nil
			}
			return nil, fmt.Errorf("无法对非数字类型应用负号")
		}

		return expr, nil
	}

	return p.parsePrimary()
}

// parsePrimary 解析基本表达式
func (p *Parser) parsePrimary() (interface{}, error) {
	token := p.currentToken()

	switch token.Type {
	case TokenNumber:
		p.advance()
		if strings.Contains(token.Value, ".") {
			return strconv.ParseFloat(token.Value, 64)
		}
		return strconv.ParseInt(token.Value, 10, 64)

	case TokenString:
		p.advance()
		return token.Value, nil

	case TokenIdentifier:
		return p.parseIdentifier()

	case TokenLeftParen:
		p.advance() // 跳过 '('
		expr, err := p.parseExpression()
		if err != nil {
			return nil, err
		}

		if p.currentToken().Type != TokenRightParen {
			return nil, fmt.Errorf("期望 ')'，位置: %d", p.currentToken().Pos)
		}
		p.advance() // 跳过 ')'
		return expr, nil

	default:
		return nil, fmt.Errorf("意外的标记: %s，位置: %d", token.Value, token.Pos)
	}
}

// parseIdentifier 解析标识符（变量或函数调用）
func (p *Parser) parseIdentifier() (interface{}, error) {
	name := p.currentToken().Value
	p.advance()

	// 检查是否是函数调用
	if p.currentToken().Type == TokenLeftParen {
		return p.parseFunctionCall(name)
	}

	// 变量访问
	return p.getVariable(name), nil
}

// parseFunctionCall 解析函数调用
func (p *Parser) parseFunctionCall(funcName string) (interface{}, error) {
	p.advance() // 跳过 '('

	var args []interface{}

	// 解析参数
	if p.currentToken().Type != TokenRightParen {
		for {
			arg, err := p.parseExpression()
			if err != nil {
				return nil, err
			}
			args = append(args, arg)

			if p.currentToken().Type == TokenComma {
				p.advance()
			} else {
				break
			}
		}
	}

	if p.currentToken().Type != TokenRightParen {
		return nil, fmt.Errorf("期望 ')'，位置: %d", p.currentToken().Pos)
	}
	p.advance() // 跳过 ')'

	// 调用内置函数
	return p.callFunction(funcName, args)
}

// getVariable 获取变量值
func (p *Parser) getVariable(name string) interface{} {
	// 支持点号访问嵌套属性
	parts := strings.Split(name, ".")

	var current interface{} = p.context
	for _, part := range parts {
		if m, ok := current.(map[string]interface{}); ok {
			if value, exists := m[part]; exists {
				current = value
			} else {
				return nil
			}
		} else {
			return nil
		}
	}

	return current
}

// callFunction 调用内置函数
func (p *Parser) callFunction(name string, args []interface{}) (interface{}, error) {
	switch name {
	case "len":
		if len(args) != 1 {
			return nil, fmt.Errorf("len() 需要1个参数，得到 %d 个", len(args))
		}
		return p.getLength(args[0])

	case "contains":
		if len(args) != 2 {
			return nil, fmt.Errorf("contains() 需要2个参数，得到 %d 个", len(args))
		}
		return p.contains(args[0], args[1]), nil

	case "startsWith":
		if len(args) != 2 {
			return nil, fmt.Errorf("startsWith() 需要2个参数，得到 %d 个", len(args))
		}
		return p.startsWith(args[0], args[1]), nil

	case "endsWith":
		if len(args) != 2 {
			return nil, fmt.Errorf("endsWith() 需要2个参数，得到 %d 个", len(args))
		}
		return p.endsWith(args[0], args[1]), nil

	case "matches":
		if len(args) != 2 {
			return nil, fmt.Errorf("matches() 需要2个参数，得到 %d 个", len(args))
		}
		return p.matches(args[0], args[1])

	case "toUpper":
		if len(args) != 1 {
			return nil, fmt.Errorf("toUpper() 需要1个参数，得到 %d 个", len(args))
		}
		if str, ok := args[0].(string); ok {
			return strings.ToUpper(str), nil
		}
		return fmt.Sprintf("%v", args[0]), nil

	case "toLower":
		if len(args) != 1 {
			return nil, fmt.Errorf("toLower() 需要1个参数，得到 %d 个", len(args))
		}
		if str, ok := args[0].(string); ok {
			return strings.ToLower(str), nil
		}
		return fmt.Sprintf("%v", args[0]), nil

	default:
		return nil, fmt.Errorf("未知函数: %s", name)
	}
}

// 辅助方法
func (p *Parser) currentToken() Token {
	if p.pos >= len(p.tokens) {
		return Token{TokenEOF, "", -1}
	}
	return p.tokens[p.pos]
}

func (p *Parser) advance() {
	if p.pos < len(p.tokens) {
		p.pos++
	}
}

func (p *Parser) toBool(value interface{}) bool {
	if value == nil {
		return false
	}

	switch v := value.(type) {
	case bool:
		return v
	case int64:
		return v != 0
	case float64:
		return v != 0.0
	case string:
		return v != "" && v != "false" && v != "0"
	default:
		return true
	}
}

func (p *Parser) toNumber(value interface{}) (float64, bool) {
	switch v := value.(type) {
	case int64:
		return float64(v), true
	case float64:
		return v, true
	case string:
		if num, err := strconv.ParseFloat(v, 64); err == nil {
			return num, true
		}
	}
	return 0, false
}

func (p *Parser) isEqual(left, right interface{}) bool {
	if left == nil && right == nil {
		return true
	}
	if left == nil || right == nil {
		return false
	}

	// 尝试数字比较
	if leftNum, leftOk := p.toNumber(left); leftOk {
		if rightNum, rightOk := p.toNumber(right); rightOk {
			return leftNum == rightNum
		}
	}

	// 字符串比较
	return fmt.Sprintf("%v", left) == fmt.Sprintf("%v", right)
}

func (p *Parser) compare(left, right interface{}, operator string) (bool, error) {
	leftNum, leftOk := p.toNumber(left)
	rightNum, rightOk := p.toNumber(right)

	if !leftOk || !rightOk {
		return false, fmt.Errorf("无法比较非数字类型")
	}

	switch operator {
	case "<":
		return leftNum < rightNum, nil
	case "<=":
		return leftNum <= rightNum, nil
	case ">":
		return leftNum > rightNum, nil
	case ">=":
		return leftNum >= rightNum, nil
	default:
		return false, fmt.Errorf("未知比较运算符: %s", operator)
	}
}

func (p *Parser) arithmetic(left, right interface{}, operator string) (interface{}, error) {
	// 字符串连接
	if operator == "+" {
		if leftStr, leftOk := left.(string); leftOk {
			return leftStr + fmt.Sprintf("%v", right), nil
		}
		if rightStr, rightOk := right.(string); rightOk {
			return fmt.Sprintf("%v", left) + rightStr, nil
		}
	}

	// 数字运算
	leftNum, leftOk := p.toNumber(left)
	rightNum, rightOk := p.toNumber(right)

	if !leftOk || !rightOk {
		return nil, fmt.Errorf("无法对非数字类型进行算术运算")
	}

	switch operator {
	case "+":
		return leftNum + rightNum, nil
	case "-":
		return leftNum - rightNum, nil
	case "*":
		return leftNum * rightNum, nil
	case "/":
		if rightNum == 0 {
			return nil, fmt.Errorf("除零错误")
		}
		return leftNum / rightNum, nil
	case "%":
		if rightNum == 0 {
			return nil, fmt.Errorf("除零错误")
		}
		return math.Mod(leftNum, rightNum), nil
	default:
		return nil, fmt.Errorf("未知算术运算符: %s", operator)
	}
}

func (p *Parser) getLength(value interface{}) (int64, error) {
	switch v := value.(type) {
	case string:
		return int64(len(v)), nil
	case []interface{}:
		return int64(len(v)), nil
	case map[string]interface{}:
		return int64(len(v)), nil
	default:
		return 0, fmt.Errorf("无法获取类型 %T 的长度", value)
	}
}

func (p *Parser) contains(container, item interface{}) bool {
	switch c := container.(type) {
	case string:
		return strings.Contains(c, fmt.Sprintf("%v", item))
	case []interface{}:
		for _, v := range c {
			if p.isEqual(v, item) {
				return true
			}
		}
	case map[string]interface{}:
		key := fmt.Sprintf("%v", item)
		_, exists := c[key]
		return exists
	}
	return false
}

func (p *Parser) startsWith(str, prefix interface{}) bool {
	strVal := fmt.Sprintf("%v", str)
	prefixVal := fmt.Sprintf("%v", prefix)
	return strings.HasPrefix(strVal, prefixVal)
}

func (p *Parser) endsWith(str, suffix interface{}) bool {
	strVal := fmt.Sprintf("%v", str)
	suffixVal := fmt.Sprintf("%v", suffix)
	return strings.HasSuffix(strVal, suffixVal)
}

func (p *Parser) matches(str, pattern interface{}) (bool, error) {
	strVal := fmt.Sprintf("%v", str)
	patternVal := fmt.Sprintf("%v", pattern)

	matched, err := regexp.MatchString(patternVal, strVal)
	if err != nil {
		return false, fmt.Errorf("正则表达式错误: %v", err)
	}
	return matched, nil
}

// 字符检查函数
func isWhitespace(ch byte) bool {
	return ch == ' ' || ch == '\t' || ch == '\n' || ch == '\r'
}

func isDigit(ch byte) bool {
	return ch >= '0' && ch <= '9'
}

func isLetter(ch byte) bool {
	return (ch >= 'a' && ch <= 'z') || (ch >= 'A' && ch <= 'Z')
}

func isAlphaNumeric(ch byte) bool {
	return isLetter(ch) || isDigit(ch)
}
