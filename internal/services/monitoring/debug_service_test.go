package monitoring

import (
	"testing"

	"flexproxy/common/logger"
)

// createTestDebugService 创建测试用的调试服务
func createTestDebugService() *debugService {
	service := &debugService{
		logger: logger.NewSimpleLogger("test-debug"),
	}
	return service
}

// TestLexicalAnalysis 测试词法分析
func TestLexicalAnalysis(t *testing.T) {
	
	tests := []struct {
		name       string
		expression string
		expected   []Token
		description string
	}{
		{
			name:       "简单数字",
			expression: "123",
			expected: []Token{
				{Type: TokenNumber, Value: "123"},
				{Type: TokenEOF, Value: ""},
			},
			description: "单个数字应该被正确识别",
		},
		{
			name:       "简单标识符",
			expression: "variable",
			expected: []Token{
				{Type: TokenIdentifier, Value: "variable"},
				{Type: TokenEOF, Value: ""},
			},
			description: "标识符应该被正确识别",
		},
		{
			name:       "算术表达式",
			expression: "a + b * 2",
			expected: []Token{
				{Type: TokenIdentifier, Value: "a"},
				{Type: TokenOperator, Value: "+"},
				{Type: TokenIdentifier, Value: "b"},
				{Type: TokenOperator, Value: "*"},
				{Type: TokenNumber, Value: "2"},
				{Type: TokenEOF, Value: ""},
			},
			description: "算术表达式应该被正确分词",
		},
		{
			name:       "函数调用",
			expression: "len(array)",
			expected: []Token{
				{Type: TokenIdentifier, Value: "len"},
				{Type: TokenLeftParen, Value: "("},
				{Type: TokenIdentifier, Value: "array"},
				{Type: TokenRightParen, Value: ")"},
				{Type: TokenEOF, Value: ""},
			},
			description: "函数调用应该被正确分词",
		},
		{
			name:       "字符串字面量",
			expression: `"hello world"`,
			expected: []Token{
				{Type: TokenString, Value: "hello world"},
				{Type: TokenEOF, Value: ""},
			},
			description: "字符串字面量应该被正确识别",
		},
		{
			name:       "比较操作",
			expression: "x >= 10",
			expected: []Token{
				{Type: TokenIdentifier, Value: "x"},
				{Type: TokenOperator, Value: ">="},
				{Type: TokenNumber, Value: "10"},
				{Type: TokenEOF, Value: ""},
			},
			description: "比较操作符应该被正确识别",
		},
		{
			name:       "逻辑表达式",
			expression: "a && b || c",
			expected: []Token{
				{Type: TokenIdentifier, Value: "a"},
				{Type: TokenLogical, Value: "&&"},
				{Type: TokenIdentifier, Value: "b"},
				{Type: TokenLogical, Value: "||"},
				{Type: TokenIdentifier, Value: "c"},
				{Type: TokenEOF, Value: ""},
			},
			description: "逻辑操作符应该被正确识别",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建表达式解析器
			parser := &ExpressionParser{}
			tokens, err := parser.tokenize(tt.expression)

			if err != nil {
				t.Errorf("词法分析失败: 表达式='%s', 错误=%v", tt.expression, err)
				return
			}

			if len(tokens) != len(tt.expected) {
				t.Errorf("词法分析结果数量不正确: 表达式='%s', 期望=%d, 实际=%d",
					tt.expression, len(tt.expected), len(tokens))
				t.Logf("期望: %+v", tt.expected)
				t.Logf("实际: %+v", tokens)
				return
			}

			for i, expected := range tt.expected {
				if i >= len(tokens) {
					t.Errorf("词法分析结果不足: 位置=%d", i)
					break
				}

				if tokens[i].Type != expected.Type || tokens[i].Value != expected.Value {
					t.Errorf("词法分析结果不正确: 位置=%d, 期望=%+v, 实际=%+v",
						i, expected, tokens[i])
				}
			}

			t.Logf("测试 %s: '%s' -> %+v (%s)", tt.name, tt.expression, tokens, tt.description)
		})
	}
}

// TestSyntaxAnalysis 测试语法分析
func TestSyntaxAnalysis(t *testing.T) {
	
	tests := []struct {
		name        string
		expression  string
		shouldParse bool
		description string
	}{
		{
			name:        "简单表达式",
			expression:  "1 + 2",
			shouldParse: true,
			description: "简单算术表达式应该能解析",
		},
		{
			name:        "复杂表达式",
			expression:  "a * (b + c) / d",
			shouldParse: true,
			description: "带括号的复杂表达式应该能解析",
		},
		{
			name:        "函数调用",
			expression:  "max(a, b)",
			shouldParse: true,
			description: "函数调用应该能解析",
		},
		{
			name:        "嵌套函数调用",
			expression:  "len(substr(text, 0, 10))",
			shouldParse: true,
			description: "嵌套函数调用应该能解析",
		},
		{
			name:        "比较表达式",
			expression:  "x > 0 && y < 100",
			shouldParse: true,
			description: "比较和逻辑表达式应该能解析",
		},
		{
			name:        "语法错误-不匹配括号",
			expression:  "a + (b * c",
			shouldParse: false,
			description: "不匹配的括号应该导致解析失败",
		},
		{
			name:        "语法错误-无效操作符",
			expression:  "a ++ b",
			shouldParse: false,
			description: "无效的操作符应该导致解析失败",
		},
		{
			name:        "语法错误-空表达式",
			expression:  "",
			shouldParse: false,
			description: "空表达式应该导致解析失败",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建解析器
			parser := &ExpressionParser{}
			tokens, err := parser.tokenize(tt.expression)
			if err != nil && tt.shouldParse {
				t.Errorf("词法分析失败: 表达式='%s', 错误=%v", tt.expression, err)
				return
			}

			// 创建语法解析器
			syntaxParser := &Parser{
				tokens: tokens,
				pos:    0,
				context: make(map[string]interface{}),
				logger: logger.NewSimpleLogger("test-parser"),
			}

			ast, err := syntaxParser.parseExpression()

			if tt.shouldParse {
				if err != nil {
					t.Errorf("语法分析应该成功: 表达式='%s', 错误=%v (%s)",
						tt.expression, err, tt.description)
					return
				}
				if ast == nil {
					t.Errorf("语法分析应该返回AST: 表达式='%s' (%s)",
						tt.expression, tt.description)
					return
				}
			} else {
				if err == nil {
					t.Errorf("语法分析应该失败: 表达式='%s' (%s)",
						tt.expression, tt.description)
					return
				}
			}

			t.Logf("测试 %s: '%s' -> 成功=%t (%s)",
				tt.name, tt.expression, tt.shouldParse, tt.description)
		})
	}
}

// TestExpressionEvaluation 测试表达式求值（条件评估）
func TestExpressionEvaluation(t *testing.T) {
	service := createTestDebugService()

	// 设置测试变量
	variables := map[string]interface{}{
		"x":      10,
		"y":      20,
		"name":   "test",
		"active": true,
		"items":  []interface{}{1, 2, 3, 4, 5},
	}

	tests := []struct {
		name        string
		expression  string
		expected    bool
		description string
	}{
		{
			name:        "字符串比较",
			expression:  `name == "test"`,
			expected:    true,
			description: "字符串比较应该正确",
		},
		{
			name:        "布尔值测试",
			expression:  "active",
			expected:    true,
			description: "布尔变量应该正确评估",
		},
		{
			name:        "函数调用-len",
			expression:  "len(items) > 0",
			expected:    true,
			description: "len函数应该正确工作",
		},
		{
			name:        "函数调用-contains",
			expression:  `contains(name, "test")`,
			expected:    true,
			description: "contains函数应该正确工作",
		},
		{
			name:        "复杂条件",
			expression:  `active && name == "test"`,
			expected:    true,
			description: "复杂条件表达式应该正确计算",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.evaluateCondition(tt.expression, variables)

			if result != tt.expected {
				t.Errorf("表达式求值结果不正确: 表达式='%s', 期望=%v, 实际=%v (%s)",
					tt.expression, tt.expected, result, tt.description)
				return
			}

			t.Logf("测试 %s: '%s' -> %v (%s)",
				tt.name, tt.expression, result, tt.description)
		})
	}
}

// TestBuiltinFunctions 测试内置函数（条件评估中的函数）
func TestBuiltinFunctions(t *testing.T) {
	service := createTestDebugService()

	variables := map[string]interface{}{
		"text":   "hello world",
		"number": 42,
		"array":  []interface{}{1, 2, 3, 4, 5},
		"obj":    map[string]interface{}{"key": "value"},
	}

	tests := []struct {
		name        string
		expression  string
		expected    bool
		description string
	}{
		{
			name:        "len函数-数组长度检查",
			expression:  "len(array) == 5",
			expected:    true,
			description: "len函数应该正确返回数组长度",
		},
		{
			name:        "len函数-字符串长度检查",
			expression:  "len(text) > 10",
			expected:    true,
			description: "len函数应该正确返回字符串长度",
		},
		{
			name:        "contains函数",
			expression:  `contains(text, "world")`,
			expected:    true,
			description: "contains函数应该检查字符串包含",
		},
		{
			name:        "contains函数-不包含",
			expression:  `contains(text, "xyz")`,
			expected:    false,
			description: "contains函数应该正确识别不包含的情况",
		},
		{
			name:        "组合函数调用",
			expression:  `len(text) > 5 && contains(text, "hello")`,
			expected:    true,
			description: "组合函数调用应该正确工作",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.evaluateCondition(tt.expression, variables)

			if result != tt.expected {
				t.Errorf("内置函数结果不正确: 表达式='%s', 期望=%v, 实际=%v (%s)",
					tt.expression, tt.expected, result, tt.description)
				return
			}

			t.Logf("测试 %s: '%s' -> %v (%s)",
				tt.name, tt.expression, result, tt.description)
		})
	}
}
