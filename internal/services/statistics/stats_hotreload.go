package statistics

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"flexproxy/common/logger"
	"flexproxy/internal/config"
)

// StatisticsHotReloadManager 统计数据热重载管理器
type StatisticsHotReloadManager struct {
	mu                sync.RWMutex
	logger            *logger.LoggerAdapter
	preservationRules *StatisticsPreservationRules
	migrationHistory  []StatsMigrationRecord
	
	// 统计数据存储
	requestStats      *RequestStatistics
	proxyStats        *ProxyStatistics
	performanceStats  *PerformanceStatistics
	errorStats        *ErrorStatistics
}

// StatisticsPreservationRules 统计数据保留规则
type StatisticsPreservationRules struct {
	PreserveRequestStats     bool          `yaml:"preserve_request_stats" json:"preserve_request_stats"`
	PreserveProxyStats       bool          `yaml:"preserve_proxy_stats" json:"preserve_proxy_stats"`
	PreservePerformanceStats bool          `yaml:"preserve_performance_stats" json:"preserve_performance_stats"`
	PreserveErrorStats       bool          `yaml:"preserve_error_stats" json:"preserve_error_stats"`
	RetentionPeriod          time.Duration `yaml:"retention_period" json:"retention_period"`
	MaxHistoryEntries        int           `yaml:"max_history_entries" json:"max_history_entries"`
	CompressOldData          bool          `yaml:"compress_old_data" json:"compress_old_data"`
}

// RequestStatistics 请求统计数据
type RequestStatistics struct {
	TotalRequests     int64                    `json:"total_requests"`
	SuccessRequests   int64                    `json:"success_requests"`
	FailedRequests    int64                    `json:"failed_requests"`
	RequestsByHour    map[string]int64         `json:"requests_by_hour"`
	RequestsByDomain  map[string]int64         `json:"requests_by_domain"`
	RequestsByMethod  map[string]int64         `json:"requests_by_method"`
	AvgResponseTime   time.Duration            `json:"avg_response_time"`
	LastUpdated       time.Time                `json:"last_updated"`
}

// ProxyStatistics 代理统计数据
type ProxyStatistics struct {
	ActiveProxies     int                      `json:"active_proxies"`
	TotalProxies      int                      `json:"total_proxies"`
	ProxyUsageCount   map[string]int64         `json:"proxy_usage_count"`
	ProxySuccessRate  map[string]float64       `json:"proxy_success_rate"`
	ProxyAvgTime      map[string]time.Duration `json:"proxy_avg_time"`
	BlacklistedCount  int                      `json:"blacklisted_count"`
	LastUpdated       time.Time                `json:"last_updated"`
}

// PerformanceStatistics 性能统计数据
type PerformanceStatistics struct {
	MemoryUsage       int64                    `json:"memory_usage"`
	CPUUsage          float64                  `json:"cpu_usage"`
	GoroutineCount    int                      `json:"goroutine_count"`
	ConnectionCount   int                      `json:"connection_count"`
	CacheHitRate      float64                  `json:"cache_hit_rate"`
	ThroughputPerSec  int64                    `json:"throughput_per_sec"`
	HistoricalData    []PerformanceSnapshot    `json:"historical_data"`
	LastUpdated       time.Time                `json:"last_updated"`
}

// ErrorStatistics 错误统计数据
type ErrorStatistics struct {
	TotalErrors       int64                    `json:"total_errors"`
	ErrorsByType      map[string]int64         `json:"errors_by_type"`
	ErrorsByHour      map[string]int64         `json:"errors_by_hour"`
	CriticalErrors    int64                    `json:"critical_errors"`
	RecentErrors      []ErrorRecord            `json:"recent_errors"`
	LastUpdated       time.Time                `json:"last_updated"`
}

// PerformanceSnapshot 性能快照
type PerformanceSnapshot struct {
	Timestamp       time.Time `json:"timestamp"`
	MemoryUsage     int64     `json:"memory_usage"`
	CPUUsage        float64   `json:"cpu_usage"`
	ThroughputPerSec int64    `json:"throughput_per_sec"`
}

// ErrorRecord 错误记录
type ErrorRecord struct {
	Timestamp   time.Time `json:"timestamp"`
	ErrorType   string    `json:"error_type"`
	Message     string    `json:"message"`
	Severity    string    `json:"severity"`
	ProxyURL    string    `json:"proxy_url,omitempty"`
}

// StatsMigrationRecord 统计数据迁移记录
type StatsMigrationRecord struct {
	Timestamp         time.Time `json:"timestamp"`
	FromVersion       string    `json:"from_version"`
	ToVersion         string    `json:"to_version"`
	MigratedDataTypes []string  `json:"migrated_data_types"`
	Success           bool      `json:"success"`
	ErrorMessage      string    `json:"error_message,omitempty"`
	Duration          time.Duration `json:"duration"`
}

// StatisticsSnapshot 统计数据快照
type StatisticsSnapshot struct {
	Timestamp        time.Time              `json:"timestamp"`
	Version          string                 `json:"version"`
	RequestStats     *RequestStatistics     `json:"request_stats"`
	ProxyStats       *ProxyStatistics       `json:"proxy_stats"`
	PerformanceStats *PerformanceStatistics `json:"performance_stats"`
	ErrorStats       *ErrorStatistics       `json:"error_stats"`
}

// NewStatisticsHotReloadManager 创建统计数据热重载管理器
func NewStatisticsHotReloadManager() *StatisticsHotReloadManager {
	return &StatisticsHotReloadManager{
		logger:           logger.GetLoggerAdapter(logger.ModuleStatistics),
		migrationHistory: make([]StatsMigrationRecord, 0),
		preservationRules: &StatisticsPreservationRules{
			PreserveRequestStats:     true,
			PreserveProxyStats:       true,
			PreservePerformanceStats: true,
			PreserveErrorStats:       true,
			RetentionPeriod:          24 * time.Hour,
			MaxHistoryEntries:        1000,
			CompressOldData:          true,
		},
		requestStats: &RequestStatistics{
			RequestsByHour:   make(map[string]int64),
			RequestsByDomain: make(map[string]int64),
			RequestsByMethod: make(map[string]int64),
		},
		proxyStats: &ProxyStatistics{
			ProxyUsageCount:  make(map[string]int64),
			ProxySuccessRate: make(map[string]float64),
			ProxyAvgTime:     make(map[string]time.Duration),
		},
		performanceStats: &PerformanceStatistics{
			HistoricalData: make([]PerformanceSnapshot, 0),
		},
		errorStats: &ErrorStatistics{
			ErrorsByType: make(map[string]int64),
			ErrorsByHour: make(map[string]int64),
			RecentErrors: make([]ErrorRecord, 0),
		},
	}
}

// UpdateConfig 更新统计配置并执行数据迁移
func (shrm *StatisticsHotReloadManager) UpdateConfig(oldConfig, newConfig *config.StatisticsConfig) error {
	shrm.mu.Lock()
	defer shrm.mu.Unlock()

	shrm.logger.Info("开始统计数据热重载和迁移")
	startTime := time.Now()

	// 1. 创建当前统计数据快照
	snapshot, err := shrm.createStatisticsSnapshot("before_migration")
	if err != nil {
		shrm.logger.Error(fmt.Sprintf("创建统计数据快照失败: %v", err))
		return fmt.Errorf("创建统计数据快照失败: %v", err)
	}

	// 2. 分析配置变更
	migrationPlan := shrm.analyzeStatisticsConfigChanges(oldConfig, newConfig)
	shrm.logger.Info(fmt.Sprintf("统计数据迁移计划: %s", migrationPlan.Description))

	// 3. 执行数据迁移
	migratedTypes, err := shrm.executeStatisticsMigration(migrationPlan, snapshot)
	if err != nil {
		shrm.logger.Error(fmt.Sprintf("统计数据迁移失败: %v", err))
		return fmt.Errorf("统计数据迁移失败: %v", err)
	}

	// 4. 清理过期数据
	if err := shrm.cleanupExpiredStatistics(); err != nil {
		shrm.logger.Warn(fmt.Sprintf("清理过期统计数据失败: %v", err))
	}

	// 5. 记录迁移历史
	migrationRecord := StatsMigrationRecord{
		Timestamp:         startTime,
		FromVersion:       shrm.getConfigVersion(oldConfig),
		ToVersion:         shrm.getConfigVersion(newConfig),
		MigratedDataTypes: migratedTypes,
		Success:           true,
		Duration:          time.Since(startTime),
	}
	shrm.migrationHistory = append(shrm.migrationHistory, migrationRecord)

	// 限制迁移历史记录数量
	if len(shrm.migrationHistory) > shrm.preservationRules.MaxHistoryEntries {
		shrm.migrationHistory = shrm.migrationHistory[len(shrm.migrationHistory)-shrm.preservationRules.MaxHistoryEntries:]
	}

	shrm.logger.Info(fmt.Sprintf("统计数据热重载完成，迁移了 %d 种数据类型，耗时: %v", 
		len(migratedTypes), migrationRecord.Duration))

	return nil
}

// StatisticsMigrationPlan 统计数据迁移计划
type StatisticsMigrationPlan struct {
	Description           string
	PreserveRequestStats  bool
	PreserveProxyStats    bool
	PreservePerfStats     bool
	PreserveErrorStats    bool
	CompressHistorical    bool
	CleanupExpired        bool
}

// analyzeStatisticsConfigChanges 分析统计配置变更
func (shrm *StatisticsHotReloadManager) analyzeStatisticsConfigChanges(oldConfig, newConfig *config.StatisticsConfig) *StatisticsMigrationPlan {
	plan := &StatisticsMigrationPlan{
		PreserveRequestStats: shrm.preservationRules.PreserveRequestStats,
		PreserveProxyStats:   shrm.preservationRules.PreserveProxyStats,
		PreservePerfStats:    shrm.preservationRules.PreservePerformanceStats,
		PreserveErrorStats:   shrm.preservationRules.PreserveErrorStats,
		CompressHistorical:   shrm.preservationRules.CompressOldData,
		CleanupExpired:       true,
	}

	if oldConfig != nil && newConfig != nil {
		plan.Description = "统计配置已更新，保留现有数据"
	} else {
		plan.Description = "初始化统计配置"
	}

	return plan
}

// executeStatisticsMigration 执行统计数据迁移
func (shrm *StatisticsHotReloadManager) executeStatisticsMigration(plan *StatisticsMigrationPlan, snapshot *StatisticsSnapshot) ([]string, error) {
	var migratedTypes []string

	// 1. 迁移请求统计
	if plan.PreserveRequestStats && snapshot.RequestStats != nil {
		shrm.migrateRequestStatistics(snapshot.RequestStats)
		migratedTypes = append(migratedTypes, "request_stats")
		shrm.logger.Debug("已迁移请求统计数据")
	}

	// 2. 迁移代理统计
	if plan.PreserveProxyStats && snapshot.ProxyStats != nil {
		shrm.migrateProxyStatistics(snapshot.ProxyStats)
		migratedTypes = append(migratedTypes, "proxy_stats")
		shrm.logger.Debug("已迁移代理统计数据")
	}

	// 3. 迁移性能统计
	if plan.PreservePerfStats && snapshot.PerformanceStats != nil {
		shrm.migratePerformanceStatistics(snapshot.PerformanceStats, plan.CompressHistorical)
		migratedTypes = append(migratedTypes, "performance_stats")
		shrm.logger.Debug("已迁移性能统计数据")
	}

	// 4. 迁移错误统计
	if plan.PreserveErrorStats && snapshot.ErrorStats != nil {
		shrm.migrateErrorStatistics(snapshot.ErrorStats)
		migratedTypes = append(migratedTypes, "error_stats")
		shrm.logger.Debug("已迁移错误统计数据")
	}

	return migratedTypes, nil
}

// createStatisticsSnapshot 创建统计数据快照
func (shrm *StatisticsHotReloadManager) createStatisticsSnapshot(version string) (*StatisticsSnapshot, error) {
	snapshot := &StatisticsSnapshot{
		Timestamp: time.Now(),
		Version:   version,
	}

	// 深拷贝请求统计
	if shrm.requestStats != nil {
		requestStatsCopy := *shrm.requestStats
		requestStatsCopy.RequestsByHour = make(map[string]int64)
		requestStatsCopy.RequestsByDomain = make(map[string]int64)
		requestStatsCopy.RequestsByMethod = make(map[string]int64)
		
		for k, v := range shrm.requestStats.RequestsByHour {
			requestStatsCopy.RequestsByHour[k] = v
		}
		for k, v := range shrm.requestStats.RequestsByDomain {
			requestStatsCopy.RequestsByDomain[k] = v
		}
		for k, v := range shrm.requestStats.RequestsByMethod {
			requestStatsCopy.RequestsByMethod[k] = v
		}
		
		snapshot.RequestStats = &requestStatsCopy
	}

	// 深拷贝代理统计
	if shrm.proxyStats != nil {
		proxyStatsCopy := *shrm.proxyStats
		proxyStatsCopy.ProxyUsageCount = make(map[string]int64)
		proxyStatsCopy.ProxySuccessRate = make(map[string]float64)
		proxyStatsCopy.ProxyAvgTime = make(map[string]time.Duration)
		
		for k, v := range shrm.proxyStats.ProxyUsageCount {
			proxyStatsCopy.ProxyUsageCount[k] = v
		}
		for k, v := range shrm.proxyStats.ProxySuccessRate {
			proxyStatsCopy.ProxySuccessRate[k] = v
		}
		for k, v := range shrm.proxyStats.ProxyAvgTime {
			proxyStatsCopy.ProxyAvgTime[k] = v
		}
		
		snapshot.ProxyStats = &proxyStatsCopy
	}

	// 深拷贝性能统计
	if shrm.performanceStats != nil {
		perfStatsCopy := *shrm.performanceStats
		perfStatsCopy.HistoricalData = make([]PerformanceSnapshot, len(shrm.performanceStats.HistoricalData))
		copy(perfStatsCopy.HistoricalData, shrm.performanceStats.HistoricalData)
		
		snapshot.PerformanceStats = &perfStatsCopy
	}

	// 深拷贝错误统计
	if shrm.errorStats != nil {
		errorStatsCopy := *shrm.errorStats
		errorStatsCopy.ErrorsByType = make(map[string]int64)
		errorStatsCopy.ErrorsByHour = make(map[string]int64)
		errorStatsCopy.RecentErrors = make([]ErrorRecord, len(shrm.errorStats.RecentErrors))
		
		for k, v := range shrm.errorStats.ErrorsByType {
			errorStatsCopy.ErrorsByType[k] = v
		}
		for k, v := range shrm.errorStats.ErrorsByHour {
			errorStatsCopy.ErrorsByHour[k] = v
		}
		copy(errorStatsCopy.RecentErrors, shrm.errorStats.RecentErrors)
		
		snapshot.ErrorStats = &errorStatsCopy
	}

	shrm.logger.Debug("统计数据快照创建完成")
	return snapshot, nil
}

// migrateRequestStatistics 迁移请求统计数据
func (shrm *StatisticsHotReloadManager) migrateRequestStatistics(stats *RequestStatistics) {
	shrm.requestStats.TotalRequests = stats.TotalRequests
	shrm.requestStats.SuccessRequests = stats.SuccessRequests
	shrm.requestStats.FailedRequests = stats.FailedRequests
	shrm.requestStats.AvgResponseTime = stats.AvgResponseTime
	
	// 合并映射数据
	for k, v := range stats.RequestsByHour {
		shrm.requestStats.RequestsByHour[k] = v
	}
	for k, v := range stats.RequestsByDomain {
		shrm.requestStats.RequestsByDomain[k] = v
	}
	for k, v := range stats.RequestsByMethod {
		shrm.requestStats.RequestsByMethod[k] = v
	}
	
	shrm.requestStats.LastUpdated = time.Now()
}

// migrateProxyStatistics 迁移代理统计数据
func (shrm *StatisticsHotReloadManager) migrateProxyStatistics(stats *ProxyStatistics) {
	shrm.proxyStats.ActiveProxies = stats.ActiveProxies
	shrm.proxyStats.TotalProxies = stats.TotalProxies
	shrm.proxyStats.BlacklistedCount = stats.BlacklistedCount
	
	// 合并映射数据
	for k, v := range stats.ProxyUsageCount {
		shrm.proxyStats.ProxyUsageCount[k] = v
	}
	for k, v := range stats.ProxySuccessRate {
		shrm.proxyStats.ProxySuccessRate[k] = v
	}
	for k, v := range stats.ProxyAvgTime {
		shrm.proxyStats.ProxyAvgTime[k] = v
	}
	
	shrm.proxyStats.LastUpdated = time.Now()
}

// migratePerformanceStatistics 迁移性能统计数据
func (shrm *StatisticsHotReloadManager) migratePerformanceStatistics(stats *PerformanceStatistics, compress bool) {
	shrm.performanceStats.MemoryUsage = stats.MemoryUsage
	shrm.performanceStats.CPUUsage = stats.CPUUsage
	shrm.performanceStats.GoroutineCount = stats.GoroutineCount
	shrm.performanceStats.ConnectionCount = stats.ConnectionCount
	shrm.performanceStats.CacheHitRate = stats.CacheHitRate
	shrm.performanceStats.ThroughputPerSec = stats.ThroughputPerSec
	
	// 迁移历史数据
	if compress {
		// 压缩历史数据：只保留最近的数据点
		maxHistoryPoints := 100
		if len(stats.HistoricalData) > maxHistoryPoints {
			shrm.performanceStats.HistoricalData = stats.HistoricalData[len(stats.HistoricalData)-maxHistoryPoints:]
		} else {
			shrm.performanceStats.HistoricalData = stats.HistoricalData
		}
	} else {
		shrm.performanceStats.HistoricalData = stats.HistoricalData
	}
	
	shrm.performanceStats.LastUpdated = time.Now()
}

// migrateErrorStatistics 迁移错误统计数据
func (shrm *StatisticsHotReloadManager) migrateErrorStatistics(stats *ErrorStatistics) {
	shrm.errorStats.TotalErrors = stats.TotalErrors
	shrm.errorStats.CriticalErrors = stats.CriticalErrors
	
	// 合并映射数据
	for k, v := range stats.ErrorsByType {
		shrm.errorStats.ErrorsByType[k] = v
	}
	for k, v := range stats.ErrorsByHour {
		shrm.errorStats.ErrorsByHour[k] = v
	}
	
	// 迁移最近错误记录（限制数量）
	maxRecentErrors := 50
	if len(stats.RecentErrors) > maxRecentErrors {
		shrm.errorStats.RecentErrors = stats.RecentErrors[len(stats.RecentErrors)-maxRecentErrors:]
	} else {
		shrm.errorStats.RecentErrors = stats.RecentErrors
	}
	
	shrm.errorStats.LastUpdated = time.Now()
}

// cleanupExpiredStatistics 清理过期统计数据
func (shrm *StatisticsHotReloadManager) cleanupExpiredStatistics() error {
	cutoffTime := time.Now().Add(-shrm.preservationRules.RetentionPeriod)
	cleanedCount := 0

	// 清理过期的小时统计数据
	for hour := range shrm.requestStats.RequestsByHour {
		if hourTime, err := time.Parse("2006-01-02-15", hour); err == nil {
			if hourTime.Before(cutoffTime) {
				delete(shrm.requestStats.RequestsByHour, hour)
				cleanedCount++
			}
		}
	}

	// 清理过期的错误小时统计数据
	for hour := range shrm.errorStats.ErrorsByHour {
		if hourTime, err := time.Parse("2006-01-02-15", hour); err == nil {
			if hourTime.Before(cutoffTime) {
				delete(shrm.errorStats.ErrorsByHour, hour)
				cleanedCount++
			}
		}
	}

	// 清理过期的性能历史数据
	var validHistoricalData []PerformanceSnapshot
	for _, snapshot := range shrm.performanceStats.HistoricalData {
		if snapshot.Timestamp.After(cutoffTime) {
			validHistoricalData = append(validHistoricalData, snapshot)
		} else {
			cleanedCount++
		}
	}
	shrm.performanceStats.HistoricalData = validHistoricalData

	// 清理过期的错误记录
	var validErrorRecords []ErrorRecord
	for _, errorRecord := range shrm.errorStats.RecentErrors {
		if errorRecord.Timestamp.After(cutoffTime) {
			validErrorRecords = append(validErrorRecords, errorRecord)
		} else {
			cleanedCount++
		}
	}
	shrm.errorStats.RecentErrors = validErrorRecords

	if cleanedCount > 0 {
		shrm.logger.Info(fmt.Sprintf("清理了 %d 个过期的统计数据条目", cleanedCount))
	}

	return nil
}

// getConfigVersion 获取配置版本标识
func (shrm *StatisticsHotReloadManager) getConfigVersion(config *config.StatisticsConfig) string {
	if config == nil {
		return "unknown"
	}
	
	configBytes, _ := json.Marshal(config)
	return fmt.Sprintf("stats_%x", configBytes[:8]) // 使用配置的前8字节作为版本标识
}

// GetMigrationHistory 获取迁移历史
func (shrm *StatisticsHotReloadManager) GetMigrationHistory() []StatsMigrationRecord {
	shrm.mu.RLock()
	defer shrm.mu.RUnlock()

	// 返回副本
	history := make([]StatsMigrationRecord, len(shrm.migrationHistory))
	copy(history, shrm.migrationHistory)
	return history
}

// GetCurrentStatistics 获取当前统计数据
func (shrm *StatisticsHotReloadManager) GetCurrentStatistics() *StatisticsSnapshot {
	shrm.mu.RLock()
	defer shrm.mu.RUnlock()

	snapshot, _ := shrm.createStatisticsSnapshot("current")
	return snapshot
}
