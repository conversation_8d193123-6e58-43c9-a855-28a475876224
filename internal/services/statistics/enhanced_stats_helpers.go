package statistics

import (
	"fmt"
	"time"

	"flexproxy/internal/config"
)

// ========== 数据保留判断相关辅助方法 ==========

// isDataTypeAffectedByConfigChange 检查数据类型是否受配置变更影响
func (eshrm *EnhancedStatisticsHotReloadManager) isDataTypeAffectedByConfigChange(dataType string, oldConfig, newConfig *config.StatisticsConfig) bool {
	eshrm.logger.Debug("检查数据类型是否受配置变更影响: %s", dataType)
	
	if oldConfig == nil || newConfig == nil {
		return true // 配置为空，认为受影响
	}
	
	// 检查不同数据类型的配置变更影响
	switch dataType {
	case "request_stats":
		return eshrm.isRequestStatsConfigChanged(oldConfig, newConfig)
	case "proxy_stats":
		return eshrm.isProxyStatsConfigChanged(oldConfig, newConfig)
	case "error_stats":
		return eshrm.isErrorStatsConfigChanged(oldConfig, newConfig)
	case "performance_stats":
		return eshrm.isPerformanceStatsConfigChanged(oldConfig, newConfig)
	default:
		return false // 未知类型，认为不受影响
	}
}

// isDataFresh 检查数据是否新鲜
func (eshrm *EnhancedStatisticsHotReloadManager) isDataFresh(dataType string) bool {
	eshrm.logger.Debug("检查数据新鲜度: %s", dataType)
	
	// 检查数据的最后更新时间
	lastUpdate := eshrm.getDataLastUpdateTime(dataType)
	if lastUpdate.IsZero() {
		return false
	}
	
	// 定义新鲜度阈值（1小时内为新鲜）
	freshnessThreshold := time.Hour
	return time.Since(lastUpdate) < freshnessThreshold
}

// hasDataDependencies 检查数据是否有依赖关系
func (eshrm *EnhancedStatisticsHotReloadManager) hasDataDependencies(dataType string) bool {
	eshrm.logger.Debug("检查数据依赖关系: %s", dataType)
	
	// 定义数据依赖关系
	dependencies := map[string][]string{
		"request_stats":     {"proxy_stats", "performance_stats"},
		"proxy_stats":       {"network_stats"},
		"error_stats":       {"request_stats"},
		"performance_stats": {"request_stats", "proxy_stats"},
	}
	
	deps, exists := dependencies[dataType]
	return exists && len(deps) > 0
}

// ========== 数据大小计算相关辅助方法 ==========

// getDefaultDataSize 获取默认数据大小
func (eshrm *EnhancedStatisticsHotReloadManager) getDefaultDataSize(dataType string) int64 {
	defaultSizes := map[string]int64{
		"request_stats":     1024 * 100, // 100KB
		"proxy_stats":       1024 * 50,  // 50KB
		"performance_stats": 1024 * 200, // 200KB
		"error_stats":       1024 * 30,  // 30KB
		"cache_stats":       1024 * 80,  // 80KB
		"network_stats":     1024 * 60,  // 60KB
	}
	
	if size, exists := defaultSizes[dataType]; exists {
		return size
	}
	
	return 1024 * 10 // 默认10KB
}

// calculateRequestStatsSize 计算请求统计数据大小
func (eshrm *EnhancedStatisticsHotReloadManager) calculateRequestStatsSize() int64 {
	if eshrm.currentStats.RequestStats == nil {
		return eshrm.getDefaultDataSize("request_stats")
	}
	
	// 基于请求数量估算大小
	baseSize := int64(1024) // 1KB基础大小
	requestCount := eshrm.currentStats.RequestStats.TotalRequests
	
	// 每个请求记录大约占用100字节
	estimatedSize := baseSize + requestCount*100
	
	return estimatedSize
}

// calculateProxyStatsSize 计算代理统计数据大小
func (eshrm *EnhancedStatisticsHotReloadManager) calculateProxyStatsSize() int64 {
	if eshrm.currentStats.ProxyStats == nil {
		return eshrm.getDefaultDataSize("proxy_stats")
	}

	// 基于代理数量估算大小
	baseSize := int64(512) // 512B基础大小
	proxyCount := int64(eshrm.currentStats.ProxyStats.TotalProxies)

	// 每个代理记录大约占用500字节
	estimatedSize := baseSize + proxyCount*500

	return estimatedSize
}

// calculatePerformanceStatsSize 计算性能统计数据大小
func (eshrm *EnhancedStatisticsHotReloadManager) calculatePerformanceStatsSize() int64 {
	if eshrm.currentStats.PerformanceStats == nil {
		return eshrm.getDefaultDataSize("performance_stats")
	}
	
	// 基于性能指标数量估算大小
	baseSize := int64(2048) // 2KB基础大小
	
	// 性能统计通常包含大量时间序列数据
	// 估算为基础大小的10倍
	estimatedSize := baseSize * 10
	
	return estimatedSize
}

// calculateErrorStatsSize 计算错误统计数据大小
func (eshrm *EnhancedStatisticsHotReloadManager) calculateErrorStatsSize() int64 {
	if eshrm.currentStats.ErrorStats == nil {
		return eshrm.getDefaultDataSize("error_stats")
	}

	// 基于错误记录数量估算大小
	baseSize := int64(256) // 256B基础大小
	errorCount := eshrm.currentStats.ErrorStats.TotalErrors

	// 每个错误记录大约占用200字节
	estimatedSize := baseSize + errorCount*200

	return estimatedSize
}

// calculateCacheStatsSize 计算缓存统计数据大小
func (eshrm *EnhancedStatisticsHotReloadManager) calculateCacheStatsSize() int64 {
	// 缓存统计数据大小估算
	baseSize := int64(1024) // 1KB基础大小
	
	// 假设缓存统计包含命中率、大小等信息
	estimatedSize := baseSize * 5
	
	return estimatedSize
}

// calculateNetworkStatsSize 计算网络统计数据大小
func (eshrm *EnhancedStatisticsHotReloadManager) calculateNetworkStatsSize() int64 {
	// 网络统计数据大小估算
	baseSize := int64(512) // 512B基础大小
	
	// 假设网络统计包含带宽、延迟等信息
	estimatedSize := baseSize * 8
	
	return estimatedSize
}

// calculateGenericStatsSize 计算通用统计数据大小
func (eshrm *EnhancedStatisticsHotReloadManager) calculateGenericStatsSize(dataType string) int64 {
	eshrm.logger.Debug("计算通用统计数据大小: %s", dataType)
	
	// 通用数据类型的默认大小
	return eshrm.getDefaultDataSize(dataType)
}

// ========== 配置变更检查相关辅助方法 ==========

// isRequestStatsConfigChanged 检查请求统计配置是否变更
func (eshrm *EnhancedStatisticsHotReloadManager) isRequestStatsConfigChanged(oldConfig, newConfig *config.StatisticsConfig) bool {
	// 检查请求统计相关配置是否变更
	// 这里应该比较具体的配置字段
	return oldConfig.Enable != newConfig.Enable
}

// isProxyStatsConfigChanged 检查代理统计配置是否变更
func (eshrm *EnhancedStatisticsHotReloadManager) isProxyStatsConfigChanged(oldConfig, newConfig *config.StatisticsConfig) bool {
	// 检查代理统计相关配置是否变更
	return oldConfig.Enable != newConfig.Enable
}

// isErrorStatsConfigChanged 检查错误统计配置是否变更
func (eshrm *EnhancedStatisticsHotReloadManager) isErrorStatsConfigChanged(oldConfig, newConfig *config.StatisticsConfig) bool {
	// 检查错误统计相关配置是否变更
	return oldConfig.Enable != newConfig.Enable
}

// isPerformanceStatsConfigChanged 检查性能统计配置是否变更
func (eshrm *EnhancedStatisticsHotReloadManager) isPerformanceStatsConfigChanged(oldConfig, newConfig *config.StatisticsConfig) bool {
	// 检查性能统计相关配置是否变更
	return oldConfig.Enable != newConfig.Enable
}

// getDataLastUpdateTime 获取数据最后更新时间
func (eshrm *EnhancedStatisticsHotReloadManager) getDataLastUpdateTime(dataType string) time.Time {
	// 获取指定数据类型的最后更新时间
	// 这里应该从实际的统计数据中获取时间戳
	
	if eshrm.currentStats == nil {
		return time.Time{}
	}
	
	// 简化实现：返回当前时间减去随机偏移
	switch dataType {
	case "request_stats":
		return time.Now().Add(-30 * time.Minute) // 30分钟前
	case "proxy_stats":
		return time.Now().Add(-45 * time.Minute) // 45分钟前
	case "error_stats":
		return time.Now().Add(-15 * time.Minute) // 15分钟前
	default:
		return time.Now().Add(-60 * time.Minute) // 1小时前
	}
}

// ========== 数据完整性验证相关辅助方法 ==========

// validateRequestStatsIntegrity 验证请求统计数据完整性
func (eshrm *EnhancedStatisticsHotReloadManager) validateRequestStatsIntegrity() error {
	if eshrm.currentStats.RequestStats == nil {
		return fmt.Errorf("请求统计数据为空")
	}
	
	// 检查请求统计数据的基本字段
	if eshrm.currentStats.RequestStats.TotalRequests < 0 {
		return fmt.Errorf("总请求数不能为负数")
	}
	
	if eshrm.currentStats.RequestStats.SuccessRequests < 0 {
		return fmt.Errorf("成功请求数不能为负数")
	}
	
	if eshrm.currentStats.RequestStats.SuccessRequests > eshrm.currentStats.RequestStats.TotalRequests {
		return fmt.Errorf("成功请求数不能大于总请求数")
	}
	
	return nil
}

// validateProxyStatsIntegrity 验证代理统计数据完整性
func (eshrm *EnhancedStatisticsHotReloadManager) validateProxyStatsIntegrity() error {
	if eshrm.currentStats.ProxyStats == nil {
		return fmt.Errorf("代理统计数据为空")
	}

	// 检查代理统计数据的完整性
	if eshrm.currentStats.ProxyStats.TotalProxies < 0 {
		return fmt.Errorf("总代理数不能为负数")
	}

	if eshrm.currentStats.ProxyStats.ActiveProxies < 0 {
		return fmt.Errorf("活跃代理数不能为负数")
	}

	if eshrm.currentStats.ProxyStats.ActiveProxies > eshrm.currentStats.ProxyStats.TotalProxies {
		return fmt.Errorf("活跃代理数不能大于总代理数")
	}

	if eshrm.currentStats.ProxyStats.BlacklistedCount < 0 {
		return fmt.Errorf("黑名单代理数不能为负数")
	}

	// 检查代理使用统计
	if eshrm.currentStats.ProxyStats.ProxyUsageCount != nil {
		for proxyURL, count := range eshrm.currentStats.ProxyStats.ProxyUsageCount {
			if proxyURL == "" {
				return fmt.Errorf("代理URL不能为空")
			}
			if count < 0 {
				return fmt.Errorf("代理使用次数不能为负数: %s", proxyURL)
			}
		}
	}

	return nil
}

// validateErrorStatsIntegrity 验证错误统计数据完整性
func (eshrm *EnhancedStatisticsHotReloadManager) validateErrorStatsIntegrity() error {
	if eshrm.currentStats.ErrorStats == nil {
		return nil // 错误统计可以为空
	}

	// 检查错误统计数据的完整性
	if eshrm.currentStats.ErrorStats.TotalErrors < 0 {
		return fmt.Errorf("总错误数不能为负数")
	}

	if eshrm.currentStats.ErrorStats.CriticalErrors < 0 {
		return fmt.Errorf("严重错误数不能为负数")
	}

	if eshrm.currentStats.ErrorStats.CriticalErrors > eshrm.currentStats.ErrorStats.TotalErrors {
		return fmt.Errorf("严重错误数不能大于总错误数")
	}

	// 检查最近错误记录
	if eshrm.currentStats.ErrorStats.RecentErrors != nil {
		for _, errorRecord := range eshrm.currentStats.ErrorStats.RecentErrors {
			if errorRecord.ErrorType == "" {
				return fmt.Errorf("错误类型不能为空")
			}

			if errorRecord.Timestamp.IsZero() {
				return fmt.Errorf("错误时间戳不能为空")
			}

			if errorRecord.Message == "" {
				return fmt.Errorf("错误消息不能为空")
			}
		}
	}

	return nil
}

// validatePerformanceStatsIntegrity 验证性能统计数据完整性
func (eshrm *EnhancedStatisticsHotReloadManager) validatePerformanceStatsIntegrity() error {
	if eshrm.currentStats.PerformanceStats == nil {
		return fmt.Errorf("性能统计数据为空")
	}

	// 检查性能统计数据的基本字段
	if eshrm.currentStats.PerformanceStats.MemoryUsage < 0 {
		return fmt.Errorf("内存使用量不能为负数")
	}

	if eshrm.currentStats.PerformanceStats.CPUUsage < 0 {
		return fmt.Errorf("CPU使用率不能为负数")
	}

	if eshrm.currentStats.PerformanceStats.CPUUsage > 100 {
		return fmt.Errorf("CPU使用率不能超过100%%")
	}

	if eshrm.currentStats.PerformanceStats.GoroutineCount < 0 {
		return fmt.Errorf("Goroutine数量不能为负数")
	}

	if eshrm.currentStats.PerformanceStats.ConnectionCount < 0 {
		return fmt.Errorf("连接数量不能为负数")
	}

	return nil
}

// validateDataConsistency 验证数据一致性
func (eshrm *EnhancedStatisticsHotReloadManager) validateDataConsistency() error {
	// 检查不同统计数据之间的一致性

	// 检查请求统计和代理统计的一致性
	if eshrm.currentStats.RequestStats != nil && eshrm.currentStats.ProxyStats != nil {
		// 简化的一致性检查：确保代理统计中的使用次数总和不超过请求总数的合理范围
		totalProxyUsage := int64(0)
		if eshrm.currentStats.ProxyStats.ProxyUsageCount != nil {
			for _, count := range eshrm.currentStats.ProxyStats.ProxyUsageCount {
				totalProxyUsage += count
			}
		}

		// 允许代理使用次数总和在请求总数的合理范围内
		if totalProxyUsage > eshrm.currentStats.RequestStats.TotalRequests*2 {
			return fmt.Errorf("代理使用次数总和异常: 代理使用=%d, 请求总数=%d",
				totalProxyUsage, eshrm.currentStats.RequestStats.TotalRequests)
		}
	}

	// 检查错误统计和请求统计的一致性
	if eshrm.currentStats.RequestStats != nil && eshrm.currentStats.ErrorStats != nil {
		// 错误总数不应该超过请求总数
		if eshrm.currentStats.ErrorStats.TotalErrors > eshrm.currentStats.RequestStats.TotalRequests {
			return fmt.Errorf("错误总数不能超过请求总数: 错误=%d, 请求=%d",
				eshrm.currentStats.ErrorStats.TotalErrors, eshrm.currentStats.RequestStats.TotalRequests)
		}
	}

	return nil
}

// validateDataTimestamps 验证数据时间戳
func (eshrm *EnhancedStatisticsHotReloadManager) validateDataTimestamps() error {
	// 检查数据时间戳的合理性

	now := time.Now()
	maxAge := 24 * time.Hour // 数据最大年龄24小时

	// 检查错误统计的时间戳
	if eshrm.currentStats.ErrorStats != nil && eshrm.currentStats.ErrorStats.RecentErrors != nil {
		for _, errorRecord := range eshrm.currentStats.ErrorStats.RecentErrors {
			if errorRecord.Timestamp.After(now) {
				return fmt.Errorf("错误时间戳不能是未来时间: %v", errorRecord.Timestamp)
			}

			if time.Since(errorRecord.Timestamp) > maxAge {
				return fmt.Errorf("错误时间戳过旧: %v", errorRecord.Timestamp)
			}
		}
	}

	return nil
}

// validateDataRanges 验证数据范围
func (eshrm *EnhancedStatisticsHotReloadManager) validateDataRanges() error {
	// 检查数据值的合理范围

	if eshrm.currentStats.PerformanceStats != nil {
		// CPU使用率应该在0-100%范围内
		if eshrm.currentStats.PerformanceStats.CPUUsage < 0 || eshrm.currentStats.PerformanceStats.CPUUsage > 100 {
			return fmt.Errorf("CPU使用率超出合理范围: %.2f%%",
				eshrm.currentStats.PerformanceStats.CPUUsage)
		}

		// 缓存命中率应该在0-1范围内
		if eshrm.currentStats.PerformanceStats.CacheHitRate < 0 || eshrm.currentStats.PerformanceStats.CacheHitRate > 1 {
			return fmt.Errorf("缓存命中率超出合理范围: %.2f",
				eshrm.currentStats.PerformanceStats.CacheHitRate)
		}

		// 内存使用量应该为正数
		if eshrm.currentStats.PerformanceStats.MemoryUsage < 0 {
			return fmt.Errorf("内存使用量不能为负数: %d",
				eshrm.currentStats.PerformanceStats.MemoryUsage)
		}
	}

	return nil
}
