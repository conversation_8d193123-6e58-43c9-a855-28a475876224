package basic

import (
	"testing"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/logger"
)

// MockCacheService 模拟缓存服务
type MockCacheService struct{}

func (m *MockCacheService) GetDNSCache(key string) ([]string, bool) { return nil, false }
func (m *MockCacheService) SetDNSCache(key string, value []string, ttl int) {}
func (m *MockCacheService) GetRegexCache(pattern string) (interface{}, bool) { return nil, false }
func (m *MockCacheService) SetRegexCache(pattern string, value interface{}) {}
func (m *MockCacheService) UpdateProxyPool(proxies []string) {}
func (m *MockCacheService) StartCleanupRoutine() {}
func (m *MockCacheService) GetCacheStats() map[string]interface{} { return make(map[string]interface{}) }
func (m *MockCacheService) ClearAllCache() {}
func (m *MockCacheService) GetAllDNSCache() map[string]interface{} { return make(map[string]interface{}) }
func (m *MockCacheService) GetAllRegexCache() map[string]interface{} { return make(map[string]interface{}) }
func (m *MockCacheService) UpdateConfig(config interface{}) error { return nil }

// MockLogger 模拟日志服务
type MockLogger struct{}

func (m *MockLogger) Debug(msg string) {}
func (m *MockLogger) Info(msg string) {}
func (m *MockLogger) Warn(msg string) {}
func (m *MockLogger) Error(msg string) {}
func (m *MockLogger) Fatal(msg string) {}

// createTestProxyService 创建测试用的代理服务
func createTestProxyService() *proxyService {
	mockCache := &MockCacheService{}
	mockLogger := logger.NewSimpleLogger("test")

	service := NewProxyService(mockCache, mockLogger).(*proxyService)
	
	// 初始化测试代理列表
	testProxies := []string{
		"http://proxy1.example.com:8080",
		"http://proxy2.example.com:8080", 
		"http://proxy3.example.com:8080",
		"http://proxy4.example.com:8080",
		"http://proxy5.example.com:8080",
	}
	service.UpdateProxyList(testProxies)
	
	return service
}

// TestCalculateComprehensiveQualityScore 测试综合质量评分计算
func TestCalculateComprehensiveQualityScore(t *testing.T) {
	service := createTestProxyService()
	
	tests := []struct {
		name           string
		proxy          string
		setupQuality   func(*proxyService, string)
		expectedRange  [2]float64 // [min, max]
		description    string
	}{
		{
			name:  "新代理默认评分",
			proxy: "http://new-proxy.example.com:8080",
			setupQuality: func(s *proxyService, proxy string) {
				// 不设置任何质量信息，测试新代理的默认评分
			},
			expectedRange: [2]float64{55, 65}, // 新代理应该得到略高于默认的评分
			description:   "新代理应该获得鼓励性的初始评分",
		},
		{
			name:  "高质量代理",
			proxy: "http://proxy1.example.com:8080",
			setupQuality: func(s *proxyService, proxy string) {
				s.qualityMutex.Lock()
				s.qualityInfo[proxy] = &ProxyQualityInfo{
					ProxyURL:        proxy,
					SuccessCount:    95,
					FailureCount:    5,
					TotalRequests:   100,
					AvgResponseTime: 200 * time.Millisecond,
					LastUsed:        time.Now().Add(-10 * time.Minute),
					QualityTier:     constants.QualityTierPremium,
					DomainStats:     make(map[string]*DomainPerformance),
					CreatedAt:       time.Now().Add(-24 * time.Hour),
					UpdatedAt:       time.Now(),
				}
				s.qualityMutex.Unlock()
			},
			expectedRange: [2]float64{85, 100},
			description:   "高成功率和快速响应的代理应该获得高评分",
		},
		{
			name:  "低质量代理",
			proxy: "http://proxy2.example.com:8080",
			setupQuality: func(s *proxyService, proxy string) {
				s.qualityMutex.Lock()
				s.qualityInfo[proxy] = &ProxyQualityInfo{
					ProxyURL:        proxy,
					SuccessCount:    30,
					FailureCount:    70,
					TotalRequests:   100,
					AvgResponseTime: 5 * time.Second,
					LastUsed:        time.Now().Add(-2 * time.Hour),
					QualityTier:     constants.QualityTierBackup,
					DomainStats:     make(map[string]*DomainPerformance),
					CreatedAt:       time.Now().Add(-24 * time.Hour),
					UpdatedAt:       time.Now(),
				}
				s.qualityMutex.Unlock()
			},
			expectedRange: [2]float64{35, 50},
			description:   "低成功率和慢响应的代理应该获得低评分",
		},
		{
			name:  "过度使用的代理",
			proxy: "http://proxy3.example.com:8080",
			setupQuality: func(s *proxyService, proxy string) {
				s.qualityMutex.Lock()
				// 设置其他代理的使用情况
				for _, p := range []string{"http://proxy1.example.com:8080", "http://proxy2.example.com:8080"} {
					s.qualityInfo[p] = &ProxyQualityInfo{
						ProxyURL:      p,
						TotalRequests: 10,
					}
				}
				// 设置当前代理为过度使用
				s.qualityInfo[proxy] = &ProxyQualityInfo{
					ProxyURL:        proxy,
					SuccessCount:    80,
					FailureCount:    20,
					TotalRequests:   200, // 远超平均使用量
					AvgResponseTime: 300 * time.Millisecond,
					LastUsed:        time.Now(),
					QualityTier:     constants.QualityTierStandard,
					DomainStats:     make(map[string]*DomainPerformance),
					CreatedAt:       time.Now().Add(-24 * time.Hour),
					UpdatedAt:       time.Now(),
				}
				s.qualityMutex.Unlock()
			},
			expectedRange: [2]float64{70, 85},
			description:   "过度使用的代理应该因负载均衡因素而降低评分",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置测试数据
			tt.setupQuality(service, tt.proxy)
			
			// 计算质量评分
			score := service.calculateComprehensiveQualityScore(tt.proxy)
			
			// 验证评分范围
			if score < tt.expectedRange[0] || score > tt.expectedRange[1] {
				t.Errorf("质量评分超出预期范围: 得到 %.2f, 期望 [%.2f, %.2f] - %s", 
					score, tt.expectedRange[0], tt.expectedRange[1], tt.description)
			}
			
			t.Logf("代理 %s 质量评分: %.2f (%s)", tt.proxy, score, tt.description)
		})
	}
}

// TestUpdateProxyQuality 测试代理质量更新
func TestUpdateProxyQuality(t *testing.T) {
	service := createTestProxyService()
	proxy := "http://proxy1.example.com:8080"
	domain := "example.com"

	// 测试成功请求更新
	t.Run("成功请求更新", func(t *testing.T) {
		service.UpdateProxyQuality(proxy, true, 200*time.Millisecond, domain)
		
		info, exists := service.GetProxyQualityInfo(proxy)
		if !exists {
			t.Fatal("代理质量信息应该存在")
		}
		
		if info.SuccessCount != 1 {
			t.Errorf("成功次数应该为1，实际为 %d", info.SuccessCount)
		}
		
		if info.TotalRequests != 1 {
			t.Errorf("总请求数应该为1，实际为 %d", info.TotalRequests)
		}
		
		if info.AvgResponseTime != 200*time.Millisecond {
			t.Errorf("平均响应时间应该为200ms，实际为 %v", info.AvgResponseTime)
		}
	})

	// 测试失败请求更新
	t.Run("失败请求更新", func(t *testing.T) {
		service.UpdateProxyQuality(proxy, false, 5*time.Second, domain)
		
		info, exists := service.GetProxyQualityInfo(proxy)
		if !exists {
			t.Fatal("代理质量信息应该存在")
		}
		
		if info.FailureCount != 1 {
			t.Errorf("失败次数应该为1，实际为 %d", info.FailureCount)
		}
		
		if info.TotalRequests != 2 {
			t.Errorf("总请求数应该为2，实际为 %d", info.TotalRequests)
		}
	})

	// 测试域名统计更新
	t.Run("域名统计更新", func(t *testing.T) {
		info, _ := service.GetProxyQualityInfo(proxy)
		
		domainStats, exists := info.DomainStats[domain]
		if !exists {
			t.Fatal("域名统计信息应该存在")
		}
		
		if domainStats.SuccessCount != 1 {
			t.Errorf("域名成功次数应该为1，实际为 %d", domainStats.SuccessCount)
		}
		
		if domainStats.FailureCount != 1 {
			t.Errorf("域名失败次数应该为1，实际为 %d", domainStats.FailureCount)
		}
	})
}

// TestQualityTierUpdate 测试质量等级更新
func TestQualityTierUpdate(t *testing.T) {
	service := createTestProxyService()
	proxy := "http://proxy1.example.com:8080"

	tests := []struct {
		name         string
		score        float64
		expectedTier string
	}{
		{"高质量代理", 90.0, constants.QualityTierPremium},
		{"标准质量代理", 65.0, constants.QualityTierStandard},
		{"备用代理", 30.0, constants.QualityTierBackup},
		{"边界情况-高", 80.0, constants.QualityTierPremium},
		{"边界情况-中", 50.0, constants.QualityTierStandard},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建质量信息
			service.qualityMutex.Lock()
			qualityInfo := &ProxyQualityInfo{
				ProxyURL:    proxy,
				QualityScore: tt.score,
				DomainStats: make(map[string]*DomainPerformance),
			}
			service.qualityInfo[proxy] = qualityInfo
			
			// 更新质量等级
			service.updateQualityTier(qualityInfo)
			service.qualityMutex.Unlock()
			
			// 验证等级
			if qualityInfo.QualityTier != tt.expectedTier {
				t.Errorf("质量等级不正确: 评分 %.1f 应该对应 %s，实际为 %s", 
					tt.score, tt.expectedTier, qualityInfo.QualityTier)
			}
		})
	}
}

// TestGetQualityProxy 测试质量优先代理选择
func TestGetQualityProxy(t *testing.T) {
	service := createTestProxyService()

	// 设置不同质量的代理
	proxies := []struct {
		url   string
		score float64
	}{
		{"http://proxy1.example.com:8080", 95.0},
		{"http://proxy2.example.com:8080", 75.0},
		{"http://proxy3.example.com:8080", 45.0},
		{"http://proxy4.example.com:8080", 85.0},
		{"http://proxy5.example.com:8080", 25.0},
	}

	service.qualityMutex.Lock()
	for _, p := range proxies {
		service.qualityInfo[p.url] = &ProxyQualityInfo{
			ProxyURL:     p.url,
			QualityScore: p.score,
			SuccessCount: int64(p.score),
			TotalRequests: 100,
			AvgResponseTime: time.Duration(1000-p.score*10) * time.Millisecond,
			LastUsed:     time.Now(),
			QualityTier:  getQualityTier(p.score),
			DomainStats:  make(map[string]*DomainPerformance),
		}
	}
	service.qualityMutex.Unlock()

	// 多次选择代理，验证是否选择最高质量的
	selectedCounts := make(map[string]int)
	for i := 0; i < 10; i++ { // 减少循环次数避免卡住
		proxy, err := service.getQualityProxy()
		if err != nil {
			t.Fatalf("选择代理失败: %v", err)
		}
		selectedCounts[proxy]++
	}

	// 验证最高质量的代理被选择最多
	maxCount := 0
	mostSelectedProxy := ""
	for proxy, count := range selectedCounts {
		if count > maxCount {
			maxCount = count
			mostSelectedProxy = proxy
		}
	}

	if mostSelectedProxy != "http://proxy1.example.com:8080" {
		t.Errorf("应该选择最高质量的代理 proxy1，实际选择了 %s", mostSelectedProxy)
	}

	t.Logf("代理选择统计: %v", selectedCounts)
}

// TestResponseTimeScore 测试响应时间评分
func TestResponseTimeScore(t *testing.T) {
	service := createTestProxyService()

	tests := []struct {
		responseTime   time.Duration
		expectedRange  [2]float64
		description    string
	}{
		{100 * time.Millisecond, [2]float64{95, 100}, "极快响应"},
		{500 * time.Millisecond, [2]float64{95, 100}, "快速响应"},
		{1000 * time.Millisecond, [2]float64{70, 90}, "中等响应"},
		{2000 * time.Millisecond, [2]float64{55, 65}, "较慢响应"},
		{3500 * time.Millisecond, [2]float64{30, 50}, "慢响应"},
		{6000 * time.Millisecond, [2]float64{0, 5}, "极慢响应"},
	}

	for _, tt := range tests {
		t.Run(tt.description, func(t *testing.T) {
			score := service.calculateResponseTimeScore(tt.responseTime)

			if score < tt.expectedRange[0] || score > tt.expectedRange[1] {
				t.Errorf("响应时间评分超出预期: %v -> %.2f, 期望 [%.2f, %.2f]",
					tt.responseTime, score, tt.expectedRange[0], tt.expectedRange[1])
			}

			t.Logf("响应时间 %v 评分: %.2f", tt.responseTime, score)
		})
	}
}

// 辅助函数
func getQualityTier(score float64) string {
	if score >= 80 {
		return constants.QualityTierPremium
	} else if score >= 50 {
		return constants.QualityTierStandard
	} else {
		return constants.QualityTierBackup
	}
}
