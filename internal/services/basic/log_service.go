// Package services 提供各种服务实现
package basic

import (
	"fmt"
	"sync"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
	"flexproxy/internal/interfaces"
)

// logService 统一日志服务实现
type logService struct {
	mu        sync.RWMutex
	logger    *logger.LoggerAdapter
	component string
	fields    map[string]interface{}
	config    *config.LoggingConfig
}

// NewLogService 创建日志服务实例
func NewLogService(outputFile string) interfaces.LogService {
	// 使用统一日志适配器
	log := logger.GetLoggerAdapter(logger.ModuleLogService)

	ls := &logService{
		logger:    log,
		component: "log_service",
		fields:    make(map[string]interface{}),
	}

	ls.logger.Info("日志服务已初始化")
	return ls
}

// NewLogServiceFromAdapter 从LoggerAdapter创建日志服务实例
func NewLogServiceFromAdapter(loggerAdapter *logger.LoggerAdapter) interfaces.LogService {
	ls := &logService{
		logger:    loggerAdapter,
		component: "log_service",
		fields:    make(map[string]interface{}),
	}

	return ls
}

// Info 记录信息级别日志
func (ls *logService) Info(msg string, args ...interface{}) {
	ls.logger.Info(msg, args...)
}

// Warn 记录警告级别日志
func (ls *logService) Warn(msg string, args ...interface{}) {
	ls.logger.Warn(msg, args...)
}

// Error 记录错误级别日志
func (ls *logService) Error(msg string, args ...interface{}) {
	ls.logger.Error(msg, args...)
}

// Debug 记录调试级别日志
func (ls *logService) Debug(msg string, args ...interface{}) {
	ls.logger.Debug(msg, args...)
}

// Fatal 记录致命错误日志并退出程序
func (ls *logService) Fatal(msg string, args ...interface{}) {
	ls.logger.Fatal(msg, args...)
}

// GetLogger 获取日志器实例
func (ls *logService) GetLogger() interface{} {
	return ls.logger
}

// WithTraceID 为日志添加追踪ID
func (ls *logService) WithTraceID(traceID string) interfaces.LogService {
	newFields := make(map[string]interface{})
	for k, v := range ls.fields {
		newFields[k] = v
	}
	newFields["trace_id"] = traceID
	newLogger := &logService{
		logger:    ls.logger,
		component: ls.component,
		fields:    newFields,
	}
	return newLogger
}

// WithFields 为日志添加字段
func (ls *logService) WithFields(fields map[string]interface{}) interfaces.LogService {
	newFields := make(map[string]interface{})
	for k, v := range ls.fields {
		newFields[k] = v
	}
	for k, v := range fields {
		newFields[k] = v
	}
	newLogger := &logService{
		logger:    ls.logger,
		component: ls.component,
		fields:    newFields,
	}
	return newLogger
}

// LogError 记录FlexProxyError类型的错误
func (ls *logService) LogError(err error, msg string, args ...interface{}) {
	// 先记录用户提供的消息
	if msg != "" {
		ls.logger.Error(msg, args...)
	}

	if flexErr, ok := err.(*errors.FlexProxyError); ok {
		errorMsg := fmt.Sprintf("FlexProxyError: %s [type=%s, code=%s, trace_id=%s, timestamp=%s]",
			flexErr.Message,
			flexErr.Type.String(),
			flexErr.Code.String(),
			flexErr.TraceID,
			flexErr.Timestamp.Format(constants.TimeFormatDefault))
		ls.logger.Error(errorMsg)

		// 记录错误详情
		if flexErr.Details != "" {
			ls.logger.Debug("错误详情: %s", flexErr.Details)
		}

		// 记录错误链
		if flexErr.Cause != nil {
			ls.logger.Debug("错误原因: %s", flexErr.Cause.Error())
		}
	} else {
		ls.Error("未知错误: %v", err)
	}
}

// UpdateConfig 更新日志服务配置
// 支持热重载日志级别、输出格式等配置
func (ls *logService) UpdateConfig(configInterface interface{}) error {
	ls.mu.Lock()
	defer ls.mu.Unlock()

	// 类型断言
	cfg, ok := configInterface.(*config.LoggingConfig)
	if !ok {
		return fmt.Errorf("无效的配置类型，期望 *config.LoggingConfig")
	}

	if cfg == nil {
		return fmt.Errorf("配置不能为空")
	}

	oldConfig := ls.config
	ls.config = cfg

	// 记录配置更新
	ls.logger.Info("开始更新日志服务配置")

	// 1. 更新日志级别
	if err := ls.updateLogLevel(oldConfig, cfg); err != nil {
		ls.logger.Warn("更新日志级别失败: %v", err)
	}

	// 2. 更新日志格式
	if err := ls.updateLogFormat(oldConfig, cfg); err != nil {
		ls.logger.Warn("更新日志格式失败: %v", err)
	}

	// 3. 更新时间格式
	if err := ls.updateTimeFormat(oldConfig, cfg); err != nil {
		ls.logger.Warn("更新时间格式失败: %v", err)
	}

	// 4. 应用其他日志设置
	ls.applyLogSettings(cfg)

	ls.logger.Info("日志服务配置更新完成")
	return nil
}

// updateLogLevel 更新日志级别
func (ls *logService) updateLogLevel(oldConfig, newConfig *config.LoggingConfig) error {
	newLevel := constants.LogLevelInfo // 默认级别
	if newConfig.Level != "" {
		newLevel = newConfig.Level
	}

	// 验证日志级别有效性
	validLevels := map[string]bool{
		constants.LogLevelDebug: true,
		constants.LogLevelInfo:  true,
		constants.LogLevelWarn:  true,
		constants.LogLevelError: true,
		constants.LogLevelFatal: true,
	}

	if !validLevels[newLevel] {
		return fmt.Errorf("无效的日志级别: %s", newLevel)
	}

	// 检查是否有变化
	oldLevel := constants.LogLevelInfo
	if oldConfig != nil && oldConfig.Level != "" {
		oldLevel = oldConfig.Level
	}

	if newLevel != oldLevel {
		ls.logger.Info("日志级别已更新: %s -> %s", oldLevel, newLevel)
		// 注意：实际的日志级别更新需要底层logger支持
		// 这里只是记录配置变更，实际实现可能需要重新创建logger实例
	}

	return nil
}

// updateLogFormat 更新日志格式
func (ls *logService) updateLogFormat(oldConfig, newConfig *config.LoggingConfig) error {
	newFormat := constants.LogFormatJSON // 默认格式
	if newConfig.Format != "" {
		newFormat = newConfig.Format
	}

	// 验证日志格式有效性
	validFormats := map[string]bool{
		constants.LogFormatJSON: true,
		constants.LogFormatText: true,
	}

	if !validFormats[newFormat] {
		return fmt.Errorf("无效的日志格式: %s", newFormat)
	}

	// 检查是否有变化
	oldFormat := constants.LogFormatJSON
	if oldConfig != nil && oldConfig.Format != "" {
		oldFormat = oldConfig.Format
	}

	if newFormat != oldFormat {
		ls.logger.Info("日志格式已更新: %s -> %s", oldFormat, newFormat)
		// 注意：实际的日志格式更新需要底层logger支持
	}

	return nil
}

// updateTimeFormat 更新时间格式
func (ls *logService) updateTimeFormat(oldConfig, newConfig *config.LoggingConfig) error {
	newTimeFormat := constants.TimeFormatDefault // 默认时间格式
	if newConfig.TimeFormat != "" {
		newTimeFormat = newConfig.TimeFormat
	}

	// 检查是否有变化
	oldTimeFormat := constants.TimeFormatDefault
	if oldConfig != nil && oldConfig.TimeFormat != "" {
		oldTimeFormat = oldConfig.TimeFormat
	}

	if newTimeFormat != oldTimeFormat {
		ls.logger.Info("时间格式已更新: %s -> %s", oldTimeFormat, newTimeFormat)
		// 注意：实际的时间格式更新需要底层logger支持
	}

	return nil
}

// applyLogSettings 应用其他日志设置
func (ls *logService) applyLogSettings(cfg *config.LoggingConfig) {
	settings := make([]string, 0)

	if cfg.File != "" {
		settings = append(settings, fmt.Sprintf("日志文件: %s", cfg.File))
	}

	if cfg.MaxSize > 0 {
		settings = append(settings, fmt.Sprintf("最大文件大小: %dMB", cfg.MaxSize))
	}

	if cfg.MaxAge > 0 {
		settings = append(settings, fmt.Sprintf("最大保存天数: %d天", cfg.MaxAge))
	}

	if cfg.MaxBackups >= 0 {
		settings = append(settings, fmt.Sprintf("最大备份文件数: %d", cfg.MaxBackups))
	}

	if len(settings) > 0 {
		ls.logger.Info("应用日志设置: %v", settings)
	}
}
