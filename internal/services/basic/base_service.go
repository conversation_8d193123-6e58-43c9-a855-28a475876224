// Package services 提供基础服务功能
package basic

import (
	"fmt"
	"sync"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
)

// BaseService 基础服务结构，提供通用的服务生命周期管理
type BaseService struct {
	mu      sync.RWMutex
	logger  logger.Logger
	running bool
	name    string
}

// NewBaseService 创建基础服务实例
func NewBaseService(name string, logger logger.Logger) *BaseService {
	return &BaseService{
		logger: logger,
		name:   name,
	}
}

// StartService 启动服务的通用逻辑
func (bs *BaseService) StartService() error {
	bs.mu.Lock()
	defer bs.mu.Unlock()

	if bs.running {
		return errors.NewErrorWithDetails(
			errors.ErrTypeOperation,
			errors.ErrCodeServiceAlreadyRunning,
			"服务已在运行",
			fmt.Sprintf("服务 %s 已在运行", bs.name))
	}

	bs.running = true
	bs.logger.Info(fmt.Sprintf("%s已启动", bs.name))
	return nil
}

// StopService 停止服务的通用逻辑
func (bs *BaseService) StopService() error {
	bs.mu.Lock()
	defer bs.mu.Unlock()

	if !bs.running {
		return nil
	}

	bs.running = false
	bs.logger.Info(fmt.Sprintf("%s已停止", bs.name))
	return nil
}

// IsRunning 检查服务是否正在运行
func (bs *BaseService) IsRunning() bool {
	bs.mu.RLock()
	defer bs.mu.RUnlock()
	return bs.running
}

// GetServiceName 获取服务名称
func (bs *BaseService) GetServiceName() string {
	return bs.name
}

// SetServiceName 设置服务名称
func (bs *BaseService) SetServiceName(name string) {
	bs.mu.Lock()
	defer bs.mu.Unlock()
	bs.name = name
}

// GetLogger 获取日志记录器
func (bs *BaseService) GetLogger() logger.Logger {
	return bs.logger
}

// SetLogger 设置日志记录器
func (bs *BaseService) SetLogger(logger logger.Logger) {
	bs.mu.Lock()
	defer bs.mu.Unlock()
	bs.logger = logger
}

// LogInfo 记录信息日志
func (bs *BaseService) LogInfo(message string) {
	bs.logger.Info(fmt.Sprintf("[%s] %s", bs.name, message))
}

// LogWarn 记录警告日志
func (bs *BaseService) LogWarn(message string) {
	bs.logger.Warn(fmt.Sprintf("[%s] %s", bs.name, message))
}

// LogError 记录错误日志
func (bs *BaseService) LogError(message string) {
	bs.logger.Error(fmt.Sprintf("[%s] %s", bs.name, message))
}

// LogDebug 记录调试日志
func (bs *BaseService) LogDebug(message string) {
	bs.logger.Debug(fmt.Sprintf("[%s] %s", bs.name, message))
}

// ConfigService 配置服务接口，定义配置服务的基本方法
type ConfigService interface {
	Start() error
	Stop() error
	IsRunning() bool
	GetServiceName() string
}



// 通用配置验证函数

// ValidateConfigNotNil 验证配置不为空
func ValidateConfigNotNil(config interface{}) error {
	if config == nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigNotLoaded,
			"配置不能为空",
			"配置对象不能为nil")
	}
	return nil
}

// ValidatePortRange 验证端口范围
func ValidatePortRange(port int) error {
	if port < constants.MinPort || port > constants.MaxPort {
		return errors.NewErrorWithDetails(
			errors.ErrTypeValidation,
			errors.ErrCodeConfigPortInvalid,
			errors.ErrConfigPortInvalid.Message,
			fmt.Sprintf("端口必须在%d-%d之间", constants.MinPort, constants.MaxPort))
	}
	return nil
}

// ValidatePermission 验证权限值
func ValidatePermission(permission int) error {
	if permission < constants.MinPermission || permission > constants.MaxPermission {
		return errors.NewErrorWithDetails(
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidParameter,
			"权限配置无效",
			fmt.Sprintf("权限值 %d 超出有效范围", permission))
	}
	return nil
}

// ValidateRetryAttempts 验证重试次数
func ValidateRetryAttempts(attempts int) error {
	if attempts < constants.MinRetryAttempts || attempts > constants.MaxRetryAttempts {
		return errors.NewErrorWithDetails(
			errors.ErrTypeValidation,
			errors.ErrCodeConfigRetryAttemptsInvalid,
			errors.ErrConfigRetryAttemptsInvalid.Message,
			fmt.Sprintf("重试次数必须在%d-%d之间", constants.MinRetryAttempts, constants.MaxRetryAttempts))
	}
	return nil
}

// ValidateHexLength 验证十六进制长度
func ValidateHexLength(length int) error {
	if length < constants.MinHexLength || length > constants.MaxHexLength {
		return errors.NewErrorWithDetails(
			errors.ErrTypeValidation,
			errors.ErrCodeConfigHexLengthInvalid,
			errors.ErrConfigHexLengthInvalid.Message,
			fmt.Sprintf("十六进制长度必须在%d-%d之间", constants.MinHexLength, constants.MaxHexLength))
	}
	return nil
}

// ValidateWorkerPoolSize 验证工作池大小
func ValidateWorkerPoolSize(size int) error {
	if size < constants.MinWorkerPoolSize || size > constants.MaxWorkerPoolSize {
		return errors.NewErrorWithDetails(
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidParameter,
			"工作池大小配置无效",
			fmt.Sprintf("工作池大小必须在%d-%d之间", constants.MinWorkerPoolSize, constants.MaxWorkerPoolSize))
	}
	return nil
}

// ValidateQueueSize 验证队列大小
func ValidateQueueSize(size int) error {
	if size < constants.MinQueueSize || size > constants.MaxQueueSize {
		return errors.NewErrorWithDetails(
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidParameter,
			"队列大小配置无效",
			fmt.Sprintf("队列大小必须在%d-%d之间", constants.MinQueueSize, constants.MaxQueueSize))
	}
	return nil
}

// ValidateSequenceModulus 验证序列模数
func ValidateSequenceModulus(modulus int) error {
	if modulus < constants.MinSequenceModulus || modulus > constants.MaxSequenceModulus {
		return errors.NewErrorWithDetails(
			errors.ErrTypeValidation,
			errors.ErrCodeConfigSequenceModulusInvalid,
			errors.ErrConfigSequenceModulusInvalid.Message,
			fmt.Sprintf("序列模数必须在%d-%d之间", constants.MinSequenceModulus, constants.MaxSequenceModulus))
	}
	return nil
}

// ServiceManager 服务管理器，用于管理多个配置服务
type ServiceManager struct {
	mu       sync.RWMutex
	services map[string]ConfigService
	logger   logger.Logger
}

// NewServiceManager 创建服务管理器
func NewServiceManager(logger logger.Logger) *ServiceManager {
	return &ServiceManager{
		services: make(map[string]ConfigService),
		logger:   logger,
	}
}

// RegisterService 注册服务
func (sm *ServiceManager) RegisterService(name string, service ConfigService) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if _, exists := sm.services[name]; exists {
		return fmt.Errorf("服务已存在: %s", name)
	}

	sm.services[name] = service
	sm.logger.Info(fmt.Sprintf("服务已注册: %s", name))
	return nil
}

// UnregisterService 注销服务
func (sm *ServiceManager) UnregisterService(name string) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	service, exists := sm.services[name]
	if !exists {
		return fmt.Errorf("服务不存在: %s", name)
	}

	// 停止服务
	if service.IsRunning() {
		if err := service.Stop(); err != nil {
			sm.logger.Warn(fmt.Sprintf("停止服务失败: %s, 错误: %v", name, err))
		}
	}

	delete(sm.services, name)
	sm.logger.Info(fmt.Sprintf("服务已注销: %s", name))
	return nil
}

// StartAllServices 启动所有服务
func (sm *ServiceManager) StartAllServices() error {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	for name, service := range sm.services {
		if !service.IsRunning() {
			if err := service.Start(); err != nil {
				sm.logger.Error(fmt.Sprintf("启动服务失败: %s, 错误: %v", name, err))
				return err
			}
		}
	}

	sm.logger.Info("所有服务已启动")
	return nil
}

// StopAllServices 停止所有服务
func (sm *ServiceManager) StopAllServices() error {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	for name, service := range sm.services {
		if service.IsRunning() {
			if err := service.Stop(); err != nil {
				sm.logger.Error(fmt.Sprintf("停止服务失败: %s, 错误: %v", name, err))
				return err
			}
		}
	}

	sm.logger.Info("所有服务已停止")
	return nil
}

// GetService 获取服务
func (sm *ServiceManager) GetService(name string) (ConfigService, error) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	service, exists := sm.services[name]
	if !exists {
		return nil, fmt.Errorf("服务不存在: %s", name)
	}

	return service, nil
}

// GetRunningServices 获取正在运行的服务列表
func (sm *ServiceManager) GetRunningServices() []string {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	var running []string
	for name, service := range sm.services {
		if service.IsRunning() {
			running = append(running, name)
		}
	}

	return running
}

// GetAllServices 获取所有服务列表
func (sm *ServiceManager) GetAllServices() []string {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	var all []string
	for name := range sm.services {
		all = append(all, name)
	}

	return all
}
