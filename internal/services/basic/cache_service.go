// Package services 提供各种服务实现
package basic

import (
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
	"flexproxy/internal/interfaces"
)

// CacheService 提供缓存功能
// cacheService 缓存服务实现
type cacheService struct {
	mu         sync.RWMutex
	dnsCache   map[string]*cacheItem
	regexCache map[string]interface{}
	proxyPool  []string
	logger     logger.Logger

	// 配置相关字段
	config           *config.CacheConfig
	defaultTTL       time.Duration
	cleanupInterval  time.Duration
	cleanupTicker    *time.Ticker
	cleanupStop      chan struct{}
}

// cacheItem 缓存项
type cacheItem struct {
	value    interface{}
	expireAt time.Time
}

// NewCacheService 创建新的缓存服务实例
func NewCacheService(log logger.Logger) interfaces.CacheService {
	if log == nil {
		log = logger.GetLogger("cache")
	}

	cs := &cacheService{
		dnsCache:        make(map[string]*cacheItem),
		regexCache:      make(map[string]interface{}),
		proxyPool:       make([]string, 0),
		logger:          log,
		defaultTTL:      constants.DefaultCacheTTL,
		cleanupInterval: constants.DefaultCleanupInterval,
		cleanupStop:     make(chan struct{}),
	}

	// 在测试环境中，不启动清理协程
	if !cs.isTestEnvironment() {
		// 启动清理过期缓存的协程
		cs.startCleanupRoutine()
	} else {
		cs.logger.Info("测试环境：跳过清理协程启动")
	}
	cs.logger.Info("缓存服务已初始化")

	return cs
}

// isTestEnvironment 检查是否在测试环境中
func (cs *cacheService) isTestEnvironment() bool {
	// 检查是否在测试环境中运行
	return os.Getenv("GO_ENV") == "test" || strings.Contains(os.Args[0], ".test")
}

// NewCacheServiceWithConfig 创建带配置检查的缓存服务实例
func NewCacheServiceWithConfig(config interface{}, log logger.Logger) interfaces.CacheService {
	if log == nil {
		log = logger.GetLogger("cache")
	}

	// 使用正常的缓存服务（配置检查在调用方进行）

	// 使用正常的缓存服务
	return NewCacheService(log)
}

// GetDNSCache 从缓存中获取DNS记录
func (cs *cacheService) GetDNSCache(key string) ([]string, bool) {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	item, exists := cs.dnsCache[key]
	if !exists {
		cs.logger.Debug(fmt.Sprintf("DNS缓存未命中: %s", key))
		return nil, false
	}

	// 检查是否过期
	if time.Now().After(item.expireAt) {
		// 异步删除过期项
		go func() {
			cs.mu.Lock()
			delete(cs.dnsCache, key)
			cs.mu.Unlock()
			cs.logger.Debug(fmt.Sprintf("删除过期DNS缓存项: %s", key))
		}()
		return nil, false
	}

	// 类型断言为[]string
	if result, ok := item.value.([]string); ok {
		cs.logger.Debug(fmt.Sprintf("DNS缓存命中: %s", key))
		return result, true
	}

	// 如果类型不匹配，返回空
	cs.logger.Debug(fmt.Sprintf("DNS缓存类型不匹配: %s", key))
	return nil, false
}

// SetDNSCache 在缓存中设置DNS记录
func (cs *cacheService) SetDNSCache(key string, value []string, ttl int) {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	if ttl <= 0 {
		ttl = int(cs.defaultTTL.Seconds())
	}

	expireAt := time.Now().Add(time.Duration(ttl) * time.Second)
	cs.dnsCache[key] = &cacheItem{
		value:    value,
		expireAt: expireAt,
	}

	cs.logger.Debug(fmt.Sprintf("DNS缓存已设置: %s, TTL: %d秒, 过期时间: %s, 总缓存数: %d", key, ttl, expireAt.Format(constants.TimeFormatDefault), len(cs.dnsCache)))
}

// GetRegexCache 获取正则表达式缓存
func (cs *cacheService) GetRegexCache(pattern string) (interface{}, bool) {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	compiled, exists := cs.regexCache[pattern]
	if exists {
		cs.logger.Debug(fmt.Sprintf("正则表达式缓存命中: %s", pattern))
	} else {
		cs.logger.Debug(fmt.Sprintf("正则表达式缓存未命中: %s", pattern))
	}
	return compiled, exists
}

// SetRegexCache 设置正则表达式缓存
func (cs *cacheService) SetRegexCache(pattern string, compiled interface{}) {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	cs.regexCache[pattern] = compiled
	cs.logger.Debug(fmt.Sprintf("正则表达式缓存已设置: %s, 总缓存数: %d", pattern, len(cs.regexCache)))
}

// GetProxyPool 获取代理池
func (cs *cacheService) GetProxyPool() []string {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	// 返回副本以避免并发修改
	pool := make([]string, len(cs.proxyPool))
	copy(pool, cs.proxyPool)
	return pool
}

// UpdateProxyPool 更新代理池
func (cs *cacheService) UpdateProxyPool(proxies []string) {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	oldSize := len(cs.proxyPool)
	cs.proxyPool = make([]string, len(proxies))
	copy(cs.proxyPool, proxies)

	cs.logger.Info(fmt.Sprintf("代理池已更新，旧大小: %d, 新大小: %d, 时间: %s", oldSize, len(proxies), time.Now().Format(constants.TimeFormatDefault)))
}

// startCleanupRoutine 启动清理协程
func (cs *cacheService) startCleanupRoutine() {
	cs.cleanupTicker = time.NewTicker(cs.cleanupInterval)
	go cs.cleanupExpiredCache()
}

// stopCleanupRoutine 停止清理协程
func (cs *cacheService) stopCleanupRoutine() {
	if cs.cleanupTicker != nil {
		cs.cleanupTicker.Stop()
		cs.cleanupTicker = nil
	}

	close(cs.cleanupStop)
	cs.cleanupStop = make(chan struct{})
}

// cleanupExpiredCache 清理过期缓存的协程
func (cs *cacheService) cleanupExpiredCache() {
	cs.logger.Info("缓存清理协程已启动")
	defer cs.logger.Info("缓存清理协程已停止")

	for {
		select {
		case <-cs.cleanupTicker.C:
			cs.performCleanup()
		case <-cs.cleanupStop:
			return
		}
	}
}

// performCleanup 执行清理操作
func (cs *cacheService) performCleanup() {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	now := time.Now()
	expiredCount := 0
	totalBefore := len(cs.dnsCache)

	for key, item := range cs.dnsCache {
		if now.After(item.expireAt) {
			delete(cs.dnsCache, key)
			expiredCount++
		}
	}

	if expiredCount > 0 {
		cs.logger.Info(fmt.Sprintf("清理过期DNS缓存项完成，过期数: %d, 清理前: %d, 清理后: %d, 时间: %s", expiredCount, totalBefore, len(cs.dnsCache), now.Format(constants.TimeFormatDefault)))
	}
}

// GetCacheStats 获取缓存统计信息
func (cs *cacheService) GetCacheStats() map[string]interface{} {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	stats := map[string]interface{}{
		"dns_cache_size":   len(cs.dnsCache),
		"regex_cache_size": len(cs.regexCache),
		"proxy_pool_size":  len(cs.proxyPool),
		"timestamp":        time.Now().Format(constants.TimeFormatDefault),
	}

	cs.logger.Debug(fmt.Sprintf("获取缓存统计信息: DNS缓存大小: %d, 正则缓存大小: %d, 代理池大小: %d", len(cs.dnsCache), len(cs.regexCache), len(cs.proxyPool)))
	return stats
}

// ClearCache 从缓存中删除所有值
func (cs *cacheService) ClearCache() {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	dnsCacheSize := len(cs.dnsCache)
	regexCacheSize := len(cs.regexCache)

	cs.dnsCache = make(map[string]*cacheItem)
	cs.regexCache = make(map[string]interface{})

	cs.logger.Info(fmt.Sprintf("所有缓存已清空，DNS缓存: %d, 正则缓存: %d, 时间: %s", dnsCacheSize, regexCacheSize, time.Now().Format(constants.TimeFormatDefault)))
}

// ClearAllCache 清空所有缓存（接口兼容方法）
func (cs *cacheService) ClearAllCache() {
	cs.ClearCache()
}

// StartCleanupRoutine 启动清理协程（接口兼容方法）
func (cs *cacheService) StartCleanupRoutine() {
	// 清理协程已在NewCacheService中启动，这里只是接口兼容
	cs.logger.Info("缓存清理协程已启动（接口兼容调用）")
}

// GetAllDNSCache 获取所有DNS缓存条目（用于热重载）
func (cs *cacheService) GetAllDNSCache() map[string]interface{} {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	result := make(map[string]interface{})
	now := time.Now()

	for key, item := range cs.dnsCache {
		// 跳过过期条目
		if now.After(item.expireAt) {
			continue
		}

		// 构造缓存条目信息
		result[key] = map[string]interface{}{
			"value":      item.value,
			"expires_at": item.expireAt,
			"created_at": item.expireAt.Add(-cs.defaultTTL), // 估算创建时间
			"ttl":        int(item.expireAt.Sub(now).Seconds()),
		}
	}

	cs.logger.Debug(fmt.Sprintf("获取所有DNS缓存条目: %d 个有效条目", len(result)))
	return result
}

// GetAllRegexCache 获取所有正则缓存条目（用于热重载）
func (cs *cacheService) GetAllRegexCache() map[string]interface{} {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	result := make(map[string]interface{})

	for pattern, compiled := range cs.regexCache {
		// 正则缓存通常不设置过期时间，直接返回
		result[pattern] = map[string]interface{}{
			"value":      compiled,
			"expires_at": time.Now().Add(24 * time.Hour), // 默认24小时过期
			"created_at": time.Now().Add(-time.Hour),     // 估算创建时间
			"ttl":        86400, // 24小时
		}
	}

	cs.logger.Debug(fmt.Sprintf("获取所有正则缓存条目: %d 个条目", len(result)))
	return result
}

// UpdateConfig 更新缓存服务配置
// 支持热重载缓存TTL、清理间隔等配置
func (cs *cacheService) UpdateConfig(configInterface interface{}) error {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	// 类型断言
	cfg, ok := configInterface.(*config.CacheConfig)
	if !ok {
		return fmt.Errorf("无效的配置类型，期望 *config.CacheConfig")
	}

	if cfg == nil {
		return fmt.Errorf("配置不能为空")
	}

	oldConfig := cs.config
	cs.config = cfg

	// 记录配置更新
	cs.logger.Info("开始更新缓存服务配置")

	// 1. 更新默认TTL
	if err := cs.updateDefaultTTL(cfg); err != nil {
		cs.logger.Warn(fmt.Sprintf("更新默认TTL失败: %v", err))
	}

	// 2. 更新清理间隔
	if err := cs.updateCleanupInterval(oldConfig, cfg); err != nil {
		cs.logger.Warn(fmt.Sprintf("更新清理间隔失败: %v", err))
	}

	// 3. 应用其他缓存配置
	cs.applyCacheSettings(cfg)

	cs.logger.Info("缓存服务配置更新完成")
	return nil
}

// updateDefaultTTL 更新默认TTL
func (cs *cacheService) updateDefaultTTL(cfg *config.CacheConfig) error {
	var newTTL time.Duration = constants.DefaultCacheTTL

	if cfg.Global != nil && cfg.Global.DefaultTTL != "" {
		if parsedTTL, err := time.ParseDuration(cfg.Global.DefaultTTL); err == nil {
			newTTL = parsedTTL
		} else {
			return fmt.Errorf("无效的默认TTL格式: %s", cfg.Global.DefaultTTL)
		}
	}

	if newTTL != cs.defaultTTL {
		oldTTL := cs.defaultTTL
		cs.defaultTTL = newTTL
		cs.logger.Info(fmt.Sprintf("默认TTL已更新: %s -> %s", oldTTL, newTTL))
	}

	return nil
}

// updateCleanupInterval 更新清理间隔
func (cs *cacheService) updateCleanupInterval(oldConfig, newConfig *config.CacheConfig) error {
	var newInterval time.Duration = constants.DefaultCleanupInterval

	if newConfig.Global != nil && newConfig.Global.CleanupInterval != "" {
		if parsedInterval, err := time.ParseDuration(newConfig.Global.CleanupInterval); err == nil {
			newInterval = parsedInterval
		} else {
			return fmt.Errorf("无效的清理间隔格式: %s", newConfig.Global.CleanupInterval)
		}
	}

	if newInterval != cs.cleanupInterval {
		oldInterval := cs.cleanupInterval
		cs.cleanupInterval = newInterval

		// 只有在间隔真正改变时才重启清理协程
		if cs.cleanupTicker != nil {
			cs.stopCleanupRoutine()
			cs.startCleanupRoutine()
		}

		cs.logger.Info(fmt.Sprintf("清理间隔已更新: %s -> %s", oldInterval, newInterval))
	}

	return nil
}

// applyCacheSettings 应用其他缓存设置
func (cs *cacheService) applyCacheSettings(cfg *config.CacheConfig) {
	if cfg.Global == nil {
		return
	}

	// 记录配置应用情况
	settings := make([]string, 0)

	if cfg.Global.Enabled {
		settings = append(settings, "全局缓存已启用")
	} else {
		settings = append(settings, "全局缓存已禁用")
	}

	if cfg.Global.EvictionPolicy != "" {
		settings = append(settings, fmt.Sprintf("淘汰策略: %s", cfg.Global.EvictionPolicy))
	}

	if cfg.Global.MaxMemoryUsage != "" {
		settings = append(settings, fmt.Sprintf("最大内存使用: %s", cfg.Global.MaxMemoryUsage))
	}

	if len(settings) > 0 {
		cs.logger.Info(fmt.Sprintf("应用缓存设置: %v", settings))
	}
}
