// Package services 提供各种服务实现
package basic

import (
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	mathrand "math/rand"
	"sort"
	"sync"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
	"flexproxy/internal/interfaces"
)

// ProxyQualityInfo 代理质量信息
type ProxyQualityInfo struct {
	ProxyURL        string                        // 代理URL
	SuccessCount    int64                         // 成功请求数
	FailureCount    int64                         // 失败请求数
	TotalRequests   int64                         // 总请求数
	AvgResponseTime time.Duration                 // 平均响应时间
	LastUsed        time.Time                     // 最后使用时间
	QualityScore    float64                       // 质量评分 (0-100)
	QualityTier     string                        // 质量等级: "premium", "standard", "backup"
	DomainStats     map[string]*DomainPerformance // 域名级别性能统计
	CreatedAt       time.Time                     // 创建时间
	UpdatedAt       time.Time                     // 更新时间
}

// DomainPerformance 域名性能统计
type DomainPerformance struct {
	Domain          string        // 域名
	SuccessCount    int64         // 成功次数
	FailureCount    int64         // 失败次数
	AvgResponseTime time.Duration // 平均响应时间
	LastUsed        time.Time     // 最后使用时间
	QualityScore    float64       // 质量评分
}

// ProxyService 提供代理管理功能
// proxyService 代理管理服务实现
type proxyService struct {
	mu            sync.RWMutex
	proxies       []string
	currentIndex  int
	failedProxies map[string]time.Time
	bannedIPs     map[string]time.Time
	bannedDomains map[string]time.Time
	trustedIPs    map[string]bool
	config        *config.Config
	cacheService  interfaces.CacheService
	logger        logger.Logger
	banCleaner    context.CancelFunc
	// 质量管理相关字段
	qualityInfo   map[string]*ProxyQualityInfo // 代理质量信息
	qualityMutex  sync.RWMutex                 // 质量信息读写锁
	// 随机数生成器
	secureRand    *mathrand.Rand               // 安全随机数生成器
	randMutex     sync.Mutex                   // 随机数生成器锁
}

// NewProxyService 创建新的代理服务实例
func NewProxyService(cacheService interfaces.CacheService, log logger.Logger) interfaces.ProxyService {
	if log == nil {
		log = logger.GetProxyServiceLogger().GetRawLogger()
	}

	// 创建安全的随机数生成器
	secureRand := mathrand.New(mathrand.NewSource(time.Now().UnixNano()))

	return &proxyService{
		proxies:       make([]string, 0),
		currentIndex:  0,
		failedProxies: make(map[string]time.Time),
		bannedIPs:     make(map[string]time.Time),
		bannedDomains: make(map[string]time.Time),
		trustedIPs:    make(map[string]bool),
		cacheService:  cacheService,
		logger:        log,
		qualityInfo:   make(map[string]*ProxyQualityInfo),
		secureRand:    secureRand,
	}
}

// GetNextProxy 获取下一个可用代理
func (ps *proxyService) GetNextProxy() (string, error) {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	if len(ps.proxies) == 0 {
		ps.logger.Error("代理池为空")
		return "", errors.NewFlexProxyError(
			errors.ErrTypeProxy,
			errors.ErrCodeProxyNotFound,
			"代理池为空",
		)
	}

	// 根据配置的IP轮换模式选择代理
	if ps.config != nil {
		switch ps.config.Global.IPRotationMode {
		case constants.StrategyRandom:
			return ps.getRandomProxy()
		case constants.StrategySmart:
			return ps.getSmartProxy()
		case constants.StrategyQuality:
			return ps.getQualityProxy()
		}
	}

	// 默认顺序轮换
	return ps.getSequentialProxy()
}

// getSequentialProxy 顺序获取代理
func (ps *proxyService) getSequentialProxy() (string, error) {
	startIndex := ps.currentIndex

	for i := 0; i < len(ps.proxies); i++ {
		proxy := ps.proxies[ps.currentIndex]
		ps.currentIndex = (ps.currentIndex + 1) % len(ps.proxies)

		// 检查代理是否可用
		if ps.isProxyAvailable(proxy) {
			ps.logger.Debug(fmt.Sprintf("选择代理: %s, 策略: %s", proxy, constants.StrategySequential))
			return proxy, nil
		}

		// 如果回到起始位置，说明所有代理都不可用
		if ps.currentIndex == startIndex {
			break
		}
	}

	ps.logger.Error("没有可用的代理")
	return "", errors.WrapErrorWithDetails(
		nil,
		errors.ErrTypeProxy,
		errors.ErrCodeProxyUnavailable,
		"没有可用的代理",
		"",
	)
}

// getRandomProxy 随机获取代理
func (ps *proxyService) getRandomProxy() (string, error) {
	availableProxies := make([]string, 0)

	for _, proxy := range ps.proxies {
		if ps.isProxyAvailable(proxy) {
			availableProxies = append(availableProxies, proxy)
		}
	}

	if len(availableProxies) == 0 {
		ps.logger.Error("没有可用的代理")
		return "", errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeProxy,
			errors.ErrCodeProxyUnavailable,
			"没有可用的代理",
			"",
		)
	}

	// 使用增强的随机选择算法
	proxy, err := ps.selectRandomProxySecurely(availableProxies)
	if err != nil {
		ps.logger.Error("安全随机选择失败，回退到基础随机", err)
		// 回退到基础随机选择
		ps.randMutex.Lock()
		index := ps.secureRand.Intn(len(availableProxies))
		ps.randMutex.Unlock()
		proxy = availableProxies[index]
	}

	ps.logger.Debug(fmt.Sprintf("随机选择代理: %s, 策略: %s, 可用数量: %d", proxy, constants.StrategyRandom, len(availableProxies)))
	return proxy, nil
}

// getQualityProxy 基于质量获取代理
// 实现完整的质量评估算法，根据成功率、响应时间、使用频率等多维度指标选择最优代理
func (ps *proxyService) getQualityProxy() (string, error) {
	// 获取所有可用代理
	availableProxies := make([]string, 0)
	for _, proxy := range ps.proxies {
		if ps.isProxyAvailable(proxy) {
			availableProxies = append(availableProxies, proxy)
		}
	}

	if len(availableProxies) == 0 {
		ps.logger.Error(constants.ErrMsgNoProxyAvailable)
		return "", errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeProxy,
			errors.ErrCodeProxyUnavailable,
			constants.ErrMsgNoProxyAvailable,
			"",
		)
	}

	// 为每个可用代理计算综合质量评分
	type proxyScore struct {
		proxy string
		score float64
	}

	proxyScores := make([]proxyScore, 0, len(availableProxies))

	// 使用读锁计算评分
	ps.qualityMutex.RLock()
	for _, proxy := range availableProxies {
		score := ps.calculateComprehensiveQualityScore(proxy)
		proxyScores = append(proxyScores, proxyScore{
			proxy: proxy,
			score: score,
		})
	}
	ps.qualityMutex.RUnlock()

	// 按质量评分降序排序
	sort.Slice(proxyScores, func(i, j int) bool {
		return proxyScores[i].score > proxyScores[j].score
	})

	// 选择最高质量的代理
	bestProxy := proxyScores[0].proxy
	bestScore := proxyScores[0].score

	ps.logger.Debug(fmt.Sprintf("质量选择代理: %s, 策略: %s, 质量评分: %.2f, 可用数量: %d",
		bestProxy, constants.StrategyQuality, bestScore, len(availableProxies)))

	// 更新代理使用时间
	ps.updateProxyUsage(bestProxy)

	return bestProxy, nil
}

// getSmartProxy 智能获取代理
func (ps *proxyService) getSmartProxy() (string, error) {
	// 智能模式：根据当前情况选择最合适的策略
	// 1. 如果有高质量代理，优先使用
	// 2. 否则使用随机选择避免模式化

	availableProxies := make([]string, 0)
	for _, proxy := range ps.proxies {
		if ps.isProxyAvailable(proxy) {
			availableProxies = append(availableProxies, proxy)
		}
	}

	if len(availableProxies) == 0 {
		ps.logger.Error("没有可用的代理")
		return "", errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeProxy,
			errors.ErrCodeProxyUnavailable,
			"没有可用的代理",
			"",
		)
	}

	// 智能选择：如果失败代理较少，使用顺序选择；否则使用随机选择
	var proxy string
	if len(ps.failedProxies) < len(ps.proxies)/2 {
		// 失败代理较少，使用顺序选择保持稳定性
		proxy = ps.getSequentialProxyFromList(availableProxies)
	} else {
		// 失败代理较多，使用随机选择增加成功概率
		index := time.Now().UnixNano() % int64(len(availableProxies))
		proxy = availableProxies[index]
	}

	ps.logger.Debug(fmt.Sprintf("智能选择代理: %s, 策略: %s, 可用数量: %d, 失败数量: %d", proxy, constants.StrategySmart, len(availableProxies), len(ps.failedProxies)))
	return proxy, nil
}

// calculateComprehensiveQualityScore 计算综合质量评分
// 综合考虑成功率、响应时间、使用频率、稳定性等多个维度
func (ps *proxyService) calculateComprehensiveQualityScore(proxy string) float64 {
	qualityInfo, exists := ps.qualityInfo[proxy]
	if !exists {
		// 新代理给予默认评分，稍微偏向于尝试新代理
		return constants.DefaultQualityScore * 100 + 10 // 60分，略高于默认50分
	}

	// 如果请求数太少，给予较高的初始评分以鼓励使用
	if qualityInfo.TotalRequests < constants.MinRequestsForStats {
		return constants.DefaultQualityScore * 100 + 5 // 55分
	}

	// 计算成功率评分 (0-100)
	successRate := float64(qualityInfo.SuccessCount) / float64(qualityInfo.TotalRequests)
	successScore := successRate * 100

	// 计算响应时间评分 (0-100)
	responseTimeScore := ps.calculateResponseTimeScore(qualityInfo.AvgResponseTime)

	// 计算稳定性评分 (0-100) - 基于最近的使用情况
	stabilityScore := ps.calculateStabilityScore(qualityInfo)

	// 计算使用频率评分 (0-100) - 避免过度使用单一代理
	usageScore := ps.calculateUsageScore(qualityInfo)

	// 加权计算最终评分
	finalScore := successScore*constants.ProxySuccessRateWeight +
		responseTimeScore*constants.ProxyResponseTimeWeight +
		stabilityScore*constants.StabilityWeight +
		usageScore*constants.UsageBalanceWeight

	// 确保评分在合理范围内
	if finalScore < 0 {
		finalScore = 0
	} else if finalScore > 100 {
		finalScore = 100
	}

	return finalScore
}

// calculateResponseTimeScore 计算响应时间评分
func (ps *proxyService) calculateResponseTimeScore(avgResponseTime time.Duration) float64 {
	if avgResponseTime <= 0 {
		return 100 // 没有响应时间数据时给满分
	}

	responseTimeMs := float64(avgResponseTime.Milliseconds())

	// 响应时间评分算法：
	// 0-500ms: 100分
	// 500-2000ms: 线性递减到60分
	// 2000-5000ms: 线性递减到20分
	// >5000ms: 0分
	if responseTimeMs <= 500 {
		return 100
	} else if responseTimeMs <= 2000 {
		return 100 - (responseTimeMs-500)/1500*40 // 从100分递减到60分
	} else if responseTimeMs <= 5000 {
		return 60 - (responseTimeMs-2000)/3000*40 // 从60分递减到20分
	} else {
		return 0
	}
}

// calculateStabilityScore 计算稳定性评分
func (ps *proxyService) calculateStabilityScore(qualityInfo *ProxyQualityInfo) float64 {
	// 基于最近使用时间和总体表现计算稳定性
	timeSinceLastUse := time.Since(qualityInfo.LastUsed)

	// 如果很久没有使用，稳定性评分降低
	if timeSinceLastUse > 24*time.Hour {
		return 30 // 长时间未使用，稳定性未知
	} else if timeSinceLastUse > 6*time.Hour {
		return 60 // 较长时间未使用
	} else if timeSinceLastUse > 1*time.Hour {
		return 80 // 最近使用过
	} else {
		return 100 // 刚刚使用过
	}
}

// calculateUsageScore 计算使用频率评分
func (ps *proxyService) calculateUsageScore(qualityInfo *ProxyQualityInfo) float64 {
	// 避免过度使用单一代理，鼓励负载均衡
	totalProxies := len(ps.proxies)
	if totalProxies <= 1 {
		return 100 // 只有一个代理时不考虑使用频率
	}

	// 计算理想的平均使用次数
	var totalRequests int64
	for _, info := range ps.qualityInfo {
		totalRequests += info.TotalRequests
	}

	if totalRequests == 0 {
		return 100 // 没有使用记录时给满分
	}

	idealUsage := float64(totalRequests) / float64(totalProxies)
	actualUsage := float64(qualityInfo.TotalRequests)

	// 使用率评分：接近理想使用率时得分最高
	usageRatio := actualUsage / idealUsage
	if usageRatio <= 0.5 {
		return 100 // 使用不足，鼓励使用
	} else if usageRatio <= 1.0 {
		return 100 - (usageRatio-0.5)*40 // 从100分递减到80分
	} else if usageRatio <= 2.0 {
		return 80 - (usageRatio-1.0)*60 // 从80分递减到20分
	} else {
		return 20 // 过度使用，降低评分
	}
}

// updateProxyUsage 更新代理使用记录
func (ps *proxyService) updateProxyUsage(proxy string) {
	ps.qualityMutex.Lock()
	defer ps.qualityMutex.Unlock()

	qualityInfo, exists := ps.qualityInfo[proxy]
	if !exists {
		qualityInfo = &ProxyQualityInfo{
			ProxyURL:     proxy,
			QualityScore: constants.DefaultQualityScore * 100,
			QualityTier:  constants.QualityTierStandard,
			DomainStats:  make(map[string]*DomainPerformance),
			CreatedAt:    time.Now(),
		}
		ps.qualityInfo[proxy] = qualityInfo
	}

	qualityInfo.LastUsed = time.Now()
	qualityInfo.UpdatedAt = time.Now()
}

// getSequentialProxyFromList 从给定列表中顺序选择代理
func (ps *proxyService) getSequentialProxyFromList(proxies []string) string {
	if len(proxies) == 0 {
		return ""
	}

	// 使用当前索引在可用代理列表中选择
	index := ps.currentIndex % len(proxies)
	ps.currentIndex++
	return proxies[index]
}

// isProxyAvailable 检查代理是否可用
func (ps *proxyService) isProxyAvailable(proxy string) bool {
	// 检查是否在失败列表中
	if failTime, exists := ps.failedProxies[proxy]; exists {
		// 检查冷却时间
		cooldownTime := constants.DefaultRetryInterval
		if ps.config != nil && ps.config.Global.RetryProxyCooldownTime > 0 {
			cooldownTime = time.Duration(ps.config.Global.RetryProxyCooldownTime) * time.Second
		}

		if time.Since(failTime) < cooldownTime {
			return false
		}

		// 冷却时间已过，移除失败记录
		delete(ps.failedProxies, proxy)
		ps.logger.Debug(fmt.Sprintf("代理冷却时间已过，重新可用: %s", proxy))
	}

	return true
}

// MarkProxyFailed 标记代理失败
func (ps *proxyService) MarkProxyFailed(proxy string) {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	ps.failedProxies[proxy] = time.Now()
	ps.logger.Warn(fmt.Sprintf("代理标记为失败: %s, 失败数量: %d, 时间: %s", proxy, len(ps.failedProxies), time.Now().Format(constants.TimeFormatDefault)))
}

// MarkProxySuccess 标记代理成功
func (ps *proxyService) MarkProxySuccess(proxy string) {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	// 移除失败记录
	delete(ps.failedProxies, proxy)
	ps.logger.Debug(fmt.Sprintf("代理标记为成功: %s, 剩余失败数量: %d", proxy, len(ps.failedProxies)))
}

// UpdateProxyQuality 更新代理质量信息
// 根据请求成功/失败和响应时间更新代理的质量评估
func (ps *proxyService) UpdateProxyQuality(proxy string, success bool, responseTime time.Duration, domain string) {
	ps.qualityMutex.Lock()
	defer ps.qualityMutex.Unlock()

	qualityInfo, exists := ps.qualityInfo[proxy]
	if !exists {
		qualityInfo = &ProxyQualityInfo{
			ProxyURL:     proxy,
			QualityScore: constants.DefaultQualityScore * 100,
			QualityTier:  constants.QualityTierStandard,
			DomainStats:  make(map[string]*DomainPerformance),
			CreatedAt:    time.Now(),
		}
		ps.qualityInfo[proxy] = qualityInfo
	}

	// 更新基本统计信息
	qualityInfo.TotalRequests++
	qualityInfo.LastUsed = time.Now()
	qualityInfo.UpdatedAt = time.Now()

	if success {
		qualityInfo.SuccessCount++
	} else {
		qualityInfo.FailureCount++
	}

	// 更新平均响应时间（使用指数移动平均）
	if responseTime > 0 {
		if qualityInfo.AvgResponseTime == 0 {
			qualityInfo.AvgResponseTime = responseTime
		} else {
			// 使用指数移动平均平滑响应时间
			smoothingFactor := constants.ProxyResponseTimeSmoothingFactor
			qualityInfo.AvgResponseTime = time.Duration(
				float64(qualityInfo.AvgResponseTime)*smoothingFactor +
					float64(responseTime)*(1-smoothingFactor),
			)
		}
	}

	// 更新域名级别统计
	if domain != "" {
		ps.updateDomainStats(qualityInfo, domain, success, responseTime)
	}

	// 重新计算质量评分
	qualityInfo.QualityScore = ps.calculateComprehensiveQualityScore(proxy)

	// 更新质量等级
	ps.updateQualityTier(qualityInfo)

	ps.logger.Debug(fmt.Sprintf("代理质量已更新: %s, 成功: %t, 响应时间: %v, 质量评分: %.2f, 等级: %s",
		proxy, success, responseTime, qualityInfo.QualityScore, qualityInfo.QualityTier))
}

// selectRandomProxySecurely 使用加密安全的随机数选择代理
func (ps *proxyService) selectRandomProxySecurely(proxies []string) (string, error) {
	if len(proxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理列表为空")
	}

	if len(proxies) == 1 {
		return proxies[0], nil
	}

	// 尝试使用加密安全的随机数
	maxBig := big.NewInt(int64(len(proxies)))
	randomBig, err := rand.Int(rand.Reader, maxBig)
	if err != nil {
		// 如果加密随机数失败，返回错误让调用者回退
		return "", errors.WrapError(err, errors.ErrTypeProxy, errors.ErrCodeProxyUnavailable, "加密随机数生成失败")
	}

	index := int(randomBig.Int64())
	return proxies[index], nil
}

// generateWeightedRandomIndex 生成加权随机索引（用于质量代理选择）
func (ps *proxyService) generateWeightedRandomIndex(weights []float64) (int, error) {
	if len(weights) == 0 {
		return 0, errors.NewError(errors.ErrTypeProxy, errors.ErrCodeProxyUnavailable, "权重列表为空")
	}

	// 计算总权重
	totalWeight := 0.0
	for _, weight := range weights {
		totalWeight += weight
	}

	if totalWeight <= 0 {
		return 0, errors.NewError(errors.ErrTypeProxy, errors.ErrCodeProxyUnavailable, "总权重无效")
	}

	// 生成0-1之间的随机数
	randomFloat, err := ps.generateSecureRandomFloat()
	if err != nil {
		return 0, err
	}

	// 按权重选择
	target := randomFloat * totalWeight
	currentWeight := 0.0

	for i, weight := range weights {
		currentWeight += weight
		if target <= currentWeight {
			return i, nil
		}
	}

	// 回退到最后一个索引
	return len(weights) - 1, nil
}

// generateSecureRandomFloat 生成加密安全的0-1之间的随机浮点数
func (ps *proxyService) generateSecureRandomFloat() (float64, error) {
	// 生成64位随机整数
	maxBig := big.NewInt(1 << 53) // 使用53位精度避免浮点数精度问题
	randomBig, err := rand.Int(rand.Reader, maxBig)
	if err != nil {
		return 0, errors.WrapError(err, errors.ErrTypeProxy, errors.ErrCodeProxyUnavailable, "随机浮点数生成失败")
	}

	// 转换为0-1之间的浮点数
	randomFloat := float64(randomBig.Int64()) / float64(1<<53)
	return randomFloat, nil
}

// shuffleProxiesSecurely 使用Fisher-Yates算法安全地打乱代理列表
func (ps *proxyService) shuffleProxiesSecurely(proxies []string) error {
	if len(proxies) <= 1 {
		return nil
	}

	// Fisher-Yates洗牌算法
	for i := len(proxies) - 1; i > 0; i-- {
		// 生成0到i之间的随机索引
		maxBig := big.NewInt(int64(i + 1))
		randomBig, err := rand.Int(rand.Reader, maxBig)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeProxy, errors.ErrCodeProxyUnavailable, "洗牌算法随机数生成失败")
		}

		j := int(randomBig.Int64())

		// 交换元素
		proxies[i], proxies[j] = proxies[j], proxies[i]
	}

	return nil
}

// updateDomainStats 更新域名级别的性能统计
func (ps *proxyService) updateDomainStats(qualityInfo *ProxyQualityInfo, domain string, success bool, responseTime time.Duration) {
	domainStats, exists := qualityInfo.DomainStats[domain]
	if !exists {
		domainStats = &DomainPerformance{
			Domain: domain,
		}
		qualityInfo.DomainStats[domain] = domainStats
	}

	domainStats.LastUsed = time.Now()
	if success {
		domainStats.SuccessCount++
	} else {
		domainStats.FailureCount++
	}

	// 更新域名级别的平均响应时间
	if responseTime > 0 {
		if domainStats.AvgResponseTime == 0 {
			domainStats.AvgResponseTime = responseTime
		} else {
			smoothingFactor := constants.ProxyResponseTimeSmoothingFactor
			domainStats.AvgResponseTime = time.Duration(
				float64(domainStats.AvgResponseTime)*smoothingFactor +
					float64(responseTime)*(1-smoothingFactor),
			)
		}
	}

	// 计算域名级别的质量评分
	totalRequests := domainStats.SuccessCount + domainStats.FailureCount
	if totalRequests > 0 {
		successRate := float64(domainStats.SuccessCount) / float64(totalRequests)
		responseTimeScore := ps.calculateResponseTimeScore(domainStats.AvgResponseTime)
		domainStats.QualityScore = successRate*constants.ProxySuccessRateWeight +
			responseTimeScore/100*constants.ProxyResponseTimeWeight
	}
}

// updateQualityTier 根据质量评分更新代理等级
func (ps *proxyService) updateQualityTier(qualityInfo *ProxyQualityInfo) {
	score := qualityInfo.QualityScore

	if score >= 80 {
		qualityInfo.QualityTier = constants.QualityTierPremium
	} else if score >= 50 {
		qualityInfo.QualityTier = constants.QualityTierStandard
	} else {
		qualityInfo.QualityTier = constants.QualityTierBackup
	}
}

// GetProxyQualityInfo 获取代理质量信息
func (ps *proxyService) GetProxyQualityInfo(proxy string) (*ProxyQualityInfo, bool) {
	ps.qualityMutex.RLock()
	defer ps.qualityMutex.RUnlock()

	info, exists := ps.qualityInfo[proxy]
	return info, exists
}

// GetQualityStats 获取质量统计信息
func (ps *proxyService) GetQualityStats() map[string]interface{} {
	ps.qualityMutex.RLock()
	defer ps.qualityMutex.RUnlock()

	stats := make(map[string]interface{})
	tierCounts := make(map[string]int)
	totalScore := 0.0
	count := 0

	for _, info := range ps.qualityInfo {
		tierCounts[info.QualityTier]++
		totalScore += info.QualityScore
		count++
	}

	if count > 0 {
		stats["average_quality_score"] = totalScore / float64(count)
	} else {
		stats["average_quality_score"] = 0.0
	}

	stats["tier_counts"] = tierCounts
	stats["total_proxies_with_quality_data"] = count

	return stats
}

// GetProxyCount 获取代理数量
func (ps *proxyService) GetProxyCount() int {
	ps.mu.RLock()
	defer ps.mu.RUnlock()
	return len(ps.proxies)
}

// InitBanSystem 初始化封禁系统
func (ps *proxyService) InitBanSystem(cfg interface{}) {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	// 类型断言
	globalCfg, ok := cfg.(*config.Config)
	if !ok {
		ps.logger.Error("InitBanSystem: 无效的配置类型")
		return
	}
	ps.config = globalCfg

	// 初始化被阻止的IP
	for _, ip := range globalCfg.Global.BlockedIPs {
		ps.bannedIPs[ip] = time.Now().Add(24 * time.Hour) // 默认24小时
	}

	// 初始化被阻止的域名
	for _, domainConfig := range globalCfg.Global.BannedDomains {
		var domain string
		if domainConfig.Domain != "" {
			domain = domainConfig.Domain
		} else {
			continue
		}

		// 处理持续时间
		var duration time.Duration
		switch v := domainConfig.Duration.(type) {
		case int:
			duration = time.Duration(v) * time.Second
		case string:
			if v == "reboot" {
				duration = 24 * time.Hour // 默认24小时
			} else {
				continue
			}
		default:
			duration = 24 * time.Hour // 默认24小时
		}

		ps.bannedDomains[domain] = time.Now().Add(duration)
	}

	// 初始化受信任的IP
	for _, ip := range globalCfg.Global.TrustedIPs {
		ps.trustedIPs[ip] = true
	}

	ps.logger.Info(fmt.Sprintf("封禁系统已初始化，封禁IP: %d, 封禁域名: %d, 受信任IP: %d", len(ps.bannedIPs), len(ps.bannedDomains), len(ps.trustedIPs)))
}

// StartBanCleaner 启动封禁清理器
func (ps *proxyService) StartBanCleaner(ctx context.Context) {
	cleanerCtx, cancel := context.WithCancel(ctx)
	ps.banCleaner = cancel

	go func() {
		ticker := time.NewTicker(10 * time.Minute) // 每10分钟清理一次
		defer ticker.Stop()

		for {
			select {
			case <-cleanerCtx.Done():
				return
			case <-ticker.C:
				ps.cleanExpiredBans()
			}
		}
	}()

	ps.logger.Info("封禁清理器已启动")
}

// cleanExpiredBans 清理过期的封禁
func (ps *proxyService) cleanExpiredBans() {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	now := time.Now()
	expiredIPs := 0
	expiredDomains := 0

	// 清理过期的IP封禁
	for ip, expireTime := range ps.bannedIPs {
		if now.After(expireTime) {
			delete(ps.bannedIPs, ip)
			expiredIPs++
			ps.logger.Debug(fmt.Sprintf("IP解封: %s", ip))
		}
	}

	// 清理过期的域名封禁
	for domain, expireTime := range ps.bannedDomains {
		if now.After(expireTime) {
			delete(ps.bannedDomains, domain)
			expiredDomains++
			ps.logger.Debug(fmt.Sprintf("域名解封: %s", domain))
		}
	}

	if expiredIPs > 0 || expiredDomains > 0 {
		ps.logger.Info(fmt.Sprintf("清理过期封禁项完成，清理IP: %d, 清理域名: %d, 剩余封禁IP: %d, 剩余封禁域名: %d", expiredIPs, expiredDomains, len(ps.bannedIPs), len(ps.bannedDomains)))
	}
}

// BanIP 封禁IP
func (ps *proxyService) BanIP(ip string, duration int) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	ps.bannedIPs[ip] = time.Now().Add(time.Duration(duration) * time.Second)
	ps.logger.Warn(fmt.Sprintf("IP已被封禁: %s, 持续时间: %d秒, 总封禁IP数: %d, 时间: %s", ip, duration, len(ps.bannedIPs), time.Now().Format(constants.TimeFormatDefault)))
	return nil
}

// BanDomain 封禁域名
func (ps *proxyService) BanDomain(domain string, duration int) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	ps.bannedDomains[domain] = time.Now().Add(time.Duration(duration) * time.Second)
	ps.logger.Warn(fmt.Sprintf("域名已被封禁: %s, 持续时间: %d秒, 总封禁域名数: %d, 时间: %s", domain, duration, len(ps.bannedDomains), time.Now().Format(constants.TimeFormatDefault)))
	return nil
}

// IsIPBanned 检查IP是否被封禁
func (ps *proxyService) IsIPBanned(ip string) bool {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	// 检查是否是受信任的IP
	if ps.trustedIPs[ip] {
		return false
	}

	// 检查是否被封禁
	if expireTime, exists := ps.bannedIPs[ip]; exists {
		return time.Now().Before(expireTime)
	}

	return false
}

// IsDomainBanned 检查域名是否被封禁
func (ps *proxyService) IsDomainBanned(domain string) bool {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	if expireTime, exists := ps.bannedDomains[domain]; exists {
		return time.Now().Before(expireTime)
	}

	return false
}

// UpdateProxyList 更新代理列表
func (ps *proxyService) UpdateProxyList(proxies []string) {
	ps.mu.Lock()
	ps.qualityMutex.Lock()
	defer ps.mu.Unlock()
	defer ps.qualityMutex.Unlock()

	oldCount := len(ps.proxies)
	ps.proxies = make([]string, len(proxies))
	copy(ps.proxies, proxies)
	ps.currentIndex = 0
	// 记录清理前的失败代理数量
	failedCount := len(ps.failedProxies)

	// 清理失败代理记录
	ps.failedProxies = make(map[string]time.Time)

	// 初始化新代理的质量信息
	ps.initializeQualityInfoForNewProxies(proxies)

	// 更新缓存服务中的代理池
	ps.cacheService.UpdateProxyPool(proxies)

	ps.logger.Info(fmt.Sprintf("代理列表已更新，旧数量: %d, 新数量: %d, 已清理失败记录: %d, 质量信息已初始化", oldCount, len(proxies), failedCount))
}

// initializeQualityInfoForNewProxies 为新代理初始化质量信息
func (ps *proxyService) initializeQualityInfoForNewProxies(proxies []string) {
	now := time.Now()

	// 创建新的质量信息映射，保留现有代理的历史数据
	newQualityInfo := make(map[string]*ProxyQualityInfo)

	for _, proxy := range proxies {
		if existingInfo, exists := ps.qualityInfo[proxy]; exists {
			// 保留现有代理的质量信息
			newQualityInfo[proxy] = existingInfo
		} else {
			// 为新代理创建初始质量信息
			newQualityInfo[proxy] = &ProxyQualityInfo{
				ProxyURL:     proxy,
				QualityScore: constants.DefaultQualityScore * 100,
				QualityTier:  constants.QualityTierStandard,
				DomainStats:  make(map[string]*DomainPerformance),
				CreatedAt:    now,
				UpdatedAt:    now,
			}
		}
	}

	ps.qualityInfo = newQualityInfo
	ps.logger.Debug(fmt.Sprintf("质量信息已初始化，代理数量: %d, 新代理数量: %d",
		len(newQualityInfo), len(proxies)-len(ps.qualityInfo)+len(newQualityInfo)))
}

// GetBanStats 获取封禁统计信息
func (ps *proxyService) GetBanStats() map[string]interface{} {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	stats := map[string]interface{}{
		"banned_ips":     len(ps.bannedIPs),
		"banned_domains": len(ps.bannedDomains),
		"trusted_ips":    len(ps.trustedIPs),
		"failed_proxies": len(ps.failedProxies),
		"total_proxies":  len(ps.proxies),
		"current_index":  ps.currentIndex,
	}

	ps.logger.Debug(fmt.Sprintf("获取封禁统计: 封禁IP: %d, 封禁域名: %d, 受信任IP: %d, 失败代理: %d, 总代理: %d, 当前索引: %d", len(ps.bannedIPs), len(ps.bannedDomains), len(ps.trustedIPs), len(ps.failedProxies), len(ps.proxies), ps.currentIndex))
	return stats
}

// Stop 停止代理服务
func (ps *proxyService) Stop() {
	if ps.banCleaner != nil {
		ps.banCleaner()
		ps.logger.Info(fmt.Sprintf("代理服务已停止，总代理: %d, 失败代理: %d, 封禁IP: %d, 封禁域名: %d", len(ps.proxies), len(ps.failedProxies), len(ps.bannedIPs), len(ps.bannedDomains)))
	}
}

// UpdateConfig 更新代理服务配置
// 支持热重载代理配置、封禁规则、轮换策略等
func (ps *proxyService) UpdateConfig(configInterface interface{}) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	// 类型断言
	cfg, ok := configInterface.(*config.Config)
	if !ok {
		return errors.NewFlexProxyError(
			errors.ErrTypeConfig,
			errors.ErrCodeInvalidInput,
			"无效的配置类型",
		)
	}

	if cfg == nil {
		return errors.NewFlexProxyError(
			errors.ErrTypeConfig,
			errors.ErrCodeInvalidInput,
			"配置不能为空",
		)
	}

	oldConfig := ps.config
	ps.config = cfg

	// 记录配置更新
	ps.logger.Info("开始更新代理服务配置")

	// 1. 更新代理轮换策略
	if oldConfig == nil || oldConfig.Global.IPRotationMode != cfg.Global.IPRotationMode {
		ps.logger.Info(fmt.Sprintf("代理轮换策略已更新: %s -> %s",
			getRotationModeString(oldConfig), cfg.Global.IPRotationMode))
	}

	// 2. 更新封禁系统配置
	if err := ps.updateBanSystemConfig(oldConfig, cfg); err != nil {
		ps.logger.Error(fmt.Sprintf("更新封禁系统配置失败: %v", err))
		// 不回滚，继续处理其他配置
	}

	// 3. 更新受信任IP列表
	ps.updateTrustedIPs(cfg.Global.TrustedIPs)

	ps.logger.Info("代理服务配置更新完成")
	return nil
}

// getRotationModeString 获取轮换模式字符串，处理空配置情况
func getRotationModeString(cfg *config.Config) string {
	if cfg == nil {
		return constants.StrategySequential // 默认策略
	}
	if cfg.Global.IPRotationMode == "" {
		return constants.StrategySequential
	}
	return cfg.Global.IPRotationMode
}

// updateBanSystemConfig 更新封禁系统配置
func (ps *proxyService) updateBanSystemConfig(oldConfig, newConfig *config.Config) error {
	// 清空现有封禁规则（保留运行时添加的封禁）
	configBasedBannedIPs := make(map[string]time.Time)
	configBasedBannedDomains := make(map[string]time.Time)

	// 重新初始化配置中的封禁IP
	for _, ipConfig := range newConfig.Global.GlobalBannedIPs {
		if ipConfig.IP == "" {
			continue
		}

		// 处理持续时间
		var duration time.Duration
		switch v := ipConfig.Duration.(type) {
		case int:
			duration = time.Duration(v) * time.Second
		case string:
			if v == "reboot" {
				duration = 24 * time.Hour // 默认24小时
			} else if parsedDuration, err := time.ParseDuration(v); err == nil {
				duration = parsedDuration
			} else {
				ps.logger.Warn(fmt.Sprintf("无效的封禁时长格式: %s, 使用默认24小时", v))
				duration = 24 * time.Hour
			}
		default:
			duration = 24 * time.Hour // 默认24小时
		}

		configBasedBannedIPs[ipConfig.IP] = time.Now().Add(duration)
	}

	// 重新初始化配置中的封禁域名
	for _, domainConfig := range newConfig.Global.BannedDomains {
		var domain string
		if domainConfig.Domain != "" {
			domain = domainConfig.Domain
		} else {
			continue
		}

		// 处理持续时间
		var duration time.Duration
		switch v := domainConfig.Duration.(type) {
		case int:
			duration = time.Duration(v) * time.Second
		case string:
			if v == "reboot" {
				duration = 24 * time.Hour // 默认24小时
			} else if parsedDuration, err := time.ParseDuration(v); err == nil {
				duration = parsedDuration
			} else {
				ps.logger.Warn(fmt.Sprintf("无效的域名封禁时长格式: %s, 使用默认24小时", v))
				duration = 24 * time.Hour
			}
		default:
			duration = 24 * time.Hour // 默认24小时
		}

		configBasedBannedDomains[domain] = time.Now().Add(duration)
	}

	// 合并配置封禁和运行时封禁
	// 这里采用增量更新策略，只更新配置中的封禁规则，保留运行时添加的封禁
	for ip, expireTime := range configBasedBannedIPs {
		ps.bannedIPs[ip] = expireTime
	}

	for domain, expireTime := range configBasedBannedDomains {
		ps.bannedDomains[domain] = expireTime
	}

	ps.logger.Info(fmt.Sprintf("封禁系统配置已更新，配置封禁IP: %d, 配置封禁域名: %d, 总封禁IP: %d, 总封禁域名: %d",
		len(configBasedBannedIPs), len(configBasedBannedDomains), len(ps.bannedIPs), len(ps.bannedDomains)))

	return nil
}

// updateTrustedIPs 更新受信任IP列表
func (ps *proxyService) updateTrustedIPs(trustedIPs []string) {
	// 清空现有受信任IP列表
	ps.trustedIPs = make(map[string]bool)

	// 重新添加受信任IP
	for _, ip := range trustedIPs {
		if ip != "" {
			ps.trustedIPs[ip] = true
		}
	}

	ps.logger.Info(fmt.Sprintf("受信任IP列表已更新，数量: %d", len(ps.trustedIPs)))
}

// UnbanIP 解封IP
func (ps *proxyService) UnbanIP(ip string) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	if _, exists := ps.bannedIPs[ip]; !exists {
		ps.logger.Warn(fmt.Sprintf("尝试解封不存在的IP: %s", ip))
		return errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidInput,
			"IP不在封禁列表中",
			fmt.Sprintf("ip: %s", ip),
		)
	}

	delete(ps.bannedIPs, ip)
	ps.logger.Info(fmt.Sprintf("IP已解封: %s, 剩余封禁IP: %d", ip, len(ps.bannedIPs)))
	return nil
}

// UnbanDomain 解封域名
func (ps *proxyService) UnbanDomain(domain string) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	if _, exists := ps.bannedDomains[domain]; !exists {
		ps.logger.Warn(fmt.Sprintf("尝试解封不存在的域名: %s", domain))
		return errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidInput,
			"域名不在封禁列表中",
			fmt.Sprintf("domain: %s", domain),
		)
	}

	delete(ps.bannedDomains, domain)
	ps.logger.Info(fmt.Sprintf("域名已解封: %s, 剩余封禁域名: %d", domain, len(ps.bannedDomains)))
	return nil
}

// GetProxyStats 获取代理统计信息
func (ps *proxyService) GetProxyStats() map[string]interface{} {
	ps.mu.RLock()
	ps.qualityMutex.RLock()
	defer ps.mu.RUnlock()
	defer ps.qualityMutex.RUnlock()

	availableCount := 0
	for _, proxy := range ps.proxies {
		if ps.isProxyAvailable(proxy) {
			availableCount++
		}
	}

	// 计算质量统计
	qualityStats := ps.calculateQualityStatistics()

	stats := map[string]interface{}{
		"total_proxies":     len(ps.proxies),
		"available_proxies": availableCount,
		"failed_proxies":    len(ps.failedProxies),
		"current_index":     ps.currentIndex,
		"rotation_mode":     "sequential",
		"quality_stats":     qualityStats,
	}

	if ps.config != nil {
		stats["rotation_mode"] = ps.config.Global.IPRotationMode
	}

	ps.logger.Debug(fmt.Sprintf("获取代理统计: 总代理: %d, 可用代理: %d, 失败代理: %d, 当前索引: %d, 轮换模式: %v, 平均质量: %.2f",
		len(ps.proxies), availableCount, len(ps.failedProxies), ps.currentIndex, stats["rotation_mode"], qualityStats["average_score"]))
	return stats
}

// calculateQualityStatistics 计算质量统计信息
func (ps *proxyService) calculateQualityStatistics() map[string]interface{} {
	if len(ps.qualityInfo) == 0 {
		return map[string]interface{}{
			"average_score":    0.0,
			"tier_distribution": map[string]int{},
			"total_requests":   0,
		}
	}

	var totalScore float64
	var totalRequests int64
	tierCounts := make(map[string]int)

	for _, info := range ps.qualityInfo {
		totalScore += info.QualityScore
		totalRequests += info.TotalRequests
		tierCounts[info.QualityTier]++
	}

	return map[string]interface{}{
		"average_score":     totalScore / float64(len(ps.qualityInfo)),
		"tier_distribution": tierCounts,
		"total_requests":    totalRequests,
		"proxies_with_data": len(ps.qualityInfo),
	}
}

// ResetFailedProxies 重置失败代理记录
func (ps *proxyService) ResetFailedProxies() {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	failedCount := len(ps.failedProxies)
	ps.failedProxies = make(map[string]time.Time)

	ps.logger.Info(fmt.Sprintf("失败代理记录已重置，重置数量: %d, 总代理: %d", failedCount, len(ps.proxies)))
}
