// Package services 提供各种服务实现
package basic

import (
	"fmt"
	"sync"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
	"flexproxy/internal/infrastructure/trace"
	"flexproxy/internal/interfaces"
	"github.com/fsnotify/fsnotify"
)

// ConfigService 提供配置管理
// configService 配置服务实现
type configService struct {
	mu         sync.RWMutex
	config     *config.Config
	configFile string
	watcher    *fsnotify.Watcher
	callbacks  []func(interface{})
	logger     logger.Logger
	validator  *config.ConfigValidator
}

// NewConfigService 创建新的配置服务
func NewConfigService(log logger.Logger) interfaces.ConfigService {
	if log == nil {
		log = logger.GetLogger("config")
	}

	return &configService{
		logger:    log,
		callbacks: make([]func(interface{}), 0),
		validator: config.NewConfigValidator(),
	}
}

// LoadConfig 加载配置文件
func (cs *configService) LoadConfig(configFile string) (interface{}, error) {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	traceID := trace.GenerateTraceID()

	if configFile == "" {
		cs.logger.Info(fmt.Sprintf("未指定配置文件，将使用默认配置 [component=config_service, trace_id=%s]", traceID))
		return nil, nil
	}

	cs.logger.Info(fmt.Sprintf("开始加载配置文件 [component=config_service, config_file=%s, trace_id=%s]", configFile, traceID))

	cfg, err := config.LoadConfigFromYAML(configFile)
	if err != nil {
		cs.logger.Error(fmt.Sprintf("配置文件加载失败 [component=config_service, config_file=%s, trace_id=%s]", configFile, traceID))
		return nil, errors.WrapErrorWithDetails(
			err,
			errors.ErrTypeConfig,
			errors.ErrCodeConfigLoadFailed,
			"配置文件加载失败",
			fmt.Sprintf("config_file: %s", configFile),
		).WithTraceID(traceID)
	}

	// 验证配置
	if err := cs.validator.ValidateConfig(cfg); err != nil {
		cs.logger.Error(fmt.Sprintf("配置验证失败 [component=config_service, config_file=%s, validation_error=%s, trace_id=%s]", configFile, err.Error(), traceID))
		return nil, errors.WrapErrorWithDetails(
			err,
			errors.ErrTypeValidation,
			errors.ErrCodeConfigValidationFailed,
			"配置验证失败",
			fmt.Sprintf("config_file: %s", configFile),
		).WithTraceID(traceID)
	}

	cs.config = cfg
	cs.configFile = configFile
	cs.logger.Info(fmt.Sprintf("配置文件加载和验证成功 [component=config_service, config_file=%s, callback_count=%d, load_time=%s, trace_id=%s]", configFile, len(cs.callbacks), time.Now().Format(constants.TimeFormatDefault), traceID))

	// 通知所有回调函数
	for i, callback := range cs.callbacks {
		go func(idx int, cb func(interface{})) {
			cs.logger.Debug(fmt.Sprintf("执行配置变更回调 [component=config_service, callback_index=%d, trace_id=%s]", idx, traceID))
			cb(cfg)
		}(i, callback)
	}

	return cfg, nil
}

// GetConfig 返回当前配置
func (cs *configService) GetConfig() interface{} {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	return cs.config
}

// AddConfigChangeCallback 添加配置变更回调
func (cs *configService) AddConfigChangeCallback(callback func(interface{})) {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	cs.callbacks = append(cs.callbacks, callback)
	cs.logger.Debug(fmt.Sprintf("添加配置变更回调 [component=config_service, total_callbacks=%d]", len(cs.callbacks)))
}

// WatchConfig 监听配置文件变化
func (cs *configService) WatchConfig() error {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	// 如果还没有创建文件监听器，则创建一个
	if cs.watcher == nil && cs.configFile != "" {
		watcher, err := fsnotify.NewWatcher()
		if err != nil {
			cs.logger.Error(fmt.Sprintf("创建文件监听器失败 [component=config_service, config_file=%s]", cs.configFile))
			return errors.WrapErrorWithDetails(
				err,
				errors.ErrTypeSystem,
				errors.ErrCodeSystemError,
				"创建文件监听器失败",
				fmt.Sprintf("config_file: %s", cs.configFile),
			)
		}

		cs.watcher = watcher

		// 添加配置文件到监听列表
		err = cs.watcher.Add(cs.configFile)
		if err != nil {
			cs.logger.Error(fmt.Sprintf("添加配置文件监听失败 [component=config_service, config_file=%s]", cs.configFile))
			return errors.WrapErrorWithDetails(
				err,
				errors.ErrTypeSystem,
				errors.ErrCodeSystemError,
				"添加配置文件监听失败",
				fmt.Sprintf("config_file: %s", cs.configFile),
			)
		}

		// 启动监听协程
		go cs.watchConfigFile()
		cs.logger.Info(fmt.Sprintf("开始监听配置文件变化 [component=config_service, config_file=%s, callback_count=%d]", cs.configFile, len(cs.callbacks)))
	}

	return nil
}

// watchConfigFile 监听配置文件变化的协程
func (cs *configService) watchConfigFile() {
	cs.logger.Info("配置文件监听协程已启动 [component=config_service]")
	defer cs.logger.Info("配置文件监听协程已停止 [component=config_service]")

	for {
		select {
		case event, ok := <-cs.watcher.Events:
			if !ok {
				cs.logger.Warn("配置文件监听事件通道已关闭 [component=config_service]")
				return
			}

			// 检查是否是写入事件
			if event.Op&fsnotify.Write == fsnotify.Write {
				cs.logger.Info(fmt.Sprintf("检测到配置文件变化 [component=config_service, event_name=%s, event_op=%s, timestamp=%s]", event.Name, event.Op.String(), time.Now().Format(constants.TimeFormatDefault)))

				// 添加短暂延迟，避免文件写入过程中读取
				time.Sleep(constants.DefaultDebounceDelay)

				// 重新加载配置
				_, err := cs.LoadConfig(cs.configFile)
				if err != nil {
					if flexErr, ok := err.(*errors.FlexProxyError); ok {
						cs.logger.Error(fmt.Sprintf("重新加载配置文件失败 [component=config_service, error_type=%s, error_code=%s, trace_id=%s]", flexErr.Type, flexErr.Code, flexErr.TraceID))
					} else {
						cs.logger.Error(fmt.Sprintf("重新加载配置文件失败 [component=config_service, error=%s]", err.Error()))
					}
				} else {
					cs.logger.Info("配置文件热重载成功 [component=config_service]")
				}
			}

		case err, ok := <-cs.watcher.Errors:
			if !ok {
				cs.logger.Warn("配置文件监听错误通道已关闭 [component=config_service]")
				return
			}
			cs.logger.Error(fmt.Sprintf("配置文件监听错误 [component=config_service, error=%s]", err.Error()))
		}
	}
}

// Close 关闭配置服务
func (cs *configService) Close() error {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	if cs.watcher != nil {
		cs.logger.Info(fmt.Sprintf("关闭配置服务 [component=config_service, config_file=%s, callback_count=%d]", cs.configFile, len(cs.callbacks)))

		err := cs.watcher.Close()
		if err != nil {
			cs.logger.Error(fmt.Sprintf("关闭文件监听器失败 [component=config_service, error=%s]", err.Error()))
			return errors.WrapErrorWithDetails(
				err,
				errors.ErrTypeSystem,
				errors.ErrCodeSystemError,
				"关闭文件监听器失败",
				"",
			)
		}
		cs.watcher = nil
	}

	cs.logger.Info("配置服务已关闭 [component=config_service]")
	return nil
}
