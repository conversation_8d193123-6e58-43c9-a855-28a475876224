// Package interfaces 定义DNS配置提供者接口
package interfaces

import (
	"flexproxy/internal/config"
)

// DNSConfigProvider DNS配置提供者接口
// 遵循依赖倒置原则，DNSManager依赖此抽象接口而非具体实现
type DNSConfigProvider interface {
	// GetDNSConfig 获取当前DNS配置
	// 返回DNS服务配置，如果配置不存在则返回默认配置
	GetDNSConfig() (*config.DNSServiceConfig, error)
	
	// ValidateConfig 验证DNS配置的有效性
	// 在应用配置前进行验证，确保配置的正确性
	ValidateConfig(cfg *config.DNSServiceConfig) error
	
	// IsConfigAvailable 检查配置是否可用
	// 用于快速检查配置状态，避免不必要的配置获取
	IsConfigAvailable() bool
	
	// GetConfigVersion 获取配置版本信息
	// 用于配置变更检测和缓存失效
	GetConfigVersion() string
}

// DNSConfigWatcher DNS配置监听器接口
// 支持配置热重载的扩展接口
type DNSConfigWatcher interface {
	DNSConfigProvider
	
	// WatchConfigChanges 监听配置变更
	// 返回配置变更通知通道
	WatchConfigChanges() <-chan *config.DNSServiceConfig
	
	// StopWatching 停止配置监听
	StopWatching() error
}

// DNSConfigCache DNS配置缓存接口
// 支持配置缓存的扩展接口
type DNSConfigCache interface {
	DNSConfigProvider
	
	// CacheConfig 缓存配置
	CacheConfig(cfg *config.DNSServiceConfig) error
	
	// InvalidateCache 使缓存失效
	InvalidateCache() error
	
	// GetCachedConfig 获取缓存的配置
	GetCachedConfig() (*config.DNSServiceConfig, bool)
}
