package interfaces

import (
	"context"
	"crypto/tls"
	"net/http"
	"time"

	"github.com/fsnotify/fsnotify"
	"flexproxy/internal/config"
)

// ProxyStateResetter 代理状态重置接口
type ProxyStateResetter interface {
	ResetProxyState()
}

// ProxyManagerInterface 代理管理器接口
// 定义了代理管理器的核心功能，包括代理操作、质量管理、封禁管理等
type ProxyManagerInterface interface {
	// 基本代理操作
	GetProxy(mode string) (string, error)
	RotateProxy(domain ...string) (string, error)
	GetProxyForDomain(mode, domain string) (string, error)
	GetQualityProxy(tier, domain string) (string, error)

	// 代理质量管理
	UpdateProxyQuality(proxyURL string, success bool, responseTime time.Duration, domain string)
	AddToDomainPool(proxyURL, domain string)
	RemoveProxy(proxyURL string) error

	// 封禁管理
	BanIP(ip, durationStr, scope, resource string) error
	UnbanIP(ip string) error
	IsIPBanned(ip, resource, scope string) bool
	IsIPPermanentlyBlocked(ipOrDomain string) bool
	IsTrustedIP(ipOrDomain string) bool
	InitBanSystem(cfg interface{}) // 使用interface{}避免导入common包
	ResetBanSystem()               // 重置封禁系统初始化状态
	IsBanSystemInitialized() bool  // 检查封禁系统是否已初始化
	StartBanCleaner(ctx context.Context)

	// 代理池管理
	GetProxies() []string
	Count() int
	Reload() error
	GetAllProxies() []string

	// 生命周期管理
	Start(ctx context.Context) error
	Stop() error
	Watch() (*fsnotify.Watcher, error)
}

// SecurityService 安全服务接口
// 提供认证、授权、加密解密等安全功能
type SecurityService interface {
	// 认证相关
	Authenticate(r *http.Request) interface{} // 认证结果
	GenerateToken(userID string, scopes []string, duration time.Duration) (string, error)
	RevokeToken(token string) bool
	ValidateScope(token string, requiredScope string) bool

	// 加密解密
	Encrypt(plaintext string) (interface{}, error) // 加密结果
	Decrypt(ciphertext, nonce string) (string, error)

	// TLS配置
	GetTLSConfig() *tls.Config

	// 请求验证
	ValidateRequest(r *http.Request) error

	// 统计和管理
	GetAuthStats() map[string]interface{}
	CleanupExpiredTokens() int

	// 热重载支持
	GetConfig() interface{}                   // 获取当前配置
	UpdateConfig(config interface{}) error   // 更新配置
}

// MonitoringService 监控服务接口
// 提供系统监控、指标收集、健康检查等功能
type MonitoringService interface {
	// 指标记录
	RecordMetric(name string, value interface{}, metricType interface{}, tags map[string]string)
	RecordMetricFloat(name string, value float64, tags map[string]string) error
	GetMetrics() map[string]interface{}

	// 健康检查
	RegisterHealthCheck(name string, check interface{})
	RegisterHealthCheckFunc(name string, check func() error) error
	GetHealthStatus() map[string]interface{}

	// 生命周期管理
	Start() error
	Stop() error

	// HTTP服务器
	StartHTTPServer(addr string) error
	StopHTTPServer() error

	// 系统指标
	GetSystemMetrics() map[string]interface{}
	GetProxyMetrics() map[string]interface{}

	// 热重载支持
	GetConfig() interface{}                   // 获取当前配置
	UpdateConfig(config interface{}) error   // 更新配置
}

// PluginService 插件服务接口
// 提供插件加载、管理、钩子执行等功能
type PluginService interface {
	// 插件生命周期管理
	LoadPlugin(name, path string, config map[string]interface{}) error
	UnloadPlugin(name string) error
	EnablePlugin(name string) error
	DisablePlugin(name string) error
	ReloadPlugin(name string) error

	// 钩子管理
	ExecuteHook(hookType string, ctx interface{}) []interface{} // 使用interface{}避免导入common包
	RegisterHook(hookType, pluginName string, priority int) error
	UnregisterHook(hookType, pluginName string) error

	// 插件信息和统计
	GetPluginStats() interface{}         // 使用interface{}避免导入common包
	ListPlugins() map[string]interface{} // 使用interface{}避免导入common包

	// 配置管理
	SavePluginConfig(configPath string) error
	LoadPluginConfig(configPath string) error
}

// CacheService 缓存服务接口
// 提供DNS缓存、正则表达式缓存和代理池缓存功能
type CacheService interface {
	// DNS缓存
	GetDNSCache(key string) ([]string, bool)
	SetDNSCache(key string, value []string, ttl int)

	// 正则表达式缓存
	GetRegexCache(pattern string) (interface{}, bool)
	SetRegexCache(pattern string, value interface{})

	// 代理池缓存
	UpdateProxyPool(proxies []string)
	StartCleanupRoutine()
	GetCacheStats() map[string]interface{}
	ClearAllCache()

	// 热重载支持 - 获取所有缓存条目
	GetAllDNSCache() map[string]interface{}    // 获取所有DNS缓存条目
	GetAllRegexCache() map[string]interface{}  // 获取所有正则缓存条目

	// 配置热重载支持
	UpdateConfig(config interface{}) error // 使用interface{}避免导入config包
}

// ConfigService 配置服务接口
// 提供配置文件加载、监控、回调等功能
type ConfigService interface {
	// 配置加载和获取
	LoadConfig(configFile string) (interface{}, error) // 使用interface{}避免导入common包
	GetConfig() interface{}                            // 使用interface{}避免导入common包

	// 配置监控
	WatchConfig() error

	// 配置变更回调
	AddConfigChangeCallback(callback func(interface{})) // 使用interface{}避免导入common包

	// 生命周期管理
	Close() error
}

// TracingService 追踪服务接口
// 提供分布式追踪、链路跟踪等功能
type TracingService interface {
	IsEnabled() bool
	Enable() error
	Disable() error
	StartSpan(operationName string, parentSpanID string) (string, error)
	FinishSpan(spanID string) error
	AddSpanTag(spanID string, key string, value interface{}) error
	AddSpanLog(spanID string, level string, message string, fields map[string]interface{}) error
	GetSpan(spanID string) (interface{}, error)
	GetTraceSpans(traceID string) ([]interface{}, error)
	InjectHeaders(spanID string, headers map[string]string) error
	ExtractHeaders(headers map[string]string) (string, error)
	GenerateTraceID() string
	GetStats() map[string]interface{}
}

// PerformanceService 性能调优服务接口
// 提供任务调度、批处理、性能优化等功能
type PerformanceService interface {
	Start() error
	Stop() error
	SubmitTask(task interface{}) error
	SubmitSimpleTask(fn func() error) error
	AddToBatch(item interface{}) error
	SetBatchProcessor(processor func([]interface{}) error) error
	GetMetrics() map[string]interface{}
	GetWorkerPoolStats() map[string]interface{}
	GetBatchQueueStats() map[string]interface{}
	OptimizeGC() error
	SetGCPercent(percent int) error
}

// DebugService 调试服务接口
// 提供断点调试、监控、性能分析等功能
type DebugService interface {
	Start() error
	Stop() error
	IsEnabled() bool
	SetBreakpoint(id string, condition string, action func()) error
	RemoveBreakpoint(id string) error
	EnableBreakpoint(id string) error
	DisableBreakpoint(id string) error
	HitBreakpoint(id string, context map[string]interface{}) bool
	SetWatcher(id string, expression string, callback func(interface{})) error
	RemoveWatcher(id string) error
	UpdateWatcher(id string, value interface{}) error
	StartProfiling(profileType string, duration time.Duration) error
	StopProfiling(profileType string) ([]byte, error)
	GetDebugInfo() map[string]interface{}
	DumpStack() string
	ForceGC() error
	SetLogLevel(level string) error
}

// ProfilingService 性能分析服务接口
// 提供CPU、内存、协程等性能分析功能
type ProfilingService interface {
	Start() error
	Stop() error
	IsEnabled() bool
	StartCPUProfile(filename string) error
	StopCPUProfile() error
	WriteHeapProfile(filename string) error
	WriteGoroutineProfile(filename string) error
	WriteBlockProfile(filename string) error
	WriteMutexProfile(filename string) error
	GetProfileData(profileType string) ([]byte, error)
	SetSamplingRate(rate int) error
	GetSamplingRate() int
	SetMemProfileRate(rate int)
	GetMemProfileRate() int
	SetBlockProfileRate(rate int)
	GetBlockProfileRate() int
	SetMutexProfileFraction(rate int)
	GetMutexProfileFraction() int
	GetRuntimeStats() map[string]interface{}
	EnableProfiling(profileType string) error
	DisableProfiling(profileType string) error
}

// LogService 日志服务接口
type LogService interface {
	// 基本日志方法
	Info(msg string, args ...interface{})
	Warn(msg string, args ...interface{})
	Error(msg string, args ...interface{})
	Debug(msg string, args ...interface{})
	Fatal(msg string, args ...interface{})

	// 扩展方法
	WithTraceID(traceID string) LogService
	WithFields(fields map[string]interface{}) LogService
	LogError(err error, msg string, args ...interface{})
	GetLogger() interface{}

	// 配置热重载支持
	UpdateConfig(config interface{}) error // 使用interface{}避免导入config包
}

// DNSService DNS解析服务接口
type DNSService interface {
	Resolve(hostname string) ([]string, error)
	ReverseResolve(ip string) (string, error)
	SetMode(mode string) error
	SetCustomServers(servers []interface{}) error
	SetCacheTTL(ttl int)
	SetNoCache(noCache bool)
	SetTimeout(timeout time.Duration)
}

// Container 依赖注入容器接口
type Container interface {
	// 获取各种服务
	GetConfigService() ConfigService
	GetLogService() LogService
	GetCacheService() CacheService
	GetDNSService() DNSService
	GetProxyService() ProxyService
	GetTriggerService() TriggerService
	GetActionService() ActionService
	GetMonitoringService() MonitoringService
	GetRateLimitingService() RateLimitingService
	GetSecurityService() SecurityService
	GetPluginService() PluginService
	GetStrategyManager() interface{} // 避免导入strategy包

	// 新增的高级服务
	GetTracingService() TracingService
	GetPerformanceService() PerformanceService
	GetDebugService() DebugService
	GetProfilingService() ProfilingService
	GetAdvancedConfigService() AdvancedConfigService

	// 生命周期管理
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
}

// ProxyService 代理管理服务接口
type ProxyService interface {
	GetNextProxy() (string, error)
	MarkProxyFailed(proxy string)
	MarkProxySuccess(proxy string)
	GetProxyCount() int
	InitBanSystem(config interface{}) // 使用interface{}避免导入common包
	StartBanCleaner(ctx context.Context)
	BanIP(ip string, duration int) error
	BanDomain(domain string, duration int) error
	IsIPBanned(ip string) bool
	IsDomainBanned(domain string) bool
	UnbanIP(ip string) error
	UnbanDomain(domain string) error
	UpdateProxyList(proxies []string)
	GetBanStats() map[string]interface{}
	GetProxyStats() map[string]interface{}
	ResetFailedProxies()
	Stop()

	// 配置热重载支持
	UpdateConfig(config interface{}) error // 使用interface{}避免导入config包
}

// TriggerService 触发器服务接口
type TriggerService interface {
	// 基本触发器方法
	EvaluateTriggers(req *http.Request, resp *http.Response, data map[string]interface{}) ([]string, error)
	RegisterTrigger(name string, trigger interface{}) error
	GetTrigger(name string) (interface{}, bool)

	// 生命周期管理
	Initialize(config interface{}) error // 使用interface{}避免导入common包
	Start() error
	Stop()

	// 触发器处理
	ProcessTriggers(req interface{}) ([]string, error)

	// 触发器管理
	AddTrigger(name string, config interface{}) error // 使用interface{}避免导入common包
	EnableTrigger(name string) error
	DisableTrigger(name string) error
	GetAllTriggers() map[string]interface{}
}

// ActionService 动作执行服务接口
type ActionService interface {
	ExecuteAction(actionType string, params map[string]interface{}) error
	ExecuteActionByName(actionName string, params map[string]interface{}) error
	RegisterAction(name string, action interface{}) error
	GetAction(name string) (interface{}, bool)
	Start() error
	Stop() error
}

// RateLimitingService 限流服务接口
type RateLimitingService interface {
	Start() error
	Stop() error
	IsAllowed(key string, limit int, window time.Duration) bool
	GetStats() map[string]interface{}
	ResetLimiter(key string) error
	SetGlobalLimit(limit int, window time.Duration) error
	GetRemainingRequests(key string) int
	GetLimiterType() string
	SetLimiterType(limiterType string) error
}

// AdvancedConfigService 高级配置管理服务接口
type AdvancedConfigService interface {
	Start() error
	Stop() error
	GetConfig() map[string]interface{}
	UpdateConfig(config map[string]interface{}) error
	GetConfigValue(key string) (interface{}, error)
	SetConfigValue(key string, value interface{}) error
	AddWatcher(id string, pattern string, callback func(string, interface{}, interface{})) error
	RemoveWatcher(id string) error
	AddValidator(key string, validator func(interface{}) error) error
	GetHistory(limit int) []interface{}
}

// HotReloadableService 热重载服务接口
// 定义了支持热重载的服务必须实现的基本方法
type HotReloadableService interface {
	// 基础生命周期方法
	Start() error                                    // 启动服务
	Stop() error                                     // 停止服务

	// 热重载支持方法
	GetConfig() interface{}                          // 获取当前配置
	UpdateConfig(config interface{}) error          // 更新配置

	// 服务标识方法
	GetServiceName() string                          // 获取服务名称
	GetServiceType() string                          // 获取服务类型
	GetServiceVersion() string                       // 获取服务版本

	// 健康检查方法
	IsHealthy() bool                                 // 检查服务健康状态
	GetHealthStatus() map[string]interface{}        // 获取详细健康状态

	// 扩展功能方法
	GetStats() map[string]interface{}               // 获取服务统计信息
	ResetConfig() error                             // 重置配置到默认值
	ExportConfig(format string) ([]byte, error)    // 导出配置
	ImportConfig(data []byte, format string) error // 导入配置
}

// PathsConfigService 路径配置管理服务接口
// 提供目录管理、权限设置、路径验证等功能
type PathsConfigService interface {
	// 基础服务生命周期方法
	Start() error                                        // 启动路径配置服务
	Stop() error                                         // 停止路径配置服务
	GetConfig() *config.PathsConfig                      // 获取当前路径配置
	UpdateConfig(config *config.PathsConfig) error      // 更新路径配置

	// 路径管理功能方法
	CreateDirectories() error                            // 创建必要的目录结构
	ValidatePaths() error                                // 验证路径配置的有效性
	GetAbsolutePath(relativePath string) string         // 获取相对路径的绝对路径
	SetPermissions() error                               // 设置目录和文件权限
	CleanupTempFiles() error                             // 清理临时文件
}

// SystemConfigService 系统配置管理服务接口
// 提供系统信息检测、信号处理、资源限制等功能
type SystemConfigService interface {
	// 基础服务生命周期方法
	Start() error                                        // 启动系统配置服务
	Stop() error                                         // 停止系统配置服务
	GetConfig() *config.SystemConfig                     // 获取当前系统配置
	UpdateConfig(config *config.SystemConfig) error     // 更新系统配置

	// 系统管理功能方法
	GetSystemInfo() map[string]interface{}               // 获取系统信息(OS、架构、内存等)
	SetResourceLimits() error                            // 设置系统资源限制
	HandleSignals() error                                // 处理系统信号(优雅关闭等)
}

// ProtocolsConfigService 协议配置管理服务接口
// 提供HTTP/HTTPS/SOCKS/DNS等协议的配置和管理功能
type ProtocolsConfigService interface {
	// 基础服务生命周期方法
	Start() error                                        // 启动协议配置服务
	Stop() error                                         // 停止协议配置服务
	GetConfig() *config.ProtocolsConfig                  // 获取当前协议配置
	UpdateConfig(config *config.ProtocolsConfig) error  // 更新协议配置

	// 协议管理功能方法
	IsProtocolEnabled(protocol string) bool             // 检查指定协议是否启用
	GetProtocolConfig(protocol string) interface{}      // 获取指定协议的配置
	ValidateProtocolConfig() error                       // 验证协议配置的有效性
	GetSupportedProtocols() []string                     // 获取支持的协议列表
}

// DevelopmentConfigService 开发配置管理服务接口
// 提供开发模式、性能分析、模拟数据、热重载等功能
type DevelopmentConfigService interface {
	// 基础服务生命周期方法
	Start() error                                        // 启动开发配置服务
	Stop() error                                         // 停止开发配置服务
	GetConfig() *config.DevelopmentConfig                // 获取当前开发配置
	UpdateConfig(config *config.DevelopmentConfig) error // 更新开发配置

	// 开发模式管理方法
	IsEnabled() bool                                     // 检查开发模式是否启用
	GetMode() string                                     // 获取当前开发模式(development/production/testing)

	// 性能分析功能方法
	EnableProfiling() error                              // 启用性能分析(CPU、内存、阻塞等)
	DisableProfiling() error                             // 禁用性能分析

	// 模拟数据管理方法
	GetMockResponse(path string) (interface{}, error)    // 获取指定路径的模拟响应数据
	SetMockResponse(path string, response interface{}) error // 设置指定路径的模拟响应数据

	// 热重载功能方法
	StartHotReload() error                               // 启动配置热重载功能
	StopHotReload() error                                // 停止配置热重载功能
}

// ConfigManagementService 配置管理服务接口
// 提供配置热重载、验证、备份恢复等高级配置管理功能
type ConfigManagementService interface {
	// 基础服务生命周期方法
	Start() error                                        // 启动配置管理服务
	Stop() error                                         // 停止配置管理服务
	GetConfig() *config.ConfigManagementConfig           // 获取当前配置管理配置
	UpdateConfig(config *config.ConfigManagementConfig) error // 更新配置管理配置

	// 热重载功能方法
	StartHotReload() error                               // 启动配置文件热重载监控
	StopHotReload() error                                // 停止配置文件热重载监控

	// 配置验证方法
	ValidateConfig(configData []byte) error             // 验证配置数据的有效性

	// 配置备份和恢复方法
	BackupConfig(configPath string) error               // 备份指定的配置文件
	RestoreConfig(backupPath string) error              // 从备份文件恢复配置
	CleanupBackups() error                               // 清理过期的备份文件
	GetBackupList() ([]string, error)                   // 获取可用的备份文件列表
}
