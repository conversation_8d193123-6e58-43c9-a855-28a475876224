package trace

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"sync/atomic"
	"time"

	"flexproxy/common/constants"
)

// TraceIDGenerator 追踪ID生成器接口
type TraceIDGenerator interface {
	Generate() string
	GenerateWithPrefix(prefix string) string
}

// UUIDGenerator UUID格式的追踪ID生成器
type UUIDGenerator struct{}

// Generate 生成UUID格式的追踪ID
func (g *UUIDGenerator) Generate() string {
	b := make([]byte, 16)
	_, err := rand.Read(b)
	if err != nil {
		// 如果随机数生成失败，使用时间戳作为后备
		return fmt.Sprintf("%d", time.Now().UnixNano())
	}

	// 设置版本号 (4) 和变体位
	b[6] = (b[6] & 0x0f) | 0x40
	b[8] = (b[8] & 0x3f) | 0x80

	return fmt.Sprintf("%x-%x-%x-%x-%x", b[0:4], b[4:6], b[6:8], b[8:10], b[10:16])
}

// GenerateWithPrefix 生成带前缀的UUID格式追踪ID
func (g *UUIDGenerator) GenerateWithPrefix(prefix string) string {
	return prefix + "-" + g.Generate()
}

// HexGenerator 十六进制格式的追踪ID生成器
type HexGenerator struct {
	length int
}

// NewHexGenerator 创建新的十六进制追踪ID生成器
func NewHexGenerator(length int) *HexGenerator {
	if length <= 0 {
		length = constants.DefaultHexGeneratorLength // 默认16字节
	}
	return &HexGenerator{length: length}
}

// Generate 生成十六进制格式的追踪ID
func (g *HexGenerator) Generate() string {
	b := make([]byte, g.length)
	_, err := rand.Read(b)
	if err != nil {
		// 如果随机数生成失败，使用时间戳作为后备
		return fmt.Sprintf("%016x", time.Now().UnixNano())
	}
	return hex.EncodeToString(b)
}

// GenerateWithPrefix 生成带前缀的十六进制格式追踪ID
func (g *HexGenerator) GenerateWithPrefix(prefix string) string {
	return prefix + "-" + g.Generate()
}

// SequentialGenerator 序列号格式的追踪ID生成器
type SequentialGenerator struct {
	counter uint64
	prefix  string
}

// NewSequentialGenerator 创建新的序列号追踪ID生成器
func NewSequentialGenerator(prefix string) *SequentialGenerator {
	return &SequentialGenerator{
		counter: 0,
		prefix:  prefix,
	}
}

// Generate 生成序列号格式的追踪ID
func (g *SequentialGenerator) Generate() string {
	seq := atomic.AddUint64(&g.counter, 1)
	timestamp := time.Now().Unix()
	return fmt.Sprintf("%s-%d-%d", g.prefix, timestamp, seq)
}

// GenerateWithPrefix 生成带前缀的序列号格式追踪ID
func (g *SequentialGenerator) GenerateWithPrefix(prefix string) string {
	seq := atomic.AddUint64(&g.counter, 1)
	timestamp := time.Now().Unix()
	return fmt.Sprintf("%s-%s-%d-%d", prefix, g.prefix, timestamp, seq)
}

// TimestampGenerator 时间戳格式的追踪ID生成器
type TimestampGenerator struct {
	counter uint32
}

// NewTimestampGenerator 创建新的时间戳追踪ID生成器
func NewTimestampGenerator() *TimestampGenerator {
	return &TimestampGenerator{counter: 0}
}

// Generate 生成时间戳格式的追踪ID
func (g *TimestampGenerator) Generate() string {
	now := time.Now()
	seq := atomic.AddUint32(&g.counter, 1)
	return fmt.Sprintf("%d%03d%06d", now.Unix(), now.Nanosecond()/1000000, seq%constants.SequenceModulus)
}

// GenerateWithPrefix 生成带前缀的时间戳格式追踪ID
func (g *TimestampGenerator) GenerateWithPrefix(prefix string) string {
	return prefix + "-" + g.Generate()
}

// TraceContext 追踪上下文
type TraceContext struct {
	TraceID   string
	SpanID    string
	ParentID  string
	Flags     uint8
	Baggage   map[string]string
	StartTime time.Time
}

// NewTraceContext 创建新的追踪上下文
func NewTraceContext(traceID string) *TraceContext {
	return &TraceContext{
		TraceID:   traceID,
		SpanID:    defaultGenerator.Generate(),
		Baggage:   make(map[string]string),
		StartTime: time.Now(),
	}
}

// WithSpan 创建子Span
func (tc *TraceContext) WithSpan(spanID string) *TraceContext {
	return &TraceContext{
		TraceID:   tc.TraceID,
		SpanID:    spanID,
		ParentID:  tc.SpanID,
		Flags:     tc.Flags,
		Baggage:   tc.copyBaggage(),
		StartTime: time.Now(),
	}
}

// SetBaggage 设置行李数据
func (tc *TraceContext) SetBaggage(key, value string) {
	if tc.Baggage == nil {
		tc.Baggage = make(map[string]string)
	}
	tc.Baggage[key] = value
}

// GetBaggage 获取行李数据
func (tc *TraceContext) GetBaggage(key string) (string, bool) {
	if tc.Baggage == nil {
		return "", false
	}
	value, exists := tc.Baggage[key]
	return value, exists
}

// copyBaggage 复制行李数据
func (tc *TraceContext) copyBaggage() map[string]string {
	if tc.Baggage == nil {
		return make(map[string]string)
	}

	copy := make(map[string]string)
	for k, v := range tc.Baggage {
		copy[k] = v
	}
	return copy
}

// Duration 获取追踪持续时间
func (tc *TraceContext) Duration() time.Duration {
	return time.Since(tc.StartTime)
}

// 上下文键类型
type contextKey string

const (
	TraceContextKey contextKey = "trace_context"
	TraceIDKey      contextKey = "trace_id"
	SpanIDKey       contextKey = "span_id"
)

// 全局默认生成器
var defaultGenerator TraceIDGenerator = &UUIDGenerator{}

// SetDefaultGenerator 设置默认追踪ID生成器
func SetDefaultGenerator(generator TraceIDGenerator) {
	defaultGenerator = generator
}

// Generate 使用默认生成器生成追踪ID
func Generate() string {
	return defaultGenerator.Generate()
}

// GenerateWithPrefix 使用默认生成器生成带前缀的追踪ID
func GenerateWithPrefix(prefix string) string {
	return defaultGenerator.GenerateWithPrefix(prefix)
}

// GenerateTraceID 生成追踪ID（Generate函数的别名）
func GenerateTraceID() string {
	return defaultGenerator.Generate()
}

// NewContext 创建带追踪ID的新上下文
func NewContext(ctx context.Context, traceID string) context.Context {
	traceCtx := NewTraceContext(traceID)
	ctx = context.WithValue(ctx, TraceContextKey, traceCtx)
	ctx = context.WithValue(ctx, TraceIDKey, traceID)
	ctx = context.WithValue(ctx, SpanIDKey, traceCtx.SpanID)
	return ctx
}

// NewContextWithGenerator 使用指定生成器创建带追踪ID的新上下文
func NewContextWithGenerator(ctx context.Context, generator TraceIDGenerator) context.Context {
	traceID := generator.Generate()
	return NewContext(ctx, traceID)
}

// FromContext 从上下文中获取追踪上下文
func FromContext(ctx context.Context) (*TraceContext, bool) {
	traceCtx, ok := ctx.Value(TraceContextKey).(*TraceContext)
	return traceCtx, ok
}

// GetTraceID 从上下文中获取追踪ID
func GetTraceID(ctx context.Context) string {
	if traceID, ok := ctx.Value(TraceIDKey).(string); ok {
		return traceID
	}
	return ""
}

// GetSpanID 从上下文中获取SpanID
func GetSpanID(ctx context.Context) string {
	if spanID, ok := ctx.Value(SpanIDKey).(string); ok {
		return spanID
	}
	return ""
}

// WithSpan 在现有上下文中创建新的Span
func WithSpan(ctx context.Context, spanID string) context.Context {
	if traceCtx, ok := FromContext(ctx); ok {
		newTraceCtx := traceCtx.WithSpan(spanID)
		ctx = context.WithValue(ctx, TraceContextKey, newTraceCtx)
		ctx = context.WithValue(ctx, SpanIDKey, spanID)
	}
	return ctx
}

// WithBaggage 在上下文中设置行李数据
func WithBaggage(ctx context.Context, key, value string) context.Context {
	if traceCtx, ok := FromContext(ctx); ok {
		traceCtx.SetBaggage(key, value)
	}
	return ctx
}

// GetBaggage 从上下文中获取行李数据
func GetBaggage(ctx context.Context, key string) (string, bool) {
	if traceCtx, ok := FromContext(ctx); ok {
		return traceCtx.GetBaggage(key)
	}
	return "", false
}

// EnsureTraceID 确保上下文中有追踪ID，如果没有则生成一个
func EnsureTraceID(ctx context.Context) (context.Context, string) {
	if traceID := GetTraceID(ctx); traceID != "" {
		return ctx, traceID
	}

	traceID := Generate()
	return NewContext(ctx, traceID), traceID
}

// 预定义的生成器实例
var (
	UUIDGen      = &UUIDGenerator{}
	Hex16Gen     = NewHexGenerator(16)
	Hex32Gen     = NewHexGenerator(32)
	SeqGen       = NewSequentialGenerator("flexproxy")
	TimestampGen = NewTimestampGenerator()
)
