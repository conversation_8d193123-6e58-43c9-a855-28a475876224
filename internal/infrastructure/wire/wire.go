//go:build wireinject
// +build wireinject

//go:generate wire

package wire

import (
	"context"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
	"flexproxy/internal/interfaces"
	"flexproxy/internal/services"
	"flexproxy/internal/core/engine/strategy"
	"github.com/google/wire"
	"github.com/mbndr/logo"
)

// provideLogService 提供默认的LogService构造函数
func provideLogService() interfaces.LogService {
	return services.NewLogService("")
}

// provideLogServiceWithConfig 提供带配置的日志服务构造函数
func provideLogServiceWithConfig(cfg *config.Config) interfaces.LogService {
	// 检查日志服务是否启用
	checker := config.NewConfigChecker(cfg)
	if !checker.IsLoggingEnabled() {
		// 注意：这里不能使用常规日志记录禁用状态，因为日志本身被禁用了
		// 可以考虑使用标准输出或其他方式通知
		return services.NewNullLogService()
	}
	return services.NewLogService("")
}

// provideLoggerAdapter 提供LoggerAdapter构造函数
func provideLoggerAdapter() *logger.LoggerAdapter {
	return logger.GetLoggerAdapter(logger.ModuleActionService)
}

// provideLogger 提供基础Logger构造函数
func provideLogger() *logo.Logger {
	return logger.GetLogger(logger.ModuleMain)
}

// provideStrategyManager 提供策略管理器构造函数
func provideStrategyManager() strategy.StrategyManager {
	return strategy.NewStrategyManager()
}

// provideMonitoringService 提供监控服务构造函数
func provideMonitoringService() interfaces.MonitoringService {
	// 创建默认监控配置
	monitoringConfig := &config.MonitoringConfig{
		Path: "/metrics",
	}
	// 创建默认端口配置
	portsConfig := &config.PortsConfig{
		Monitoring: 9090,
	}
	// 创建默认全局配置
	globalConfig := &config.Config{
		Modules: &config.ModulesConfig{
			Enabled: []string{"monitoring"},
		},
	}
	return services.NewMonitoringService(monitoringConfig, portsConfig, globalConfig, nil)
}

// provideRateLimitingService 提供限流服务构造函数
func provideRateLimitingService() interfaces.RateLimitingService {
	// 创建默认限流配置
	rateLimitingConfig := &config.RateLimitingConfig{
		Algorithm:     "token_bucket",
		Rate:          100,
		Burst:         200,
		Window:        "1m",
		CleanupPeriod: "5m",
	}
	return services.NewRateLimitingService(rateLimitingConfig, nil, nil)
}

// provideSecurityService 提供安全服务构造函数
func provideSecurityService() interfaces.SecurityService {
	// 创建默认安全配置
	securityConfig := &config.SecurityConfig{
		Auth: &config.AuthConfig{
			Type:        "none",
			TokenExpiry: "24h",
		},
		Encryption: &config.EncryptionConfig{
			Algorithm: "aes256",
			KeyLength: 32,
		},
	}
	return services.NewSecurityService(securityConfig, nil)
}

// provideSecurityServiceWithConfig 提供带配置的安全服务构造函数
func provideSecurityServiceWithConfig(cfg *config.Config) interfaces.SecurityService {
	// 检查安全服务是否启用
	checker := config.NewConfigChecker(cfg)
	if !checker.IsSecurityEnabled() {
		log := logger.GetLogger("security")
		log.Info("安全服务已禁用，使用空实现")
		return services.NewNullSecurityService(log)
	}

	// 使用配置中的安全设置，如果没有则使用默认配置
	securityConfig := cfg.Security
	if securityConfig == nil {
		securityConfig = &config.SecurityConfig{
			Auth: &config.AuthConfig{
				Type:        "none",
				TokenExpiry: "24h",
			},
			Encryption: &config.EncryptionConfig{
				Algorithm: "aes256",
				KeyLength: 32,
			},
		}
	}
	return services.NewSecurityService(securityConfig, nil)
}

// providePluginService 提供插件服务构造函数
func providePluginService() interfaces.PluginService {
	// 创建默认插件配置
	pluginsConfig := &config.PluginsConfig{
		Enabled:   false,
		Directory: "./plugins",
		AutoLoad:  false,
	}
	return services.NewPluginService(pluginsConfig, nil)
}

// provideCacheService 提供缓存服务构造函数
func provideCacheService() interfaces.CacheService {
	return services.NewCacheService(nil)
}

// provideCacheServiceWithConfig 提供带配置的缓存服务构造函数
func provideCacheServiceWithConfig(cfg *config.Config) interfaces.CacheService {
	// 检查缓存是否启用
	if cfg != nil && cfg.Cache != nil && cfg.Cache.Global != nil && !cfg.Cache.Global.Enabled {
		log := logger.GetLogger("cache")
		log.Info("缓存服务已禁用，使用空实现")
		return services.NewNullCacheService(log)
	}
	return services.NewCacheService(nil)
}

// provideTracingService 提供追踪服务
func provideTracingService(logger *logo.Logger) interfaces.TracingService {
	tracingConfig := &config.TracingConfig{
		Enabled:            true,
		HexGeneratorLength: 16,
		SequenceModulus:    10000,
	}
	return services.NewTracingService(tracingConfig, logger)
}

// providePerformanceService 提供性能调优服务
func providePerformanceService(logger *logo.Logger) interfaces.PerformanceService {
	performanceConfig := &config.PerformanceConfig{
		WorkerPoolSize: 10,
		QueueSize:      1000,
		BatchSize:      100,
		FlushInterval:  "5s",
	}
	return services.NewPerformanceService(performanceConfig, logger)
}

// provideDebugService 提供调试服务
func provideDebugService(logger *logo.Logger) interfaces.DebugService {
	debugConfig := &config.DebugConfig{
		Enabled:        false,
		VerboseLogging: false,
		DumpRequests:   false,
		DumpResponses:  false,
		ProfileEnabled: false,
	}
	// 创建默认端口配置
	portsConfig := &config.PortsConfig{
		Debug: 8081,
	}
	return services.NewDebugService(debugConfig, portsConfig, logger)
}

// provideDebugServiceWithConfig 提供带配置的调试服务
func provideDebugServiceWithConfig(cfg *config.Config, logger *logo.Logger) interfaces.DebugService {
	// 检查开发配置是否启用，以及调试功能是否启用
	if cfg != nil && cfg.Development != nil && !cfg.Development.Enabled {
		logger.Info("开发模式已禁用，调试服务使用空实现")
		return services.NewNullDebugService(logger)
	}

	// 使用配置中的调试设置
	debugConfig := &config.DebugConfig{
		Enabled:        false,
		VerboseLogging: false,
		DumpRequests:   false,
		DumpResponses:  false,
		ProfileEnabled: false,
	}

	// 如果有高级配置且调试配置存在，使用配置中的值
	if cfg != nil && cfg.Advanced != nil && cfg.Advanced.Debug != nil {
		debugConfig = cfg.Advanced.Debug
	}

	// 创建默认端口配置
	portsConfig := &config.PortsConfig{
		Debug: 8081,
	}
	return services.NewDebugService(debugConfig, portsConfig, logger)
}

// provideProfilingService 提供性能分析服务
func provideProfilingService(logger *logo.Logger) interfaces.ProfilingService {
	profilingConfig := &config.ProfilingConfig{
		Enabled:       false,
		CPUProfile:    false,
		MemoryProfile: false,
		BlockProfile:  false,
		MutexProfile:  false,
	}
	return services.NewProfilingService(profilingConfig, logger)
}

// provideProfilingServiceWithConfig 提供带配置的性能分析服务
func provideProfilingServiceWithConfig(cfg *config.Config, logger *logo.Logger) interfaces.ProfilingService {
	// 检查开发配置是否启用
	if cfg != nil && cfg.Development != nil && !cfg.Development.Enabled {
		logger.Info("开发模式已禁用，性能分析服务使用空实现")
		return services.NewNullProfilingService(logger)
	}

	// 使用配置中的性能分析设置
	profilingConfig := &config.ProfilingConfig{
		Enabled:       false,
		CPUProfile:    false,
		MemoryProfile: false,
		BlockProfile:  false,
		MutexProfile:  false,
	}

	// 如果开发配置中有性能分析配置，使用配置中的值
	if cfg != nil && cfg.Development != nil && cfg.Development.Profiling != nil {
		profilingConfig = cfg.Development.Profiling
	}

	return services.NewProfilingService(profilingConfig, logger)
}

// provideAdvancedConfigService 提供高级配置管理服务
func provideAdvancedConfigService(logger *logo.Logger) interfaces.AdvancedConfigService {
	advancedConfig := &config.AdvancedConfig{
		Enabled: false, // 默认禁用高级功能
		ErrorRecovery: &config.ErrorRecoveryConfig{
			MaxRetryAttempts:    3,
			InitialRetryDelay:   "1s",
			MaxRetryDelay:       "30s",
			RetryMultiplier:     2.0,
			FailureThreshold:    5,
			SuccessThreshold:    3,
			CircuitTimeout:      "60s",
			CircuitResetTimeout: "300s",
		},
		Performance: &config.PerformanceConfig{
			WorkerPoolSize: 10,
			QueueSize:      1000,
			BatchSize:      100,
			FlushInterval:  "5s",
		},
	}
	return services.NewAdvancedConfigService(advancedConfig, logger)
}

// provideAdvancedConfigServiceWithConfig 提供带配置的高级配置管理服务
func provideAdvancedConfigServiceWithConfig(cfg *config.Config, logger *logo.Logger) interfaces.AdvancedConfigService {
	// 检查高级功能是否启用
	if cfg != nil && cfg.Advanced != nil && !cfg.Advanced.Enabled {
		logger.Info("高级功能已禁用，使用空实现")
		return services.NewNullAdvancedConfigService(logger)
	}

	// 使用配置中的高级设置
	advancedConfig := cfg.Advanced
	if advancedConfig == nil {
		advancedConfig = &config.AdvancedConfig{
			Enabled: false,
			ErrorRecovery: &config.ErrorRecoveryConfig{
				MaxRetryAttempts:    3,
				InitialRetryDelay:   "1s",
				MaxRetryDelay:       "30s",
				RetryMultiplier:     2.0,
				FailureThreshold:    5,
				SuccessThreshold:    3,
				CircuitTimeout:      "60s",
				CircuitResetTimeout: "300s",
			},
			Performance: &config.PerformanceConfig{
				WorkerPoolSize: 10,
				QueueSize:      1000,
				BatchSize:      100,
				FlushInterval:  "5s",
			},
		}
	}
	return services.NewAdvancedConfigService(advancedConfig, logger)
}

// ProviderSet 包含所有服务的提供者
var ProviderSet = wire.NewSet(
	// 基础服务
	provideLogService,
	provideLoggerAdapter,
	provideLogger,
	services.NewConfigService,
	provideCacheService,

	// 策略管理
	provideStrategyManager,

	// 核心服务
	services.NewDNSService,
	services.NewProxyService,

	// 业务服务
	services.NewTriggerService,
	services.NewActionService,

	// 新增服务
	provideMonitoringService,
	provideRateLimitingService,
	provideSecurityService,
	providePluginService,

	// 高级服务
	provideTracingService,
	providePerformanceService,
	provideDebugService,
	provideProfilingService,
	provideAdvancedConfigService,
)

// containerImpl 实现容器接口
type containerImpl struct {
	configService       interfaces.ConfigService
	logService          interfaces.LogService
	cacheService        interfaces.CacheService
	dnsService          interfaces.DNSService
	proxyService        interfaces.ProxyService
	triggerService      interfaces.TriggerService
	actionService       interfaces.ActionService
	monitoringService   interfaces.MonitoringService
	rateLimitingService interfaces.RateLimitingService
	securityService     interfaces.SecurityService
	pluginService       interfaces.PluginService
	strategyManager     strategy.StrategyManager

	// 新增的高级服务
	tracingService        interfaces.TracingService
	performanceService    interfaces.PerformanceService
	debugService          interfaces.DebugService
	profilingService      interfaces.ProfilingService
	advancedConfigService interfaces.AdvancedConfigService
}

// NewContainer 创建新的容器实例
func NewContainer(
	logService interfaces.LogService,
	configService interfaces.ConfigService,
	cacheService interfaces.CacheService,
	dnsService interfaces.DNSService,
	proxyService interfaces.ProxyService,
	triggerService interfaces.TriggerService,
	actionService interfaces.ActionService,
	monitoringService interfaces.MonitoringService,
	rateLimitingService interfaces.RateLimitingService,
	securityService interfaces.SecurityService,
	pluginService interfaces.PluginService,
	strategyManager strategy.StrategyManager,
	tracingService interfaces.TracingService,
	performanceService interfaces.PerformanceService,
	debugService interfaces.DebugService,
	profilingService interfaces.ProfilingService,
	advancedConfigService interfaces.AdvancedConfigService,
) interfaces.Container {
	return &containerImpl{
		logService:            logService,
		configService:         configService,
		cacheService:          cacheService,
		dnsService:            dnsService,
		proxyService:          proxyService,
		triggerService:        triggerService,
		actionService:         actionService,
		monitoringService:     monitoringService,
		rateLimitingService:   rateLimitingService,
		securityService:       securityService,
		pluginService:         pluginService,
		strategyManager:       strategyManager,
		tracingService:        tracingService,
		performanceService:    performanceService,
		debugService:          debugService,
		profilingService:      profilingService,
		advancedConfigService: advancedConfigService,
	}
}

// LogService 获取日志服务
func (c *containerImpl) LogService() interfaces.LogService {
	return c.logService
}

// ConfigService 获取配置服务
func (c *containerImpl) ConfigService() interfaces.ConfigService {
	return c.configService
}

// CacheService 获取缓存服务
func (c *containerImpl) CacheService() interfaces.CacheService {
	return c.cacheService
}

// DNSService 获取DNS服务
func (c *containerImpl) DNSService() interfaces.DNSService {
	return c.dnsService
}

// ProxyService 获取代理服务
func (c *containerImpl) ProxyService() interfaces.ProxyService {
	return c.proxyService
}

// TriggerService 获取触发器服务
func (c *containerImpl) TriggerService() interfaces.TriggerService {
	return c.triggerService
}

// ActionService 获取动作服务
func (c *containerImpl) ActionService() interfaces.ActionService {
	return c.actionService
}

// GetActionService 获取动作服务（兼容方法）
func (c *containerImpl) GetActionService() interfaces.ActionService {
	return c.actionService
}

// GetLogService 获取日志服务（兼容方法）
func (c *containerImpl) GetLogService() interfaces.LogService {
	return c.logService
}

// GetConfigService 获取配置服务（兼容方法）
func (c *containerImpl) GetConfigService() interfaces.ConfigService {
	return c.configService
}

// GetCacheService 获取缓存服务（兼容方法）
func (c *containerImpl) GetCacheService() interfaces.CacheService {
	return c.cacheService
}

// GetDNSService 获取DNS服务（兼容方法）
func (c *containerImpl) GetDNSService() interfaces.DNSService {
	return c.dnsService
}

// GetProxyService 获取代理服务（兼容方法）
func (c *containerImpl) GetProxyService() interfaces.ProxyService {
	return c.proxyService
}

// GetTriggerService 获取触发器服务（兼容方法）
func (c *containerImpl) GetTriggerService() interfaces.TriggerService {
	return c.triggerService
}

// GetStrategyManager 获取策略管理器
func (c *containerImpl) GetStrategyManager() interface{} {
	return c.strategyManager
}

// GetMonitoringService 获取监控服务
func (c *containerImpl) GetMonitoringService() interfaces.MonitoringService {
	return c.monitoringService
}

// GetRateLimitingService 获取限流服务
func (c *containerImpl) GetRateLimitingService() interfaces.RateLimitingService {
	return c.rateLimitingService
}

// GetSecurityService 获取安全服务
func (c *containerImpl) GetSecurityService() interfaces.SecurityService {
	return c.securityService
}

// GetPluginService 获取插件服务
func (c *containerImpl) GetPluginService() interfaces.PluginService {
	return c.pluginService
}

// GetTracingService 获取追踪服务
func (c *containerImpl) GetTracingService() interfaces.TracingService {
	return c.tracingService
}

// GetPerformanceService 获取性能调优服务
func (c *containerImpl) GetPerformanceService() interfaces.PerformanceService {
	return c.performanceService
}

// GetDebugService 获取调试服务
func (c *containerImpl) GetDebugService() interfaces.DebugService {
	return c.debugService
}

// GetProfilingService 获取性能分析服务
func (c *containerImpl) GetProfilingService() interfaces.ProfilingService {
	return c.profilingService
}

// GetAdvancedConfigService 获取高级配置管理服务
func (c *containerImpl) GetAdvancedConfigService() interfaces.AdvancedConfigService {
	return c.advancedConfigService
}

// Start 启动容器
func (c *containerImpl) Start(ctx context.Context) error {
	// 这里可以添加启动逻辑
	return nil
}

// Stop 停止容器
func (c *containerImpl) Stop(ctx context.Context) error {
	// 这里可以添加停止逻辑
	return nil
}

// containerSet 定义容器的依赖集合
var containerSet = wire.NewSet(
	ProviderSet,
	NewContainer,
)

// InitializeContainer 初始化容器
func InitializeContainer() (interfaces.Container, error) {
	wire.Build(containerSet)
	return nil, nil
}

// InitializeContainerWithConfig 使用配置初始化容器
func InitializeContainerWithConfig(cfg *config.Config) (interfaces.Container, error) {
	wire.Build(containerSet)
	return nil, nil
}
