// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
	"flexproxy/internal/core/engine/strategy"
	"flexproxy/internal/interfaces"
	"flexproxy/internal/services"
	"github.com/google/wire"
	"github.com/mbndr/logo"
)

// Injectors from wire.go:

// InitializeContainer 初始化容器
func InitializeContainer() (interfaces.Container, error) {
	logService := provideLogService()
	logger := provideLogger()
	configService := services.NewConfigService(logger)
	cacheService := provideCacheService()
	dnsService := services.NewDNSService(cacheService, logService)
	proxyService := services.NewProxyService(cacheService, logger)
	triggerService := services.NewTriggerService(cacheService, logger)
	loggerAdapter := provideLoggerAdapter()
	actionService := services.NewActionService(loggerAdapter, cacheService, proxyService, dnsService)
	monitoringService := provideMonitoringService()
	rateLimitingService := provideRateLimitingService()
	securityService := provideSecurityService()
	pluginService := providePluginService()
	strategyManager := provideStrategyManager()
	tracingService := provideTracingService(logger)
	performanceService := providePerformanceService(logger)
	debugService := provideDebugService(logger)
	profilingService := provideProfilingService(logger)
	advancedConfigService := provideAdvancedConfigService(logger)
	container := NewContainer(logService, configService, cacheService, dnsService, proxyService, triggerService, actionService, monitoringService, rateLimitingService, securityService, pluginService, strategyManager, tracingService, performanceService, debugService, profilingService, advancedConfigService)
	return container, nil
}

// InitializeContainerWithConfig 使用配置初始化容器
func InitializeContainerWithConfig(cfg *config.Config) (interfaces.Container, error) {
	logService := provideLogService()
	logger := provideLogger()
	configService := services.NewConfigService(logger)
	cacheService := provideCacheService()
	dnsService := services.NewDNSService(cacheService, logService)
	proxyService := services.NewProxyService(cacheService, logger)
	triggerService := services.NewTriggerService(cacheService, logger)
	loggerAdapter := provideLoggerAdapter()
	actionService := services.NewActionService(loggerAdapter, cacheService, proxyService, dnsService)
	monitoringService := provideMonitoringService()
	rateLimitingService := provideRateLimitingService()
	securityService := provideSecurityService()
	pluginService := providePluginService()
	strategyManager := provideStrategyManager()
	tracingService := provideTracingService(logger)
	performanceService := providePerformanceService(logger)
	debugService := provideDebugService(logger)
	profilingService := provideProfilingService(logger)
	advancedConfigService := provideAdvancedConfigService(logger)
	container := NewContainer(logService, configService, cacheService, dnsService, proxyService, triggerService, actionService, monitoringService, rateLimitingService, securityService, pluginService, strategyManager, tracingService, performanceService, debugService, profilingService, advancedConfigService)
	return container, nil
}

// wire.go:

// provideLogService 提供默认的LogService构造函数
func provideLogService() interfaces.LogService {
	return services.NewLogService("")
}

// provideLogServiceWithConfig 提供带配置的日志服务构造函数
func provideLogServiceWithConfig(cfg *config.Config) interfaces.LogService {

	checker := config.NewConfigChecker(cfg)
	if !checker.IsLoggingEnabled() {

		return services.NewNullLogService()
	}
	return services.NewLogService("")
}

// provideLoggerAdapter 提供LoggerAdapter构造函数
func provideLoggerAdapter() *logger.LoggerAdapter {
	return logger.GetLoggerAdapter(logger.ModuleActionService)
}

// provideLogger 提供基础Logger构造函数
func provideLogger() *logo.Logger {
	return logger.GetLogger(logger.ModuleMain)
}

// provideStrategyManager 提供策略管理器构造函数
func provideStrategyManager() strategy.StrategyManager {
	return strategy.NewStrategyManager()
}

// provideMonitoringService 提供监控服务构造函数
func provideMonitoringService() interfaces.MonitoringService {

	monitoringConfig := &config.MonitoringConfig{
		Path: "/metrics",
	}

	portsConfig := &config.PortsConfig{
		Monitoring: 9090,
	}

	globalConfig := &config.Config{
		Modules: &config.ModulesConfig{
			Enabled: []string{"monitoring"},
		},
	}
	return services.NewMonitoringService(monitoringConfig, portsConfig, globalConfig, nil)
}

// provideRateLimitingService 提供限流服务构造函数
func provideRateLimitingService() interfaces.RateLimitingService {

	rateLimitingConfig := &config.RateLimitingConfig{
		Algorithm:     "token_bucket",
		Rate:          100,
		Burst:         200,
		Window:        "1m",
		CleanupPeriod: "5m",
	}
	return services.NewRateLimitingService(rateLimitingConfig, nil, nil)
}

// provideSecurityService 提供安全服务构造函数
func provideSecurityService() interfaces.SecurityService {

	securityConfig := &config.SecurityConfig{
		Auth: &config.AuthConfig{
			Type:        "none",
			TokenExpiry: "24h",
		},
		Encryption: &config.EncryptionConfig{
			Algorithm: "aes256",
			KeyLength: 32,
		},
	}
	return services.NewSecurityService(securityConfig, nil)
}

// provideSecurityServiceWithConfig 提供带配置的安全服务构造函数
func provideSecurityServiceWithConfig(cfg *config.Config) interfaces.SecurityService {

	checker := config.NewConfigChecker(cfg)
	if !checker.IsSecurityEnabled() {
		log := logger.GetLogger("security")
		log.Info("安全服务已禁用，使用空实现")
		return services.NewNullSecurityService(log)
	}

	securityConfig := cfg.Security
	if securityConfig == nil {
		securityConfig = &config.SecurityConfig{
			Auth: &config.AuthConfig{
				Type:        "none",
				TokenExpiry: "24h",
			},
			Encryption: &config.EncryptionConfig{
				Algorithm: "aes256",
				KeyLength: 32,
			},
		}
	}
	return services.NewSecurityService(securityConfig, nil)
}

// providePluginService 提供插件服务构造函数
func providePluginService() interfaces.PluginService {

	pluginsConfig := &config.PluginsConfig{
		Enabled:   false,
		Directory: "./plugins",
		AutoLoad:  false,
	}
	return services.NewPluginService(pluginsConfig, nil)
}

// provideCacheService 提供缓存服务构造函数
func provideCacheService() interfaces.CacheService {
	return services.NewCacheService(nil)
}

// provideCacheServiceWithConfig 提供带配置的缓存服务构造函数
func provideCacheServiceWithConfig(cfg *config.Config) interfaces.CacheService {

	if cfg != nil && cfg.Cache != nil && cfg.Cache.Global != nil && !cfg.Cache.Global.Enabled {
		log := logger.GetLogger("cache")
		log.Info("缓存服务已禁用，使用空实现")
		return services.NewNullCacheService(log)
	}
	return services.NewCacheService(nil)
}

// provideTracingService 提供追踪服务
func provideTracingService(logger2 *logo.Logger) interfaces.TracingService {
	tracingConfig := &config.TracingConfig{
		Enabled:            true,
		HexGeneratorLength: 16,
		SequenceModulus:    10000,
	}
	return services.NewTracingService(tracingConfig, logger2)
}

// providePerformanceService 提供性能调优服务
func providePerformanceService(logger2 *logo.Logger) interfaces.PerformanceService {
	performanceConfig := &config.PerformanceConfig{
		WorkerPoolSize: 10,
		QueueSize:      1000,
		BatchSize:      100,
		FlushInterval:  "5s",
	}
	return services.NewPerformanceService(performanceConfig, logger2)
}

// provideDebugService 提供调试服务
func provideDebugService(logger2 *logo.Logger) interfaces.DebugService {
	debugConfig := &config.DebugConfig{
		Enabled:        false,
		VerboseLogging: false,
		DumpRequests:   false,
		DumpResponses:  false,
		ProfileEnabled: false,
	}

	portsConfig := &config.PortsConfig{
		Debug: 8081,
	}
	return services.NewDebugService(debugConfig, portsConfig, logger2)
}

// provideDebugServiceWithConfig 提供带配置的调试服务
func provideDebugServiceWithConfig(cfg *config.Config, logger2 *logo.Logger) interfaces.DebugService {

	if cfg != nil && cfg.Development != nil && !cfg.Development.Enabled {
		logger2.
			Info("开发模式已禁用，调试服务使用空实现")
		return services.NewNullDebugService(logger2)
	}

	debugConfig := &config.DebugConfig{
		Enabled:        false,
		VerboseLogging: false,
		DumpRequests:   false,
		DumpResponses:  false,
		ProfileEnabled: false,
	}

	if cfg != nil && cfg.Advanced != nil && cfg.Advanced.Debug != nil {
		debugConfig = cfg.Advanced.Debug
	}

	portsConfig := &config.PortsConfig{
		Debug: 8081,
	}
	return services.NewDebugService(debugConfig, portsConfig, logger2)
}

// provideProfilingService 提供性能分析服务
func provideProfilingService(logger2 *logo.Logger) interfaces.ProfilingService {
	profilingConfig := &config.ProfilingConfig{
		Enabled:       false,
		CPUProfile:    false,
		MemoryProfile: false,
		BlockProfile:  false,
		MutexProfile:  false,
	}
	return services.NewProfilingService(profilingConfig, logger2)
}

// provideProfilingServiceWithConfig 提供带配置的性能分析服务
func provideProfilingServiceWithConfig(cfg *config.Config, logger2 *logo.Logger) interfaces.ProfilingService {

	if cfg != nil && cfg.Development != nil && !cfg.Development.Enabled {
		logger2.
			Info("开发模式已禁用，性能分析服务使用空实现")
		return services.NewNullProfilingService(logger2)
	}

	profilingConfig := &config.ProfilingConfig{
		Enabled:       false,
		CPUProfile:    false,
		MemoryProfile: false,
		BlockProfile:  false,
		MutexProfile:  false,
	}

	if cfg != nil && cfg.Development != nil && cfg.Development.Profiling != nil {
		profilingConfig = cfg.Development.Profiling
	}

	return services.NewProfilingService(profilingConfig, logger2)
}

// provideAdvancedConfigService 提供高级配置管理服务
func provideAdvancedConfigService(logger2 *logo.Logger) interfaces.AdvancedConfigService {
	advancedConfig := &config.AdvancedConfig{
		Enabled: false,
		ErrorRecovery: &config.ErrorRecoveryConfig{
			MaxRetryAttempts:    3,
			InitialRetryDelay:   "1s",
			MaxRetryDelay:       "30s",
			RetryMultiplier:     2.0,
			FailureThreshold:    5,
			SuccessThreshold:    3,
			CircuitTimeout:      "60s",
			CircuitResetTimeout: "300s",
		},
		Performance: &config.PerformanceConfig{
			WorkerPoolSize: 10,
			QueueSize:      1000,
			BatchSize:      100,
			FlushInterval:  "5s",
		},
	}
	return services.NewAdvancedConfigService(advancedConfig, logger2)
}

// provideAdvancedConfigServiceWithConfig 提供带配置的高级配置管理服务
func provideAdvancedConfigServiceWithConfig(cfg *config.Config, logger2 *logo.Logger) interfaces.AdvancedConfigService {

	if cfg != nil && cfg.Advanced != nil && !cfg.Advanced.Enabled {
		logger2.
			Info("高级功能已禁用，使用空实现")
		return services.NewNullAdvancedConfigService(logger2)
	}

	advancedConfig := cfg.Advanced
	if advancedConfig == nil {
		advancedConfig = &config.AdvancedConfig{
			Enabled: false,
			ErrorRecovery: &config.ErrorRecoveryConfig{
				MaxRetryAttempts:    3,
				InitialRetryDelay:   "1s",
				MaxRetryDelay:       "30s",
				RetryMultiplier:     2.0,
				FailureThreshold:    5,
				SuccessThreshold:    3,
				CircuitTimeout:      "60s",
				CircuitResetTimeout: "300s",
			},
			Performance: &config.PerformanceConfig{
				WorkerPoolSize: 10,
				QueueSize:      1000,
				BatchSize:      100,
				FlushInterval:  "5s",
			},
		}
	}
	return services.NewAdvancedConfigService(advancedConfig, logger2)
}

// ProviderSet 包含所有服务的提供者
var ProviderSet = wire.NewSet(

	provideLogService,
	provideLoggerAdapter,
	provideLogger, services.NewConfigService, provideCacheService,

	provideStrategyManager, services.NewDNSService, services.NewProxyService, services.NewTriggerService, services.NewActionService, provideMonitoringService,
	provideRateLimitingService,
	provideSecurityService,
	providePluginService,

	provideTracingService,
	providePerformanceService,
	provideDebugService,
	provideProfilingService,
	provideAdvancedConfigService,
)

// containerImpl 实现容器接口
type containerImpl struct {
	configService       interfaces.ConfigService
	logService          interfaces.LogService
	cacheService        interfaces.CacheService
	dnsService          interfaces.DNSService
	proxyService        interfaces.ProxyService
	triggerService      interfaces.TriggerService
	actionService       interfaces.ActionService
	monitoringService   interfaces.MonitoringService
	rateLimitingService interfaces.RateLimitingService
	securityService     interfaces.SecurityService
	pluginService       interfaces.PluginService
	strategyManager     strategy.StrategyManager

	// 新增的高级服务
	tracingService        interfaces.TracingService
	performanceService    interfaces.PerformanceService
	debugService          interfaces.DebugService
	profilingService      interfaces.ProfilingService
	advancedConfigService interfaces.AdvancedConfigService
}

// NewContainer 创建新的容器实例
func NewContainer(
	logService interfaces.LogService,
	configService interfaces.ConfigService,
	cacheService interfaces.CacheService,
	dnsService interfaces.DNSService,
	proxyService interfaces.ProxyService,
	triggerService interfaces.TriggerService,
	actionService interfaces.ActionService,
	monitoringService interfaces.MonitoringService,
	rateLimitingService interfaces.RateLimitingService,
	securityService interfaces.SecurityService,
	pluginService interfaces.PluginService,
	strategyManager strategy.StrategyManager,
	tracingService interfaces.TracingService,
	performanceService interfaces.PerformanceService,
	debugService interfaces.DebugService,
	profilingService interfaces.ProfilingService,
	advancedConfigService interfaces.AdvancedConfigService,
) interfaces.Container {
	return &containerImpl{
		logService:            logService,
		configService:         configService,
		cacheService:          cacheService,
		dnsService:            dnsService,
		proxyService:          proxyService,
		triggerService:        triggerService,
		actionService:         actionService,
		monitoringService:     monitoringService,
		rateLimitingService:   rateLimitingService,
		securityService:       securityService,
		pluginService:         pluginService,
		strategyManager:       strategyManager,
		tracingService:        tracingService,
		performanceService:    performanceService,
		debugService:          debugService,
		profilingService:      profilingService,
		advancedConfigService: advancedConfigService,
	}
}

// LogService 获取日志服务
func (c *containerImpl) LogService() interfaces.LogService {
	return c.logService
}

// ConfigService 获取配置服务
func (c *containerImpl) ConfigService() interfaces.ConfigService {
	return c.configService
}

// CacheService 获取缓存服务
func (c *containerImpl) CacheService() interfaces.CacheService {
	return c.cacheService
}

// DNSService 获取DNS服务
func (c *containerImpl) DNSService() interfaces.DNSService {
	return c.dnsService
}

// ProxyService 获取代理服务
func (c *containerImpl) ProxyService() interfaces.ProxyService {
	return c.proxyService
}

// TriggerService 获取触发器服务
func (c *containerImpl) TriggerService() interfaces.TriggerService {
	return c.triggerService
}

// ActionService 获取动作服务
func (c *containerImpl) ActionService() interfaces.ActionService {
	return c.actionService
}

// GetActionService 获取动作服务（兼容方法）
func (c *containerImpl) GetActionService() interfaces.ActionService {
	return c.actionService
}

// GetLogService 获取日志服务（兼容方法）
func (c *containerImpl) GetLogService() interfaces.LogService {
	return c.logService
}

// GetConfigService 获取配置服务（兼容方法）
func (c *containerImpl) GetConfigService() interfaces.ConfigService {
	return c.configService
}

// GetCacheService 获取缓存服务（兼容方法）
func (c *containerImpl) GetCacheService() interfaces.CacheService {
	return c.cacheService
}

// GetDNSService 获取DNS服务（兼容方法）
func (c *containerImpl) GetDNSService() interfaces.DNSService {
	return c.dnsService
}

// GetProxyService 获取代理服务（兼容方法）
func (c *containerImpl) GetProxyService() interfaces.ProxyService {
	return c.proxyService
}

// GetTriggerService 获取触发器服务（兼容方法）
func (c *containerImpl) GetTriggerService() interfaces.TriggerService {
	return c.triggerService
}

// GetStrategyManager 获取策略管理器
func (c *containerImpl) GetStrategyManager() interface{} {
	return c.strategyManager
}

// GetMonitoringService 获取监控服务
func (c *containerImpl) GetMonitoringService() interfaces.MonitoringService {
	return c.monitoringService
}

// GetRateLimitingService 获取限流服务
func (c *containerImpl) GetRateLimitingService() interfaces.RateLimitingService {
	return c.rateLimitingService
}

// GetSecurityService 获取安全服务
func (c *containerImpl) GetSecurityService() interfaces.SecurityService {
	return c.securityService
}

// GetPluginService 获取插件服务
func (c *containerImpl) GetPluginService() interfaces.PluginService {
	return c.pluginService
}

// GetTracingService 获取追踪服务
func (c *containerImpl) GetTracingService() interfaces.TracingService {
	return c.tracingService
}

// GetPerformanceService 获取性能调优服务
func (c *containerImpl) GetPerformanceService() interfaces.PerformanceService {
	return c.performanceService
}

// GetDebugService 获取调试服务
func (c *containerImpl) GetDebugService() interfaces.DebugService {
	return c.debugService
}

// GetProfilingService 获取性能分析服务
func (c *containerImpl) GetProfilingService() interfaces.ProfilingService {
	return c.profilingService
}

// GetAdvancedConfigService 获取高级配置管理服务
func (c *containerImpl) GetAdvancedConfigService() interfaces.AdvancedConfigService {
	return c.advancedConfigService
}

// Start 启动容器
func (c *containerImpl) Start(ctx context.Context) error {

	return nil
}

// Stop 停止容器
func (c *containerImpl) Stop(ctx context.Context) error {

	return nil
}

// containerSet 定义容器的依赖集合
var containerSet = wire.NewSet(
	ProviderSet,
	NewContainer,
)
