// dns_cache.go - 优化的DNS解析缓存管理
package cache

import (
	"context"
	"net"
	"sync"
	"time"
)

// DNSCacheEntry DNS缓存条目
type DNSCacheEntry struct {
	IPs       []net.IP      // 解析结果IP列表
	Names     []string      // 反向解析结果域名列表
	CreatedAt time.Time     // 创建时间
	LastUsed  time.Time     // 最后使用时间
	HitCount  int64         // 命中次数
	TTL       time.Duration // 生存时间
}

// IsExpired 检查缓存条目是否过期
func (entry *DNSCacheEntry) IsExpired() bool {
	return time.Since(entry.CreatedAt) > entry.TTL
}

// DNSCache 优化的DNS缓存
type DNSCache struct {
	mu           sync.RWMutex              // 读写锁
	forwardCache map[string]*DNSCacheEntry // 正向解析缓存 (域名->IP)
	reverseCache map[string]*DNSCacheEntry // 反向解析缓存 (IP->域名)
	maxSize      int                       // 最大缓存大小
	defaultTTL   time.Duration             // 默认TTL
	cleanupTick  time.Duration             // 清理间隔
	stopCleanup  chan struct{}             // 停止清理信号
	stats        DNSCacheStats             // 缓存统计
}

// DNSCacheStats DNS缓存统计信息
type DNSCacheStats struct {
	ForwardHits   int64 // 正向解析命中次数
	ForwardMisses int64 // 正向解析未命中次数
	ReverseHits   int64 // 反向解析命中次数
	ReverseMisses int64 // 反向解析未命中次数
	Evictions     int64 // 驱逐次数
	ResolveTime   int64 // 总解析时间（纳秒）
	ResolveCount  int64 // 解析次数
}

// NewDNSCache 创建新的DNS缓存
func NewDNSCache(maxSize int, defaultTTL time.Duration) *DNSCache {
	if maxSize <= 0 {
		maxSize = 10000 // 默认最大缓存10000个条目
	}
	if defaultTTL <= 0 {
		defaultTTL = 5 * time.Minute // 默认5分钟TTL
	}

	cache := &DNSCache{
		forwardCache: make(map[string]*DNSCacheEntry),
		reverseCache: make(map[string]*DNSCacheEntry),
		maxSize:      maxSize,
		defaultTTL:   defaultTTL,
		cleanupTick:  defaultTTL / 4, // 每1/4 TTL时间清理一次
		stopCleanup:  make(chan struct{}),
	}

	// 启动后台清理协程
	go cache.startCleanup()

	return cache
}

// LookupIP 查询域名对应的IP地址（带缓存）
func (dc *DNSCache) LookupIP(ctx context.Context, host string, resolver func(ctx context.Context, host string) ([]net.IP, error)) ([]net.IP, error) {
	// 先尝试从缓存获取
	if ips := dc.getForwardCache(host); ips != nil {
		dc.stats.ForwardHits++
		return ips, nil
	}

	dc.stats.ForwardMisses++

	// 缓存未命中，执行实际解析
	start := time.Now()
	ips, err := resolver(ctx, host)
	resolveTime := time.Since(start)

	// 记录解析时间统计
	dc.stats.ResolveTime += resolveTime.Nanoseconds()
	dc.stats.ResolveCount++

	if err != nil {
		return nil, err
	}

	// 缓存解析结果
	dc.setForwardCache(host, ips, dc.defaultTTL)

	return ips, nil
}

// LookupAddr 查询IP对应的域名（带缓存）
func (dc *DNSCache) LookupAddr(ctx context.Context, addr string, resolver func(ctx context.Context, addr string) ([]string, error)) ([]string, error) {
	// 先尝试从缓存获取
	if names := dc.getReverseCache(addr); names != nil {
		dc.stats.ReverseHits++
		return names, nil
	}

	dc.stats.ReverseMisses++

	// 缓存未命中，执行实际解析
	start := time.Now()
	names, err := resolver(ctx, addr)
	resolveTime := time.Since(start)

	// 记录解析时间统计
	dc.stats.ResolveTime += resolveTime.Nanoseconds()
	dc.stats.ResolveCount++

	if err != nil {
		return nil, err
	}

	// 缓存解析结果
	dc.setReverseCache(addr, names, dc.defaultTTL)

	return names, nil
}

// getForwardCache 从正向缓存获取IP
func (dc *DNSCache) getForwardCache(host string) []net.IP {
	dc.mu.RLock()
	entry, exists := dc.forwardCache[host]
	dc.mu.RUnlock()

	if !exists || entry.IsExpired() {
		if exists {
			// 删除过期条目
			dc.mu.Lock()
			delete(dc.forwardCache, host)
			dc.mu.Unlock()
			dc.stats.Evictions++
		}
		return nil
	}

	// 更新使用时间和命中次数
	dc.mu.Lock()
	entry.LastUsed = time.Now()
	entry.HitCount++
	dc.mu.Unlock()

	return entry.IPs
}

// setForwardCache 设置正向缓存
func (dc *DNSCache) setForwardCache(host string, ips []net.IP, ttl time.Duration) {
	dc.mu.Lock()
	defer dc.mu.Unlock()

	// 检查缓存大小，如果超过限制则清理
	if len(dc.forwardCache)+len(dc.reverseCache) >= dc.maxSize {
		dc.evictLRU()
	}

	now := time.Now()
	dc.forwardCache[host] = &DNSCacheEntry{
		IPs:       ips,
		CreatedAt: now,
		LastUsed:  now,
		HitCount:  0,
		TTL:       ttl,
	}
}

// getReverseCache 从反向缓存获取域名
func (dc *DNSCache) getReverseCache(addr string) []string {
	dc.mu.RLock()
	entry, exists := dc.reverseCache[addr]
	dc.mu.RUnlock()

	if !exists || entry.IsExpired() {
		if exists {
			// 删除过期条目
			dc.mu.Lock()
			delete(dc.reverseCache, addr)
			dc.mu.Unlock()
			dc.stats.Evictions++
		}
		return nil
	}

	// 更新使用时间和命中次数
	dc.mu.Lock()
	entry.LastUsed = time.Now()
	entry.HitCount++
	dc.mu.Unlock()

	return entry.Names
}

// setReverseCache 设置反向缓存
func (dc *DNSCache) setReverseCache(addr string, names []string, ttl time.Duration) {
	dc.mu.Lock()
	defer dc.mu.Unlock()

	// 检查缓存大小，如果超过限制则清理
	if len(dc.forwardCache)+len(dc.reverseCache) >= dc.maxSize {
		dc.evictLRU()
	}

	now := time.Now()
	dc.reverseCache[addr] = &DNSCacheEntry{
		Names:     names,
		CreatedAt: now,
		LastUsed:  now,
		HitCount:  0,
		TTL:       ttl,
	}
}

// evictLRU 驱逐最少使用的缓存条目
func (dc *DNSCache) evictLRU() {
	var oldestKey string
	var oldestTime time.Time
	var lowestHitCount int64 = -1
	var isForward bool

	// 在正向缓存中查找最少使用的条目
	for key, entry := range dc.forwardCache {
		if lowestHitCount == -1 || entry.HitCount < lowestHitCount ||
			(entry.HitCount == lowestHitCount && (oldestTime.IsZero() || entry.LastUsed.Before(oldestTime))) {
			oldestKey = key
			oldestTime = entry.LastUsed
			lowestHitCount = entry.HitCount
			isForward = true
		}
	}

	// 在反向缓存中查找最少使用的条目
	for key, entry := range dc.reverseCache {
		if lowestHitCount == -1 || entry.HitCount < lowestHitCount ||
			(entry.HitCount == lowestHitCount && (oldestTime.IsZero() || entry.LastUsed.Before(oldestTime))) {
			oldestKey = key
			oldestTime = entry.LastUsed
			lowestHitCount = entry.HitCount
			isForward = false
		}
	}

	// 删除最少使用的条目
	if oldestKey != "" {
		if isForward {
			delete(dc.forwardCache, oldestKey)
		} else {
			delete(dc.reverseCache, oldestKey)
		}
		dc.stats.Evictions++
	}
}

// startCleanup 启动后台清理过期条目
func (dc *DNSCache) startCleanup() {
	ticker := time.NewTicker(dc.cleanupTick)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			dc.cleanup()
		case <-dc.stopCleanup:
			return
		}
	}
}

// cleanup 清理过期的缓存条目
func (dc *DNSCache) cleanup() {
	dc.mu.Lock()
	defer dc.mu.Unlock()

	// 清理正向缓存中的过期条目
	for key, entry := range dc.forwardCache {
		if entry.IsExpired() {
			delete(dc.forwardCache, key)
			dc.stats.Evictions++
		}
	}

	// 清理反向缓存中的过期条目
	for key, entry := range dc.reverseCache {
		if entry.IsExpired() {
			delete(dc.reverseCache, key)
			dc.stats.Evictions++
		}
	}
}

// GetStats 获取DNS缓存统计信息
func (dc *DNSCache) GetStats() DNSCacheStats {
	return dc.stats
}

// GetForwardHitRate 获取正向解析缓存命中率
func (dc *DNSCache) GetForwardHitRate() float64 {
	total := dc.stats.ForwardHits + dc.stats.ForwardMisses
	if total == 0 {
		return 0
	}
	return float64(dc.stats.ForwardHits) / float64(total)
}

// GetReverseHitRate 获取反向解析缓存命中率
func (dc *DNSCache) GetReverseHitRate() float64 {
	total := dc.stats.ReverseHits + dc.stats.ReverseMisses
	if total == 0 {
		return 0
	}
	return float64(dc.stats.ReverseHits) / float64(total)
}

// GetAverageResolveTime 获取平均解析时间
func (dc *DNSCache) GetAverageResolveTime() time.Duration {
	if dc.stats.ResolveCount == 0 {
		return 0
	}
	return time.Duration(dc.stats.ResolveTime / dc.stats.ResolveCount)
}

// Size 获取当前缓存大小
func (dc *DNSCache) Size() (forward, reverse int) {
	dc.mu.RLock()
	defer dc.mu.RUnlock()
	return len(dc.forwardCache), len(dc.reverseCache)
}

// Clear 清空缓存
func (dc *DNSCache) Clear() {
	dc.mu.Lock()
	defer dc.mu.Unlock()
	dc.forwardCache = make(map[string]*DNSCacheEntry)
	dc.reverseCache = make(map[string]*DNSCacheEntry)
}

// Close 关闭缓存，停止后台清理
func (dc *DNSCache) Close() {
	close(dc.stopCleanup)
}
