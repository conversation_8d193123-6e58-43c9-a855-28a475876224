// regex_cache.go - 优化的正则表达式缓存管理
package cache

import (
	"regexp"
	"sync"
	"time"
)

// RegexCacheEntry 正则表达式缓存条目
type RegexCacheEntry struct {
	Regex     *regexp.Regexp // 编译后的正则表达式
	LastUsed  time.Time      // 最后使用时间
	HitCount  int64          // 命中次数
	CreatedAt time.Time      // 创建时间
}

// RegexCache 优化的正则表达式缓存
type RegexCache struct {
	mu          sync.RWMutex                // 读写锁，提高并发性能
	cache       map[string]*RegexCacheEntry // 缓存存储
	maxSize     int                         // 最大缓存大小
	ttl         time.Duration               // 缓存生存时间
	cleanupTick time.Duration               // 清理间隔
	stopCleanup chan struct{}               // 停止清理信号
	stats       CacheStats                  // 缓存统计
}

// CacheStats 缓存统计信息
type CacheStats struct {
	Hits        int64 // 命中次数
	Misses      int64 // 未命中次数
	Evictions   int64 // 驱逐次数
	CompileTime int64 // 编译时间（纳秒）
}

// NewRegexCache 创建新的正则表达式缓存
func NewRegexCache(maxSize int, ttl time.Duration) *RegexCache {
	if maxSize <= 0 {
		maxSize = 1000 // 默认最大缓存1000个正则表达式
	}
	if ttl <= 0 {
		ttl = 30 * time.Minute // 默认30分钟TTL
	}

	cache := &RegexCache{
		cache:       make(map[string]*RegexCacheEntry),
		maxSize:     maxSize,
		ttl:         ttl,
		cleanupTick: ttl / 4, // 每1/4 TTL时间清理一次
		stopCleanup: make(chan struct{}),
	}

	// 启动后台清理协程
	go cache.startCleanup()

	return cache
}

// Get 获取编译后的正则表达式
func (rc *RegexCache) Get(pattern string) (*regexp.Regexp, bool) {
	rc.mu.RLock()
	entry, exists := rc.cache[pattern]
	rc.mu.RUnlock()

	if !exists {
		rc.stats.Misses++
		return nil, false
	}

	// 检查是否过期
	if time.Since(entry.CreatedAt) > rc.ttl {
		rc.mu.Lock()
		delete(rc.cache, pattern)
		rc.mu.Unlock()
		rc.stats.Misses++
		rc.stats.Evictions++
		return nil, false
	}

	// 更新使用时间和命中次数
	rc.mu.Lock()
	entry.LastUsed = time.Now()
	entry.HitCount++
	rc.mu.Unlock()

	rc.stats.Hits++
	return entry.Regex, true
}

// Set 设置编译后的正则表达式到缓存
func (rc *RegexCache) Set(pattern string, regex *regexp.Regexp) {
	rc.mu.Lock()
	defer rc.mu.Unlock()

	// 检查缓存大小，如果超过限制则清理最少使用的条目
	if len(rc.cache) >= rc.maxSize {
		rc.evictLRU()
	}

	now := time.Now()
	rc.cache[pattern] = &RegexCacheEntry{
		Regex:     regex,
		LastUsed:  now,
		HitCount:  0,
		CreatedAt: now,
	}
}

// Compile 编译正则表达式并缓存
func (rc *RegexCache) Compile(pattern string) (*regexp.Regexp, error) {
	// 先尝试从缓存获取
	if regex, exists := rc.Get(pattern); exists {
		return regex, nil
	}

	// 编译新的正则表达式
	start := time.Now()
	regex, err := regexp.Compile(pattern)
	compileTime := time.Since(start)

	if err != nil {
		return nil, err
	}

	// 记录编译时间
	rc.stats.CompileTime += compileTime.Nanoseconds()

	// 缓存编译结果
	rc.Set(pattern, regex)

	return regex, nil
}

// evictLRU 驱逐最少使用的缓存条目
func (rc *RegexCache) evictLRU() {
	var oldestPattern string
	var oldestTime time.Time
	var lowestHitCount int64 = -1

	// 找到最少使用的条目（优先考虑命中次数，其次考虑最后使用时间）
	for pattern, entry := range rc.cache {
		if lowestHitCount == -1 || entry.HitCount < lowestHitCount ||
			(entry.HitCount == lowestHitCount && (oldestTime.IsZero() || entry.LastUsed.Before(oldestTime))) {
			oldestPattern = pattern
			oldestTime = entry.LastUsed
			lowestHitCount = entry.HitCount
		}
	}

	if oldestPattern != "" {
		delete(rc.cache, oldestPattern)
		rc.stats.Evictions++
	}
}

// startCleanup 启动后台清理过期条目
func (rc *RegexCache) startCleanup() {
	ticker := time.NewTicker(rc.cleanupTick)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			rc.cleanup()
		case <-rc.stopCleanup:
			return
		}
	}
}

// cleanup 清理过期的缓存条目
func (rc *RegexCache) cleanup() {
	rc.mu.Lock()
	defer rc.mu.Unlock()

	now := time.Now()
	for pattern, entry := range rc.cache {
		if now.Sub(entry.CreatedAt) > rc.ttl {
			delete(rc.cache, pattern)
			rc.stats.Evictions++
		}
	}
}

// GetStats 获取缓存统计信息
func (rc *RegexCache) GetStats() CacheStats {
	return rc.stats
}

// GetHitRate 获取缓存命中率
func (rc *RegexCache) GetHitRate() float64 {
	total := rc.stats.Hits + rc.stats.Misses
	if total == 0 {
		return 0
	}
	return float64(rc.stats.Hits) / float64(total)
}

// Size 获取当前缓存大小
func (rc *RegexCache) Size() int {
	rc.mu.RLock()
	defer rc.mu.RUnlock()
	return len(rc.cache)
}

// Clear 清空缓存
func (rc *RegexCache) Clear() {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	rc.cache = make(map[string]*RegexCacheEntry)
}

// Close 关闭缓存，停止后台清理
func (rc *RegexCache) Close() {
	close(rc.stopCleanup)
}
