package trigger

import (
	"context"
	"fmt"
	"time"

	"flexproxy/common/errors"
	"flexproxy/internal/interfaces"
)

// ErrorHandlingStrategy 错误处理策略枚举
type ErrorHandlingStrategy int

const (
	ErrorHandlingContinue ErrorHandlingStrategy = iota // 继续执行后续触发器
	ErrorHandlingStop                                  // 停止执行后续触发器
	ErrorHandlingRetry                                 // 重试当前触发器
	ErrorHandlingSkip                                  // 跳过当前触发器，继续执行后续触发器
)

// String 返回策略的字符串表示
func (s ErrorHandlingStrategy) String() string {
	switch s {
	case ErrorHandlingContinue:
		return "continue"
	case ErrorHandlingStop:
		return "stop"
	case ErrorHandlingRetry:
		return "retry"
	case ErrorHandlingSkip:
		return "skip"
	default:
		return "unknown"
	}
}

// TriggerErrorHandler 触发器错误处理器
type TriggerErrorHandler struct {
	strategy   ErrorHandlingStrategy
	maxRetries int
	retryDelay time.Duration
	logger     interfaces.LogService
}

// NewTriggerErrorHandler 创建新的错误处理器
func NewTriggerErrorHandler(strategy ErrorHandlingStrategy, maxRetries int, retryDelay time.Duration, logger interfaces.LogService) *TriggerErrorHandler {
	return &TriggerErrorHandler{
		strategy:   strategy,
		maxRetries: maxRetries,
		retryDelay: retryDelay,
		logger:     logger,
	}
}

// HandleError 处理触发器执行错误
func (teh *TriggerErrorHandler) HandleError(ctx context.Context, err error, triggerName string, retryCount int) (shouldContinue bool, shouldRetry bool) {
	if err == nil {
		return true, false
	}

	// 记录错误到全局追踪器
	traceID := errors.GenerateTraceID()
	if flexErr, ok := err.(*errors.FlexProxyError); ok {
		errors.TrackErrorGlobal(traceID, flexErr)
	} else {
		// 将普通错误转换为FlexProxyError
		flexErr := errors.NewError(errors.ErrTypeTrigger, errors.ErrCodeTriggerExecutionFailed, err.Error())
		errors.TrackErrorGlobal(traceID, flexErr)
	}

	// 根据策略处理错误
	switch teh.strategy {
	case ErrorHandlingContinue:
		return teh.handleContinue(err, triggerName)
	case ErrorHandlingStop:
		return teh.handleStop(err, triggerName)
	case ErrorHandlingRetry:
		return teh.handleRetry(err, triggerName, retryCount)
	case ErrorHandlingSkip:
		return teh.handleSkip(err, triggerName)
	default:
		return teh.handleContinue(err, triggerName)
	}
}

// handleContinue 处理继续策略
func (teh *TriggerErrorHandler) handleContinue(err error, triggerName string) (bool, bool) {
	teh.logger.Warn(fmt.Sprintf("触发器 '%s' 执行错误，继续执行后续触发器: %v", triggerName, err))
	return true, false
}

// handleStop 处理停止策略
func (teh *TriggerErrorHandler) handleStop(err error, triggerName string) (bool, bool) {
	teh.logger.Error(fmt.Sprintf("触发器 '%s' 执行错误，停止执行后续触发器: %v", triggerName, err))
	return false, false
}

// handleRetry 处理重试策略
func (teh *TriggerErrorHandler) handleRetry(err error, triggerName string, retryCount int) (bool, bool) {
	if retryCount >= teh.maxRetries {
		teh.logger.Error(fmt.Sprintf("触发器 '%s' 重试次数已达上限 (%d)，停止重试: %v", triggerName, teh.maxRetries, err))
		return false, false
	}

	teh.logger.Warn(fmt.Sprintf("触发器 '%s' 执行错误，将进行第 %d 次重试: %v", triggerName, retryCount+1, err))

	// 如果设置了重试延迟，则等待
	if teh.retryDelay > 0 {
		time.Sleep(teh.retryDelay)
	}

	return true, true
}

// handleSkip 处理跳过策略
func (teh *TriggerErrorHandler) handleSkip(err error, triggerName string) (bool, bool) {
	teh.logger.Warn(fmt.Sprintf("触发器 '%s' 执行错误，跳过当前触发器: %v", triggerName, err))
	return true, false
}

// IsRetryableError 判断错误是否可重试
func (teh *TriggerErrorHandler) IsRetryableError(err error) bool {
	if err == nil {
		return false
	}

	// 检查是否为FlexProxy错误
	if flexErr, ok := err.(*errors.FlexProxyError); ok {
		switch flexErr.Type {
		case errors.ErrTypeNetwork, errors.ErrTypeTimeout:
			return true
		case errors.ErrTypeValidation, errors.ErrTypeConfiguration:
			return false
		default:
			return true
		}
	}

	// 对于其他类型的错误，默认可重试
	return true
}

// GetErrorSummary 获取错误摘要信息
func (teh *TriggerErrorHandler) GetErrorSummary(triggerName string) map[string]interface{} {
	return map[string]interface{}{
		"trigger_name": triggerName,
		"strategy":     teh.strategy.String(),
		"max_retries":  teh.maxRetries,
		"retry_delay":  teh.retryDelay.String(),
	}
}

// ErrorHandlerConfig 错误处理器配置
type ErrorHandlerConfig struct {
	Strategy   string        `yaml:"strategy" validate:"oneof=continue stop retry skip"`
	MaxRetries int           `yaml:"max_retries" validate:"min=0,max=10"`
	RetryDelay time.Duration `yaml:"retry_delay"`
}

// DefaultErrorHandlerConfig 默认错误处理器配置
func DefaultErrorHandlerConfig() *ErrorHandlerConfig {
	return &ErrorHandlerConfig{
		Strategy:   "continue",
		MaxRetries: 3,
		RetryDelay: time.Second,
	}
}

// ParseStrategy 解析策略字符串
func ParseStrategy(strategy string) ErrorHandlingStrategy {
	switch strategy {
	case "continue":
		return ErrorHandlingContinue
	case "stop":
		return ErrorHandlingStop
	case "retry":
		return ErrorHandlingRetry
	case "skip":
		return ErrorHandlingSkip
	default:
		return ErrorHandlingContinue
	}
}
