package trigger

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/dop251/goja"
	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
	"flexproxy/internal/core/encoding"
)

// JavaScriptTrigger JavaScript触发器
type JavaScriptTrigger struct {
	Code         string
	VM           *goja.Runtime
	Priority     int
	ProcessStage ProcessStage
	Actions      []config.ActionConfig
}

// NewJavaScriptTrigger 创建JavaScript触发器
func NewJavaScriptTrigger(code string, priority int, stage ProcessStage, actions []config.ActionConfig) *JavaScriptTrigger {
	vm := goja.New()
	_, err := vm.RunString(`
        function matchRegex(str, pattern) { return new RegExp(pattern).test(str); }
        function parseURL(url) { try { const parsed = new URL(url); return { protocol: parsed.protocol, hostname: parsed.hostname, pathname: parsed.pathname, search: parsed.search, hash: parsed.hash }; } catch(e) { return null; } }
        function base64Encode(str) { return btoa(str); }
        function base64Decode(str) { return atob(str); }
    `)
	if err != nil {
		triggerLogger.GetRawLogger().Errorf("预加载JavaScript工具函数失败: %v", err)
	}
	return &JavaScriptTrigger{
		Code:         code,
		VM:           vm,
		Priority:     priority,
		ProcessStage: stage,
		Actions:      actions,
	}
}

// Match 执行JavaScript代码判断是否匹配
func (t *JavaScriptTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	options := map[string]interface{}{
		"url":         req.URL.String(),
		"method":      req.Method,
		"time_passed": reqTime.Milliseconds(),
		"headers":     getHeadersMap(req.Header),
	}
	if req.Body != nil {
		bodyBytes, err := readBodyWithoutConsuming(req)
		if err == nil {
			// 使用专业的字符编码转换器确保健壮性
			converter := encoding.NewCharsetConverter()
			bodyStrBytes, convErr := converter.Convert(bodyBytes, constants.CharsetUTF8, constants.CharsetUTF8)
			if convErr != nil {
				// 如果编码转换失败，记录错误但继续使用原始数据
				advancedLogger := logger.GetTriggerLogger()
				advancedLogger.Warn(fmt.Sprintf("JavaScript触发器请求body编码转换失败，使用原始数据: %v", convErr))
				bodyStrBytes = bodyBytes
			}
			options["request_body"] = string(bodyStrBytes)
		}
	}
	if resp != nil {
		options["status"] = resp.StatusCode
		options["response_headers"] = getHeadersMap(resp.Header)
		if resp.Body != nil {
			bodyBytes, err := readBodyWithoutConsuming(resp)
			if err == nil {
				// 使用专业的字符编码转换器确保健壮性
				converter := encoding.NewCharsetConverter()
				bodyStrBytes, convErr := converter.Convert(bodyBytes, constants.CharsetUTF8, constants.CharsetUTF8)
				if convErr != nil {
					// 如果编码转换失败，记录错误但继续使用原始数据
					advancedLogger := logger.GetTriggerLogger()
					advancedLogger.Warn(fmt.Sprintf("JavaScript触发器响应body编码转换失败，使用原始数据: %v", convErr))
					bodyStrBytes = bodyBytes
				}
				options["response_body"] = string(bodyStrBytes)
			}
		}
	}
	err := t.VM.Set("opt", options)
	if err != nil {
		return false
	}
	val, err := t.VM.RunString(t.Code)
	if err != nil {
		return false
	}
	result, ok := val.Export().(bool)
	if !ok {
		return false
	}
	return result
}

func (t *JavaScriptTrigger) GetPriority() int                  { return t.Priority }
func (t *JavaScriptTrigger) GetProcessStage() ProcessStage     { return t.ProcessStage }
func (t *JavaScriptTrigger) GetActions() []config.ActionConfig { return t.Actions }

// GetMatchedActions 获取匹配的动作
func (t *JavaScriptTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []config.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []config.ActionConfig{}
}



type MultiConditionTrigger struct {
	Conditions    []Trigger
	LogicOperator string
	Priority      int
	ProcessStage  ProcessStage
	Actions       []config.ActionConfig
}

func NewMultiConditionTrigger(conditions []Trigger, operator string, priority int, stage ProcessStage, actions []config.ActionConfig) *MultiConditionTrigger {
	return &MultiConditionTrigger{Conditions: conditions, LogicOperator: operator, Priority: priority, ProcessStage: stage, Actions: actions}
}
func (t *MultiConditionTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if len(t.Conditions) == 0 {
		return false
	}
	switch t.LogicOperator {
	case "AND":
		for _, cond := range t.Conditions {
			if !cond.Match(req, resp, reqTime) {
				return false
			}
		}
		return true
	case "OR":
		for _, cond := range t.Conditions {
			if cond.Match(req, resp, reqTime) {
				return true
			}
		}
		return false
	case "NOT":
		if len(t.Conditions) > 0 {
			return !t.Conditions[0].Match(req, resp, reqTime)
		}
		return false
	default:
		for _, cond := range t.Conditions {
			if cond.Match(req, resp, reqTime) {
				return true
			}
		}
		return false
	}
}
func (t *MultiConditionTrigger) GetPriority() int                  { return t.Priority }
func (t *MultiConditionTrigger) GetProcessStage() ProcessStage     { return t.ProcessStage }
func (t *MultiConditionTrigger) GetActions() []config.ActionConfig { return t.Actions }

// GetMatchedActions 获取匹配的动作
func (t *MultiConditionTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []config.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []config.ActionConfig{}
}



func readBodyWithoutConsuming(obj interface{}) ([]byte, error) {
	var body io.ReadCloser
	var err error
	var bodyBytes []byte
	switch v := obj.(type) {
	case *http.Request:
		if v.Body == nil {
			return nil, nil
		}
		body = v.Body
		bodyBytes, err = io.ReadAll(body)
		if err != nil {
			return nil, err
		}
		v.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	case *http.Response:
		if v.Body == nil {
			return nil, nil
		}
		body = v.Body
		bodyBytes, err = io.ReadAll(body)
		if err != nil {
			return nil, err
		}
		v.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	default:
		return nil, errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "不支持的类型", fmt.Sprintf("type: %T", obj))
	}
	return bodyBytes, nil
}
func getHeadersMap(header http.Header) map[string]string {
	headers := make(map[string]string)
	for name, values := range header {
		if len(values) > 0 {
			headers[name] = strings.Join(values, ", ")
		}
	}
	return headers
}
