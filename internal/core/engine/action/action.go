package action

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
	charsetencoding "flexproxy/internal/core/encoding"
	"flexproxy/internal/core/engine/action/utils"
	"flexproxy/internal/core/validation"
	"flexproxy/internal/interfaces"
)

// =============================================================================
// Executor接口定义
// =============================================================================

// Executor 定义动作执行器的接口
type Executor interface {
	// Execute 执行动作
	Execute(ctx context.Context, parameters map[string]interface{}) error
	// Validate 验证参数
	Validate(parameters map[string]interface{}) error
	// GetType 获取执行器类型
	GetType() string
	// GetDescription 获取执行器描述
	GetDescription() string
}

// HTTPExecutor 定义可以处理HTTP请求/响应的执行器接口
type HTTPExecutor interface {
	Executor
	// ExecuteHTTP 执行HTTP相关动作，可以修改请求或响应
	ExecuteHTTP(ctx context.Context, parameters map[string]interface{}, req *http.Request, resp *http.Response) (*http.Request, *http.Response, error)
}

// =============================================================================
// 数据结构定义
// =============================================================================

// BodyConfig 定义body修改的详细配置
type BodyConfig struct {
	Content     string `json:"content" yaml:"content"`           // 内容
	ContentType string `json:"content_type" yaml:"content_type"` // MIME类型
	Format      string `json:"format" yaml:"format"`             // 格式：json, xml, html, text, form, binary
	Encoding    string `json:"encoding" yaml:"encoding"`         // 编码：utf-8, base64
}

// MatchType 定义匹配类型
type MatchType string

const (
	MatchTypeExact    MatchType = "exact"    // 精确匹配
	MatchTypeContains MatchType = "contains" // 包含匹配
	MatchTypeWildcard MatchType = "wildcard" // 通配符匹配
	MatchTypeRegex    MatchType = "regex"    // 正则表达式匹配
)

// OperationType 定义操作类型
type OperationType string

const (
	OperationTypeAdd     OperationType = "add"     // 添加
	OperationTypeReplace OperationType = "replace" // 替换
	OperationTypeRemove  OperationType = "remove"  // 删除
	OperationTypeAppend  OperationType = "append"  // 追加
)

// ConditionType 定义条件类型
type ConditionType string

const (
	ConditionExists     ConditionType = "exists"      // 存在
	ConditionNotExists  ConditionType = "not_exists"  // 不存在
	ConditionValueMatch ConditionType = "value_match" // 值匹配
)

// HeaderKeywordOperation 定义Header关键字操作
type HeaderKeywordOperation struct {
	Operation     OperationType `json:"operation" yaml:"operation"`           // 操作类型
	MatchType     MatchType     `json:"match_type" yaml:"match_type"`         // 匹配类型
	Pattern       string        `json:"pattern" yaml:"pattern"`               // 匹配模式
	ValuePattern  string        `json:"value_pattern" yaml:"value_pattern"`   // 值匹配模式
	Replacement   string        `json:"replacement" yaml:"replacement"`       // 替换值
	NewValue      string        `json:"new_value" yaml:"new_value"`           // 新值
	Condition     ConditionType `json:"condition" yaml:"condition"`           // 条件
	CaseSensitive bool          `json:"case_sensitive" yaml:"case_sensitive"` // 大小写敏感
}

// BodyKeywordOperation 定义Body关键字操作
type BodyKeywordOperation struct {
	Operation         OperationType `json:"operation" yaml:"operation"`                   // 操作类型
	Format            string        `json:"format" yaml:"format"`                         // 内容格式
	MatchType         MatchType     `json:"match_type" yaml:"match_type"`                 // 匹配类型
	Pattern           string        `json:"pattern" yaml:"pattern"`                       // 匹配模式
	Replacement       string        `json:"replacement" yaml:"replacement"`               // 替换值
	CaseSensitive     bool          `json:"case_sensitive" yaml:"case_sensitive"`         // 大小写敏感
	PreserveStructure bool          `json:"preserve_structure" yaml:"preserve_structure"` // 保持结构完整性
	JSONPath          string        `json:"json_path" yaml:"json_path"`                   // JSON路径（可选）
}

// KeywordOperations 定义关键字操作集合
type KeywordOperations struct {
	Headers []HeaderKeywordOperation `json:"headers" yaml:"headers"` // Header操作列表
	Body    []BodyKeywordOperation   `json:"body" yaml:"body"`       // Body操作列表
}

// =============================================================================
// 关键字匹配引擎
// =============================================================================

// matchPattern 通用模式匹配函数
func matchPattern(text, pattern string, matchType MatchType, caseSensitive bool) (bool, error) {
	if !caseSensitive {
		text = strings.ToLower(text)
		pattern = strings.ToLower(pattern)
	}

	switch matchType {
	case MatchTypeExact:
		return text == pattern, nil
	case MatchTypeContains:
		return strings.Contains(text, pattern), nil
	case MatchTypeWildcard:
		matched, err := filepath.Match(pattern, text)
		return matched, err
	case MatchTypeRegex:
		matched, err := regexp.MatchString(pattern, text)
		return matched, err
	default:
		return false, errors.WrapError(nil, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "不支持的匹配类型: "+string(matchType))
	}
}

// matchHeaderCondition 检查Header条件
func matchHeaderCondition(header http.Header, headerName string, condition ConditionType, valuePattern string, caseSensitive bool) bool {
	headerValue := header.Get(headerName)

	switch condition {
	case ConditionExists:
		return headerValue != ""
	case ConditionNotExists:
		return headerValue == ""
	case ConditionValueMatch:
		if valuePattern == "" {
			return true
		}
		matched, err := matchPattern(headerValue, valuePattern, MatchTypeRegex, caseSensitive)
		return err == nil && matched
	default:
		return true
	}
}

// =============================================================================
// 辅助函数
// =============================================================================

// getContentTypeByFormat 根据格式获取Content-Type
func getContentTypeByFormat(format string) string {
	switch strings.ToLower(format) {
	case constants.FormatJSON:
		return constants.ContentTypeJSON
	case constants.FormatXML:
		return constants.ContentTypeXML
	case constants.FormatHTML:
		return constants.ContentTypeHTML
	case constants.FormatText, constants.FormatPlain:
		return constants.ContentTypeText
	case constants.FormatForm, constants.FormatURLEncoded:
		return constants.ContentTypeForm
	case constants.FormatBinary, constants.FormatOctet:
		return "application/octet-stream"
	default:
		return constants.ContentTypeText
	}
}

// detectContentType 自动检测内容类型
func detectContentType(content string) string {
	content = strings.TrimSpace(content)

	// 检测JSON
	if (strings.HasPrefix(content, "{") && strings.HasSuffix(content, "}")) ||
		(strings.HasPrefix(content, "[") && strings.HasSuffix(content, "]")) {
		var js json.RawMessage
		if json.Unmarshal([]byte(content), &js) == nil {
			return constants.ContentTypeJSON
		}
	}

	// 检测XML
	if strings.HasPrefix(content, "<") && strings.HasSuffix(content, ">") {
		if strings.Contains(content, "<?xml") || strings.Contains(content, "<html") {
			if strings.Contains(content, "<html") {
				return "text/html"
			}
			return "application/xml"
		}
	}

	// 检测URL编码格式
	if strings.Contains(content, "=") && strings.Contains(content, "&") {
		return "application/x-www-form-urlencoded"
	}

	// 默认为纯文本
	return constants.ContentTypeText
}

// validateContent 验证内容格式
func validateContent(content, format string) error {
	switch strings.ToLower(format) {
	case constants.FormatJSON:
		var js json.RawMessage
		if err := json.Unmarshal([]byte(content), &js); err != nil {
			return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "无效的JSON格式")
		}
	case constants.FormatXML:
		if err := xml.Unmarshal([]byte(content), &struct{}{}); err != nil {
			// XML验证比较复杂，这里只做基本检查
			if !strings.Contains(content, "<") || !strings.Contains(content, ">") {
				return errors.ErrInvalidXMLFormat
			}
		}
	}
	return nil
}

// validateBodyConfig 验证body_config参数
func validateBodyConfig(bodyConfigRaw interface{}, actionType string) error {
	bodyConfigMap, ok := bodyConfigRaw.(map[string]interface{})
	if !ok {
		return errors.WrapError(nil, errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的body_config参数必须是对象格式", actionType))
	}

	// 验证content字段（必需）
	contentRaw, hasContent := bodyConfigMap["content"]
	if !hasContent {
		return errors.WrapError(nil, errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
			fmt.Sprintf("%s动作的body_config.content字段是必需的", actionType))
	}
	content, ok := contentRaw.(string)
	if !ok {
		return errors.WrapError(nil, errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的body_config.content必须是字符串", actionType))
	}

	// 验证format字段
	var format string
	if formatRaw, hasFormat := bodyConfigMap["format"]; hasFormat {
		var ok bool
		format, ok = formatRaw.(string)
		if !ok {
			return errors.WrapError(nil, errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.format必须是字符串", actionType))
		}

		// 验证format的有效值
		isValidFormat := false
		formatLower := strings.ToLower(format)
		for _, validFormat := range constants.ValidFormats {
			if formatLower == validFormat {
				isValidFormat = true
				break
			}
		}
		if !isValidFormat {
			return errors.WrapError(nil, errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.format值无效: %s，支持的格式: %s",
					actionType, format, strings.Join(constants.ValidFormats, ", ")))
		}
	} else {
		format = constants.FormatAuto // 默认自动检测
	}

	// 验证encoding字段
	if encodingRaw, hasEncoding := bodyConfigMap["encoding"]; hasEncoding {
		encoding, ok := encodingRaw.(string)
		if !ok {
			return errors.WrapError(nil, errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.encoding必须是字符串", actionType))
		}

		// 验证encoding的有效值
		isValidEncoding := false
		encodingLower := strings.ToLower(encoding)
		for _, validEncoding := range constants.ValidEncodings {
			if encodingLower == validEncoding {
				isValidEncoding = true
				break
			}
		}
		if !isValidEncoding {
			return errors.WrapError(nil, errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.encoding值无效: %s，支持的编码: %s",
					actionType, encoding, strings.Join(constants.ValidEncodings, ", ")))
		}
	}

	// 验证content_type字段
	if contentTypeRaw, hasContentType := bodyConfigMap["content_type"]; hasContentType {
		if _, ok := contentTypeRaw.(string); !ok {
			return errors.WrapError(nil, errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.content_type必须是字符串", actionType))
		}
	}

	// 验证auto_content_type字段
	if autoContentTypeRaw, hasAutoContentType := bodyConfigMap["auto_content_type"]; hasAutoContentType {
		if _, ok := autoContentTypeRaw.(bool); !ok {
			return errors.WrapError(nil, errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.auto_content_type必须是布尔值", actionType))
		}
	}

	// 根据format验证content内容
	if format != constants.FormatAuto && format != constants.FormatBinary && format != constants.FormatOctet {
		if err := validateContent(content, format); err != nil {
			return errors.WrapError(err, errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的body_config.content格式验证失败", actionType))
		}
	}

	return nil
}

// validateKeywordOperations 验证keyword_operations参数
func validateKeywordOperations(keywordOpsRaw interface{}, actionType string) error {
	keywordOpsMap, ok := keywordOpsRaw.(map[string]interface{})
	if !ok {
		return errors.WrapError(nil, errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的keyword_operations参数必须是对象格式", actionType))
	}

	// 验证headers操作
	if headersRaw, hasHeaders := keywordOpsMap["headers"]; hasHeaders {
		if err := validateHeaderKeywordOperations(headersRaw, actionType); err != nil {
			return err
		}
	}

	// 验证body操作
	if bodyRaw, hasBody := keywordOpsMap["body"]; hasBody {
		if err := validateBodyKeywordOperations(bodyRaw, actionType); err != nil {
			return err
		}
	}

	return nil
}

// validateHeaderKeywordOperations 验证header关键字操作
func validateHeaderKeywordOperations(headersRaw interface{}, actionType string) error {
	headersList, ok := headersRaw.([]interface{})
	if !ok {
		return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的keyword_operations.headers必须是数组格式", actionType))
	}

	validOperations := constants.ValidOperations
	validMatchTypes := constants.ValidMatchTypes
	validConditions := constants.ValidConditions

	for i, headerOpRaw := range headersList {
		headerOpMap, ok := headerOpRaw.(map[string]interface{})
		if !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.headers[%d]必须是对象格式", actionType, i))
		}

		// 验证operation字段（必需）
		operationRaw, hasOperation := headerOpMap["operation"]
		if !hasOperation {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
				fmt.Sprintf("%s动作的keyword_operations.headers[%d].operation字段是必需的", actionType, i))
		}
		operation, ok := operationRaw.(string)
		if !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.headers[%d].operation必须是字符串", actionType, i))
		}

		// 验证operation的有效值
		isValidOperation := false
		for _, validOp := range validOperations {
			if operation == validOp {
				isValidOperation = true
				break
			}
		}
		if !isValidOperation {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.headers[%d].operation值无效: %s，支持的操作: %s",
					actionType, i, operation, strings.Join(validOperations, ", ")))
		}

		// 验证match_type字段
		if matchTypeRaw, hasMatchType := headerOpMap["match_type"]; hasMatchType {
			matchType, ok := matchTypeRaw.(string)
			if !ok {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.headers[%d].match_type必须是字符串", actionType, i))
			}

			// 验证match_type的有效值
			isValidMatchType := false
			for _, validMatchType := range validMatchTypes {
				if matchType == validMatchType {
					isValidMatchType = true
					break
				}
			}
			if !isValidMatchType {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.headers[%d].match_type值无效: %s，支持的匹配类型: %s",
						actionType, i, matchType, strings.Join(validMatchTypes, ", ")))
			}
		}

		// 验证condition字段
		if conditionRaw, hasCondition := headerOpMap["condition"]; hasCondition {
			condition, ok := conditionRaw.(string)
			if !ok {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.headers[%d].condition必须是字符串", actionType, i))
			}

			// 验证condition的有效值
			isValidCondition := false
			for _, validCondition := range validConditions {
				if condition == validCondition {
					isValidCondition = true
					break
				}
			}
			if !isValidCondition {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.headers[%d].condition值无效: %s，支持的条件: %s",
						actionType, i, condition, strings.Join(validConditions, ", ")))
			}
		}

		// 验证pattern字段（某些操作需要）
		if operation != "remove" {
			if patternRaw, hasPattern := headerOpMap["pattern"]; hasPattern {
				if _, ok := patternRaw.(string); !ok {
					return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
						fmt.Sprintf("%s动作的keyword_operations.headers[%d].pattern必须是字符串", actionType, i))
				}
			}
		}
	}

	return nil
}

// validateBodyKeywordOperations 验证body关键字操作
func validateBodyKeywordOperations(bodyRaw interface{}, actionType string) error {
	bodyList, ok := bodyRaw.([]interface{})
	if !ok {
		return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
			fmt.Sprintf("%s动作的keyword_operations.body必须是数组格式", actionType))
	}

	validOperations := constants.ValidOperations
	validMatchTypes := constants.ValidMatchTypes
	validFormats := []string{constants.FormatJSON, constants.FormatXML, constants.FormatHTML, constants.FormatText, constants.FormatAuto}

	for i, bodyOpRaw := range bodyList {
		bodyOpMap, ok := bodyOpRaw.(map[string]interface{})
		if !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.body[%d]必须是对象格式", actionType, i))
		}

		// 验证operation字段（必需）
		operationRaw, hasOperation := bodyOpMap["operation"]
		if !hasOperation {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
				fmt.Sprintf("%s动作的keyword_operations.body[%d].operation字段是必需的", actionType, i))
		}
		operation, ok := operationRaw.(string)
		if !ok {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.body[%d].operation必须是字符串", actionType, i))
		}

		// 验证operation的有效值
		isValidOperation := false
		for _, validOp := range validOperations {
			if operation == validOp {
				isValidOperation = true
				break
			}
		}
		if !isValidOperation {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				fmt.Sprintf("%s动作的keyword_operations.body[%d].operation值无效: %s，支持的操作: %s",
					actionType, i, operation, strings.Join(validOperations, ", ")))
		}

		// 验证format字段
		if formatRaw, hasFormat := bodyOpMap["format"]; hasFormat {
			format, ok := formatRaw.(string)
			if !ok {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].format必须是字符串", actionType, i))
			}

			// 验证format的有效值
			isValidFormat := false
			for _, validFormat := range validFormats {
				if format == validFormat {
					isValidFormat = true
					break
				}
			}
			if !isValidFormat {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].format值无效: %s，支持的格式: %s",
						actionType, i, format, strings.Join(validFormats, ", ")))
			}
		}

		// 验证match_type字段
		if matchTypeRaw, hasMatchType := bodyOpMap["match_type"]; hasMatchType {
			matchType, ok := matchTypeRaw.(string)
			if !ok {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].match_type必须是字符串", actionType, i))
			}

			// 验证match_type的有效值
			isValidMatchType := false
			for _, validMatchType := range validMatchTypes {
				if matchType == validMatchType {
					isValidMatchType = true
					break
				}
			}
			if !isValidMatchType {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].match_type值无效: %s，支持的匹配类型: %s",
						actionType, i, matchType, strings.Join(validMatchTypes, ", ")))
			}
		}

		// 验证pattern字段（必需）
		if patternRaw, hasPattern := bodyOpMap["pattern"]; hasPattern {
			if _, ok := patternRaw.(string); !ok {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].pattern必须是字符串", actionType, i))
			}
		} else {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
				fmt.Sprintf("%s动作的keyword_operations.body[%d].pattern字段是必需的", actionType, i))
		}

		// 验证replacement字段（replace和add操作需要）
		if operation == "replace" || operation == "add" || operation == "append" {
			if replacementRaw, hasReplacement := bodyOpMap["replacement"]; hasReplacement {
				if _, ok := replacementRaw.(string); !ok {
					return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
						fmt.Sprintf("%s动作的keyword_operations.body[%d].replacement必须是字符串", actionType, i))
				}
			} else {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldRequired,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].replacement字段对于%s操作是必需的", actionType, i, operation))
			}
		}

		// 验证case_sensitive字段
		if caseSensitiveRaw, hasCaseSensitive := bodyOpMap["case_sensitive"]; hasCaseSensitive {
			if _, ok := caseSensitiveRaw.(bool); !ok {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].case_sensitive必须是布尔值", actionType, i))
			}
		}

		// 验证preserve_structure字段
		if preserveStructureRaw, hasPreserveStructure := bodyOpMap["preserve_structure"]; hasPreserveStructure {
			if _, ok := preserveStructureRaw.(bool); !ok {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("%s动作的keyword_operations.body[%d].preserve_structure必须是布尔值", actionType, i))
			}
		}
	}

	return nil
}

// processHeaderKeywordOperations 处理Header关键字操作
func processHeaderKeywordOperations(header http.Header, operations []HeaderKeywordOperation, logger interfaces.LogService) error {
	for _, op := range operations {
		logger.Debug("执行Header关键字操作: operation=%s, pattern=%s, match_type=%s", op.Operation, op.Pattern, op.MatchType)

		switch op.Operation {
		case OperationTypeAdd:
			err := processHeaderAdd(header, op, logger)
			if err != nil {
				return err
			}
		case OperationTypeReplace:
			err := processHeaderReplace(header, op, logger)
			if err != nil {
				return err
			}
		case OperationTypeRemove:
			err := processHeaderRemove(header, op, logger)
			if err != nil {
				return err
			}
		case OperationTypeAppend:
			err := processHeaderAppend(header, op, logger)
			if err != nil {
				return err
			}
		default:
			return errors.WrapError(nil, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "不支持的Header操作类型: "+string(op.Operation))
		}
	}
	return nil
}

// processHeaderAdd 处理Header添加操作
func processHeaderAdd(header http.Header, op HeaderKeywordOperation, logger interfaces.LogService) error {
	// 检查条件
	if !matchHeaderCondition(header, op.Pattern, op.Condition, op.ValuePattern, op.CaseSensitive) {
		logger.Debug("Header添加操作条件不满足: %s", op.Pattern)
		return nil
	}

	header.Set(op.Pattern, op.NewValue)
	logger.Debug("添加Header: %s = %s", op.Pattern, op.NewValue)
	return nil
}

// processHeaderReplace 处理Header替换操作
func processHeaderReplace(header http.Header, op HeaderKeywordOperation, logger interfaces.LogService) error {
	var headersToReplace []string

	// 查找匹配的headers
	for headerName := range header {
		matched, err := matchPattern(headerName, op.Pattern, op.MatchType, op.CaseSensitive)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "Header名称匹配失败")
		}

		if matched {
			// 检查条件
			if matchHeaderCondition(header, headerName, op.Condition, op.ValuePattern, op.CaseSensitive) {
				headersToReplace = append(headersToReplace, headerName)
			}
		}
	}

	// 执行替换
	for _, headerName := range headersToReplace {
		if op.ValuePattern != "" {
			// 替换header值
			oldValue := header.Get(headerName)
			var newValue string
			if op.MatchType == MatchTypeRegex {
				re, err := regexp.Compile(op.ValuePattern)
				if err != nil {
					return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "正则表达式编译失败")
				}
				newValue = re.ReplaceAllString(oldValue, op.Replacement)
			} else {
				if op.CaseSensitive {
					newValue = strings.ReplaceAll(oldValue, op.ValuePattern, op.Replacement)
				} else {
					// 大小写不敏感替换
					lowerPattern := strings.ToLower(op.ValuePattern)
					newValue = oldValue
					for {
						index := strings.Index(strings.ToLower(newValue), lowerPattern)
						if index == -1 {
							break
						}
						newValue = newValue[:index] + op.Replacement + newValue[index+len(op.ValuePattern):]
					}
				}
			}
			header.Set(headerName, newValue)
			logger.Debug("替换Header值: %s = %s -> %s", headerName, oldValue, newValue)
		} else {
			// 替换整个header
			header.Set(headerName, op.Replacement)
			logger.Debug("替换Header: %s = %s", headerName, op.Replacement)
		}
	}

	return nil
}

// processHeaderRemove 处理Header删除操作
func processHeaderRemove(header http.Header, op HeaderKeywordOperation, logger interfaces.LogService) error {
	var headersToRemove []string

	// 查找匹配的headers
	for headerName := range header {
		matched, err := matchPattern(headerName, op.Pattern, op.MatchType, op.CaseSensitive)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "Header名称匹配失败")
		}

		if matched {
			// 检查条件
			if matchHeaderCondition(header, headerName, op.Condition, op.ValuePattern, op.CaseSensitive) {
				headersToRemove = append(headersToRemove, headerName)
			}
		}
	}

	// 执行删除
	for _, headerName := range headersToRemove {
		header.Del(headerName)
		logger.Debug("删除Header: %s", headerName)
	}

	return nil
}

// processHeaderAppend 处理Header追加操作
func processHeaderAppend(header http.Header, op HeaderKeywordOperation, logger interfaces.LogService) error {
	// 检查条件
	if !matchHeaderCondition(header, op.Pattern, op.Condition, op.ValuePattern, op.CaseSensitive) {
		logger.Debug("Header追加操作条件不满足: %s", op.Pattern)
		return nil
	}

	oldValue := header.Get(op.Pattern)
	newValue := oldValue + op.NewValue
	header.Set(op.Pattern, newValue)
	logger.Debug("追加Header: %s = %s -> %s", op.Pattern, oldValue, newValue)
	return nil
}

// modifyHTTPHeaders 修改HTTP头部（保持向后兼容）
func modifyHTTPHeaders(header http.Header, headersToAdd map[string]interface{}, headersToRemove []string) {
	// 添加或修改头部
	if headersToAdd != nil {
		for key, value := range headersToAdd {
			if strValue, ok := value.(string); ok {
				header.Set(key, strValue)
			}
		}
	}

	// 删除指定头部
	if headersToRemove != nil {
		for _, key := range headersToRemove {
			header.Del(key)
		}
	}
}

// processBodyKeywordOperations 处理Body关键字操作
func processBodyKeywordOperations(content string, operations []BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	result := content

	for _, op := range operations {
		logger.Debug("执行Body关键字操作: operation=%s, pattern=%s, format=%s", op.Operation, op.Pattern, op.Format)

		var err error
		switch op.Operation {
		case OperationTypeReplace:
			result, err = processBodyReplace(result, op, logger)
		case OperationTypeRemove:
			result, err = processBodyRemove(result, op, logger)
		case OperationTypeAdd:
			result, err = processBodyAdd(result, op, logger)
		case OperationTypeAppend:
			result, err = processBodyAppend(result, op, logger)
		default:
			return result, errors.WrapError(nil, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "不支持的Body操作类型: "+string(op.Operation))
		}

		if err != nil {
			return result, err
		}
	}

	return result, nil
}

// processBodyReplace 处理Body替换操作
func processBodyReplace(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	format := strings.ToLower(op.Format)
	if format == "" || format == "auto" {
		format = strings.ToLower(detectContentType(content))
		if strings.Contains(format, "json") {
			format = "json"
		} else if strings.Contains(format, "xml") {
			format = "xml"
		} else if strings.Contains(format, "html") {
			format = "html"
		} else {
			format = "text"
		}
	}

	switch format {
	case "json":
		return processJSONReplace(content, op, logger)
	case "xml":
		return processXMLReplace(content, op, logger)
	case "html":
		return processHTMLReplace(content, op, logger)
	case "text", "plain":
		return processTextReplace(content, op, logger)
	default:
		return processTextReplace(content, op, logger)
	}
}

// processJSONReplace 处理JSON格式的替换
func processJSONReplace(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	if op.PreserveStructure {
		// 保持JSON结构的替换
		var jsonData interface{}
		if err := json.Unmarshal([]byte(content), &jsonData); err != nil {
			return content, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "JSON解析失败")
		}

		// 递归替换JSON中的值
		modifiedData := replaceInJSONValue(jsonData, op)

		// 重新序列化
		modifiedBytes, err := json.Marshal(modifiedData)
		if err != nil {
			return content, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "JSON序列化失败")
		}

		// 使用专业的字符编码转换器确保健壮性
		converter := charsetencoding.NewCharsetConverter()
		resultBytes, err := converter.Convert(modifiedBytes, constants.CharsetUTF8, constants.CharsetUTF8)
		if err != nil {
			return content, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "JSON处理结果编码转换失败")
		}
		result := string(resultBytes)
		logger.Debug("JSON结构化替换完成")
		return result, nil
	} else {
		// 执行文本模式替换操作，支持多种匹配策略（正则表达式、精确匹配、包含匹配）
		// 包含大小写敏感控制和复杂的字符串替换算法
		return processTextReplace(content, op, logger)
	}
}

// replaceInJSONValue 递归替换JSON值
func replaceInJSONValue(value interface{}, op BodyKeywordOperation) interface{} {
	switch v := value.(type) {
	case string:
		// 对字符串值进行匹配和替换
		if op.MatchType == MatchTypeRegex {
			re, err := regexp.Compile(op.Pattern)
			if err == nil {
				return re.ReplaceAllString(v, op.Replacement)
			}
		} else {
			matched, _ := matchPattern(v, op.Pattern, op.MatchType, op.CaseSensitive)
			if matched {
				if op.CaseSensitive {
					return strings.ReplaceAll(v, op.Pattern, op.Replacement)
				} else {
					// 大小写不敏感替换
					lowerPattern := strings.ToLower(op.Pattern)
					result := v
					for {
						index := strings.Index(strings.ToLower(result), lowerPattern)
						if index == -1 {
							break
						}
						result = result[:index] + op.Replacement + result[index+len(op.Pattern):]
					}
					return result
				}
			}
		}
		return v
	case map[string]interface{}:
		result := make(map[string]interface{})
		for k, val := range v {
			// 检查键名是否匹配
			keyMatched, _ := matchPattern(k, op.Pattern, op.MatchType, op.CaseSensitive)
			if keyMatched {
				// 如果键名匹配，替换对应的值
				result[k] = op.Replacement
			} else {
				// 递归处理值
				result[k] = replaceInJSONValue(val, op)
			}
		}
		return result
	case []interface{}:
		result := make([]interface{}, len(v))
		for i, val := range v {
			result[i] = replaceInJSONValue(val, op)
		}
		return result
	default:
		return v
	}
}

// processXMLReplace 处理XML格式的替换
func processXMLReplace(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	if op.PreserveStructure {
		// 保持XML结构的替换 - 使用DOM解析
		return processXMLReplaceWithDOM(content, op, logger)
	} else {
		// 不保持结构时使用文本替换
		logger.Debug("使用文本替换处理XML内容")
		return processTextReplace(content, op, logger)
	}
}

// processXMLReplaceWithDOM 使用DOM解析处理XML替换，保持结构完整性
func processXMLReplaceWithDOM(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	// 解析XML文档
	doc := &xmlDocument{}
	if err := xml.Unmarshal([]byte(content), doc); err != nil {
		logger.Warn("XML解析失败，回退到字符串替换: %v", err)
		return processTextReplace(content, op, logger)
	}

	// 递归替换XML节点中的文本内容
	modified := replaceInXMLNode(doc, op)
	if !modified {
		logger.Debug("XML内容未发生变化")
		return content, nil
	}

	// 重新序列化XML
	result, err := xml.Marshal(doc)
	if err != nil {
		logger.Error("XML序列化失败，回退到原始内容: %v", err)
		return content, nil
	}

	logger.Debug("XML结构化替换完成")
	return string(result), nil
}

// xmlDocument 通用XML文档结构
type xmlDocument struct {
	XMLName xml.Name
	Content string `xml:",innerxml"`
}

// replaceInXMLNode 递归替换XML节点中的文本内容
func replaceInXMLNode(doc *xmlDocument, op BodyKeywordOperation) bool {
	if doc == nil {
		return false
	}

	originalContent := doc.Content

	// 对文本内容进行匹配和替换
	if op.MatchType == constants.MatchTypeRegex {
		re, err := regexp.Compile(op.Pattern)
		if err == nil {
			doc.Content = re.ReplaceAllString(doc.Content, op.Replacement)
		}
	} else {
		matched, _ := matchPattern(doc.Content, op.Pattern, op.MatchType, op.CaseSensitive)
		if matched {
			if op.CaseSensitive {
				doc.Content = strings.ReplaceAll(doc.Content, op.Pattern, op.Replacement)
			} else {
				// 大小写不敏感替换
				lowerPattern := strings.ToLower(op.Pattern)
				result := doc.Content
				for {
					index := strings.Index(strings.ToLower(result), lowerPattern)
					if index == -1 {
						break
					}
					result = result[:index] + op.Replacement + result[index+len(op.Pattern):]
				}
				doc.Content = result
			}
		}
	}

	return doc.Content != originalContent
}

// processHTMLReplace 处理HTML格式的替换
func processHTMLReplace(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	// HTML处理，注意不要破坏标签结构
	return processTextReplace(content, op, logger)
}

// processTextReplace 处理纯文本替换
func processTextReplace(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	var result string

	switch op.MatchType {
	case MatchTypeRegex:
		re, err := regexp.Compile(op.Pattern)
		if err != nil {
			return content, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "正则表达式编译失败")
		}
		result = re.ReplaceAllString(content, op.Replacement)
	case MatchTypeExact, MatchTypeContains:
		if op.CaseSensitive {
			result = strings.ReplaceAll(content, op.Pattern, op.Replacement)
		} else {
			// 大小写不敏感替换
			lowerPattern := strings.ToLower(op.Pattern)

			// 找到所有匹配位置并替换
			result = content
			for {
				index := strings.Index(strings.ToLower(result), lowerPattern)
				if index == -1 {
					break
				}
				result = result[:index] + op.Replacement + result[index+len(op.Pattern):]
			}
		}
	default:
		result = strings.ReplaceAll(content, op.Pattern, op.Replacement)
	}

	logger.Debug("文本替换完成: 模式=%s, 替换=%s", op.Pattern, op.Replacement)
	return result, nil
}

// processBodyRemove 处理Body删除操作
func processBodyRemove(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	// 删除操作就是替换为空字符串
	removeOp := op
	removeOp.Replacement = ""
	return processBodyReplace(content, removeOp, logger)
}

// processBodyAdd 处理Body添加操作
func processBodyAdd(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	// 添加操作在内容开头添加
	result := op.Replacement + content
	logger.Debug("Body添加操作完成")
	return result, nil
}

// processBodyAppend 处理Body追加操作
func processBodyAppend(content string, op BodyKeywordOperation, logger interfaces.LogService) (string, error) {
	// 追加操作在内容末尾添加
	result := content + op.Replacement
	logger.Debug("Body追加操作完成")
	return result, nil
}

// processBodyConfig 处理body配置，返回处理后的内容和Content-Type
func processBodyConfig(bodyConfig *BodyConfig) ([]byte, string, error) {
	if bodyConfig == nil || bodyConfig.Content == "" {
		return nil, "", nil
	}

	content := bodyConfig.Content
	contentType := bodyConfig.ContentType
	format := bodyConfig.Format
	encoding := bodyConfig.Encoding

	// 处理编码
	var bodyBytes []byte
	var err error

	switch strings.ToLower(encoding) {
	case constants.EncodingBase64:
		bodyBytes, err = base64.StdEncoding.DecodeString(content)
		if err != nil {
			return nil, "", errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "base64解码失败")
		}
	case constants.EncodingUTF8, "":
		// 使用专业的字符编码转换器确保健壮性
		converter := charsetencoding.NewCharsetConverter()
		var err error
		bodyBytes, err = converter.Convert([]byte(content), constants.CharsetUTF8, constants.CharsetUTF8)
		if err != nil {
			return nil, "", errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "UTF-8编码转换失败")
		}
	default:
		// 检查是否为支持的字符编码
		converter := charsetencoding.NewCharsetConverter()
		if !converter.IsSupported(strings.ToLower(encoding)) {
			return nil, "", errors.WrapError(nil, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "不支持的编码格式: "+encoding)
		}

		// 使用专业的字符编码转换器处理字符编码
		var err error
		// 将指定编码的内容转换为UTF-8
		bodyBytes, err = converter.Convert([]byte(content), strings.ToLower(encoding), constants.CharsetUTF8)
		if err != nil {
			return nil, "", errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("字符编码转换失败 (%s -> UTF-8): %v", encoding, err))
		}
	}

	// 确定Content-Type
	if contentType == "" {
		if format != "" {
			contentType = getContentTypeByFormat(format)
		} else {
			contentType = detectContentType(content)
		}
	}

	// 验证内容格式（仅对文本内容）
	if encoding != "base64" && format != "" {
		if err := validateContent(content, format); err != nil {
			return nil, "", err
		}
	}

	return bodyBytes, contentType, nil
}



// replaceRequestBodyAdvanced 高级请求体替换，支持BodyConfig
func replaceRequestBodyAdvanced(req *http.Request, bodyConfig *BodyConfig, autoSetContentType bool) error {
	bodyBytes, contentType, err := processBodyConfig(bodyConfig)
	if err != nil {
		return err
	}

	if bodyBytes == nil {
		return nil
	}

	req.Body = io.NopCloser(bytes.NewReader(bodyBytes))
	req.ContentLength = int64(len(bodyBytes))

	// 更新Content-Length头部
	req.Header.Set("Content-Length", strconv.Itoa(len(bodyBytes)))

	// 自动设置Content-Type
	if autoSetContentType && contentType != "" {
		req.Header.Set("Content-Type", contentType)
	}

	return nil
}

// processRequestBodyAdvanced 高级请求体处理，支持编码转换、压缩和Transfer-Encoding
func (e *ModifyRequestExecutor) processRequestBodyAdvanced(req *http.Request, bodyConfig *BodyConfig, autoSetContentType bool) error {
	if req == nil {
		return errors.ErrHTTPRequestEmpty
	}
	if bodyConfig == nil || bodyConfig.Content == "" {
		return nil
	}

	// 处理body配置，获取处理后的内容和Content-Type
	bodyBytes, contentType, err := processBodyConfig(bodyConfig)
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("处理请求体配置失败: %v", err))
	}

	if bodyBytes == nil {
		return nil
	}

	// 检查请求体大小限制
	if len(bodyBytes) > constants.MaxRequestBodySize {
		return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf(constants.ErrMsgRequestSizeTooLarge, len(bodyBytes), constants.MaxRequestBodySize))
	}

	// 处理字符编码转换
	if bodyConfig.Encoding != "" && bodyConfig.Encoding != constants.EncodingUTF8 {
		bodyBytes, err = e.convertRequestCharsetEncoding(bodyBytes, bodyConfig.Encoding, constants.CharsetUTF8)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
				fmt.Sprintf(constants.ErrMsgRequestCharsetConversionFailed, err))
		}
	}

	// 处理内容编码（压缩）
	originalContentEncoding := req.Header.Get("Content-Encoding")
	if originalContentEncoding != "" {
		bodyBytes, err = e.handleRequestContentEncoding(bodyBytes, originalContentEncoding)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
				fmt.Sprintf(constants.ErrMsgRequestContentEncodingFailed, err))
		}
	}

	// 处理传输编码
	transferEncoding := req.Header.Get("Transfer-Encoding")
	if strings.Contains(strings.ToLower(transferEncoding), "chunked") {
		err = e.handleRequestChunkedTransferEncoding(req, bodyBytes)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
				fmt.Sprintf(constants.ErrMsgRequestChunkedEncodingFailed, err))
		}
	} else {
		// 标准的Content-Length处理
		req.Body = io.NopCloser(bytes.NewReader(bodyBytes))
		req.ContentLength = int64(len(bodyBytes))
		req.Header.Set("Content-Length", strconv.Itoa(len(bodyBytes)))
	}

	// 自动设置Content-Type
	if autoSetContentType && contentType != "" {
		req.Header.Set("Content-Type", contentType)
	}

	e.Logger.Debug("高级请求体处理完成，最终大小: %d bytes", len(bodyBytes))
	return nil
}

// convertRequestCharsetEncoding 转换请求字符编码
func (e *ModifyRequestExecutor) convertRequestCharsetEncoding(data []byte, fromCharset, toCharset string) ([]byte, error) {
	// 使用专业的字符编码转换器
	converter := charsetencoding.NewCharsetConverter()

	// 执行字符编码转换
	convertedData, err := converter.Convert(data, fromCharset, toCharset)
	if err != nil {
		e.Logger.Error("字符编码转换失败: %s -> %s, 错误: %v", fromCharset, toCharset, err)
		return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("字符编码转换失败: %s -> %s", fromCharset, toCharset))
	}

	e.Logger.Debug("字符编码转换成功: %s -> %s, 原始大小: %d bytes, 转换后大小: %d bytes",
		fromCharset, toCharset, len(data), len(convertedData))
	return convertedData, nil
}

// isValidRequestCharset 检查请求字符编码是否有效
func (e *ModifyRequestExecutor) isValidRequestCharset(charset string) bool {
	// 使用字符编码转换器验证编码是否支持
	converter := charsetencoding.NewCharsetConverter()
	return converter.IsSupported(charset)
}



// replaceResponseBodyAdvanced 高级响应体替换，支持BodyConfig
func replaceResponseBodyAdvanced(resp *http.Response, bodyConfig *BodyConfig, autoSetContentType bool) error {
	bodyBytes, contentType, err := processBodyConfig(bodyConfig)
	if err != nil {
		return err
	}

	if bodyBytes == nil {
		return nil
	}

	resp.Body = io.NopCloser(bytes.NewReader(bodyBytes))
	resp.ContentLength = int64(len(bodyBytes))

	// 更新Content-Length头部
	resp.Header.Set("Content-Length", strconv.Itoa(len(bodyBytes)))

	// 自动设置Content-Type
	if autoSetContentType && contentType != "" {
		resp.Header.Set("Content-Type", contentType)
	}

	return nil
}

// processResponseBodyAdvanced 高级响应体处理，支持编码转换、压缩和Transfer-Encoding
func (e *ModifyResponseExecutor) processResponseBodyAdvanced(resp *http.Response, bodyConfig *BodyConfig, autoSetContentType bool) error {
	if resp == nil {
		return errors.ErrHTTPResponseEmpty
	}
	if bodyConfig == nil || bodyConfig.Content == "" {
		return nil
	}

	// 处理body配置，获取处理后的内容和Content-Type
	bodyBytes, contentType, err := processBodyConfig(bodyConfig)
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("处理响应体配置失败: %v", err))
	}

	if bodyBytes == nil {
		return nil
	}

	// 检查响应体大小限制
	if len(bodyBytes) > constants.MaxResponseBodySize {
		return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf(constants.ErrMsgResponseSizeTooLarge, len(bodyBytes), constants.MaxResponseBodySize))
	}

	// 处理字符编码转换
	if bodyConfig.Encoding != "" && bodyConfig.Encoding != constants.EncodingUTF8 {
		bodyBytes, err = e.convertCharsetEncoding(bodyBytes, bodyConfig.Encoding, constants.CharsetUTF8)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
				fmt.Sprintf(constants.ErrMsgCharsetConversionFailed, err))
		}
	}

	// 处理内容编码（压缩）
	originalContentEncoding := resp.Header.Get("Content-Encoding")
	if originalContentEncoding != "" {
		bodyBytes, err = e.handleContentEncoding(bodyBytes, originalContentEncoding)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
				fmt.Sprintf(constants.ErrMsgContentEncodingProcessFailed, err))
		}
	}

	// 处理传输编码
	transferEncoding := resp.Header.Get("Transfer-Encoding")
	if strings.Contains(strings.ToLower(transferEncoding), "chunked") {
		err = e.handleChunkedTransferEncoding(resp, bodyBytes)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
				fmt.Sprintf(constants.ErrMsgChunkedEncodingFailed, err))
		}
	} else {
		// 标准的Content-Length处理
		resp.Body = io.NopCloser(bytes.NewReader(bodyBytes))
		resp.ContentLength = int64(len(bodyBytes))
		resp.Header.Set("Content-Length", strconv.Itoa(len(bodyBytes)))
	}

	// 自动设置Content-Type
	if autoSetContentType && contentType != "" {
		resp.Header.Set("Content-Type", contentType)
	}

	e.Logger.Debug("高级响应体处理完成，最终大小: %d bytes", len(bodyBytes))
	return nil
}

// convertCharsetEncoding 转换字符编码
func (e *ModifyResponseExecutor) convertCharsetEncoding(data []byte, fromCharset, toCharset string) ([]byte, error) {
	// 使用专业的字符编码转换器
	converter := charsetencoding.NewCharsetConverter()

	// 执行字符编码转换
	convertedData, err := converter.Convert(data, fromCharset, toCharset)
	if err != nil {
		e.Logger.Error("响应字符编码转换失败: %s -> %s, 错误: %v", fromCharset, toCharset, err)
		return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("响应字符编码转换失败: %s -> %s", fromCharset, toCharset))
	}

	e.Logger.Debug("响应字符编码转换成功: %s -> %s, 原始大小: %d bytes, 转换后大小: %d bytes",
		fromCharset, toCharset, len(data), len(convertedData))
	return convertedData, nil
}

// handleContentEncoding 处理内容编码（压缩/解压）
func (e *ModifyResponseExecutor) handleContentEncoding(data []byte, encoding string) ([]byte, error) {
	encodings := strings.Split(strings.ToLower(encoding), ",")
	result := data

	for _, enc := range encodings {
		enc = strings.TrimSpace(enc)
		switch enc {
		case constants.ContentEncodingGzip:
			// 对于新的响应体内容，重新进行gzip压缩
			compressed, err := e.compressGzip(result)
			if err != nil {
				return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
					fmt.Sprintf(constants.ErrMsgCompressionFailed, err))
			}
			result = compressed
			e.Logger.Debug("已重新进行gzip压缩，压缩后大小: %d bytes", len(result))

		case constants.ContentEncodingDeflate:
			// 对于新的响应体内容，重新进行deflate压缩
			compressed, err := e.compressDeflate(result)
			if err != nil {
				return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
					fmt.Sprintf(constants.ErrMsgCompressionFailed, err))
			}
			result = compressed
			e.Logger.Debug("已重新进行deflate压缩，压缩后大小: %d bytes", len(result))

		case constants.ContentEncodingIdentity:
			// identity编码表示不压缩，直接使用原数据
			e.Logger.Debug("使用identity编码，不进行压缩")

		default:
			if !e.isValidContentEncoding(enc) {
				return nil, errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
					fmt.Sprintf(constants.ErrMsgUnsupportedContentEncoding, enc))
			}
		}
	}

	return result, nil
}

// handleChunkedTransferEncoding 处理分块传输编码
func (e *ModifyResponseExecutor) handleChunkedTransferEncoding(resp *http.Response, data []byte) error {
	return e.ChunkingUtils.HandleChunkedEncodingForResponse(resp, data)
}



// isValidCharset 检查字符编码是否有效
func (e *ModifyResponseExecutor) isValidCharset(charset string) bool {
	// 使用字符编码转换器验证编码是否支持
	converter := charsetencoding.NewCharsetConverter()
	return converter.IsSupported(charset)
}

// =============================================================================
// 响应完整性保证功能
// =============================================================================

// ensureResponseIntegrity 确保响应的完整性
func (e *ModifyResponseExecutor) ensureResponseIntegrity(resp *http.Response) error {
	if resp == nil {
		return errors.ErrHTTPResponseEmpty
	}

	// 1. 验证HTTP响应结构
	err := e.validateHTTPResponseStructure(resp)
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPResponseInvalid,
			fmt.Sprintf(constants.ErrMsgProtocolValidationFailed, err))
	}

	// 2. 验证编码一致性
	err = e.verifyEncodingConsistency(resp)
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
			fmt.Sprintf("编码一致性验证失败: %v", err))
	}

	// 3. 自动修复常见的协议问题
	if constants.DefaultAutoFixProtocolIssues {
		err = e.fixCommonProtocolIssues(resp)
		if err != nil {
			e.Logger.Warn("自动修复协议问题时出现错误: %v", err)
		}
	}

	// 4. 最终验证
	err = e.performFinalValidation(resp)
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPResponseInvalid,
			fmt.Sprintf("最终验证失败: %v", err))
	}

	e.Logger.Debug("响应完整性检查通过")
	return nil
}

// validateHTTPResponseStructure 验证HTTP响应结构
func (e *ModifyResponseExecutor) validateHTTPResponseStructure(resp *http.Response) error {
	// 验证状态码
	if resp.StatusCode < constants.MinHTTPStatusCode || resp.StatusCode > constants.MaxHTTPStatusCode {
		return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPStatusError,
			fmt.Sprintf("无效的HTTP状态码: %d", resp.StatusCode))
	}

	// 验证状态消息
	if resp.Status == "" {
		resp.Status = fmt.Sprintf("%d %s", resp.StatusCode, http.StatusText(resp.StatusCode))
		e.Logger.Debug("已自动设置状态消息: %s", resp.Status)
	}

	// 验证HTTP版本
	if resp.Proto == "" {
		resp.Proto = "HTTP/1.1"
		resp.ProtoMajor = 1
		resp.ProtoMinor = 1
		e.Logger.Debug("已自动设置HTTP版本: %s", resp.Proto)
	}

	// 验证必要的头部
	if resp.Header == nil {
		resp.Header = make(http.Header)
		e.Logger.Debug("已初始化响应头部")
	}

	return nil
}

// verifyEncodingConsistency 验证编码一致性
func (e *ModifyResponseExecutor) verifyEncodingConsistency(resp *http.Response) error {
	contentLength := resp.Header.Get("Content-Length")
	transferEncoding := resp.Header.Get("Transfer-Encoding")
	contentEncoding := resp.Header.Get("Content-Encoding")

	// 检查Content-Length和Transfer-Encoding的冲突
	if contentLength != "" && strings.Contains(strings.ToLower(transferEncoding), "chunked") {
		return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
			"Content-Length和Transfer-Encoding: chunked不能同时存在")
	}

	// 验证Content-Length的有效性
	if contentLength != "" {
		length, err := strconv.ParseInt(contentLength, 10, 64)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
				fmt.Sprintf("无效的Content-Length值: %s", contentLength))
		}
		if length < 0 {
			return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
				fmt.Sprintf("Content-Length不能为负数: %d", length))
		}

		// 验证Content-Length与实际响应体长度的一致性
		if resp.ContentLength >= 0 && resp.ContentLength != length {
			return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
				fmt.Sprintf(constants.ErrMsgContentLengthMismatch, length, resp.ContentLength))
		}
	}

	// 验证Content-Encoding的有效性
	if contentEncoding != "" {
		encodings := strings.Split(strings.ToLower(contentEncoding), ",")
		for _, encoding := range encodings {
			encoding = strings.TrimSpace(encoding)
			if !e.isValidContentEncoding(encoding) {
				return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
					fmt.Sprintf(constants.ErrMsgUnsupportedContentEncoding, encoding))
			}
		}
	}

	// 验证Transfer-Encoding的有效性
	if transferEncoding != "" {
		encodings := strings.Split(strings.ToLower(transferEncoding), ",")
		for _, encoding := range encodings {
			encoding = strings.TrimSpace(encoding)
			if !e.isValidTransferEncoding(encoding) {
				return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
					fmt.Sprintf(constants.ErrMsgUnsupportedTransferEncoding, encoding))
			}
		}
	}

	return nil
}

// fixCommonProtocolIssues 修复常见的协议问题
func (e *ModifyResponseExecutor) fixCommonProtocolIssues(resp *http.Response) error {
	fixCount := 0

	// 修复缺失的Date头部
	if resp.Header.Get("Date") == "" {
		resp.Header.Set("Date", time.Now().UTC().Format(http.TimeFormat))
		fixCount++
		e.Logger.Debug("已添加Date头部")
	}

	// 修复缺失的Server头部（如果需要）
	if resp.Header.Get("Server") == "" && resp.StatusCode != 204 && resp.StatusCode != 304 {
		resp.Header.Set("Server", "FlexProxy/1.0")
		fixCount++
		e.Logger.Debug("已添加Server头部")
	}

	// 修复Content-Type头部（对于有响应体的响应）
	if resp.Header.Get("Content-Type") == "" && resp.ContentLength > 0 {
		// 尝试从响应体内容推断Content-Type
		contentType := e.detectContentType(resp)
		if contentType != "" {
			resp.Header.Set("Content-Type", contentType)
			fixCount++
			e.Logger.Debug("已自动设置Content-Type: %s", contentType)
		}
	}

	// 修复缺失的Connection头部
	if resp.Header.Get("Connection") == "" {
		if resp.ProtoMajor == 1 && resp.ProtoMinor == 0 {
			resp.Header.Set("Connection", "close")
		} else {
			resp.Header.Set("Connection", "keep-alive")
		}
		fixCount++
		e.Logger.Debug("已设置Connection头部")
	}

	// 清理内部标记头部
	e.cleanInternalHeaders(resp)

	if fixCount > 0 {
		e.Logger.Debug("已修复 %d 个协议问题", fixCount)
	}

	return nil
}

// performFinalValidation 执行最终验证
func (e *ModifyResponseExecutor) performFinalValidation(resp *http.Response) error {
	// 验证响应体大小
	if resp.ContentLength > constants.MaxResponseBodySize {
		return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf(constants.ErrMsgResponseSizeTooLarge, resp.ContentLength, constants.MaxResponseBodySize))
	}

	// 验证关键头部的存在性
	requiredHeaders := []string{"Date"}
	for _, header := range requiredHeaders {
		if resp.Header.Get(header) == "" {
			e.Logger.Warn("缺少推荐的HTTP头部: %s", header)
		}
	}

	// 验证状态码和响应体的一致性
	if resp.StatusCode == 204 || resp.StatusCode == 304 {
		if resp.ContentLength > 0 {
			e.Logger.Warn("状态码 %d 不应该有响应体，但Content-Length为 %d", resp.StatusCode, resp.ContentLength)
		}
	}

	return nil
}

// isValidTransferEncoding 检查传输编码是否有效
func (e *ModifyResponseExecutor) isValidTransferEncoding(encoding string) bool {
	for _, valid := range constants.ValidTransferEncodings {
		if encoding == valid {
			return true
		}
	}
	return false
}

// detectContentType 检测内容类型
func (e *ModifyResponseExecutor) detectContentType(resp *http.Response) string {
	if resp.Body == nil {
		return ""
	}

	// 读取前512字节用于内容类型检测
	buffer := make([]byte, 512)
	n, err := resp.Body.Read(buffer)
	if err != nil && err != io.EOF {
		return ""
	}

	// 重新设置响应体
	if n > 0 {
		// 创建新的读取器，包含已读取的数据和剩余的数据
		remaining, _ := io.ReadAll(resp.Body)
		fullData := append(buffer[:n], remaining...)
		resp.Body = io.NopCloser(bytes.NewReader(fullData))
	}

	// 使用Go标准库检测内容类型
	if n > 0 {
		return http.DetectContentType(buffer[:n])
	}

	return ""
}

// cleanInternalHeaders 清理内部标记头部
func (e *ModifyResponseExecutor) cleanInternalHeaders(resp *http.Response) {
	internalHeaders := []string{
		"X-FlexProxy-Internal",
		"X-FlexProxy-Modified",
		"X-FlexProxy-Processed",
		"X-Internal-Marker",
		"X-Proxy-Internal",
	}

	for _, header := range internalHeaders {
		if resp.Header.Get(header) != "" {
			resp.Header.Del(header)
			e.Logger.Debug("已清理内部头部: %s", header)
		}
	}
}

// =============================================================================
// 内置执行器实现
// =============================================================================

// LogExecutor 日志执行器
type LogExecutor struct {
	Logger interfaces.LogService
}

func (e *LogExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	message, ok := parameters["message"].(string)
	if !ok {
		return errors.ErrMissingMessageParameter
	}

	level, _ := parameters["level"].(string)
	if level == "" {
		level = "info"
	}

	switch strings.ToLower(level) {
	case "debug":
		e.Logger.Debug(message)
	case "info":
		e.Logger.Info(message)
	case "warn":
		e.Logger.Warn(message)
	case "error":
		e.Logger.Error(message)
	default:
		e.Logger.Info(message)
	}

	return nil
}

func (e *LogExecutor) Validate(parameters map[string]interface{}) error {
	if _, ok := parameters["message"]; !ok {
		return errors.ErrMissingRequiredMessageParameter
	}
	return nil
}

func (e *LogExecutor) GetType() string {
	return constants.ActionTypeLog
}

func (e *LogExecutor) GetDescription() string {
	return constants.DescLogExecutor
}

// BanIPExecutor IP封禁执行器
type BanIPExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *BanIPExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	// 从上下文或参数中获取IP地址
	ip := extractIPFromContext(ctx, parameters)
	if ip == "" {
		logParameterMissingError(e.Logger, "IP地址")
		return errors.ErrIPAddressNotFound
	}

	// 解析封禁时长
	duration := parseDuration(parameters["duration"])

	// 检查ProxyService是否可用
	if e.ProxyService == nil {
		e.Logger.Error(constants.ErrMsgProxyServiceNotInitIP)
		return errors.ErrProxyServiceNotInitialized
	}

	// 执行IP封禁
	err := e.ProxyService.BanIP(ip, duration)
	if err != nil {
		logIPBanError(e.Logger, ip, duration, err)
		return createIPBanError(ip, duration, err)
	}

	logIPBanSuccess(e.Logger, ip, duration)
	return nil
}

func (e *BanIPExecutor) Validate(parameters map[string]interface{}) error {
	// duration 参数是可选的，有默认值
	return nil
}

func (e *BanIPExecutor) GetType() string {
	return constants.ActionTypeBanIP
}

func (e *BanIPExecutor) GetDescription() string {
	return constants.DescBanIPExecutor
}

// BanDomainExecutor 域名封禁执行器
type BanDomainExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *BanDomainExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	// 从参数或上下文中获取域名
	domain := extractDomainFromContext(ctx, parameters)
	if domain == "" {
		logParameterMissingError(e.Logger, "域名")
		return errors.ErrDomainNotFound
	}

	// 解析封禁时长
	duration := parseDuration(parameters["duration"])

	// 获取封禁范围
	scope := constants.DefaultBanScope
	if val, ok := parameters["scope"].(string); ok && val != "" {
		scope = val
	}

	// 检查是否为永久封禁
	permanent := false
	if val, ok := parameters["permanent"].(bool); ok {
		permanent = val
	}

	// 永久封禁覆盖时长设置
	if permanent {
		duration = constants.PermanentBanDurationSeconds
	}

	// 检查ProxyService是否可用
	if e.ProxyService == nil {
		e.Logger.Error(constants.ErrMsgProxyServiceNotInitDomain)
		return errors.ErrProxyServiceNotInitialized
	}

	// 执行域名封禁
	err := e.ProxyService.BanDomain(domain, duration)
	if err != nil {
		logDomainBanError(e.Logger, domain, duration, scope, permanent, err)
		return createDomainBanError(domain, duration, err)
	}

	logDomainBanSuccess(e.Logger, domain, duration, scope, permanent)
	return nil
}

func (e *BanDomainExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *BanDomainExecutor) GetType() string {
	return constants.ActionTypeBanDomain
}

func (e *BanDomainExecutor) GetDescription() string {
	return constants.DescBanDomainExecutor
}

// BlockRequestExecutor 阻止请求执行器
type BlockRequestExecutor struct {
	Logger interfaces.LogService
}

func (e *BlockRequestExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	reason, _ := parameters["reason"].(string)
	if reason == "" {
		reason = "请求被阻止"
	}

	statusCode := http.StatusForbidden
	if val, ok := parameters["status_code"]; ok {
		if sc, ok := val.(int); ok {
			statusCode = sc
		}
	}

	e.Logger.Info(constants.LogMsgBlockRequestSuccess, reason, statusCode)
	return nil
}

// ExecuteHTTP 实现HTTPExecutor接口，实际阻止HTTP请求
func (e *BlockRequestExecutor) ExecuteHTTP(ctx context.Context, parameters map[string]interface{}, req *http.Request, resp *http.Response) (*http.Request, *http.Response, error) {
	if req == nil {
		return req, resp, errors.ErrHTTPRequestEmpty
	}

	// 获取阻止原因
	reason, _ := parameters["reason"].(string)
	if reason == "" {
		reason = constants.DefaultBlockReason
	}

	// 获取状态码并验证
	statusCode := constants.DefaultBlockStatusCode
	if val, ok := parameters["status_code"]; ok {
		if sc, ok := val.(int); ok {
			statusCode = validateHTTPStatusCode(sc)
		}
	}

	// 获取自定义响应体
	body, _ := parameters["body"].(string)
	if body == "" {
		body = fmt.Sprintf(`{"error": "%s", "status": %d, "timestamp": "%s"}`,
			reason, statusCode, time.Now().Format(time.RFC3339))
	}

	// 获取自定义响应头
	customHeaders, _ := parameters["headers"].(map[string]interface{})

	// 创建阻止响应
	blockResp := &http.Response{
		StatusCode:    statusCode,
		Status:        fmt.Sprintf("%d %s", statusCode, http.StatusText(statusCode)),
		Proto:         req.Proto,
		ProtoMajor:    req.ProtoMajor,
		ProtoMinor:    req.ProtoMinor,
		Header:        make(http.Header),
		Body:          io.NopCloser(strings.NewReader(body)),
		ContentLength: int64(len(body)),
		Request:       req,
	}

	// 设置默认响应头
	blockResp.Header.Set(constants.HeaderContentType, constants.ContentTypeJSON+"; charset=utf-8")
	blockResp.Header.Set(constants.HeaderContentLength, strconv.Itoa(len(body)))
	blockResp.Header.Set(constants.HeaderXFlexProxyBlocked, "1")
	blockResp.Header.Set(constants.HeaderXFlexProxyReason, reason)

	// 添加自定义响应头
	if customHeaders != nil {
		for key, value := range customHeaders {
			if strValue, ok := value.(string); ok {
				blockResp.Header.Set(key, strValue)
			}
		}
	}

	logHTTPRequestBlocked(e.Logger, req, reason, statusCode)

	// 返回阻止响应，不返回原始请求
	return req, blockResp, nil
}

func (e *BlockRequestExecutor) Validate(parameters map[string]interface{}) error {
	// 所有参数都是可选的，有默认值
	return nil
}

func (e *BlockRequestExecutor) GetType() string {
	return constants.ActionTypeBlockRequest
}

func (e *BlockRequestExecutor) GetDescription() string {
	return constants.DescBlockRequestExecutor
}

// ModifyRequestExecutor 修改请求执行器
type ModifyRequestExecutor struct {
	Logger           interfaces.LogService
	CompressionUtils *utils.HTTPCompressionUtils
	ChunkingUtils    *utils.HTTPChunkingUtils
	ValidationUtils  *utils.HTTPValidationUtils
}

// NewModifyRequestExecutor 创建修改请求执行器实例
func NewModifyRequestExecutor(logger interfaces.LogService) *ModifyRequestExecutor {
	return &ModifyRequestExecutor{
		Logger:           logger,
		CompressionUtils: utils.NewHTTPCompressionUtils(logger),
		ChunkingUtils:    utils.NewHTTPChunkingUtils(logger),
		ValidationUtils:  utils.NewHTTPValidationUtils(logger),
	}
}

func (e *ModifyRequestExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	headers, _ := parameters["headers"].(map[string]interface{})
	removeHeaders, _ := parameters["remove_headers"].([]string)
	body, _ := parameters["body"].(string)

	e.Logger.Info(constants.LogMsgModifyRequestSuccess, headers, removeHeaders, len(body))
	return nil
}

// RequestModificationState 请求修改状态跟踪
type RequestModificationState struct {
	OriginalHeaders       http.Header
	OriginalBody          []byte
	OriginalContentLength int64
	ModifiedSteps         []string
	FailedStep            string
	ErrorContext          map[string]interface{}
}

// ExecuteHTTP 实现HTTPExecutor接口，实际修改HTTP请求
func (e *ModifyRequestExecutor) ExecuteHTTP(ctx context.Context, parameters map[string]interface{}, req *http.Request, resp *http.Response) (*http.Request, *http.Response, error) {
	if req == nil {
		return req, resp, errors.ErrHTTPRequestEmpty
	}

	// 创建修改状态跟踪
	state := &RequestModificationState{
		OriginalHeaders: make(http.Header),
		ModifiedSteps:   make([]string, 0),
		ErrorContext:    make(map[string]interface{}),
	}

	// 备份原始状态
	for k, v := range req.Header {
		state.OriginalHeaders[k] = append([]string(nil), v...)
	}
	state.OriginalContentLength = req.ContentLength

	// 备份原始body
	if req.Body != nil {
		bodyBytes, err := io.ReadAll(req.Body)
		if err != nil {
			e.Logger.Error(constants.ErrMsgReadRequestBodyFailed, err)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "读取原始请求体失败")
		}
		req.Body.Close()
		state.OriginalBody = bodyBytes
		req.Body = io.NopCloser(bytes.NewReader(bodyBytes))
	}

	// 解析参数
	headers, _ := parameters["headers"].(map[string]interface{})
	removeHeaders, _ := parameters["remove_headers"].([]string)
	body, _ := parameters["body"].(string)
	bodyConfigRaw, _ := parameters["body_config"]
	keywordOperationsRaw, _ := parameters["keyword_operations"]
	autoSetContentType, _ := parameters["auto_content_type"].(bool)

	// 默认启用自动Content-Type设置
	if _, exists := parameters["auto_content_type"]; !exists {
		autoSetContentType = true
	}

	e.Logger.Info(constants.LogMsgModifyRequestStart,
		headers, removeHeaders, len(body), bodyConfigRaw != nil, keywordOperationsRaw != nil)

	// 使用defer确保在发生错误时能够回退
	defer func() {
		if r := recover(); r != nil {
			e.Logger.Error(constants.ErrMsgModifyRequestPanic, r)
			e.rollbackRequestModification(req, state)
		}
	}()

	// 1. 处理关键字操作（优先级最高）
	if keywordOperationsRaw != nil {
		state.ErrorContext["step"] = "keyword_operations"
		state.ErrorContext["operations"] = keywordOperationsRaw

		err := e.processRequestKeywordOperations(req, keywordOperationsRaw)
		if err != nil {
			state.FailedStep = "keyword_operations"
			e.Logger.Error(constants.ErrMsgKeywordOperationFailed, err)
			e.rollbackRequestModification(req, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("关键字操作失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "keyword_operations")
		e.Logger.Debug("关键字操作完成")
	}

	// 2. 处理传统的headers修改（使用智能修改）
	if headers != nil || removeHeaders != nil {
		state.ErrorContext["step"] = "headers"
		state.ErrorContext["headers"] = headers
		state.ErrorContext["remove_headers"] = removeHeaders

		err := e.smartModifyRequestHeaders(req, headers, removeHeaders)
		if err != nil {
			state.FailedStep = "headers"
			e.Logger.Error(constants.ErrMsgHeaderModifyFailed, err)
			e.rollbackRequestModification(req, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("请求头修改失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "headers")
		e.Logger.Debug("智能请求头修改完成")
	}

	// 3. 处理body修改 - 优先使用body_config，然后处理body参数
	if bodyConfigRaw != nil {
		state.ErrorContext["step"] = "body_config"
		state.ErrorContext["body_config"] = bodyConfigRaw

		// 解析body_config
		var bodyConfig BodyConfig
		if configMap, ok := bodyConfigRaw.(map[string]interface{}); ok {
			if content, ok := configMap["content"].(string); ok {
				bodyConfig.Content = content
			}
			if contentType, ok := configMap["content_type"].(string); ok {
				bodyConfig.ContentType = contentType
			}
			if format, ok := configMap["format"].(string); ok {
				bodyConfig.Format = format
			}
			if encoding, ok := configMap["encoding"].(string); ok {
				bodyConfig.Encoding = encoding
			}
		}

		err := e.processRequestBodyAdvanced(req, &bodyConfig, autoSetContentType)
		if err != nil {
			state.FailedStep = "body_config"
			e.Logger.Error(constants.ErrMsgAdvancedBodyModifyFailed, err)
			e.rollbackRequestModification(req, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("高级请求体修改失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "body_config")
		e.Logger.Debug("高级请求体修改完成，内容长度: %d", req.ContentLength)
	} else if body != "" {
		state.ErrorContext["step"] = "body"
		state.ErrorContext["body_length"] = len(body)

		// 将字符串格式的body参数封装为BodyConfig结构体，启用高级处理流程
		// 包括自动格式检测、编码转换、Content-Type处理和完整性验证
		bodyConfig := &BodyConfig{
			Content:     body,
			ContentType: "", // 让系统自动检测
			Format:      constants.FormatAuto,
			Encoding:    constants.EncodingUTF8,
		}

		err := replaceRequestBodyAdvanced(req, bodyConfig, autoSetContentType)
		if err != nil {
			state.FailedStep = "body"
			e.Logger.Error(constants.ErrMsgBodyModifyFailed, err)
			e.rollbackRequestModification(req, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("请求体修改失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "body")
		e.Logger.Debug("请求体修改完成，新长度: %d", req.ContentLength)
	}

	// 4. 处理表单数据配置
	if formConfig, ok := parameters["form_config"].(map[string]interface{}); ok {
		state.ErrorContext["step"] = "form_config"
		state.ErrorContext["form_config"] = formConfig

		// 转换为FormConfig结构
		var fc FormConfig
		if fields, ok := formConfig["fields"].(map[string]interface{}); ok {
			fc.Fields = make(map[string]string)
			for k, v := range fields {
				if str, ok := v.(string); ok {
					fc.Fields[k] = str
				}
			}
		}
		if files, ok := formConfig["files"].(map[string]interface{}); ok {
			fc.Files = make(map[string]string)
			for k, v := range files {
				if str, ok := v.(string); ok {
					fc.Files[k] = str
				}
			}
		}
		if encoding, ok := formConfig["encoding"].(string); ok {
			fc.Encoding = encoding
		}
		if boundary, ok := formConfig["boundary"].(string); ok {
			fc.Boundary = boundary
		}
		if maxFields, ok := formConfig["max_fields"].(int); ok {
			fc.MaxFields = maxFields
		}

		err := e.processFormData(req, &fc)
		if err != nil {
			state.FailedStep = "form_config"
			e.Logger.Error(constants.ErrMsgFormDataProcessFailed, err)
			e.rollbackRequestModification(req, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("表单数据处理失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "form_config")
		e.Logger.Debug("表单数据处理完成")
	}

	// 5. 最终的请求完整性保证
	if constants.DefaultRequestIntegrityCheck {
		err := e.ensureRequestIntegrity(req)
		if err != nil {
			e.Logger.Warn(constants.ErrMsgRequestIntegrityCheckFailed, err)
			// 不返回错误，只记录警告，因为这是最终的完整性检查
		}
	}

	e.Logger.Info(constants.LogMsgModifyRequestComplete, state.ModifiedSteps)
	return req, resp, nil
}

// rollbackRequestModification 回退请求修改
func (e *ModifyRequestExecutor) rollbackRequestModification(req *http.Request, state *RequestModificationState) {
	e.Logger.Warn(constants.LogMsgModifyRequestRollback, state.FailedStep, state.ModifiedSteps)

	// 恢复原始headers
	req.Header = make(http.Header)
	for k, v := range state.OriginalHeaders {
		req.Header[k] = append([]string(nil), v...)
	}

	// 恢复原始body
	if state.OriginalBody != nil {
		req.Body = io.NopCloser(bytes.NewReader(state.OriginalBody))
		req.ContentLength = state.OriginalContentLength
	} else {
		req.Body = nil
		req.ContentLength = 0
	}

	e.Logger.Info(constants.LogMsgModifyRequestRollbackComplete)
}

// safeModifyHTTPHeaders 安全的HTTP头部修改
func (e *ModifyRequestExecutor) safeModifyHTTPHeaders(header http.Header, headers map[string]interface{}, removeHeaders []string) error {
	// 验证headers参数
	if headers != nil {
		for key, value := range headers {
			if key == "" {
				return errors.ErrHeaderNameEmpty
			}
			if valueStr, ok := value.(string); ok {
				if len(valueStr) > constants.MaxHeaderValueLength {
					return errors.WrapError(nil, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
						fmt.Sprintf("请求头 %s 的值过长，最大允许%d字符", key, constants.MaxHeaderValueLength))
				}
			}
		}
	}

	// 验证removeHeaders参数
	if removeHeaders != nil {
		for _, headerName := range removeHeaders {
			if headerName == "" {
				return errors.ErrRemoveHeaderNameEmpty
			}
		}
	}

	// 执行修改
	modifyHTTPHeaders(header, headers, removeHeaders)
	return nil
}

// processRequestKeywordOperations 处理请求的关键字操作
func (e *ModifyRequestExecutor) processRequestKeywordOperations(req *http.Request, keywordOperationsRaw interface{}) error {
	// 解析keyword_operations
	var keywordOps KeywordOperations
	if opsMap, ok := keywordOperationsRaw.(map[string]interface{}); ok {
		// 解析headers操作
		if headersRaw, exists := opsMap["headers"]; exists {
			if headersList, ok := headersRaw.([]interface{}); ok {
				for _, headerOpRaw := range headersList {
					if headerOpMap, ok := headerOpRaw.(map[string]interface{}); ok {
						headerOp := HeaderKeywordOperation{}
						if operation, ok := headerOpMap["operation"].(string); ok {
							headerOp.Operation = OperationType(operation)
						}
						if matchType, ok := headerOpMap["match_type"].(string); ok {
							headerOp.MatchType = MatchType(matchType)
						}
						if pattern, ok := headerOpMap["pattern"].(string); ok {
							headerOp.Pattern = pattern
						}
						if valuePattern, ok := headerOpMap["value_pattern"].(string); ok {
							headerOp.ValuePattern = valuePattern
						}
						if replacement, ok := headerOpMap["replacement"].(string); ok {
							headerOp.Replacement = replacement
						}
						if newValue, ok := headerOpMap["new_value"].(string); ok {
							headerOp.NewValue = newValue
						}
						if condition, ok := headerOpMap["condition"].(string); ok {
							headerOp.Condition = ConditionType(condition)
						}
						if caseSensitive, ok := headerOpMap["case_sensitive"].(bool); ok {
							headerOp.CaseSensitive = caseSensitive
						}
						keywordOps.Headers = append(keywordOps.Headers, headerOp)
					}
				}
			}
		}

		// 解析body操作
		if bodyRaw, exists := opsMap["body"]; exists {
			if bodyList, ok := bodyRaw.([]interface{}); ok {
				for _, bodyOpRaw := range bodyList {
					if bodyOpMap, ok := bodyOpRaw.(map[string]interface{}); ok {
						bodyOp := BodyKeywordOperation{}
						if operation, ok := bodyOpMap["operation"].(string); ok {
							bodyOp.Operation = OperationType(operation)
						}
						if format, ok := bodyOpMap["format"].(string); ok {
							bodyOp.Format = format
						}
						if matchType, ok := bodyOpMap["match_type"].(string); ok {
							bodyOp.MatchType = MatchType(matchType)
						}
						if pattern, ok := bodyOpMap["pattern"].(string); ok {
							bodyOp.Pattern = pattern
						}
						if replacement, ok := bodyOpMap["replacement"].(string); ok {
							bodyOp.Replacement = replacement
						}
						if caseSensitive, ok := bodyOpMap["case_sensitive"].(bool); ok {
							bodyOp.CaseSensitive = caseSensitive
						}
						if preserveStructure, ok := bodyOpMap["preserve_structure"].(bool); ok {
							bodyOp.PreserveStructure = preserveStructure
						}
						keywordOps.Body = append(keywordOps.Body, bodyOp)
					}
				}
			}
		}
	}

	// 执行Header关键字操作
	if len(keywordOps.Headers) > 0 {
		err := processHeaderKeywordOperations(req.Header, keywordOps.Headers, e.Logger)
		if err != nil {
			return err
		}
	}

	// 执行Body关键字操作
	if len(keywordOps.Body) > 0 && req.Body != nil {
		// 读取原始body内容
		bodyBytes, err := io.ReadAll(req.Body)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "读取请求体失败")
		}
		req.Body.Close()

		// 执行关键字操作
		// 使用专业的字符编码转换器确保健壮性
		converter := charsetencoding.NewCharsetConverter()
		originalContentBytes, err := converter.Convert(bodyBytes, constants.CharsetUTF8, constants.CharsetUTF8)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid, "请求体内容编码转换失败")
		}
		originalContent := string(originalContentBytes)
		modifiedContent, err := processBodyKeywordOperations(originalContent, keywordOps.Body, e.Logger)
		if err != nil {
			return err
		}

		// 更新请求体
		req.Body = io.NopCloser(strings.NewReader(modifiedContent))
		req.ContentLength = int64(len(modifiedContent))
		req.Header.Set("Content-Length", strconv.Itoa(len(modifiedContent)))
	}

	return nil
}

func (e *ModifyRequestExecutor) Validate(parameters map[string]interface{}) error {
	// 验证body_config参数
	if bodyConfigRaw, exists := parameters["body_config"]; exists {
		if err := validateBodyConfig(bodyConfigRaw, "modify_request"); err != nil {
			return err
		}
	}

	// 验证headers参数
	if headersRaw, exists := parameters["headers"]; exists {
		if _, ok := headersRaw.(map[string]interface{}); !ok {
			return errors.WrapError(nil, errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				"modify_request动作的headers参数必须是键值对格式")
		}
	}

	// 验证remove_headers参数
	if removeHeadersRaw, exists := parameters["remove_headers"]; exists {
		if removeHeaders, ok := removeHeadersRaw.([]interface{}); ok {
			for i, header := range removeHeaders {
				if _, ok := header.(string); !ok {
					return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
						fmt.Sprintf("modify_request动作的remove_headers[%d]必须是字符串", i))
				}
			}
		} else if _, ok := removeHeadersRaw.([]string); !ok {
			return errors.WrapError(nil, errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				"modify_request动作的remove_headers参数必须是字符串数组")
		}
	}

	// 验证keyword_operations参数
	if keywordOpsRaw, exists := parameters["keyword_operations"]; exists {
		if err := validateKeywordOperations(keywordOpsRaw, "modify_request"); err != nil {
			return err
		}
	}

	return nil
}

func (e *ModifyRequestExecutor) GetType() string {
	return constants.ActionTypeModifyRequest
}

func (e *ModifyRequestExecutor) GetDescription() string {
	return constants.DescModifyRequestExecutor
}

// =============================================================================
// 请求修改增强功能
// =============================================================================

// smartModifyRequestHeaders 智能修改请求头，自动处理特殊头部和相关字段更新
func (e *ModifyRequestExecutor) smartModifyRequestHeaders(req *http.Request, headers map[string]interface{}, removeHeaders []string) error {
	if req == nil {
		return errors.ErrHTTPRequestEmpty
	}

	// 记录原始的特殊头部值
	originalContentLength := req.Header.Get("Content-Length")
	originalTransferEncoding := req.Header.Get("Transfer-Encoding")
	originalContentEncoding := req.Header.Get("Content-Encoding")

	// 执行基础头部修改
	err := e.safeModifyHTTPHeaders(req.Header, headers, removeHeaders)
	if err != nil {
		return err
	}

	// 检查是否修改了特殊头部，需要进行一致性处理
	needsConsistencyCheck := false
	if headers != nil {
		for key := range headers {
			lowerKey := strings.ToLower(key)
			if lowerKey == "content-length" || lowerKey == "transfer-encoding" ||
			   lowerKey == "content-encoding" || lowerKey == "content-type" {
				needsConsistencyCheck = true
				break
			}
		}
	}

	if removeHeaders != nil {
		for _, key := range removeHeaders {
			lowerKey := strings.ToLower(key)
			if lowerKey == "content-length" || lowerKey == "transfer-encoding" ||
			   lowerKey == "content-encoding" || lowerKey == "content-type" {
				needsConsistencyCheck = true
				break
			}
		}
	}

	// 检查是否存在无效的特殊头部，即使没有修改也需要修复
	if !needsConsistencyCheck {
		contentLength := req.Header.Get("Content-Length")
		if contentLength != "" {
			if _, err := strconv.ParseInt(contentLength, 10, 64); err != nil {
				needsConsistencyCheck = true
				e.Logger.Debug("检测到无效的Content-Length，启动一致性检查: %s", contentLength)
			}
		}
	}

	// 如果修改了特殊头部或检测到无效头部，进行一致性检查和自动修复
	if needsConsistencyCheck {
		err = e.ensureRequestHeaderConsistency(req, originalContentLength, originalTransferEncoding, originalContentEncoding)
		if err != nil {
			e.Logger.Warn(constants.ErrMsgRequestIntegrityCheckFailed, err)
			// 不返回错误，只记录警告，因为这是自动修复过程
		}
	}

	return nil
}

// ensureRequestHeaderConsistency 确保请求头的一致性
func (e *ModifyRequestExecutor) ensureRequestHeaderConsistency(req *http.Request, originalContentLength, originalTransferEncoding, originalContentEncoding string) error {
	// 检查 Content-Length 和 Transfer-Encoding 的冲突
	contentLength := req.Header.Get("Content-Length")
	transferEncoding := req.Header.Get("Transfer-Encoding")

	// 如果同时存在 Content-Length 和 Transfer-Encoding: chunked，删除 Content-Length
	if contentLength != "" && strings.Contains(strings.ToLower(transferEncoding), "chunked") {
		req.Header.Del("Content-Length")
		req.ContentLength = -1
		e.Logger.Debug(constants.LogMsgRequestHeaderConflictResolved, "删除Content-Length头部，因为使用了Transfer-Encoding: chunked")
	}

	// 如果删除了 Transfer-Encoding 但没有 Content-Length，需要根据请求体设置 Content-Length
	if originalTransferEncoding != "" && transferEncoding == "" && contentLength == "" {
		if req.Body != nil {
			// 读取请求体以计算长度
			bodyBytes, err := io.ReadAll(req.Body)
			if err != nil {
				return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid, "读取请求体失败")
			}

			// 重新设置请求体
			req.Body = io.NopCloser(bytes.NewReader(bodyBytes))
			req.ContentLength = int64(len(bodyBytes))
			req.Header.Set("Content-Length", strconv.Itoa(len(bodyBytes)))
			e.Logger.Debug("已自动设置 Content-Length: %d", len(bodyBytes))
		}
	}

	// 重新获取 Content-Length 的值（可能在上面的步骤中被修改）
	contentLength = req.Header.Get("Content-Length")

	// 验证 Content-Length 的有效性
	if contentLength != "" {
		if length, err := strconv.ParseInt(contentLength, 10, 64); err != nil {
			req.Header.Del("Content-Length")
			req.ContentLength = -1
			e.Logger.Warn("无效的 Content-Length 值: %s，已删除", contentLength)
		} else if length < 0 {
			req.Header.Del("Content-Length")
			req.ContentLength = -1
			e.Logger.Warn("负数的 Content-Length 值: %d，已删除", length)
		} else {
			req.ContentLength = length
		}
	} else {
		// 如果没有 Content-Length 头部，确保 ContentLength 字段也被正确设置
		if strings.Contains(strings.ToLower(req.Header.Get("Transfer-Encoding")), "chunked") {
			req.ContentLength = -1
		}
	}

	// 验证 Content-Encoding 的有效性
	contentEncoding := req.Header.Get("Content-Encoding")
	if contentEncoding != "" {
		encodings := strings.Split(strings.ToLower(contentEncoding), ",")
		validEncodings := make([]string, 0, len(encodings))

		for _, encoding := range encodings {
			encoding = strings.TrimSpace(encoding)
			if e.isValidContentEncoding(encoding) {
				validEncodings = append(validEncodings, encoding)
			} else {
				e.Logger.Warn("不支持的内容编码: %s", encoding)
			}
		}

		if len(validEncodings) == 0 {
			req.Header.Del("Content-Encoding")
			e.Logger.Debug("已删除无效的 Content-Encoding 头部")
		} else if len(validEncodings) != len(encodings) {
			req.Header.Set("Content-Encoding", strings.Join(validEncodings, ", "))
			e.Logger.Debug("已修正 Content-Encoding 头部: %s", strings.Join(validEncodings, ", "))
		}
	}

	return nil
}

// isValidContentEncoding 检查内容编码是否有效
func (e *ModifyRequestExecutor) isValidContentEncoding(encoding string) bool {
	return e.ValidationUtils.IsValidContentEncoding(encoding)
}

// =============================================================================
// 请求完整性保证功能
// =============================================================================

// ensureRequestIntegrity 确保请求的完整性
func (e *ModifyRequestExecutor) ensureRequestIntegrity(req *http.Request) error {
	if req == nil {
		return errors.ErrHTTPRequestEmpty
	}

	e.Logger.Debug(constants.LogMsgRequestIntegrityCheckStart)

	// 1. 验证HTTP请求结构
	err := e.validateHTTPRequestStructure(req)
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPRequestInvalid,
			fmt.Sprintf(constants.ErrMsgRequestProtocolValidationFailed, err))
	}

	// 2. 验证编码一致性
	err = e.verifyRequestEncodingConsistency(req)
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
			fmt.Sprintf("请求编码一致性验证失败: %v", err))
	}

	// 3. 自动修复常见的协议问题
	if constants.DefaultAutoFixRequestIssues {
		err = e.fixCommonRequestIssues(req)
		if err != nil {
			e.Logger.Warn("自动修复请求协议问题时出现错误: %v", err)
		}
	}

	// 4. 最终验证
	err = e.performFinalRequestValidation(req)
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPRequestInvalid,
			fmt.Sprintf("请求最终验证失败: %v", err))
	}

	e.Logger.Debug(constants.LogMsgRequestIntegrityCheckComplete)
	return nil
}

// validateHTTPRequestStructure 验证HTTP请求结构
func (e *ModifyRequestExecutor) validateHTTPRequestStructure(req *http.Request) error {
	// 验证请求方法
	if req.Method == "" {
		req.Method = "GET"
		e.Logger.Debug("已自动设置请求方法: GET")
	}

	// 验证请求方法的有效性
	validMethods := []string{"GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS", "PATCH", "TRACE", "CONNECT"}
	isValidMethod := false
	for _, method := range validMethods {
		if req.Method == method {
			isValidMethod = true
			break
		}
	}
	if !isValidMethod {
		return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPRequestInvalid,
			fmt.Sprintf(constants.ErrMsgInvalidRequestMethod, req.Method))
	}

	// 验证URL
	if req.URL == nil {
		return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPRequestInvalid,
			"请求URL不能为空")
	}

	// 验证URL长度
	urlString := req.URL.String()
	if len(urlString) > constants.MaxURLLength {
		return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPRequestInvalid,
			fmt.Sprintf(constants.ErrMsgRequestURLTooLong, len(urlString), constants.MaxURLLength))
	}

	// 验证HTTP版本
	if req.Proto == "" {
		req.Proto = "HTTP/1.1"
		req.ProtoMajor = 1
		req.ProtoMinor = 1
		e.Logger.Debug("已自动设置HTTP版本: %s", req.Proto)
	}

	// 验证必要的头部
	if req.Header == nil {
		req.Header = make(http.Header)
		e.Logger.Debug("已初始化请求头部")
	}

	// 验证请求头数量
	if len(req.Header) > constants.MaxRequestHeaderCount {
		return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
			fmt.Sprintf(constants.ErrMsgRequestHeaderCountExceeded, len(req.Header), constants.MaxRequestHeaderCount))
	}

	return nil
}

// verifyRequestEncodingConsistency 验证请求编码一致性
func (e *ModifyRequestExecutor) verifyRequestEncodingConsistency(req *http.Request) error {
	contentLength := req.Header.Get("Content-Length")
	transferEncoding := req.Header.Get("Transfer-Encoding")
	contentEncoding := req.Header.Get("Content-Encoding")

	// 检查Content-Length和Transfer-Encoding的冲突
	if contentLength != "" && strings.Contains(strings.ToLower(transferEncoding), "chunked") {
		return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
			"Content-Length和Transfer-Encoding: chunked不能同时存在")
	}

	// 验证Content-Length的有效性
	if contentLength != "" {
		length, err := strconv.ParseInt(contentLength, 10, 64)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
				fmt.Sprintf("无效的Content-Length值: %s", contentLength))
		}
		if length < 0 {
			return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
				fmt.Sprintf("Content-Length不能为负数: %d", length))
		}

		// 验证Content-Length与实际请求体长度的一致性
		if req.ContentLength >= 0 && req.ContentLength != length {
			return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
				fmt.Sprintf(constants.ErrMsgContentLengthMismatch, length, req.ContentLength))
		}
	}

	// 验证Content-Encoding的有效性
	if contentEncoding != "" {
		encodings := strings.Split(strings.ToLower(contentEncoding), ",")
		for _, encoding := range encodings {
			encoding = strings.TrimSpace(encoding)
			if !e.isValidContentEncoding(encoding) {
				return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
					fmt.Sprintf(constants.ErrMsgUnsupportedContentEncoding, encoding))
			}
		}
	}

	// 验证Transfer-Encoding的有效性
	if transferEncoding != "" {
		encodings := strings.Split(strings.ToLower(transferEncoding), ",")
		for _, encoding := range encodings {
			encoding = strings.TrimSpace(encoding)
			if !e.isValidTransferEncoding(encoding) {
				return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
					fmt.Sprintf(constants.ErrMsgUnsupportedTransferEncoding, encoding))
			}
		}
	}

	return nil
}

// isValidTransferEncoding 检查传输编码是否有效
func (e *ModifyRequestExecutor) isValidTransferEncoding(encoding string) bool {
	for _, valid := range constants.ValidTransferEncodings {
		if encoding == valid {
			return true
		}
	}
	return false
}

// fixCommonRequestIssues 修复常见的请求协议问题
func (e *ModifyRequestExecutor) fixCommonRequestIssues(req *http.Request) error {
	fixCount := 0

	// 修复缺失的Host头部
	if req.Header.Get("Host") == "" && req.URL != nil && req.URL.Host != "" {
		req.Header.Set("Host", req.URL.Host)
		fixCount++
		e.Logger.Debug("已添加Host头部: %s", req.URL.Host)
	}

	// 修复缺失的User-Agent头部
	if req.Header.Get("User-Agent") == "" {
		req.Header.Set("User-Agent", "FlexProxy/1.0")
		fixCount++
		e.Logger.Debug("已添加User-Agent头部")
	}

	// 修复Content-Type头部（对于有请求体的请求）
	if req.Header.Get("Content-Type") == "" && req.ContentLength > 0 {
		// 尝试从请求体内容推断Content-Type
		contentType := e.detectRequestContentType(req)
		if contentType != "" {
			req.Header.Set("Content-Type", contentType)
			fixCount++
			e.Logger.Debug("已自动设置Content-Type: %s", contentType)
		}
	}

	// 修复缺失的Accept头部
	if req.Header.Get("Accept") == "" {
		req.Header.Set("Accept", "*/*")
		fixCount++
		e.Logger.Debug("已设置Accept头部")
	}

	// 修复缺失的Connection头部
	if req.Header.Get("Connection") == "" {
		if req.ProtoMajor == 1 && req.ProtoMinor == 0 {
			req.Header.Set("Connection", "close")
		} else {
			req.Header.Set("Connection", "keep-alive")
		}
		fixCount++
		e.Logger.Debug("已设置Connection头部")
	}

	// 清理内部标记头部
	e.cleanInternalRequestHeaders(req)

	if fixCount > 0 {
		e.Logger.Debug("已修复 %d 个请求协议问题", fixCount)
	}

	return nil
}

// performFinalRequestValidation 执行最终请求验证
func (e *ModifyRequestExecutor) performFinalRequestValidation(req *http.Request) error {
	// 验证请求体大小
	if req.ContentLength > constants.MaxRequestBodySize {
		return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf(constants.ErrMsgRequestSizeTooLarge, req.ContentLength, constants.MaxRequestBodySize))
	}

	// 验证关键头部的存在性
	requiredHeaders := []string{"Host"}
	for _, header := range requiredHeaders {
		if req.Header.Get(header) == "" {
			e.Logger.Warn("缺少推荐的HTTP头部: %s", header)
		}
	}

	// 验证请求方法和请求体的一致性
	if (req.Method == "GET" || req.Method == "HEAD") && req.ContentLength > 0 {
		e.Logger.Warn("请求方法 %s 通常不应该有请求体，但Content-Length为 %d", req.Method, req.ContentLength)
	}

	return nil
}

// detectRequestContentType 检测请求内容类型
func (e *ModifyRequestExecutor) detectRequestContentType(req *http.Request) string {
	if req.Body == nil {
		return ""
	}

	// 读取前512字节用于内容类型检测
	buffer := make([]byte, 512)
	n, err := req.Body.Read(buffer)
	if err != nil && err != io.EOF {
		return ""
	}

	// 重新设置请求体
	if n > 0 {
		// 创建新的读取器，包含已读取的数据和剩余的数据
		remaining, _ := io.ReadAll(req.Body)
		fullData := append(buffer[:n], remaining...)
		req.Body = io.NopCloser(bytes.NewReader(fullData))
	}

	// 使用Go标准库检测内容类型
	if n > 0 {
		return http.DetectContentType(buffer[:n])
	}

	return ""
}

// cleanInternalRequestHeaders 清理内部标记头部
func (e *ModifyRequestExecutor) cleanInternalRequestHeaders(req *http.Request) {
	internalHeaders := []string{
		"X-FlexProxy-Internal",
		"X-FlexProxy-Modified",
		"X-FlexProxy-Processed",
		"X-Internal-Marker",
		"X-Proxy-Internal",
	}

	for _, header := range internalHeaders {
		if req.Header.Get(header) != "" {
			req.Header.Del(header)
			e.Logger.Debug("已清理内部头部: %s", header)
		}
	}
}

// =============================================================================
// 请求压缩和编码处理功能
// =============================================================================

// handleRequestContentEncoding 处理请求内容编码（压缩/解压）
func (e *ModifyRequestExecutor) handleRequestContentEncoding(data []byte, encoding string) ([]byte, error) {
	encodings := strings.Split(strings.ToLower(encoding), ",")
	result := data

	for _, enc := range encodings {
		enc = strings.TrimSpace(enc)
		switch enc {
		case constants.ContentEncodingGzip:
			// 对于新的请求体内容，重新进行gzip压缩
			compressed, err := e.compressRequestGzip(result)
			if err != nil {
				return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
					fmt.Sprintf(constants.ErrMsgRequestCompressionFailed, err))
			}
			result = compressed
			e.Logger.Debug(constants.LogMsgRequestCompressionApplied, "gzip", len(data), len(result))

		case constants.ContentEncodingDeflate:
			// 对于新的请求体内容，重新进行deflate压缩
			compressed, err := e.compressRequestDeflate(result)
			if err != nil {
				return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
					fmt.Sprintf(constants.ErrMsgRequestCompressionFailed, err))
			}
			result = compressed
			e.Logger.Debug(constants.LogMsgRequestCompressionApplied, "deflate", len(data), len(result))

		case constants.ContentEncodingIdentity:
			// identity编码表示不压缩，直接使用原数据
			e.Logger.Debug("使用identity编码，不进行压缩")

		default:
			if !e.isValidContentEncoding(enc) {
				return nil, errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPHeaderInvalid,
					fmt.Sprintf(constants.ErrMsgUnsupportedContentEncoding, enc))
			}
		}
	}

	return result, nil
}

// handleRequestChunkedTransferEncoding 处理请求分块传输编码
func (e *ModifyRequestExecutor) handleRequestChunkedTransferEncoding(req *http.Request, data []byte) error {
	return e.ChunkingUtils.HandleChunkedEncodingForRequest(req, data)
}



// compressRequestGzip 使用gzip压缩请求数据
func (e *ModifyRequestExecutor) compressRequestGzip(data []byte) ([]byte, error) {
	return e.CompressionUtils.CompressGzip(data, "请求")
}

// compressRequestDeflate 使用deflate压缩请求数据
func (e *ModifyRequestExecutor) compressRequestDeflate(data []byte) ([]byte, error) {
	return e.CompressionUtils.CompressDeflate(data, "请求")
}

// =============================================================================
// 表单数据处理功能
// =============================================================================

// FormConfig 表单配置结构
type FormConfig struct {
	Fields    map[string]string `json:"fields" yaml:"fields"`       // 表单字段
	Files     map[string]string `json:"files" yaml:"files"`         // 文件字段
	Encoding  string            `json:"encoding" yaml:"encoding"`   // 编码类型：url-encoded, multipart
	Boundary  string            `json:"boundary" yaml:"boundary"`   // 多部分表单边界
	MaxFields int               `json:"max_fields" yaml:"max_fields"` // 最大字段数量
}

// processFormData 处理表单数据
func (e *ModifyRequestExecutor) processFormData(req *http.Request, formConfig *FormConfig) error {
	if req == nil {
		return errors.ErrHTTPRequestEmpty
	}
	if formConfig == nil {
		return nil
	}

	e.Logger.Debug("开始处理表单数据，字段数量: %d，编码类型: %s", len(formConfig.Fields), formConfig.Encoding)

	// 验证表单字段数量
	totalFields := len(formConfig.Fields) + len(formConfig.Files)
	maxFields := formConfig.MaxFields
	if maxFields <= 0 {
		maxFields = constants.MaxFormFieldCount
	}
	if totalFields > maxFields {
		return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf(constants.ErrMsgFormFieldCountExceeded, totalFields, maxFields))
	}

	// 根据编码类型处理表单数据
	switch strings.ToLower(formConfig.Encoding) {
	case "url-encoded", "application/x-www-form-urlencoded", "":
		return e.processURLEncodedForm(req, formConfig)
	case "multipart", "multipart/form-data":
		return e.processMultipartForm(req, formConfig)
	default:
		return errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("不支持的表单编码类型: %s", formConfig.Encoding))
	}
}

// processURLEncodedForm 处理URL编码表单
func (e *ModifyRequestExecutor) processURLEncodedForm(req *http.Request, formConfig *FormConfig) error {
	// 构建表单数据
	formData := make([]string, 0, len(formConfig.Fields))
	for key, value := range formConfig.Fields {
		if key == "" {
			continue
		}
		// URL编码键值对
		encodedKey := strings.ReplaceAll(key, " ", "+")
		encodedValue := strings.ReplaceAll(value, " ", "+")
		formData = append(formData, fmt.Sprintf("%s=%s", encodedKey, encodedValue))
	}

	// 生成表单字符串
	formString := strings.Join(formData, "&")

	// 使用专业的字符编码转换器确保健壮性
	converter := charsetencoding.NewCharsetConverter()
	formBytes, err := converter.Convert([]byte(formString), constants.CharsetUTF8, constants.CharsetUTF8)
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid, "表单数据编码转换失败")
	}

	// 设置请求体
	req.Body = io.NopCloser(bytes.NewReader(formBytes))
	req.ContentLength = int64(len(formBytes))

	// 设置Content-Type和Content-Length
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Content-Length", strconv.Itoa(len(formBytes)))

	e.Logger.Debug(constants.LogMsgFormDataProcessed, len(formConfig.Fields))
	return nil
}

// processMultipartForm 处理多部分表单
func (e *ModifyRequestExecutor) processMultipartForm(req *http.Request, formConfig *FormConfig) error {
	// 生成边界字符串
	boundary := formConfig.Boundary
	if boundary == "" {
		boundary = e.generateMultipartBoundary()
	}

	var formBody bytes.Buffer

	// 添加表单字段
	for key, value := range formConfig.Fields {
		if key == "" {
			continue
		}
		formBody.WriteString(fmt.Sprintf("--%s\r\n", boundary))
		formBody.WriteString(fmt.Sprintf("Content-Disposition: form-data; name=\"%s\"\r\n\r\n", key))
		formBody.WriteString(value)
		formBody.WriteString("\r\n")
	}

	// 添加文件字段 - 支持真实文件和Mock文件
	for key, fileSpec := range formConfig.Files {
		if key == "" || fileSpec == "" {
			continue
		}

		if err := e.addFileToMultipartForm(&formBody, boundary, key, fileSpec); err != nil {
			e.Logger.Warn("添加文件字段失败: %s = %s, 错误: %v", key, fileSpec, err)
			// 继续处理其他字段，不中断整个表单处理
			continue
		}
	}

	// 添加结束边界
	formBody.WriteString(fmt.Sprintf("--%s--\r\n", boundary))

	formBytes := formBody.Bytes()

	// 设置请求体
	req.Body = io.NopCloser(bytes.NewReader(formBytes))
	req.ContentLength = int64(len(formBytes))

	// 设置Content-Type和Content-Length
	req.Header.Set("Content-Type", fmt.Sprintf("multipart/form-data; boundary=%s", boundary))
	req.Header.Set("Content-Length", strconv.Itoa(len(formBytes)))

	totalParts := len(formConfig.Fields) + len(formConfig.Files)
	e.Logger.Debug(constants.LogMsgMultipartFormProcessed, totalParts)
	return nil
}

// addFileToMultipartForm 添加真实文件到多部分表单
func (e *ModifyRequestExecutor) addFileToMultipartForm(formBody *bytes.Buffer, boundary, fieldName, fileSpec string) error {
	// 解析文件规格：支持 "filename" 或 "path:filename" 格式
	var filePath, fileName string

	if strings.Contains(fileSpec, ":") {
		parts := strings.SplitN(fileSpec, ":", 2)
		filePath = parts[0]
		fileName = parts[1]
	} else {
		fileName = fileSpec
		// 如果只提供文件名，尝试在常见路径中查找
		filePath = e.findFileInCommonPaths(fileName)
	}

	// 必须找到真实文件路径
	if filePath == "" {
		return errors.NewError(errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("找不到文件: %s", fileName))
	}

	// 读取真实文件内容
	fileContent, contentType, err := e.readFileContent(filePath)
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("读取文件失败: %s", filePath))
	}

	// 写入文件字段
	formBody.WriteString(fmt.Sprintf("--%s\r\n", boundary))
	formBody.WriteString(fmt.Sprintf("Content-Disposition: form-data; name=\"%s\"; filename=\"%s\"\r\n", fieldName, fileName))
	formBody.WriteString(fmt.Sprintf("Content-Type: %s\r\n\r\n", contentType))
	formBody.Write(fileContent)
	formBody.WriteString("\r\n")

	e.Logger.Debug("添加真实文件: %s (%d bytes)", fileName, len(fileContent))
	return nil
}

// findFileInCommonPaths 在常见路径中查找文件
func (e *ModifyRequestExecutor) findFileInCommonPaths(fileName string) string {
	commonPaths := []string{
		"./uploads/",
		"./files/",
		"./assets/",
		"./testdata/",
		"./",
	}

	for _, basePath := range commonPaths {
		fullPath := filepath.Join(basePath, fileName)
		if _, err := os.Stat(fullPath); err == nil {
			return fullPath
		}
	}

	return ""
}

// readFileContent 读取文件内容并检测MIME类型
func (e *ModifyRequestExecutor) readFileContent(filePath string) ([]byte, string, error) {
	// 检查文件大小限制
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return nil, "", err
	}

	if fileInfo.Size() > constants.MaxFileUploadSize {
		return nil, "", errors.NewError(errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("文件大小超过限制: %d bytes > %d bytes", fileInfo.Size(), constants.MaxFileUploadSize))
	}

	// 读取文件内容
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, "", err
	}

	// 检测MIME类型
	contentType := http.DetectContentType(content)
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	return content, contentType, nil
}

// generateMultipartBoundary 生成多部分表单边界
func (e *ModifyRequestExecutor) generateMultipartBoundary() string {
	// 使用加密安全的随机数生成符合RFC标准的边界
	return e.generateSecureMultipartBoundary()
}

// generateSecureMultipartBoundary 生成加密安全的多部分表单边界
func (e *ModifyRequestExecutor) generateSecureMultipartBoundary() string {
	// RFC 2046规定边界字符串的要求：
	// 1. 长度不超过70个字符
	// 2. 只能包含字母、数字和特定符号
	// 3. 不能以空格结尾
	// 4. 必须唯一且不会在内容中出现

	const (
		boundaryChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
		boundaryLength = 32 // 合理的长度，既保证唯一性又不过长
		boundaryPrefix = "----FlexProxyFormBoundary"
	)

	// 生成随机字符串
	randomPart := make([]byte, boundaryLength)
	for i := range randomPart {
		// 使用加密安全的随机数选择字符
		if randomByte, err := e.generateSecureRandomByte(len(boundaryChars)); err == nil {
			randomPart[i] = boundaryChars[randomByte]
		} else {
			// 回退到时间戳+索引的方式
			randomPart[i] = boundaryChars[(int(time.Now().UnixNano())+i)%len(boundaryChars)]
		}
	}

	// 组合前缀和随机部分
	boundary := boundaryPrefix + string(randomPart)

	// 确保边界不以空格结尾（虽然我们的字符集中没有空格，但保险起见）
	boundary = strings.TrimRight(boundary, " \t")

	// 验证边界长度符合RFC要求
	if len(boundary) > 70 {
		boundary = boundary[:70]
	}

	e.Logger.Debug("生成多部分表单边界: %s", boundary)
	return boundary
}

// generateSecureRandomByte 生成指定范围内的安全随机字节
func (e *ModifyRequestExecutor) generateSecureRandomByte(max int) (int, error) {
	if max <= 0 {
		return 0, fmt.Errorf("最大值必须大于0")
	}

	// 使用crypto/rand生成安全随机数
	randomBytes := make([]byte, 1)
	if _, err := rand.Read(randomBytes); err != nil {
		return 0, err
	}

	// 转换为指定范围内的值
	return int(randomBytes[0]) % max, nil
}

// validateMultipartBoundary 验证多部分表单边界的有效性
func (e *ModifyRequestExecutor) validateMultipartBoundary(boundary string) error {
	if boundary == "" {
		return fmt.Errorf("边界字符串不能为空")
	}

	if len(boundary) > 70 {
		return fmt.Errorf("边界字符串长度超过RFC限制(70字符): %d", len(boundary))
	}

	// 检查是否包含非法字符
	validChars := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'()+_,-./:=? "
	for _, char := range boundary {
		if !strings.ContainsRune(validChars, char) {
			return fmt.Errorf("边界字符串包含非法字符: %c", char)
		}
	}

	// 检查是否以空格结尾
	if strings.HasSuffix(boundary, " ") || strings.HasSuffix(boundary, "\t") {
		return fmt.Errorf("边界字符串不能以空格结尾")
	}

	return nil
}

// parseMultipartForm 解析多部分表单（用于修改现有表单）
func (e *ModifyRequestExecutor) parseMultipartForm(req *http.Request) (*FormConfig, error) {
	if req.Body == nil {
		return nil, nil
	}

	// 检查Content-Type
	contentType := req.Header.Get("Content-Type")
	if !strings.HasPrefix(strings.ToLower(contentType), "multipart/form-data") {
		return nil, errors.WrapError(nil, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			"请求不是多部分表单数据")
	}

	// 使用标准库解析多部分表单数据，支持表单字段和文件上传处理
	// 包含内存限制控制和完整的错误处理机制
	err := req.ParseMultipartForm(constants.MaxMultipartMemory)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf(constants.ErrMsgMultipartFormParseFailed, err))
	}

	formConfig := &FormConfig{
		Fields:   make(map[string]string),
		Files:    make(map[string]string),
		Encoding: "multipart",
	}

	// 提取表单字段
	if req.MultipartForm != nil {
		for key, values := range req.MultipartForm.Value {
			if len(values) > 0 {
				formConfig.Fields[key] = values[0] // 只取第一个值
			}
		}

		// 提取文件字段
		for key, files := range req.MultipartForm.File {
			if len(files) > 0 {
				formConfig.Files[key] = files[0].Filename
			}
		}
	}

	return formConfig, nil
}

// ModifyResponseExecutor 修改响应执行器
type ModifyResponseExecutor struct {
	Logger           interfaces.LogService
	CompressionUtils *utils.HTTPCompressionUtils
	ChunkingUtils    *utils.HTTPChunkingUtils
	ValidationUtils  *utils.HTTPValidationUtils
}

// NewModifyResponseExecutor 创建修改响应执行器实例
func NewModifyResponseExecutor(logger interfaces.LogService) *ModifyResponseExecutor {
	return &ModifyResponseExecutor{
		Logger:           logger,
		CompressionUtils: utils.NewHTTPCompressionUtils(logger),
		ChunkingUtils:    utils.NewHTTPChunkingUtils(logger),
		ValidationUtils:  utils.NewHTTPValidationUtils(logger),
	}
}

func (e *ModifyResponseExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	headers, _ := parameters["headers"].(map[string]interface{})
	removeHeaders, _ := parameters["remove_headers"].([]string)
	statusCode, _ := parameters["status_code"].(int)
	body, _ := parameters["body"].(string)

	e.Logger.Info(constants.LogMsgModifyResponseSuccess, headers, removeHeaders, statusCode, len(body))
	return nil
}

// ResponseModificationState 响应修改状态跟踪
type ResponseModificationState struct {
	OriginalHeaders       http.Header
	OriginalBody          []byte
	OriginalContentLength int64
	OriginalStatusCode    int
	OriginalStatus        string
	ModifiedSteps         []string
	FailedStep            string
	ErrorContext          map[string]interface{}
}

// ExecuteHTTP 实现HTTPExecutor接口，实际修改HTTP响应
func (e *ModifyResponseExecutor) ExecuteHTTP(ctx context.Context, parameters map[string]interface{}, req *http.Request, resp *http.Response) (*http.Request, *http.Response, error) {
	if resp == nil {
		return req, resp, errors.ErrHTTPResponseEmpty
	}

	// 创建修改状态跟踪
	state := &ResponseModificationState{
		OriginalHeaders:       make(http.Header),
		ModifiedSteps:         make([]string, 0),
		ErrorContext:          make(map[string]interface{}),
		OriginalStatusCode:    resp.StatusCode,
		OriginalStatus:        resp.Status,
		OriginalContentLength: resp.ContentLength,
	}

	// 备份原始状态
	for k, v := range resp.Header {
		state.OriginalHeaders[k] = append([]string(nil), v...)
	}

	// 备份原始body
	if resp.Body != nil {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			e.Logger.Error(constants.ErrMsgReadResponseBodyFailed, err)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "读取原始响应体失败")
		}
		resp.Body.Close()
		state.OriginalBody = bodyBytes
		resp.Body = io.NopCloser(bytes.NewReader(bodyBytes))
	}

	// 解析参数
	headers, _ := parameters["headers"].(map[string]interface{})
	removeHeaders, _ := parameters["remove_headers"].([]string)
	statusCode, _ := parameters["status_code"].(int)
	body, _ := parameters["body"].(string)
	bodyConfigRaw, _ := parameters["body_config"]
	keywordOperationsRaw, _ := parameters["keyword_operations"]
	autoSetContentType, _ := parameters["auto_content_type"].(bool)

	// 默认启用自动Content-Type设置
	if _, exists := parameters["auto_content_type"]; !exists {
		autoSetContentType = true
	}

	e.Logger.Info(constants.LogMsgModifyResponseStart,
		headers, removeHeaders, statusCode, len(body), bodyConfigRaw != nil, keywordOperationsRaw != nil)

	// 使用defer确保在发生错误时能够回退
	defer func() {
		if r := recover(); r != nil {
			e.Logger.Error(constants.ErrMsgModifyResponsePanic, r)
			e.rollbackResponseModification(resp, state)
		}
	}()

	// 1. 修改响应状态码
	if statusCode > 0 {
		state.ErrorContext["step"] = "status_code"
		state.ErrorContext["status_code"] = statusCode

		if statusCode < 100 || statusCode > 599 {
			state.FailedStep = "status_code"
			e.Logger.Error(constants.ErrMsgInvalidStatusCode, statusCode)
			e.rollbackResponseModification(resp, state)
			return req, resp, errors.WrapError(nil, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("无效的状态码: %d，状态码必须在100-599范围内", statusCode))
		}

		resp.StatusCode = statusCode
		resp.Status = http.StatusText(statusCode)
		state.ModifiedSteps = append(state.ModifiedSteps, "status_code")
		e.Logger.Debug("响应状态码修改为: %d", statusCode)
	}

	// 2. 处理关键字操作（优先级高）
	if keywordOperationsRaw != nil {
		state.ErrorContext["step"] = "keyword_operations"
		state.ErrorContext["operations"] = keywordOperationsRaw

		err := e.processResponseKeywordOperations(resp, keywordOperationsRaw)
		if err != nil {
			state.FailedStep = "keyword_operations"
			e.Logger.Error(constants.ErrMsgKeywordOperationFailed, err)
			e.rollbackResponseModification(resp, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("关键字操作失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "keyword_operations")
		e.Logger.Debug("关键字操作完成")
	}

	// 3. 处理传统的headers修改（使用智能修改）
	if headers != nil || removeHeaders != nil {
		state.ErrorContext["step"] = "headers"
		state.ErrorContext["headers"] = headers
		state.ErrorContext["remove_headers"] = removeHeaders

		err := e.smartModifyResponseHeaders(resp, headers, removeHeaders)
		if err != nil {
			state.FailedStep = "headers"
			e.Logger.Error(constants.ErrMsgResponseHeaderModifyFailed, err)
			e.rollbackResponseModification(resp, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("响应头修改失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "headers")
		e.Logger.Debug("智能响应头修改完成")
	}

	// 4. 处理body修改 - 优先使用body_config，然后处理body参数
	if bodyConfigRaw != nil {
		state.ErrorContext["step"] = "body_config"
		state.ErrorContext["body_config"] = bodyConfigRaw

		// 解析body_config
		var bodyConfig BodyConfig
		if configMap, ok := bodyConfigRaw.(map[string]interface{}); ok {
			if content, ok := configMap["content"].(string); ok {
				bodyConfig.Content = content
			}
			if contentType, ok := configMap["content_type"].(string); ok {
				bodyConfig.ContentType = contentType
			}
			if format, ok := configMap["format"].(string); ok {
				bodyConfig.Format = format
			}
			if encoding, ok := configMap["encoding"].(string); ok {
				bodyConfig.Encoding = encoding
			}
		}

		err := e.processResponseBodyAdvanced(resp, &bodyConfig, autoSetContentType)
		if err != nil {
			state.FailedStep = "body_config"
			e.Logger.Error(constants.ErrMsgAdvancedResponseBodyModifyFailed, err)
			e.rollbackResponseModification(resp, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("高级响应体修改失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "body_config")
		e.Logger.Debug("高级响应体修改完成，内容长度: %d", resp.ContentLength)
	} else if body != "" {
		state.ErrorContext["step"] = "body"
		state.ErrorContext["body_length"] = len(body)

		// 将字符串格式的body参数封装为BodyConfig结构体，启用高级处理流程
		// 包括自动格式检测、编码转换、Content-Type处理和完整性验证
		bodyConfig := &BodyConfig{
			Content:     body,
			ContentType: "", // 让系统自动检测
			Format:      constants.FormatAuto,
			Encoding:    constants.EncodingUTF8,
		}

		err := replaceResponseBodyAdvanced(resp, bodyConfig, autoSetContentType)
		if err != nil {
			state.FailedStep = "body"
			e.Logger.Error(constants.ErrMsgResponseBodyModifyFailed, err)
			e.rollbackResponseModification(resp, state)
			return req, resp, errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
				fmt.Sprintf("响应体修改失败，已回退所有修改。错误详情: %v", err))
		}
		state.ModifiedSteps = append(state.ModifiedSteps, "body")
		e.Logger.Debug("响应体修改完成，新长度: %d", resp.ContentLength)
	}

	// 6. 最终的响应完整性保证
	if constants.DefaultResponseIntegrityCheck {
		err := e.ensureResponseIntegrity(resp)
		if err != nil {
			e.Logger.Warn(constants.ErrMsgResponseIntegrityCheckFailed, err)
			// 不返回错误，只记录警告，因为这是最终的完整性检查
		}
	}

	e.Logger.Info(constants.LogMsgModifyResponseComplete, state.ModifiedSteps)
	return req, resp, nil
}

// rollbackResponseModification 回退响应修改
func (e *ModifyResponseExecutor) rollbackResponseModification(resp *http.Response, state *ResponseModificationState) {
	e.Logger.Warn(constants.LogMsgModifyResponseRollback, state.FailedStep, state.ModifiedSteps)

	// 恢复原始headers
	resp.Header = make(http.Header)
	for k, v := range state.OriginalHeaders {
		resp.Header[k] = append([]string(nil), v...)
	}

	// 恢复原始状态码
	resp.StatusCode = state.OriginalStatusCode
	resp.Status = state.OriginalStatus

	// 恢复原始body
	if state.OriginalBody != nil {
		resp.Body = io.NopCloser(bytes.NewReader(state.OriginalBody))
		resp.ContentLength = state.OriginalContentLength
	} else {
		resp.Body = nil
		resp.ContentLength = 0
	}

	e.Logger.Info(constants.LogMsgModifyResponseRollbackComplete)
}

// safeModifyHTTPHeaders 安全的HTTP头部修改（响应版本）
func (e *ModifyResponseExecutor) safeModifyHTTPHeaders(header http.Header, headers map[string]interface{}, removeHeaders []string) error {
	// 验证headers参数
	if headers != nil {
		for key, value := range headers {
			if key == "" {
				return errors.ErrResponseHeaderNameEmpty
			}
			if valueStr, ok := value.(string); ok {
				if len(valueStr) > constants.MaxHeaderValueLength {
					return errors.WrapError(nil, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed,
						fmt.Sprintf("响应头 %s 的值过长，最大允许%d字符", key, constants.MaxHeaderValueLength))
				}
			}
		}
	}

	// 验证removeHeaders参数
	if removeHeaders != nil {
		for _, headerName := range removeHeaders {
			if headerName == "" {
				return errors.ErrRemoveResponseHeaderNameEmpty
			}
		}
	}

	// 执行修改
	modifyHTTPHeaders(header, headers, removeHeaders)
	return nil
}

// smartModifyResponseHeaders 智能修改响应头，自动处理特殊头部和相关字段更新
func (e *ModifyResponseExecutor) smartModifyResponseHeaders(resp *http.Response, headers map[string]interface{}, removeHeaders []string) error {
	if resp == nil {
		return errors.ErrHTTPResponseEmpty
	}

	// 记录原始的特殊头部值
	originalContentLength := resp.Header.Get("Content-Length")
	originalTransferEncoding := resp.Header.Get("Transfer-Encoding")
	originalContentEncoding := resp.Header.Get("Content-Encoding")

	// 执行基础头部修改
	err := e.safeModifyHTTPHeaders(resp.Header, headers, removeHeaders)
	if err != nil {
		return err
	}

	// 检查是否修改了特殊头部，需要进行一致性处理
	needsConsistencyCheck := false
	if headers != nil {
		for key := range headers {
			lowerKey := strings.ToLower(key)
			if lowerKey == "content-length" || lowerKey == "transfer-encoding" ||
			   lowerKey == "content-encoding" || lowerKey == "content-type" {
				needsConsistencyCheck = true
				break
			}
		}
	}

	if removeHeaders != nil {
		for _, key := range removeHeaders {
			lowerKey := strings.ToLower(key)
			if lowerKey == "content-length" || lowerKey == "transfer-encoding" ||
			   lowerKey == "content-encoding" || lowerKey == "content-type" {
				needsConsistencyCheck = true
				break
			}
		}
	}

	// 如果修改了特殊头部，进行一致性检查和自动修复
	if needsConsistencyCheck {
		err = e.ensureResponseHeaderConsistency(resp, originalContentLength, originalTransferEncoding, originalContentEncoding)
		if err != nil {
			e.Logger.Warn(constants.ErrMsgResponseIntegrityCheckFailed, err)
			// 不返回错误，只记录警告，因为这是自动修复过程
		}
	}

	return nil
}

// ensureResponseHeaderConsistency 确保响应头的一致性
func (e *ModifyResponseExecutor) ensureResponseHeaderConsistency(resp *http.Response, originalContentLength, originalTransferEncoding, originalContentEncoding string) error {
	// 检查 Content-Length 和 Transfer-Encoding 的冲突
	contentLength := resp.Header.Get("Content-Length")
	transferEncoding := resp.Header.Get("Transfer-Encoding")

	// 如果同时存在 Content-Length 和 Transfer-Encoding: chunked，删除 Content-Length
	if contentLength != "" && strings.Contains(strings.ToLower(transferEncoding), "chunked") {
		resp.Header.Del("Content-Length")
		resp.ContentLength = -1
		e.Logger.Debug("检测到 Transfer-Encoding: chunked，已删除 Content-Length 头部")
	}

	// 如果删除了 Transfer-Encoding 但没有 Content-Length，需要根据响应体设置 Content-Length
	if originalTransferEncoding != "" && transferEncoding == "" && contentLength == "" {
		if resp.Body != nil {
			// 读取响应体以计算长度
			bodyBytes, err := io.ReadAll(resp.Body)
			if err != nil {
				return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid, "读取响应体失败")
			}

			// 重新设置响应体
			resp.Body = io.NopCloser(bytes.NewReader(bodyBytes))
			resp.ContentLength = int64(len(bodyBytes))
			resp.Header.Set("Content-Length", strconv.Itoa(len(bodyBytes)))
			e.Logger.Debug("已自动设置 Content-Length: %d", len(bodyBytes))
		}
	}

	// 重新获取 Content-Length 的值（可能在上面的步骤中被修改）
	contentLength = resp.Header.Get("Content-Length")

	// 验证 Content-Length 的有效性
	if contentLength != "" {
		if length, err := strconv.ParseInt(contentLength, 10, 64); err != nil {
			resp.Header.Del("Content-Length")
			resp.ContentLength = -1
			e.Logger.Warn("无效的 Content-Length 值: %s，已删除", contentLength)
		} else if length < 0 {
			resp.Header.Del("Content-Length")
			resp.ContentLength = -1
			e.Logger.Warn("负数的 Content-Length 值: %d，已删除", length)
		} else {
			resp.ContentLength = length
		}
	} else {
		// 如果没有 Content-Length 头部，确保 ContentLength 字段也被正确设置
		if strings.Contains(strings.ToLower(resp.Header.Get("Transfer-Encoding")), "chunked") {
			resp.ContentLength = -1
		}
	}

	// 验证 Content-Encoding 的有效性
	contentEncoding := resp.Header.Get("Content-Encoding")
	if contentEncoding != "" {
		encodings := strings.Split(strings.ToLower(contentEncoding), ",")
		validEncodings := make([]string, 0, len(encodings))

		for _, encoding := range encodings {
			encoding = strings.TrimSpace(encoding)
			if e.isValidContentEncoding(encoding) {
				validEncodings = append(validEncodings, encoding)
			} else {
				e.Logger.Warn("不支持的内容编码: %s", encoding)
			}
		}

		if len(validEncodings) == 0 {
			resp.Header.Del("Content-Encoding")
			e.Logger.Debug("已删除无效的 Content-Encoding 头部")
		} else if len(validEncodings) != len(encodings) {
			resp.Header.Set("Content-Encoding", strings.Join(validEncodings, ", "))
			e.Logger.Debug("已修正 Content-Encoding 头部: %s", strings.Join(validEncodings, ", "))
		}
	}

	return nil
}

// isValidContentEncoding 检查内容编码是否有效
func (e *ModifyResponseExecutor) isValidContentEncoding(encoding string) bool {
	return e.ValidationUtils.IsValidContentEncoding(encoding)
}

// processResponseKeywordOperations 处理响应的关键字操作
func (e *ModifyResponseExecutor) processResponseKeywordOperations(resp *http.Response, keywordOperationsRaw interface{}) error {
	// 解析keyword_operations（与请求处理类似）
	var keywordOps KeywordOperations
	if opsMap, ok := keywordOperationsRaw.(map[string]interface{}); ok {
		// 解析headers操作
		if headersRaw, exists := opsMap["headers"]; exists {
			if headersList, ok := headersRaw.([]interface{}); ok {
				for _, headerOpRaw := range headersList {
					if headerOpMap, ok := headerOpRaw.(map[string]interface{}); ok {
						headerOp := HeaderKeywordOperation{}
						if operation, ok := headerOpMap["operation"].(string); ok {
							headerOp.Operation = OperationType(operation)
						}
						if matchType, ok := headerOpMap["match_type"].(string); ok {
							headerOp.MatchType = MatchType(matchType)
						}
						if pattern, ok := headerOpMap["pattern"].(string); ok {
							headerOp.Pattern = pattern
						}
						if valuePattern, ok := headerOpMap["value_pattern"].(string); ok {
							headerOp.ValuePattern = valuePattern
						}
						if replacement, ok := headerOpMap["replacement"].(string); ok {
							headerOp.Replacement = replacement
						}
						if newValue, ok := headerOpMap["new_value"].(string); ok {
							headerOp.NewValue = newValue
						}
						if condition, ok := headerOpMap["condition"].(string); ok {
							headerOp.Condition = ConditionType(condition)
						}
						if caseSensitive, ok := headerOpMap["case_sensitive"].(bool); ok {
							headerOp.CaseSensitive = caseSensitive
						}
						keywordOps.Headers = append(keywordOps.Headers, headerOp)
					}
				}
			}
		}

		// 解析body操作
		if bodyRaw, exists := opsMap["body"]; exists {
			if bodyList, ok := bodyRaw.([]interface{}); ok {
				for _, bodyOpRaw := range bodyList {
					if bodyOpMap, ok := bodyOpRaw.(map[string]interface{}); ok {
						bodyOp := BodyKeywordOperation{}
						if operation, ok := bodyOpMap["operation"].(string); ok {
							bodyOp.Operation = OperationType(operation)
						}
						if format, ok := bodyOpMap["format"].(string); ok {
							bodyOp.Format = format
						}
						if matchType, ok := bodyOpMap["match_type"].(string); ok {
							bodyOp.MatchType = MatchType(matchType)
						}
						if pattern, ok := bodyOpMap["pattern"].(string); ok {
							bodyOp.Pattern = pattern
						}
						if replacement, ok := bodyOpMap["replacement"].(string); ok {
							bodyOp.Replacement = replacement
						}
						if caseSensitive, ok := bodyOpMap["case_sensitive"].(bool); ok {
							bodyOp.CaseSensitive = caseSensitive
						}
						if preserveStructure, ok := bodyOpMap["preserve_structure"].(bool); ok {
							bodyOp.PreserveStructure = preserveStructure
						}
						keywordOps.Body = append(keywordOps.Body, bodyOp)
					}
				}
			}
		}
	}

	// 执行Header关键字操作
	if len(keywordOps.Headers) > 0 {
		err := processHeaderKeywordOperations(resp.Header, keywordOps.Headers, e.Logger)
		if err != nil {
			return err
		}
	}

	// 执行Body关键字操作
	if len(keywordOps.Body) > 0 && resp.Body != nil {
		// 读取原始body内容
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "读取响应体失败")
		}
		resp.Body.Close()

		// 执行关键字操作
		// 使用专业的字符编码转换器确保健壮性
		converter := charsetencoding.NewCharsetConverter()
		originalContentBytes, err := converter.Convert(bodyBytes, constants.CharsetUTF8, constants.CharsetUTF8)
		if err != nil {
			return errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid, "响应体内容编码转换失败")
		}
		originalContent := string(originalContentBytes)
		modifiedContent, err := processBodyKeywordOperations(originalContent, keywordOps.Body, e.Logger)
		if err != nil {
			return err
		}

		// 更新响应体
		resp.Body = io.NopCloser(strings.NewReader(modifiedContent))
		resp.ContentLength = int64(len(modifiedContent))
		resp.Header.Set("Content-Length", strconv.Itoa(len(modifiedContent)))
	}

	return nil
}

func (e *ModifyResponseExecutor) Validate(parameters map[string]interface{}) error {
	// 验证body_config参数
	if bodyConfigRaw, exists := parameters["body_config"]; exists {
		if err := validateBodyConfig(bodyConfigRaw, "modify_response"); err != nil {
			return err
		}
	}

	// 验证headers参数
	if headersRaw, exists := parameters["headers"]; exists {
		if _, ok := headersRaw.(map[string]interface{}); !ok {
			return errors.WrapError(nil, errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				"modify_response动作的headers参数必须是键值对格式")
		}
	}

	// 验证remove_headers参数
	if removeHeadersRaw, exists := parameters["remove_headers"]; exists {
		if removeHeaders, ok := removeHeadersRaw.([]interface{}); ok {
			for i, header := range removeHeaders {
				if _, ok := header.(string); !ok {
					return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
						fmt.Sprintf("modify_response动作的remove_headers[%d]必须是字符串", i))
				}
			}
		} else if _, ok := removeHeadersRaw.([]string); !ok {
			return errors.WrapError(nil, errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				"modify_response动作的remove_headers参数必须是字符串数组")
		}
	}

	// 验证status_code参数
	if statusCodeRaw, exists := parameters["status_code"]; exists {
		if statusCode, ok := statusCodeRaw.(int); ok {
			if statusCode < 100 || statusCode > 599 {
				return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
					fmt.Sprintf("modify_response动作的status_code必须在100-599范围内，当前值: %d", statusCode))
			}
		} else {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeConfigFieldInvalid,
				"modify_response动作的status_code参数必须是整数")
		}
	}

	// 验证keyword_operations参数
	if keywordOpsRaw, exists := parameters["keyword_operations"]; exists {
		if err := validateKeywordOperations(keywordOpsRaw, "modify_response"); err != nil {
			return err
		}
	}

	return nil
}

func (e *ModifyResponseExecutor) GetType() string {
	return constants.ActionTypeModifyResponse
}

func (e *ModifyResponseExecutor) GetDescription() string {
	return constants.DescModifyResponseExecutor
}

// CacheResponseExecutor 缓存响应执行器
type CacheResponseExecutor struct {
	Logger       interfaces.LogService
	CacheService interfaces.CacheService
}

func (e *CacheResponseExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	// 检查CacheService是否可用
	if e.CacheService == nil {
		e.Logger.Error(constants.ErrMsgCacheServiceNotInit)
		return errors.ErrCacheServiceNotInitialized
	}

	// 解析缓存时间
	duration := parseCacheDuration(parameters)
	if duration <= 0 || duration > constants.CacheResponseMaxDuration {
		duration = constants.CacheResponseDefaultDuration
		e.Logger.Warn(constants.LogMsgCacheTimeAdjusted, duration)
	}

	// 解析缓存键
	cacheKey := parseCacheKey(parameters, ctx)

	// 解析缓存作用域
	cacheScope := parseCacheScope(parameters)

	// 在上下文中设置缓存标记，供后续HTTP处理流程使用
	if err := setCacheResponseContext(ctx, duration, cacheKey, cacheScope); err != nil {
		e.Logger.Error(constants.ErrMsgCacheOperationFailed, err)
		return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "设置缓存上下文失败")
	}

	logCacheResponseSuccess(e.Logger, duration, cacheKey, cacheScope)
	return nil
}

func (e *CacheResponseExecutor) Validate(parameters map[string]interface{}) error {
	// 验证缓存时间
	if val, ok := parameters["duration"]; ok {
		if !isValidCacheDuration(val) {
			return errors.ErrCacheDurationInvalid
		}
	}

	// 验证缓存键
	if val, ok := parameters["key"]; ok {
		if !isValidCacheKey(val) {
			return errors.ErrCacheKeyInvalid
		}
	}

	return nil
}

func (e *CacheResponseExecutor) GetType() string {
	return constants.ActionTypeCacheResponse
}

func (e *CacheResponseExecutor) GetDescription() string {
	return constants.DescCacheResponseExecutor
}

// ScriptExecutor 脚本执行器
type ScriptExecutor struct {
	Logger interfaces.LogService
}

func (e *ScriptExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	// 解析脚本引擎
	engine := parseScriptEngine(parameters)

	// 解析脚本内容
	scriptContent, scriptType, err := parseScriptContent(parameters)
	if err != nil {
		e.Logger.Error(constants.ErrMsgScriptContentInvalid)
		return err
	}

	// 解析超时时间
	timeout := parseScriptTimeout(parameters)
	if timeout <= 0 || timeout > constants.ScriptExecutorMaxTimeout {
		timeout = constants.ScriptExecutorDefaultTimeout
		e.Logger.Warn(constants.LogMsgScriptTimeoutAdjusted, timeout)
	}

	// 验证脚本大小
	if len(scriptContent) > constants.ScriptExecutorMaxScriptSize {
		e.Logger.Error(constants.ErrMsgScriptSizeExceeded)
		return errors.ErrScriptSizeExceeded
	}

	// 在上下文中设置脚本执行标记，供后续处理流程使用
	if err := setScriptExecutorContext(ctx, engine, scriptContent, timeout, scriptType); err != nil {
		e.Logger.Error(constants.ErrMsgScriptExecutionFailed, err)
		return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "设置脚本执行上下文失败")
	}

	logScriptExecutorSuccess(e.Logger, engine, timeout, scriptType, len(scriptContent))
	return nil
}

func (e *ScriptExecutor) Validate(parameters map[string]interface{}) error {
	// 验证脚本内容
	if _, _, err := parseScriptContent(parameters); err != nil {
		return err
	}

	// 验证脚本引擎
	if val, ok := parameters["engine"]; ok {
		if !isValidScriptEngine(val) {
			return errors.ErrScriptEngineInvalid
		}
	}

	// 验证超时时间
	if val, ok := parameters["timeout"]; ok {
		if !isValidScriptTimeout(val) {
			return errors.ErrScriptTimeoutInvalid
		}
	}

	return nil
}

func (e *ScriptExecutor) GetType() string {
	return constants.ActionTypeScript
}

func (e *ScriptExecutor) GetDescription() string {
	return constants.DescScriptExecutor
}

// RetrySameExecutor 使用相同IP重试执行器
type RetrySameExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *RetrySameExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	// 解析重试次数
	retryCount := parseRetryCount(parameters)
	if retryCount <= 0 {
		logParameterMissingError(e.Logger, "重试次数")
		return errors.ErrRetryCountInvalid
	}

	// 限制最大重试次数
	if retryCount > constants.DefaultMaxRetryAttempts {
		retryCount = constants.DefaultMaxRetryAttempts
		e.Logger.Warn(constants.ErrMsgRetryCountExceedsLimit, constants.DefaultMaxRetryAttempts)
	}

	// 解析重试延迟
	delay := parseRetryDelay(parameters)

	// 获取当前代理（如果有的话）
	currentProxy := getCurrentProxy(ctx)
	if currentProxy == "" {
		// 如果没有当前代理，尝试获取一个
		if e.ProxyService != nil {
			proxy, err := e.ProxyService.GetNextProxy()
			if err != nil {
				e.Logger.Error(constants.ErrMsgGetProxyFailed, err)
				return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "获取代理失败")
			}
			currentProxy = proxy
		}
	}

	// 在上下文中设置重试标记，供后续HTTP处理流程使用
	if err := setRetrySameContext(ctx, retryCount, delay, currentProxy); err != nil {
		e.Logger.Error(constants.ErrMsgSetRetryContextFailed, err)
		return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "设置重试上下文失败")
	}

	logRetrySameSuccess(e.Logger, retryCount, delay, currentProxy)
	return nil
}

func (e *RetrySameExecutor) Validate(parameters map[string]interface{}) error {
	// 验证重试次数
	if val, ok := parameters["retry_count"]; ok {
		if !isValidRetryCount(val) {
			return errors.ErrRetryCountFormatInvalid
		}
	}

	if val, ok := parameters["attempts"]; ok {
		if !isValidRetryCount(val) {
			return errors.ErrRetryCountFormatInvalid
		}
	}

	// 验证延迟参数
	if val, ok := parameters["delay"]; ok {
		if !isValidDelay(val) {
			return errors.ErrDelayFormatInvalid
		}
	}

	return nil
}

func (e *RetrySameExecutor) GetType() string {
	return constants.ActionTypeRetrySame
}

func (e *RetrySameExecutor) GetDescription() string {
	return constants.DescRetrySameExecutor
}

// RetryExecutor 使用新IP重试执行器
type RetryExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *RetryExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	// 解析重试次数
	retryCount := parseRetryCount(parameters)
	if retryCount <= 0 {
		logParameterMissingError(e.Logger, "重试次数")
		return errors.ErrRetryCountInvalid
	}

	// 限制最大重试次数
	if retryCount > constants.DefaultMaxRetryAttempts {
		retryCount = constants.DefaultMaxRetryAttempts
		e.Logger.Warn(constants.ErrMsgRetryCountExceedsLimit, constants.DefaultMaxRetryAttempts)
	}

	// 解析重试延迟
	delay := parseRetryDelay(parameters)

	// 解析IP轮换模式
	rotationMode := parseRotationMode(parameters)

	// 检查ProxyService是否可用
	if e.ProxyService == nil {
		e.Logger.Error(constants.ErrMsgProxyServiceNotInit)
		return errors.ErrProxyServiceNotInitialized
	}

	// 检查代理池大小
	proxyCount := e.ProxyService.GetProxyCount()
	if proxyCount == 0 {
		e.Logger.Error(constants.ErrMsgProxyPoolEmpty)
		return errors.ErrProxyPoolEmpty
	}

	// 如果重试次数超过代理数量，调整重试次数
	if retryCount > proxyCount {
		retryCount = proxyCount
		e.Logger.Warn(constants.ErrMsgRetryCountExceedsProxy, proxyCount)
	}

	// 在上下文中设置重试标记，供后续HTTP处理流程使用
	if err := setRetryContext(ctx, retryCount, delay, rotationMode); err != nil {
		e.Logger.Error(constants.ErrMsgSetRetryContextFailed, err)
		return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "设置重试上下文失败")
	}

	logRetrySuccess(e.Logger, retryCount, delay, rotationMode, proxyCount)
	return nil
}

func (e *RetryExecutor) Validate(parameters map[string]interface{}) error {
	// 验证重试次数
	if val, ok := parameters["retry_count"]; ok {
		if !isValidRetryCount(val) {
			return errors.ErrRetryCountFormatInvalid
		}
	}

	if val, ok := parameters["attempts"]; ok {
		if !isValidRetryCount(val) {
			return errors.ErrRetryCountFormatInvalid
		}
	}

	// 验证延迟参数
	if val, ok := parameters["delay"]; ok {
		if !isValidDelay(val) {
			return errors.ErrDelayFormatInvalid
		}
	}

	// 验证轮换模式
	if val, ok := parameters["rotation_mode"]; ok {
		if !isValidRotationMode(val) {
			return errors.ErrRotationModeInvalid
		}
	}

	return nil
}

func (e *RetryExecutor) GetType() string {
	return constants.ActionTypeRetry
}

func (e *RetryExecutor) GetDescription() string {
	return constants.DescRetryExecutor
}

// BanIPDomainExecutor 针对域名封禁IP执行器
type BanIPDomainExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *BanIPDomainExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	// 解析封禁目标
	target, targetType, err := parseBanTarget(parameters)
	if err != nil {
		e.Logger.Error(constants.ErrMsgBanTargetInvalid)
		return err
	}

	// 解析封禁时间
	duration := parseBanDuration(parameters)
	if duration <= 0 || duration > constants.BanIPDomainMaxDuration {
		duration = constants.BanIPDomainDefaultDuration
		e.Logger.Warn(constants.LogMsgBanTimeAdjusted, duration)
	}

	// 解析封禁原因
	reason := parseBanReason(parameters)

	// 解析是否永久封禁
	permanent := parsePermanentBan(parameters)
	if permanent {
		duration = 0 // 0表示永久封禁
	}

	// 在上下文中设置封禁标记，供后续处理流程使用
	if err := setBanIPDomainContext(ctx, target, targetType, duration, reason, permanent); err != nil {
		e.Logger.Error(constants.ErrMsgBanExecutionFailed, err)
		return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "设置封禁上下文失败")
	}

	logBanIPDomainSuccess(e.Logger, target, duration, reason, targetType)
	return nil
}

func (e *BanIPDomainExecutor) Validate(parameters map[string]interface{}) error {
	// 验证封禁目标
	if _, _, err := parseBanTarget(parameters); err != nil {
		return err
	}

	// 验证封禁时间
	if val, ok := parameters["duration"]; ok {
		if !isValidBanDuration(val) {
			return errors.ErrBanDurationInvalid
		}
	}

	// 验证封禁原因
	if val, ok := parameters["reason"]; ok {
		if !isValidBanReason(val) {
			return errors.ErrBanReasonInvalid
		}
	}

	return nil
}

func (e *BanIPDomainExecutor) GetType() string {
	return constants.ActionTypeBanIPDomain
}

func (e *BanIPDomainExecutor) GetDescription() string {
	return constants.DescBanIPDomainExecutor
}

// SaveToPoolExecutor 保存到代理池执行器
type SaveToPoolExecutor struct {
	Logger       interfaces.LogService
	ProxyService interfaces.ProxyService
}

func (e *SaveToPoolExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	// 检查ProxyService是否可用
	if e.ProxyService == nil {
		e.Logger.Error(constants.ErrMsgProxyServiceNotInit)
		return errors.ErrProxyServiceNotInitialized
	}

	// 解析质量等级
	qualityTier := parseQualityTier(parameters)

	// 解析是否域名特定
	domainSpecific := parseDomainSpecific(parameters)

	// 解析最小分数
	minScore := parseMinScore(parameters)

	// 解析代理池名称
	poolName := parsePoolName(parameters)

	// 获取当前代理信息
	currentProxy := getCurrentProxy(ctx)
	if currentProxy == "" {
		e.Logger.Warn(constants.LogMsgProxyNotFoundForPool)
		// 不返回错误，因为这可能是正常情况
	}

	// 获取域名信息
	domain := extractDomainFromContext(ctx, parameters)

	// 在上下文中设置保存到代理池标记，供后续HTTP处理流程使用
	if err := setSaveToPoolContext(ctx, qualityTier, minScore, poolName, domainSpecific, currentProxy, domain); err != nil {
		e.Logger.Error(constants.ErrMsgSaveToPoolFailed, err)
		return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "设置保存到代理池上下文失败")
	}

	logSaveToPoolSuccess(e.Logger, qualityTier, minScore, poolName, domainSpecific)
	return nil
}

func (e *SaveToPoolExecutor) Validate(parameters map[string]interface{}) error {
	// 验证质量等级
	if val, ok := parameters["quality_tier"]; ok {
		if !isValidQualityTier(val) {
			return errors.ErrQualityTierInvalid
		}
	}

	// 验证最小分数
	if val, ok := parameters["min_score"]; ok {
		if !isValidMinScore(val) {
			return errors.ErrMinScoreInvalid
		}
	}

	// 验证代理池名称
	if val, ok := parameters["pool_name"]; ok {
		if !isValidPoolName(val) {
			return errors.ErrPoolNameInvalid
		}
	}

	return nil
}

func (e *SaveToPoolExecutor) GetType() string {
	return constants.ActionTypeSaveToPool
}

func (e *SaveToPoolExecutor) GetDescription() string {
	return constants.DescSaveToPoolExecutor
}

// CacheExecutor 缓存执行器
type CacheExecutor struct {
	Logger       interfaces.LogService
	CacheService interfaces.CacheService
}

func (e *CacheExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	// 检查CacheService是否可用
	if e.CacheService == nil {
		e.Logger.Error(constants.ErrMsgCacheServiceNotInit)
		return errors.ErrCacheServiceNotInitialized
	}

	// 解析缓存时间
	duration := parseCacheExecutorDuration(parameters)
	if duration <= 0 || duration > constants.CacheExecutorMaxDuration {
		duration = constants.CacheExecutorDefaultDuration
		e.Logger.Warn(constants.LogMsgCacheTimeAdjustedMs, duration)
	}

	// 解析最大使用次数
	maxUseCount := parseCacheMaxUseCount(parameters)

	// 解析缓存作用域
	cacheScope := parseCacheExecutorScope(parameters)

	// 解析自定义缓存键
	customKey := parseCustomCacheKey(parameters)

	// 解析是否忽略参数
	ignoreParams := parseIgnoreParams(parameters)

	// 在上下文中设置缓存标记，供后续处理流程使用
	if err := setCacheExecutorContext(ctx, duration, maxUseCount, cacheScope, customKey, ignoreParams); err != nil {
		e.Logger.Error(constants.ErrMsgCacheExecutionFailed, err)
		return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "设置缓存上下文失败")
	}

	logCacheExecutorSuccess(e.Logger, duration, maxUseCount, cacheScope, ignoreParams)
	return nil
}

func (e *CacheExecutor) Validate(parameters map[string]interface{}) error {
	// 验证缓存时间
	if val, ok := parameters["duration"]; ok {
		if !isValidCacheExecutorDuration(val) {
			return errors.ErrCacheDurationInvalidOld
		}
	}

	// 验证最大使用次数
	if val, ok := parameters["max_use_count"]; ok {
		if !isValidCacheMaxUseCount(val) {
			return errors.ErrCacheMaxUseInvalid
		}
	}

	// 验证缓存作用域
	if val, ok := parameters["cache_scope"]; ok {
		if !isValidCacheScope(val) {
			return errors.ErrCacheScopeInvalid
		}
	}

	return nil
}

func (e *CacheExecutor) GetType() string {
	return constants.ActionTypeCache
}

func (e *CacheExecutor) GetDescription() string {
	return constants.DescCacheExecutor
}

// RequestURLExecutor 请求URL执行器
type RequestURLExecutor struct {
	Logger interfaces.LogService
}

func (e *RequestURLExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	// 解析目标URL
	targetURL, err := parseRequestURL(parameters)
	if err != nil {
		e.Logger.Error(constants.ErrMsgURLInvalid)
		return err
	}

	// 解析HTTP方法
	method := parseHTTPMethod(parameters)

	// 解析超时时间
	timeout := parseRequestTimeout(parameters)
	if timeout <= 0 || timeout > constants.RequestURLMaxTimeout {
		timeout = constants.RequestURLDefaultTimeout
		e.Logger.Warn(constants.LogMsgRequestTimeoutAdjusted, timeout)
	}

	// 解析重试次数
	retries := parseRequestRetries(parameters)

	// 解析请求头
	headers := parseRequestHeaders(parameters)

	// 解析请求体
	body := parseRequestBody(parameters)

	// 解析其他选项
	followRedirect := parseFollowRedirect(parameters)

	// 在上下文中设置请求URL标记，供后续处理流程使用
	if err := setRequestURLContext(ctx, targetURL, method, timeout, retries, headers, body, followRedirect); err != nil {
		e.Logger.Error(constants.ErrMsgRequestExecutionFailed, err)
		return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "设置请求URL上下文失败")
	}

	logRequestURLSuccess(e.Logger, method, targetURL, timeout, retries)
	return nil
}

func (e *RequestURLExecutor) Validate(parameters map[string]interface{}) error {
	// 验证URL
	if _, err := parseRequestURL(parameters); err != nil {
		return err
	}

	// 验证HTTP方法
	if val, ok := parameters["method"]; ok {
		if !isValidHTTPMethod(val) {
			return errors.ErrHTTPMethodInvalid
		}
	}

	// 验证超时时间
	if val, ok := parameters["timeout"]; ok {
		if !isValidRequestTimeout(val) {
			return errors.ErrRequestTimeoutInvalid
		}
	}

	// 验证重试次数
	if val, ok := parameters["retries"]; ok {
		if !isValidRequestRetries(val) {
			return errors.ErrRequestRetriesInvalid
		}
	}

	// 验证请求头
	if val, ok := parameters["headers"]; ok {
		if !isValidRequestHeaders(val) {
			return errors.ErrRequestHeadersInvalid
		}
	}

	return nil
}

func (e *RequestURLExecutor) GetType() string {
	return constants.ActionTypeRequestURL
}

func (e *RequestURLExecutor) GetDescription() string {
	return constants.DescRequestURLExecutor
}

// NullResponseExecutor 空响应执行器
type NullResponseExecutor struct {
	Logger interfaces.LogService
}

func (e *NullResponseExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	// 解析状态码
	statusCode := parseStatusCode(parameters)

	// 解析内容类型
	contentType := parseContentType(parameters)

	// 解析响应体
	body := parseResponseBody(parameters)

	// 解析自定义头部
	customHeaders := parseCustomHeaders(parameters)

	// 在上下文中设置空响应标记，供后续HTTP处理流程使用
	if err := setNullResponseContext(ctx, statusCode, contentType, body, customHeaders); err != nil {
		e.Logger.Error(constants.ErrMsgNullResponseFailed, err)
		return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "设置空响应上下文失败")
	}

	logNullResponseSuccess(e.Logger, statusCode, contentType, len(body))
	return nil
}

func (e *NullResponseExecutor) Validate(parameters map[string]interface{}) error {
	// 验证状态码
	if val, ok := parameters["status_code"]; ok {
		if !isValidStatusCode(val) {
			return errors.ErrStatusCodeInvalid
		}
	}

	// 验证内容类型
	if val, ok := parameters[constants.ParamNameContentType]; ok {
		if contentType, ok := val.(string); ok && contentType != "" {
			validator := validation.NewNetworkValidator()
			result := validator.ValidateContentType(contentType)
			if !result.IsValid {
				return errors.ErrContentTypeInvalid
			}
		} else {
			return errors.ErrContentTypeInvalid
		}
	}

	// 验证响应体
	if val, ok := parameters["body"]; ok {
		if !isValidResponseBody(val) {
			return errors.ErrResponseBodyInvalid
		}
	}

	return nil
}

func (e *NullResponseExecutor) GetType() string {
	return constants.ActionTypeNullResponse
}

func (e *NullResponseExecutor) GetDescription() string {
	return constants.DescNullResponseExecutor
}

// BypassProxyExecutor 绕过代理执行器
type BypassProxyExecutor struct {
	Logger interfaces.LogService
}

func (e *BypassProxyExecutor) Execute(ctx context.Context, parameters map[string]interface{}) error {
	// 解析超时参数
	timeout := parseBypassTimeout(parameters)

	// 解析是否保持连接
	keepAlive := parseKeepAlive(parameters)

	// 解析DNS模式
	dnsMode := parseDNSMode(parameters)

	// 在上下文中设置绕过代理标记，供后续HTTP处理流程使用
	if err := setBypassProxyContext(ctx, timeout, keepAlive, dnsMode); err != nil {
		e.Logger.Error(constants.ErrMsgSetBypassContextFailed, err)
		return errors.WrapError(err, errors.ErrTypeAction, errors.ErrCodeActionExecutionFailed, "设置绕过代理上下文失败")
	}

	logBypassProxySuccess(e.Logger, timeout, keepAlive, dnsMode)
	return nil
}

func (e *BypassProxyExecutor) Validate(parameters map[string]interface{}) error {
	// 验证超时参数
	if val, ok := parameters["timeout"]; ok {
		if !isValidTimeout(val) {
			return errors.ErrTimeoutFormatInvalid
		}
	}

	if val, ok := parameters["timeout_ms"]; ok {
		if !isValidTimeout(val) {
			return errors.ErrTimeoutFormatInvalid
		}
	}

	// 验证DNS模式
	if val, ok := parameters["dns_mode"]; ok {
		if !isValidDNSMode(val) {
			return errors.ErrDNSModeInvalid
		}
	}

	return nil
}

func (e *BypassProxyExecutor) GetType() string {
	return constants.ActionTypeBypassProxy
}

func (e *BypassProxyExecutor) GetDescription() string {
	return constants.DescBypassProxyExecutor
}

// =============================================================================
// 数据结构定义
// =============================================================================

// 模块级别的日志器
var actionLogger = logger.GetActionLogger()

// ActionDefinition 动作定义（用于依赖注入架构）
type ActionDefinition struct {
	Name         string                 `json:"name"`
	Type         string                 `json:"type"`
	Enabled      bool                   `json:"enabled"`
	Parameters   map[string]interface{} `json:"parameters"`
	Description  string                 `json:"description"`
	Executor     Executor               `json:"-"`
	CreatedAt    time.Time              `json:"created_at"`
	LastExecuted time.Time              `json:"last_executed"`
	ExecuteCount int                    `json:"execute_count"`
	ErrorCount   int                    `json:"error_count"`
}

// ExecutionRequest 执行请求
type ExecutionRequest struct {
	ActionName string
	Context    context.Context
	Callback   func(error)
	Timestamp  time.Time
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	ActionName string
	Success    bool
	Error      error
	Duration   time.Duration
	Timestamp  time.Time
}

// =============================================================================
// ActionManager 动作管理器
// =============================================================================

// ActionManager 动作管理器
type ActionManager struct {
	executors map[string]Executor
	logger    *logger.LoggerAdapter
}

// NewActionManager 创建新的动作管理器
func NewActionManager() *ActionManager {
	am := &ActionManager{
		executors: make(map[string]Executor),
		logger:    logger.GetLoggerAdapter(logger.ModuleAction),
	}

	// 注册内置执行器
	am.registerBuiltinExecutors()

	return am
}

// registerBuiltinExecutors 注册内置执行器
func (am *ActionManager) registerBuiltinExecutors() {
	// 创建LogService实例
	logService := &logServiceAdapter{adapter: am.logger}

	// 注册所有内置执行器
	am.RegisterExecutor(&LogExecutor{Logger: logService})
	am.RegisterExecutor(&BanIPExecutor{Logger: logService})
	am.RegisterExecutor(&BanDomainExecutor{Logger: logService})
	am.RegisterExecutor(&BlockRequestExecutor{Logger: logService})
	am.RegisterExecutor(&ModifyRequestExecutor{Logger: logService})
	am.RegisterExecutor(&ModifyResponseExecutor{Logger: logService})
	am.RegisterExecutor(&CacheResponseExecutor{Logger: logService})
	am.RegisterExecutor(&ScriptExecutor{Logger: logService})
	am.RegisterExecutor(&RetrySameExecutor{Logger: logService})
	am.RegisterExecutor(&RetryExecutor{Logger: logService})
	am.RegisterExecutor(&BanIPDomainExecutor{Logger: logService})
	am.RegisterExecutor(&SaveToPoolExecutor{Logger: logService})
	am.RegisterExecutor(&CacheExecutor{Logger: logService})
	am.RegisterExecutor(&RequestURLExecutor{Logger: logService})
	am.RegisterExecutor(&NullResponseExecutor{Logger: logService})
	am.RegisterExecutor(&BypassProxyExecutor{Logger: logService})
}

// logServiceAdapter 适配器，将LoggerAdapter转换为LogService接口
type logServiceAdapter struct {
	adapter *logger.LoggerAdapter
	traceID string                     // 跟踪ID
	fields  map[string]interface{}     // 附加字段
}

func (lsa *logServiceAdapter) Info(msg string, args ...interface{}) {
	enhancedMsg := lsa.enhanceLogMessage(msg)
	lsa.adapter.Info(enhancedMsg, args...)
}

func (lsa *logServiceAdapter) Warn(msg string, args ...interface{}) {
	enhancedMsg := lsa.enhanceLogMessage(msg)
	lsa.adapter.Warn(enhancedMsg, args...)
}

func (lsa *logServiceAdapter) Error(msg string, args ...interface{}) {
	enhancedMsg := lsa.enhanceLogMessage(msg)
	lsa.adapter.Error(enhancedMsg, args...)
}

func (lsa *logServiceAdapter) Debug(msg string, args ...interface{}) {
	enhancedMsg := lsa.enhanceLogMessage(msg)
	lsa.adapter.Debug(enhancedMsg, args...)
}

func (lsa *logServiceAdapter) Fatal(msg string, args ...interface{}) {
	enhancedMsg := lsa.enhanceLogMessage(msg)
	lsa.adapter.Fatal(enhancedMsg, args...)
}

func (lsa *logServiceAdapter) WithTraceID(traceID string) interfaces.LogService {
	// 创建新的适配器实例，包含跟踪ID
	newAdapter := &logServiceAdapter{
		adapter:   lsa.adapter,
		traceID:   traceID,
		fields:    lsa.fields, // 保留现有字段
	}
	return newAdapter
}

func (lsa *logServiceAdapter) WithFields(fields map[string]interface{}) interfaces.LogService {
	// 创建新的适配器实例，合并字段
	newFields := make(map[string]interface{})

	// 复制现有字段
	if lsa.fields != nil {
		for k, v := range lsa.fields {
			newFields[k] = v
		}
	}

	// 添加新字段
	if fields != nil {
		for k, v := range fields {
			newFields[k] = v
		}
	}

	newAdapter := &logServiceAdapter{
		adapter:   lsa.adapter,
		traceID:   lsa.traceID, // 保留现有跟踪ID
		fields:    newFields,
	}
	return newAdapter
}

func (lsa *logServiceAdapter) LogError(err error, msg string, args ...interface{}) {
	if msg != "" {
		lsa.adapter.Error(msg, args...)
	}
	if err != nil {
		lsa.adapter.Error("Error: %v", err)
	}
}

func (lsa *logServiceAdapter) GetLogger() interface{} {
	return lsa.adapter
}

// enhanceLogMessage 增强日志消息，添加跟踪ID和字段信息
func (lsa *logServiceAdapter) enhanceLogMessage(msg string) string {
	var parts []string

	// 添加跟踪ID
	if lsa.traceID != "" {
		parts = append(parts, fmt.Sprintf("[trace_id=%s]", lsa.traceID))
	}

	// 添加字段信息
	if len(lsa.fields) > 0 {
		var fieldParts []string
		for key, value := range lsa.fields {
			fieldParts = append(fieldParts, fmt.Sprintf("%s=%v", key, value))
		}
		if len(fieldParts) > 0 {
			parts = append(parts, fmt.Sprintf("[%s]", strings.Join(fieldParts, ", ")))
		}
	}

	// 组合消息
	if len(parts) > 0 {
		return fmt.Sprintf("%s %s", strings.Join(parts, " "), msg)
	}

	return msg
}

// clone 创建适配器的副本
func (lsa *logServiceAdapter) clone() *logServiceAdapter {
	newFields := make(map[string]interface{})
	if lsa.fields != nil {
		for k, v := range lsa.fields {
			newFields[k] = v
		}
	}

	return &logServiceAdapter{
		adapter: lsa.adapter,
		traceID: lsa.traceID,
		fields:  newFields,
	}
}

func (lsa *logServiceAdapter) UpdateConfig(config interface{}) error {
	// logServiceAdapter 是一个简单的适配器，不需要复杂的配置更新
	// 这里只是为了满足接口要求
	return nil
}

// RegisterExecutor 注册执行器
func (am *ActionManager) RegisterExecutor(executor Executor) {
	am.executors[executor.GetType()] = executor
}

// GetExecutor 获取执行器
func (am *ActionManager) GetExecutor(actionType string) (Executor, bool) {
	executor, exists := am.executors[actionType]
	return executor, exists
}

// ExecuteAction 执行动作
func (am *ActionManager) ExecuteAction(ctx context.Context, actionType string, parameters map[string]interface{}) error {
	executor, exists := am.GetExecutor(actionType)
	if !exists {
		return errors.WrapError(nil, errors.ErrTypeAction, errors.ErrCodeActionNotFound, "未找到动作执行器: "+actionType)
	}

	// 验证参数
	if err := executor.Validate(parameters); err != nil {
		return err
	}

	// 执行动作
	return executor.Execute(ctx, parameters)
}

// ListExecutors 列出所有已注册的执行器
func (am *ActionManager) ListExecutors() []string {
	var types []string
	for actionType := range am.executors {
		types = append(types, actionType)
	}
	return types
}

// BuildActionFromConfig 从配置构建动作
func (am *ActionManager) BuildActionFromConfig(actionConfig config.ActionConfig) (*ActionDefinition, error) {
	actionType := actionConfig.Type
	if actionType == "" {
		return nil, errors.ErrMissingActionType
	}

	executor, exists := am.GetExecutor(actionType)
	if !exists {
		return nil, errors.WrapError(nil, errors.ErrTypeAction, errors.ErrCodeActionNotFound, "未找到动作执行器: "+actionType)
	}

	// 使用ActionConfig的Params作为参数
	parameters := actionConfig.Params
	if parameters == nil {
		parameters = make(map[string]interface{})
	}

	// 验证参数
	if err := executor.Validate(parameters); err != nil {
		return nil, err
	}

	action := &ActionDefinition{
		Name:         actionType,
		Type:         actionType,
		Enabled:      true,
		Parameters:   parameters,
		Description:  executor.GetDescription(),
		Executor:     executor,
		CreatedAt:    time.Now(),
		LastExecuted: time.Time{},
		ExecuteCount: 0,
		ErrorCount:   0,
	}

	return action, nil
}

// Execute 执行动作定义
func (ad *ActionDefinition) Execute(ctx context.Context, req interface{}, resp interface{}, proxyManager interface{}) (*http.Response, error) {
	if !ad.Enabled {
		return nil, errors.WrapError(nil, errors.ErrTypeAction, errors.ErrCodeActionDisabled, "动作已禁用: "+ad.Name)
	}

	// 更新执行统计
	ad.LastExecuted = time.Now()
	ad.ExecuteCount++

	// 检查是否为HTTPExecutor，如果是则调用ExecuteHTTP方法
	if httpExecutor, ok := ad.Executor.(HTTPExecutor); ok {
		// 类型断言HTTP请求和响应对象
		var httpReq *http.Request
		var httpResp *http.Response

		if req != nil {
			if r, ok := req.(*http.Request); ok {
				httpReq = r
			}
		}

		if resp != nil {
			if r, ok := resp.(*http.Response); ok {
				httpResp = r
			}
		}

		// 调用HTTPExecutor的ExecuteHTTP方法
		_, modifiedResp, err := httpExecutor.ExecuteHTTP(ctx, ad.Parameters, httpReq, httpResp)
		if err != nil {
			ad.ErrorCount++
			return nil, err
		}

		// 如果响应被修改，返回修改后的响应
		if modifiedResp != nil && modifiedResp != httpResp {
			return modifiedResp, nil
		}

		// 对于modify_request类型的动作，不返回响应，让修改后的请求继续处理
		return nil, nil
	}

	// 对于非HTTPExecutor，使用原有的Execute方法
	err := ad.Executor.Execute(ctx, ad.Parameters)
	if err != nil {
		ad.ErrorCount++
		return nil, err
	}

	// 对于大多数动作，不返回HTTP响应，让原始响应继续
	return nil, nil
}

// =============================================================================
// 压缩和编码辅助函数
// =============================================================================

// compressGzip 使用gzip压缩数据
func (e *ModifyResponseExecutor) compressGzip(data []byte) ([]byte, error) {
	return e.CompressionUtils.CompressGzip(data, "响应")
}

// compressDeflate 使用deflate压缩数据
func (e *ModifyResponseExecutor) compressDeflate(data []byte) ([]byte, error) {
	return e.CompressionUtils.CompressDeflate(data, "响应")
}
