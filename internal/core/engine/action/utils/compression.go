package utils

import (
	"bytes"
	"compress/flate"
	"compress/gzip"
	"fmt"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/internal/interfaces"
)

// HTTPCompressionUtils HTTP压缩工具
type HTTPCompressionUtils struct {
	Logger interfaces.LogService
}

// NewHTTPCompressionUtils 创建HTTP压缩工具实例
func NewHTTPCompressionUtils(logger interfaces.LogService) *HTTPCompressionUtils {
	return &HTTPCompressionUtils{
		Logger: logger,
	}
}

// CompressGzip 使用gzip压缩数据
func (u *HTTPCompressionUtils) CompressGzip(data []byte, context string) ([]byte, error) {
	var buf bytes.Buffer

	// 创建gzip写入器
	gzipWriter := gzip.NewWriter(&buf)
	defer gzipWriter.Close()

	// 写入数据
	_, err := gzipWriter.Write(data)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("%s gzip压缩写入失败: %v", context, err))
	}

	// 关闭写入器以确保所有数据被写入
	err = gzipWriter.Close()
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("%s gzip压缩关闭失败: %v", context, err))
	}

	return buf.Bytes(), nil
}

// CompressDeflate 使用deflate压缩数据
func (u *HTTPCompressionUtils) CompressDeflate(data []byte, context string) ([]byte, error) {
	var buf bytes.Buffer

	// 创建deflate写入器
	deflateWriter, err := flate.NewWriter(&buf, constants.DefaultCompressionLevel)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("创建%s deflate压缩器失败: %v", context, err))
	}
	defer deflateWriter.Close()

	// 写入数据
	_, err = deflateWriter.Write(data)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("%s deflate压缩写入失败: %v", context, err))
	}

	// 关闭写入器
	err = deflateWriter.Close()
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("%s deflate压缩关闭失败: %v", context, err))
	}

	return buf.Bytes(), nil
}

// DecompressGzip 使用gzip解压数据
func (u *HTTPCompressionUtils) DecompressGzip(data []byte, context string) ([]byte, error) {
	reader := bytes.NewReader(data)
	gzipReader, err := gzip.NewReader(reader)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("创建%s gzip解压器失败: %v", context, err))
	}
	defer gzipReader.Close()

	var buf bytes.Buffer
	_, err = buf.ReadFrom(gzipReader)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("%s gzip解压失败: %v", context, err))
	}

	return buf.Bytes(), nil
}

// DecompressDeflate 使用deflate解压数据
func (u *HTTPCompressionUtils) DecompressDeflate(data []byte, context string) ([]byte, error) {
	reader := bytes.NewReader(data)
	deflateReader := flate.NewReader(reader)
	defer deflateReader.Close()

	var buf bytes.Buffer
	_, err := buf.ReadFrom(deflateReader)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("%s deflate解压失败: %v", context, err))
	}

	return buf.Bytes(), nil
}

// GetCompressionRatio 计算压缩比
func (u *HTTPCompressionUtils) GetCompressionRatio(originalSize, compressedSize int) float64 {
	if originalSize == 0 {
		return 0
	}
	return float64(compressedSize) / float64(originalSize) * 100
}

// LogCompressionResult 记录压缩结果
func (u *HTTPCompressionUtils) LogCompressionResult(context, method string, originalSize, compressedSize int) {
	ratio := u.GetCompressionRatio(originalSize, compressedSize)
	u.Logger.Debug("%s %s压缩完成: 原始大小=%d bytes, 压缩后大小=%d bytes, 压缩比=%.2f%%",
		context, method, originalSize, compressedSize, ratio)
}
