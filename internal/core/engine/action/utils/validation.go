package utils

import (
	"net/http"
	"strconv"
	"strings"

	"flexproxy/common/constants"
	"flexproxy/internal/interfaces"
)

// HTTPValidationUtils HTTP验证工具
type HTTPValidationUtils struct {
	Logger interfaces.LogService
}

// NewHTTPValidationUtils 创建HTTP验证工具实例
func NewHTTPValidationUtils(logger interfaces.LogService) *HTTPValidationUtils {
	return &HTTPValidationUtils{
		Logger: logger,
	}
}

// IsValidContentEncoding 检查内容编码是否有效
func (u *HTTPValidationUtils) IsValidContentEncoding(encoding string) bool {
	for _, valid := range constants.ValidContentEncodings {
		if encoding == valid {
			return true
		}
	}
	return false
}

// IsValidTransferEncoding 检查传输编码是否有效
func (u *HTTPValidationUtils) IsValidTransferEncoding(encoding string) bool {
	for _, valid := range constants.ValidTransferEncodings {
		if encoding == valid {
			return true
		}
	}
	return false
}

// IsValidCharset 检查字符编码是否有效
func (u *HTTPValidationUtils) IsValidCharset(charset string) bool {
	for _, valid := range constants.ValidCharsets {
		if charset == valid {
			return true
		}
	}
	return false
}

// ValidateContentLength 验证Content-Length头部的有效性
func (u *HTTPValidationUtils) ValidateContentLength(contentLength string) (int64, bool) {
	if contentLength == "" {
		return 0, true // 空值是有效的
	}
	
	length, err := strconv.ParseInt(contentLength, 10, 64)
	if err != nil {
		u.Logger.Warn("无效的Content-Length值: %s", contentLength)
		return -1, false
	}
	
	if length < 0 {
		u.Logger.Warn("负数的Content-Length值: %d", length)
		return -1, false
	}
	
	return length, true
}

// ValidateContentEncodingHeader 验证Content-Encoding头部
func (u *HTTPValidationUtils) ValidateContentEncodingHeader(contentEncoding string) []string {
	if contentEncoding == "" {
		return nil
	}
	
	encodings := strings.Split(strings.ToLower(contentEncoding), ",")
	validEncodings := make([]string, 0, len(encodings))
	
	for _, encoding := range encodings {
		encoding = strings.TrimSpace(encoding)
		if u.IsValidContentEncoding(encoding) {
			validEncodings = append(validEncodings, encoding)
		} else {
			u.Logger.Warn("不支持的内容编码: %s", encoding)
		}
	}
	
	return validEncodings
}

// ValidateTransferEncodingHeader 验证Transfer-Encoding头部
func (u *HTTPValidationUtils) ValidateTransferEncodingHeader(transferEncoding string) []string {
	if transferEncoding == "" {
		return nil
	}
	
	encodings := strings.Split(strings.ToLower(transferEncoding), ",")
	validEncodings := make([]string, 0, len(encodings))
	
	for _, encoding := range encodings {
		encoding = strings.TrimSpace(encoding)
		if u.IsValidTransferEncoding(encoding) {
			validEncodings = append(validEncodings, encoding)
		} else {
			u.Logger.Warn("不支持的传输编码: %s", encoding)
		}
	}
	
	return validEncodings
}

// CheckHeaderConflict 检查Content-Length和Transfer-Encoding的冲突
func (u *HTTPValidationUtils) CheckHeaderConflict(contentLength, transferEncoding string) bool {
	// 如果同时存在 Content-Length 和 Transfer-Encoding: chunked，存在冲突
	if contentLength != "" && strings.Contains(strings.ToLower(transferEncoding), "chunked") {
		u.Logger.Debug("检测到Content-Length和Transfer-Encoding: chunked冲突")
		return true
	}
	return false
}

// ValidateHTTPMethod 验证HTTP方法的有效性
func (u *HTTPValidationUtils) ValidateHTTPMethod(method string) bool {
	validMethods := []string{"GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS", "PATCH", "TRACE", "CONNECT"}
	for _, validMethod := range validMethods {
		if method == validMethod {
			return true
		}
	}
	return false
}

// ValidateHTTPVersion 验证HTTP版本
func (u *HTTPValidationUtils) ValidateHTTPVersion(proto string, major, minor int) bool {
	// 支持的HTTP版本
	validVersions := map[string][2]int{
		"HTTP/1.0": {1, 0},
		"HTTP/1.1": {1, 1},
		"HTTP/2.0": {2, 0},
	}
	
	if expectedVersion, exists := validVersions[proto]; exists {
		return expectedVersion[0] == major && expectedVersion[1] == minor
	}
	
	return false
}

// ValidateHeaderName 验证头部名称的有效性
func (u *HTTPValidationUtils) ValidateHeaderName(name string) bool {
	if name == "" {
		return false
	}
	
	// HTTP头部名称不能包含某些字符
	invalidChars := []string{" ", "\t", "\r", "\n", ":", "(", ")", "<", ">", "@", ",", ";", "\\", "\"", "/", "[", "]", "?", "=", "{", "}"}
	for _, char := range invalidChars {
		if strings.Contains(name, char) {
			return false
		}
	}
	
	return true
}

// ValidateHeaderValue 验证头部值的有效性
func (u *HTTPValidationUtils) ValidateHeaderValue(value string) bool {
	// HTTP头部值不能包含控制字符（除了水平制表符）
	for _, char := range value {
		if char < 32 && char != 9 { // 9是水平制表符
			return false
		}
		if char == 127 { // DEL字符
			return false
		}
	}
	
	return true
}

// SanitizeHeader 清理头部值
func (u *HTTPValidationUtils) SanitizeHeader(header http.Header) {
	for name, values := range header {
		if !u.ValidateHeaderName(name) {
			delete(header, name)
			u.Logger.Warn("删除无效的头部名称: %s", name)
			continue
		}
		
		validValues := make([]string, 0, len(values))
		for _, value := range values {
			if u.ValidateHeaderValue(value) {
				validValues = append(validValues, value)
			} else {
				u.Logger.Warn("删除无效的头部值: %s = %s", name, value)
			}
		}
		
		if len(validValues) == 0 {
			delete(header, name)
		} else {
			header[name] = validValues
		}
	}
}
