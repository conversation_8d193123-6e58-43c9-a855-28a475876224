package utils

import (
	"bytes"
	"fmt"
	"io"
	"net/http"

	"flexproxy/common/constants"
	"flexproxy/internal/interfaces"
)

// HTTPChunkingUtils HTTP分块编码工具
type HTTPChunkingUtils struct {
	Logger interfaces.LogService
}

// NewHTTPChunkingUtils 创建HTTP分块编码工具实例
func NewHTTPChunkingUtils(logger interfaces.LogService) *HTTPChunkingUtils {
	return &HTTPChunkingUtils{
		Logger: logger,
	}
}

// CreateChunks 将数据分割成指定大小的块
func (u *HTTPChunkingUtils) CreateChunks(data []byte, chunkSize int) [][]byte {
	if chunkSize <= 0 {
		chunkSize = constants.MaxChunkSize
	}

	var chunks [][]byte
	for i := 0; i < len(data); i += chunkSize {
		end := i + chunkSize
		if end > len(data) {
			end = len(data)
		}
		chunks = append(chunks, data[i:end])
	}

	return chunks
}

// BuildChunkedBody 构建分块编码的请求体
func (u *HTTPChunkingUtils) BuildChunkedBody(chunks [][]byte) *bytes.Buffer {
	var chunkedBody bytes.Buffer
	
	for _, chunk := range chunks {
		// 写入块大小（十六进制）
		chunkedBody.WriteString(fmt.Sprintf("%x\r\n", len(chunk)))
		// 写入块数据
		chunkedBody.Write(chunk)
		chunkedBody.WriteString("\r\n")
	}
	
	// 写入结束块
	chunkedBody.WriteString("0\r\n\r\n")
	
	return &chunkedBody
}

// HandleChunkedEncodingForRequest 处理请求的分块传输编码
func (u *HTTPChunkingUtils) HandleChunkedEncodingForRequest(req *http.Request, data []byte) error {
	// 对于分块传输编码，我们需要将数据分成块
	chunks := u.CreateChunks(data, constants.MaxChunkSize)

	// 构建分块编码的请求体
	chunkedBody := u.BuildChunkedBody(chunks)

	// 设置请求体
	req.Body = io.NopCloser(chunkedBody)
	req.ContentLength = -1 // 分块编码时Content-Length应该为-1

	// 确保删除Content-Length头部
	req.Header.Del("Content-Length")

	u.Logger.Debug("已设置请求分块传输编码，总块数: %d", len(chunks))
	return nil
}

// HandleChunkedEncodingForResponse 处理响应的分块传输编码
func (u *HTTPChunkingUtils) HandleChunkedEncodingForResponse(resp *http.Response, data []byte) error {
	// 对于分块传输编码，我们需要将数据分成块
	chunks := u.CreateChunks(data, constants.MaxChunkSize)

	// 构建分块编码的响应体
	chunkedBody := u.BuildChunkedBody(chunks)

	// 设置响应体
	resp.Body = io.NopCloser(chunkedBody)
	resp.ContentLength = -1 // 分块编码时Content-Length应该为-1

	// 确保删除Content-Length头部
	resp.Header.Del("Content-Length")

	u.Logger.Debug("已设置响应分块传输编码，总块数: %d", len(chunks))
	return nil
}

// ValidateChunkedData 验证分块数据的完整性
func (u *HTTPChunkingUtils) ValidateChunkedData(data []byte) error {
	// 检查是否包含CRLF分隔符
	if !bytes.Contains(data, []byte("\r\n")) {
		return fmt.Errorf("分块编码数据应该包含CRLF分隔符")
	}

	// 检查是否以结束块结尾
	if !bytes.HasSuffix(data, []byte("0\r\n\r\n")) {
		return fmt.Errorf("分块编码数据应该以结束块结尾")
	}

	u.Logger.Debug("分块编码数据验证通过，数据长度: %d bytes", len(data))
	return nil
}

// GetChunkInfo 获取分块信息
func (u *HTTPChunkingUtils) GetChunkInfo(data []byte, chunkSize int) (int, int, float64) {
	chunks := u.CreateChunks(data, chunkSize)
	chunkedBody := u.BuildChunkedBody(chunks)
	
	chunkCount := len(chunks)
	originalSize := len(data)
	chunkedSize := chunkedBody.Len()
	overhead := float64(chunkedSize-originalSize) / float64(originalSize) * 100
	
	return chunkCount, chunkedSize, overhead
}

// LogChunkingResult 记录分块结果
func (u *HTTPChunkingUtils) LogChunkingResult(context string, originalSize, chunkedSize, chunkCount int) {
	overhead := float64(chunkedSize-originalSize) / float64(originalSize) * 100
	u.Logger.Debug("%s 分块编码完成: 原始大小=%d bytes, 分块后大小=%d bytes, 块数=%d, 开销=%.2f%%",
		context, originalSize, chunkedSize, chunkCount, overhead)
}
