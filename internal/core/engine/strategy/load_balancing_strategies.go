// Package strategy 提供代理选择策略
package strategy

import (
	"context"
	"math"
	"math/rand"
	"sort"
	"sync"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
)

// RoundRobinLoadBalancer 实现轮询负载均衡
type RoundRobinLoadBalancer struct {
	mu           sync.RWMutex
	currentIndex map[string]int // 每个目标的当前索引
	metrics      map[string]*TargetMetrics
	baseManager  *BaseMetricsManager
}

// NewRoundRobinLoadBalancer 创建新的轮询负载均衡器
func NewRoundRobinLoadBalancer() *RoundRobinLoadBalancer {
	return &RoundRobinLoadBalancer{
		currentIndex: make(map[string]int),
		metrics:      make(map[string]*TargetMetrics),
		baseManager:  NewBaseMetricsManager(),
	}
}

// Balance 轮询选择目标
func (lb *RoundRobinLoadBalancer) Balance(ctx context.Context, targets []Target) (Target, error) {
	if len(targets) == 0 {
		return Target{}, errors.NewError(errors.ErrTypeLoadBalancer, errors.ErrCodeLoadBalancerNoTargets, "目标列表为空")
	}

	lb.mu.Lock()
	defer lb.mu.Unlock()

	// 过滤健康的目标
	healthyTargets := make([]Target, 0, len(targets))
	for _, target := range targets {
		metrics := lb.metrics[target.ID]
		if lb.baseManager.IsTargetHealthy(metrics) {
			healthyTargets = append(healthyTargets, target)
		}
	}

	if len(healthyTargets) == 0 {
		return Target{}, errors.NewError(errors.ErrTypeLoadBalancer, errors.ErrCodeLoadBalancerNoHealthyTargets, "没有健康的目标")
	}

	// 获取当前索引
	key := "default" // 简化的key生成
	currentIdx := lb.currentIndex[key]

	// 选择目标
	target := healthyTargets[currentIdx%len(healthyTargets)]
	lb.currentIndex[key] = (currentIdx + 1) % len(healthyTargets)

	return target, nil
}

// UpdateTargetHealth 更新目标健康状态
func (lb *RoundRobinLoadBalancer) UpdateTargetHealth(target Target, healthy bool) {
	lb.baseManager.UpdateTargetHealth(lb.metrics, target, healthy, &lb.mu)
}

// GetName 获取策略名称
func (lb *RoundRobinLoadBalancer) GetName() string {
	return "round_robin"
}

// UpdateTargetMetrics 更新目标指标
func (lb *RoundRobinLoadBalancer) UpdateTargetMetrics(targetID string, success bool, responseTime time.Duration) {
	metrics := lb.baseManager.GetOrCreateTargetMetrics(lb.metrics, targetID, &lb.mu)
	lb.baseManager.UpdateTargetMetrics(metrics, success, responseTime)
	lb.baseManager.UpdateTargetHealthStatus(metrics)
}

// WeightedRoundRobinLoadBalancer 实现加权轮询负载均衡
type WeightedRoundRobinLoadBalancer struct {
	mu               sync.RWMutex
	currentWeights   map[string]int
	effectiveWeights map[string]int
	metrics          map[string]*TargetMetrics
	baseManager      *BaseMetricsManager
}

// NewWeightedRoundRobinLoadBalancer 创建新的加权轮询负载均衡器
func NewWeightedRoundRobinLoadBalancer() *WeightedRoundRobinLoadBalancer {
	return &WeightedRoundRobinLoadBalancer{
		currentWeights:   make(map[string]int),
		effectiveWeights: make(map[string]int),
		metrics:          make(map[string]*TargetMetrics),
		baseManager:      NewBaseMetricsManager(),
	}
}

// Balance 加权轮询选择目标
func (lb *WeightedRoundRobinLoadBalancer) Balance(ctx context.Context, targets []Target) (Target, error) {
	if len(targets) == 0 {
		return Target{}, errors.NewError(errors.ErrTypeLoadBalancer, errors.ErrCodeLoadBalancerNoTargets, "目标列表为空")
	}

	lb.mu.Lock()
	defer lb.mu.Unlock()

	// 过滤健康的目标
	healthyTargets := make([]Target, 0, len(targets))
	for _, target := range targets {
		metrics := lb.metrics[target.ID]
		if lb.baseManager.IsTargetHealthy(metrics) {
			healthyTargets = append(healthyTargets, target)
		}
	}

	if len(healthyTargets) == 0 {
		return Target{}, errors.NewError(errors.ErrTypeLoadBalancer, errors.ErrCodeLoadBalancerNoHealthyTargets, "没有健康的目标")
	}

	// 初始化权重
	for _, target := range healthyTargets {
		if _, exists := lb.effectiveWeights[target.ID]; !exists {
			lb.effectiveWeights[target.ID] = target.Weight
			lb.currentWeights[target.ID] = 0
		}
	}

	// 加权轮询算法
	totalWeight := 0
	var selected Target
	maxCurrentWeight := -1

	for _, target := range healthyTargets {
		totalWeight += lb.effectiveWeights[target.ID]
		lb.currentWeights[target.ID] += lb.effectiveWeights[target.ID]

		if lb.currentWeights[target.ID] > maxCurrentWeight {
			maxCurrentWeight = lb.currentWeights[target.ID]
			selected = target
		}
	}

	lb.currentWeights[selected.ID] -= totalWeight

	return selected, nil
}

// UpdateTargetHealth 更新目标健康状态
func (lb *WeightedRoundRobinLoadBalancer) UpdateTargetHealth(target Target, healthy bool) {
	lb.baseManager.UpdateTargetHealth(lb.metrics, target, healthy, &lb.mu)
}

// GetName 获取策略名称
func (lb *WeightedRoundRobinLoadBalancer) GetName() string {
	return "weighted_round_robin"
}

// UpdateTargetMetrics 更新目标指标
func (lb *WeightedRoundRobinLoadBalancer) UpdateTargetMetrics(targetID string, success bool, responseTime time.Duration) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	metrics, exists := lb.metrics[targetID]
	if !exists {
		metrics = &TargetMetrics{
			TargetID: targetID,
			Healthy:  true,
			Weight:   1,
		}
		lb.metrics[targetID] = metrics
		lb.effectiveWeights[targetID] = 1
	}

	lb.baseManager.UpdateTargetMetrics(metrics, success, responseTime)
	lb.baseManager.UpdateTargetHealthStatus(metrics)

	// 根据成功/失败调整有效权重
	if !success {
		// 失败时降低有效权重
		if lb.effectiveWeights[targetID] > 0 {
			lb.effectiveWeights[targetID]--
		}
	} else {
		// 成功时恢复有效权重
		if lb.effectiveWeights[targetID] < metrics.Weight {
			lb.effectiveWeights[targetID]++
		}
	}
}

// LeastConnectionsLoadBalancer 实现最少连接负载均衡
type LeastConnectionsLoadBalancer struct {
	mu          sync.RWMutex
	connections map[string]int64 // 当前连接数
	metrics     map[string]*TargetMetrics
	baseManager *BaseMetricsManager
}

// NewLeastConnectionsLoadBalancer 创建新的最少连接负载均衡器
func NewLeastConnectionsLoadBalancer() *LeastConnectionsLoadBalancer {
	return &LeastConnectionsLoadBalancer{
		connections: make(map[string]int64),
		metrics:     make(map[string]*TargetMetrics),
		baseManager: NewBaseMetricsManager(),
	}
}

// Balance 选择连接数最少的目标
func (lb *LeastConnectionsLoadBalancer) Balance(ctx context.Context, targets []Target) (Target, error) {
	if len(targets) == 0 {
		return Target{}, errors.NewError(errors.ErrTypeLoadBalancer, errors.ErrCodeLoadBalancerNoTargets, "目标列表为空")
	}

	lb.mu.Lock()
	defer lb.mu.Unlock()

	// 过滤健康的目标
	healthyTargets := make([]Target, 0, len(targets))
	for _, target := range targets {
		metrics := lb.metrics[target.ID]
		if lb.baseManager.IsTargetHealthy(metrics) {
			healthyTargets = append(healthyTargets, target)
		}
	}

	if len(healthyTargets) == 0 {
		return Target{}, errors.NewError(errors.ErrTypeLoadBalancer, errors.ErrCodeLoadBalancerNoHealthyTargets, "没有健康的目标")
	}

	// 找到连接数最少的目标
	selected := healthyTargets[0]
	minConnections := lb.connections[selected.ID]

	for _, target := range healthyTargets {
		connections := lb.connections[target.ID]

		if connections < minConnections {
			minConnections = connections
			selected = target
		}
	}

	// 增加选中目标的连接数
	lb.connections[selected.ID]++

	return selected, nil
}

// UpdateTargetHealth 更新目标健康状态
func (lb *LeastConnectionsLoadBalancer) UpdateTargetHealth(target Target, healthy bool) {
	lb.baseManager.UpdateTargetHealth(lb.metrics, target, healthy, &lb.mu)
}

// GetName 获取策略名称
func (lb *LeastConnectionsLoadBalancer) GetName() string {
	return "least_connections"
}

// UpdateTargetMetrics 更新目标指标
func (lb *LeastConnectionsLoadBalancer) UpdateTargetMetrics(targetID string, success bool, responseTime time.Duration) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	// 减少连接数
	if lb.connections[targetID] > 0 {
		lb.connections[targetID]--
	}

	metrics, exists := lb.metrics[targetID]
	if !exists {
		metrics = &TargetMetrics{
			TargetID: targetID,
			Healthy:  true,
			Weight:   1,
		}
		lb.metrics[targetID] = metrics
	}

	lb.baseManager.UpdateTargetMetrics(metrics, success, responseTime)
	lb.baseManager.UpdateTargetHealthStatus(metrics)
}

// ResponseTimeLoadBalancer 基于响应时间的负载均衡策略
type ResponseTimeLoadBalancer struct {
	mu          sync.RWMutex
	metrics     map[string]*TargetMetrics
	baseManager *BaseMetricsManager
	random      *rand.Rand
}

// NewResponseTimeLoadBalancer 创建基于响应时间的负载均衡器
func NewResponseTimeLoadBalancer() *ResponseTimeLoadBalancer {
	return &ResponseTimeLoadBalancer{
		metrics:     make(map[string]*TargetMetrics),
		baseManager: NewBaseMetricsManager(),
		random:      rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// Balance 选择响应时间最短的目标
func (lb *ResponseTimeLoadBalancer) Balance(ctx context.Context, targets []Target) (Target, error) {
	if len(targets) == 0 {
		return Target{}, errors.NewError(errors.ErrTypeLoadBalancer, errors.ErrCodeLoadBalancerNoTargets, "目标列表为空")
	}

	lb.mu.RLock()
	defer lb.mu.RUnlock()

	// 过滤健康的目标并按响应时间排序
	type targetScore struct {
		target       Target
		responseTime time.Duration
	}

	healthyTargets := make([]targetScore, 0, len(targets))
	for _, target := range targets {
		metrics := lb.metrics[target.ID]
		if !lb.baseManager.IsTargetHealthy(metrics) {
			continue
		}
		responseTime := time.Duration(0)
		if metrics != nil {
			responseTime = metrics.AvgResponseTime
		}

		healthyTargets = append(healthyTargets, targetScore{
			target:       target,
			responseTime: responseTime,
		})
	}

	if len(healthyTargets) == 0 {
		return Target{}, errors.NewError(errors.ErrTypeLoadBalancer, errors.ErrCodeLoadBalancerNoHealthyTargets, "没有健康的目标")
	}

	// 按响应时间升序排序
	sort.Slice(healthyTargets, func(i, j int) bool {
		// 新目标（响应时间为0）排在前面
		if healthyTargets[i].responseTime == 0 && healthyTargets[j].responseTime > 0 {
			return true
		}
		if healthyTargets[i].responseTime > 0 && healthyTargets[j].responseTime == 0 {
			return false
		}
		return healthyTargets[i].responseTime < healthyTargets[j].responseTime
	})

	// 选择前30%的快速目标进行加权随机选择
	topCount := int(math.Max(1, float64(len(healthyTargets))*constants.TopTargetsRatio))
	topTargets := healthyTargets[:topCount]

	// 基于响应时间的反向权重选择
	totalWeight := 0.0
	for _, ts := range topTargets {
		// 响应时间越短，权重越高
		weight := 1.0
		if ts.responseTime > 0 {
			weight = 1.0 / float64(ts.responseTime.Milliseconds()+1)
		}
		totalWeight += weight
	}

	if totalWeight == 0 {
		return topTargets[0].target, nil
	}

	randomValue := lb.random.Float64() * totalWeight
	currentWeight := 0.0

	for _, ts := range topTargets {
		weight := 1.0
		if ts.responseTime > 0 {
			weight = 1.0 / float64(ts.responseTime.Milliseconds()+1)
		}
		currentWeight += weight
		if currentWeight >= randomValue {
			return ts.target, nil
		}
	}

	return topTargets[0].target, nil
}

// UpdateTargetHealth 更新目标健康状态
func (lb *ResponseTimeLoadBalancer) UpdateTargetHealth(target Target, healthy bool) {
	lb.baseManager.UpdateTargetHealth(lb.metrics, target, healthy, &lb.mu)
}

// GetName 获取策略名称
func (lb *ResponseTimeLoadBalancer) GetName() string {
	return "response_time"
}

// UpdateTargetMetrics 更新目标指标
func (lb *ResponseTimeLoadBalancer) UpdateTargetMetrics(targetID string, success bool, responseTime time.Duration) {
	metrics := lb.baseManager.GetOrCreateTargetMetrics(lb.metrics, targetID, &lb.mu)
	lb.baseManager.UpdateTargetMetrics(metrics, success, responseTime)
	lb.baseManager.UpdateTargetHealthStatus(metrics)
}
