package strategy

import (
	"context"
	"math"
	"math/rand"
	"sort"
	"sync"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
)

// RandomProxyStrategy 随机代理选择策略
type RandomProxyStrategy struct {
	mu          sync.RWMutex
	metrics     map[string]*ProxyMetrics
	random      *rand.Rand
	baseManager *BaseMetricsManager
}

// NewRandomProxyStrategy 创建新的随机代理策略
func NewRandomProxyStrategy() *RandomProxyStrategy {
	return &RandomProxyStrategy{
		metrics:     make(map[string]*ProxyMetrics),
		random:      rand.New(rand.NewSource(time.Now().UnixNano())),
		baseManager: NewBaseMetricsManager(),
	}
}

// SelectProxy 随机选择代理
func (s *RandomProxyStrategy) SelectProxy(ctx context.Context, proxies []string, domain string) (string, error) {
	if len(proxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理列表为空")
	}

	s.mu.RLock()
	defer s.mu.RUnlock()

	// 过滤可用代理
	availableProxies := make([]string, 0, len(proxies))
	for _, proxy := range proxies {
		metrics := s.metrics[proxy]
		if s.baseManager.IsProxyAvailable(metrics) {
			availableProxies = append(availableProxies, proxy)
		}
	}

	if len(availableProxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "没有可用的代理")
	}

	// 随机选择
	index := s.random.Intn(len(availableProxies))
	return availableProxies[index], nil
}

// GetName 获取策略名称
func (s *RandomProxyStrategy) GetName() string {
	return "random"
}

// UpdateProxyMetrics 更新代理指标
func (s *RandomProxyStrategy) UpdateProxyMetrics(proxy string, success bool, responseTime time.Duration, domain string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	metrics, exists := s.metrics[proxy]
	if !exists {
		metrics = &ProxyMetrics{
			ProxyURL:    proxy,
			DomainStats: make(map[string]*DomainPerformance),
		}
		s.metrics[proxy] = metrics
	}

	s.baseManager.UpdateProxyMetrics(metrics, success, responseTime, domain)
}

// SequentialProxyStrategy 实现顺序代理选择
type SequentialProxyStrategy struct {
	mu           sync.RWMutex
	currentIndex int
	metrics      map[string]*ProxyMetrics
	baseManager  *BaseMetricsManager
}

// NewSequentialProxyStrategy 创建新的顺序代理策略
func NewSequentialProxyStrategy() *SequentialProxyStrategy {
	return &SequentialProxyStrategy{
		metrics:      make(map[string]*ProxyMetrics),
		baseManager:  NewBaseMetricsManager(),
		currentIndex: 0,
	}
}

// SelectProxy 顺序选择代理
func (s *SequentialProxyStrategy) SelectProxy(ctx context.Context, proxies []string, domain string) (string, error) {
	if len(proxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理列表为空")
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	// 过滤可用代理
	availableProxies := make([]string, 0, len(proxies))
	for _, proxy := range proxies {
		metrics := s.metrics[proxy]
		if s.baseManager.IsProxyAvailable(metrics) {
			availableProxies = append(availableProxies, proxy)
		}
	}

	if len(availableProxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "没有可用的代理")
	}

	// 顺序选择
	proxy := availableProxies[s.currentIndex%len(availableProxies)]
	s.currentIndex++

	return proxy, nil
}

// GetName 获取策略名称
func (s *SequentialProxyStrategy) GetName() string {
	return "sequential"
}

// UpdateProxyMetrics 更新代理指标
func (s *SequentialProxyStrategy) UpdateProxyMetrics(proxy string, success bool, responseTime time.Duration, domain string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	metrics, exists := s.metrics[proxy]
	if !exists {
		metrics = &ProxyMetrics{
			ProxyURL:    proxy,
			DomainStats: make(map[string]*DomainPerformance),
		}
		s.metrics[proxy] = metrics
	}

	s.baseManager.UpdateProxyMetrics(metrics, success, responseTime, domain)
}

// QualityBasedProxyStrategy 实现基于质量的代理选择
type QualityBasedProxyStrategy struct {
	baseManager *BaseMetricsManager
	mu          sync.RWMutex
	metrics     map[string]*ProxyMetrics
	random      *rand.Rand
}

// NewQualityBasedProxyStrategy 创建新的基于质量的代理策略
func NewQualityBasedProxyStrategy() *QualityBasedProxyStrategy {
	return &QualityBasedProxyStrategy{
		baseManager: NewBaseMetricsManager(),
		metrics:     make(map[string]*ProxyMetrics),
		random:      rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// SelectProxy 基于质量选择代理
func (s *QualityBasedProxyStrategy) SelectProxy(ctx context.Context, proxies []string, domain string) (string, error) {
	if len(proxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理列表为空")
	}

	s.mu.RLock()
	defer s.mu.RUnlock()

	// 按质量评分排序代理
	type proxyScore struct {
		proxy string
		score float64
	}

	proxyScores := make([]proxyScore, 0, len(proxies))
	for _, proxy := range proxies {
		metrics, exists := s.metrics[proxy]
		if !exists {
			metrics = &ProxyMetrics{ProxyURL: proxy}
		}
		if !s.baseManager.IsProxyAvailable(metrics) {
			continue
		}

		score := s.getProxyScore(proxy, domain)
		proxyScores = append(proxyScores, proxyScore{proxy: proxy, score: score})
	}

	if len(proxyScores) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "没有可用的代理")
	}

	// 按评分降序排序
	sort.Slice(proxyScores, func(i, j int) bool {
		return proxyScores[i].score > proxyScores[j].score
	})

	// 使用加权随机选择前30%的高质量代理
	topCount := int(math.Max(1, float64(len(proxyScores))*constants.TopProxyRatio))
	topProxies := proxyScores[:topCount]

	// 加权随机选择
	totalWeight := 0.0
	for _, ps := range topProxies {
		totalWeight += ps.score
	}

	if totalWeight == 0 {
		return topProxies[0].proxy, nil
	}

	randomValue := s.random.Float64() * totalWeight
	currentWeight := 0.0

	for _, ps := range topProxies {
		currentWeight += ps.score
		if currentWeight >= randomValue {
			return ps.proxy, nil
		}
	}

	return topProxies[0].proxy, nil
}

// GetName 获取策略名称
func (s *QualityBasedProxyStrategy) GetName() string {
	return "quality"
}

// UpdateProxyMetrics 更新代理指标
func (s *QualityBasedProxyStrategy) UpdateProxyMetrics(proxy string, success bool, responseTime time.Duration, domain string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	metrics, exists := s.metrics[proxy]
	if !exists {
		metrics = &ProxyMetrics{
			ProxyURL:    proxy,
			DomainStats: make(map[string]*DomainPerformance),
		}
		s.metrics[proxy] = metrics
	}

	// 使用基础指标管理器更新指标
	s.baseManager.UpdateProxyMetrics(metrics, success, responseTime, domain)
}

// getProxyScore 获取代理评分（考虑域名特定性能）
func (s *QualityBasedProxyStrategy) getProxyScore(proxy, domain string) float64 {
	metrics, exists := s.metrics[proxy]
	if !exists {
		return constants.DefaultQualityScore // 新代理默认评分
	}

	// 如果有域名特定统计，优先使用
	if domain != "" {
		if domainStats, exists := metrics.DomainStats[domain]; exists {
			return domainStats.QualityScore
		}
	}

	return metrics.QualityScore
}

// SmartProxyStrategy 智能代理选择策略
// 根据触发的动作类型、历史性能、负载均衡等多维度智能决定代理选择
type SmartProxyStrategy struct {
	mu                sync.RWMutex
	metrics           map[string]*ProxyMetrics
	random            *rand.Rand
	baseManager       *BaseMetricsManager
	currentProxy      string
	lastActionType    string
	// 智能决策相关字段
	proxyUsageHistory map[string]*ProxyUsageInfo // 代理使用历史
	domainProxyMap    map[string][]string        // 域名到代理的映射
	adaptiveWeights   *AdaptiveWeights           // 自适应权重
	decisionHistory   []ProxyDecision            // 决策历史
	maxHistorySize    int                        // 最大历史记录数
}

// ProxyUsageInfo 代理使用信息
type ProxyUsageInfo struct {
	LastUsed        time.Time     // 最后使用时间
	UsageCount      int64         // 使用次数
	ContinuousUse   int           // 连续使用次数
	AvgSessionTime  time.Duration // 平均会话时间
	RecentFailures  int           // 最近失败次数
	LastFailureTime time.Time     // 最后失败时间
}

// AdaptiveWeights 自适应权重
type AdaptiveWeights struct {
	QualityWeight    float64 // 质量权重
	LoadBalanceWeight float64 // 负载均衡权重
	DomainAffinityWeight float64 // 域名亲和性权重
	FreshnessWeight  float64 // 新鲜度权重
	LastUpdated      time.Time // 最后更新时间
}

// ProxyDecision 代理决策记录
type ProxyDecision struct {
	Timestamp    time.Time // 决策时间
	SelectedProxy string   // 选择的代理
	Reason       string    // 选择原因
	Domain       string    // 目标域名
	ActionType   string    // 动作类型
	Success      bool      // 是否成功
}

// proxyScore 代理评分结构
type proxyScore struct {
	proxy  string
	score  float64
	reason string
}

// NewSmartProxyStrategy 创建新的智能代理策略
func NewSmartProxyStrategy() *SmartProxyStrategy {
	return &SmartProxyStrategy{
		metrics:           make(map[string]*ProxyMetrics),
		random:            rand.New(rand.NewSource(time.Now().UnixNano())),
		baseManager:       NewBaseMetricsManager(),
		proxyUsageHistory: make(map[string]*ProxyUsageInfo),
		domainProxyMap:    make(map[string][]string),
		adaptiveWeights: &AdaptiveWeights{
			QualityWeight:        0.4,  // 质量权重
			LoadBalanceWeight:    0.3,  // 负载均衡权重
			DomainAffinityWeight: 0.2,  // 域名亲和性权重
			FreshnessWeight:      0.1,  // 新鲜度权重
			LastUpdated:          time.Now(),
		},
		decisionHistory: make([]ProxyDecision, 0),
		maxHistorySize:  100, // 保留最近100个决策记录
	}
}

// SelectProxy 智能选择代理
// 根据上下文、历史性能、负载均衡、域名亲和性等多维度智能决定代理选择
func (s *SmartProxyStrategy) SelectProxy(ctx context.Context, proxies []string, domain string) (string, error) {
	if len(proxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理列表为空")
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	// 更新自适应权重
	s.updateAdaptiveWeights()

	// 分析上下文和决策需求
	decisionContext := s.analyzeDecisionContext(ctx, domain)

	// 如果不需要强制切换且当前代理表现良好，考虑继续使用
	if !decisionContext.ForceSwitchProxy && s.shouldKeepCurrentProxy(decisionContext) {
		if s.currentProxy != "" {
			for _, proxy := range proxies {
				if proxy == s.currentProxy {
					metrics := s.metrics[proxy]
					if s.baseManager.IsProxyAvailable(metrics) && s.isProxyPerformingWell(proxy, domain) {
						s.recordDecision(proxy, "继续使用表现良好的当前代理", domain, decisionContext.ActionType)
						return proxy, nil
					}
					break
				}
			}
		}
	}

	// 获取可用代理列表
	availableProxies := s.getAvailableProxies(proxies, decisionContext)
	if len(availableProxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "没有可用的代理")
	}

	// 使用增强的智能选择算法
	selectedProxy, reason := s.selectOptimalProxy(availableProxies, domain, decisionContext)

	// 更新状态和记录决策
	s.updateProxyUsage(selectedProxy)
	s.updateDomainProxyMapping(domain, selectedProxy)
	s.recordDecision(selectedProxy, reason, domain, decisionContext.ActionType)
	s.currentProxy = selectedProxy

	return selectedProxy, nil
}

// DecisionContext 决策上下文
type DecisionContext struct {
	ForceSwitchProxy bool   // 是否强制切换代理
	ActionType       string // 动作类型
	RetryCount       int    // 重试次数
	Priority         string // 优先级
	LoadBalanceMode  string // 负载均衡模式
}

// analyzeDecisionContext 分析决策上下文
func (s *SmartProxyStrategy) analyzeDecisionContext(ctx context.Context, domain string) *DecisionContext {
	decisionCtx := &DecisionContext{
		ForceSwitchProxy: false,
		ActionType:       "normal",
		RetryCount:       0,
		Priority:         "normal",
		LoadBalanceMode:  "smart",
	}

	// 检查上下文中的重试动作类型
	if retryActionType, ok := ctx.Value("retry_action_type").(string); ok {
		decisionCtx.ActionType = retryActionType
		s.lastActionType = retryActionType
		// 如果是使用新IP重试，则需要切换代理
		decisionCtx.ForceSwitchProxy = (retryActionType == "retry")
	}

	// 检查重试次数
	if retryCount, ok := ctx.Value("retry_count").(int); ok {
		decisionCtx.RetryCount = retryCount
		// 重试次数越多，越倾向于切换代理
		if retryCount > 2 {
			decisionCtx.ForceSwitchProxy = true
		}
	}

	// 检查优先级
	if priority, ok := ctx.Value("priority").(string); ok {
		decisionCtx.Priority = priority
	}

	// 检查是否标记需要重试
	if actionRequiresRetry, ok := ctx.Value("action_requires_retry").(bool); ok && actionRequiresRetry {
		if s.lastActionType == "retry" {
			decisionCtx.ForceSwitchProxy = true
		}
	}

	return decisionCtx
}

// shouldKeepCurrentProxy 判断是否应该保持当前代理
func (s *SmartProxyStrategy) shouldKeepCurrentProxy(decisionCtx *DecisionContext) bool {
	if s.currentProxy == "" {
		return false
	}

	// 如果强制切换，不保持当前代理
	if decisionCtx.ForceSwitchProxy {
		return false
	}

	// 检查当前代理的连续使用情况
	if usageInfo, exists := s.proxyUsageHistory[s.currentProxy]; exists {
		// 如果连续使用次数过多，考虑切换以实现负载均衡
		if usageInfo.ContinuousUse > 10 {
			return false
		}

		// 如果最近有失败记录，考虑切换
		if usageInfo.RecentFailures > 2 && time.Since(usageInfo.LastFailureTime) < 5*time.Minute {
			return false
		}
	}

	return true
}

// shouldSwitchProxy 检查是否应该切换代理（保持向后兼容）
func (s *SmartProxyStrategy) shouldSwitchProxy(ctx context.Context) bool {
	// 检查上下文中的重试动作类型
	if retryActionType, ok := ctx.Value("retry_action_type").(string); ok {
		s.lastActionType = retryActionType
		// 如果是使用新IP重试，则需要切换代理
		return retryActionType == "retry"
	}

	// 检查是否标记需要重试
	if actionRequiresRetry, ok := ctx.Value("action_requires_retry").(bool); ok && actionRequiresRetry {
		// 如果上次动作类型是retry，则需要切换
		return s.lastActionType == "retry"
	}

	return false
}

// getAvailableProxies 获取可用代理列表
func (s *SmartProxyStrategy) getAvailableProxies(proxies []string, decisionCtx *DecisionContext) []string {
	availableProxies := make([]string, 0, len(proxies))

	for _, proxy := range proxies {
		// 如果需要强制切换，排除当前代理
		if decisionCtx.ForceSwitchProxy && proxy == s.currentProxy {
			continue
		}

		metrics := s.metrics[proxy]
		if s.baseManager.IsProxyAvailable(metrics) {
			availableProxies = append(availableProxies, proxy)
		}
	}

	return availableProxies
}

// selectOptimalProxy 选择最优代理
func (s *SmartProxyStrategy) selectOptimalProxy(proxies []string, domain string, decisionCtx *DecisionContext) (string, string) {
	if len(proxies) == 1 {
		return proxies[0], "唯一可用代理"
	}

	// 计算每个代理的综合评分
	proxyScores := make([]proxyScore, 0, len(proxies))

	for _, proxy := range proxies {
		score, reason := s.calculateComprehensiveScore(proxy, domain, decisionCtx)
		proxyScores = append(proxyScores, proxyScore{
			proxy:  proxy,
			score:  score,
			reason: reason,
		})
	}

	// 按评分降序排序
	sort.Slice(proxyScores, func(i, j int) bool {
		return proxyScores[i].score > proxyScores[j].score
	})

	// 根据优先级和模式选择代理
	return s.selectFromTopProxies(proxyScores, decisionCtx)
}

// calculateComprehensiveScore 计算综合评分
func (s *SmartProxyStrategy) calculateComprehensiveScore(proxy, domain string, decisionCtx *DecisionContext) (float64, string) {
	weights := s.adaptiveWeights

	// 质量评分
	qualityScore := s.getProxyScore(proxy, domain)

	// 负载均衡评分
	loadBalanceScore := s.calculateLoadBalanceScore(proxy)

	// 域名亲和性评分
	domainAffinityScore := s.calculateDomainAffinityScore(proxy, domain)

	// 新鲜度评分
	freshnessScore := s.calculateFreshnessScore(proxy)

	// 综合评分
	totalScore := qualityScore*weights.QualityWeight +
		loadBalanceScore*weights.LoadBalanceWeight +
		domainAffinityScore*weights.DomainAffinityWeight +
		freshnessScore*weights.FreshnessWeight

	reason := s.generateScoreReason(qualityScore, loadBalanceScore, domainAffinityScore, freshnessScore, weights)

	return totalScore, reason
}

// selectFromTopProxies 从顶级代理中选择
func (s *SmartProxyStrategy) selectFromTopProxies(proxyScores []proxyScore, decisionCtx *DecisionContext) (string, string) {
	// 根据优先级决定选择策略
	switch decisionCtx.Priority {
	case "high":
		// 高优先级：选择最佳代理
		return proxyScores[0].proxy, "高优先级-最佳质量: " + proxyScores[0].reason
	case "low":
		// 低优先级：负载均衡优先
		return s.selectForLoadBalance(proxyScores), "低优先级-负载均衡"
	default:
		// 正常优先级：智能选择
		return s.selectSmartChoice(proxyScores), "智能选择-综合最优"
	}
}

// selectForLoadBalance 为负载均衡选择代理
func (s *SmartProxyStrategy) selectForLoadBalance(proxyScores []proxyScore) string {
	// 选择使用次数最少的高质量代理
	minUsage := int64(math.MaxInt64)
	selectedProxy := proxyScores[0].proxy

	// 只考虑前50%的高质量代理
	topCount := int(math.Max(1, float64(len(proxyScores))*0.5))

	for i := 0; i < topCount && i < len(proxyScores); i++ {
		proxy := proxyScores[i].proxy
		if usageInfo, exists := s.proxyUsageHistory[proxy]; exists {
			if usageInfo.UsageCount < minUsage {
				minUsage = usageInfo.UsageCount
				selectedProxy = proxy
			}
		} else {
			// 新代理优先
			return proxy
		}
	}

	return selectedProxy
}

// selectSmartChoice 智能选择
func (s *SmartProxyStrategy) selectSmartChoice(proxyScores []proxyScore) string {
	// 使用加权随机选择前30%的高质量代理
	topCount := int(math.Max(1, float64(len(proxyScores))*constants.TopProxyRatio))
	topProxies := proxyScores[:topCount]

	// 加权随机选择
	totalWeight := 0.0
	for _, ps := range topProxies {
		totalWeight += ps.score
	}

	if totalWeight == 0 {
		return topProxies[0].proxy
	}

	randomValue := s.random.Float64() * totalWeight
	currentWeight := 0.0

	for _, ps := range topProxies {
		currentWeight += ps.score
		if currentWeight >= randomValue {
			return ps.proxy
		}
	}

	return topProxies[0].proxy
}

// calculateLoadBalanceScore 计算负载均衡评分
func (s *SmartProxyStrategy) calculateLoadBalanceScore(proxy string) float64 {
	if len(s.proxyUsageHistory) <= 1 {
		return 100.0 // 只有一个代理时负载均衡评分满分
	}

	// 计算总使用次数
	var totalUsage int64
	for _, usage := range s.proxyUsageHistory {
		totalUsage += usage.UsageCount
	}

	if totalUsage == 0 {
		return 100.0 // 没有使用记录时给满分
	}

	// 计算理想的平均使用次数
	idealUsage := float64(totalUsage) / float64(len(s.proxyUsageHistory))

	// 获取当前代理的使用次数
	currentUsage := float64(0)
	if usage, exists := s.proxyUsageHistory[proxy]; exists {
		currentUsage = float64(usage.UsageCount)
	}

	// 计算负载均衡评分：使用次数越接近理想值评分越高
	usageRatio := currentUsage / idealUsage
	if usageRatio <= 0.5 {
		return 100.0 // 使用不足，鼓励使用
	} else if usageRatio <= 1.0 {
		return 100.0 - (usageRatio-0.5)*40 // 从100分递减到80分
	} else if usageRatio <= 2.0 {
		return 80.0 - (usageRatio-1.0)*60 // 从80分递减到20分
	} else {
		return 20.0 // 过度使用，降低评分
	}
}

// calculateDomainAffinityScore 计算域名亲和性评分
func (s *SmartProxyStrategy) calculateDomainAffinityScore(proxy, domain string) float64 {
	if domain == "" {
		return 50.0 // 没有域名信息时给中等评分
	}

	// 检查该代理是否曾经成功处理过该域名
	if proxies, exists := s.domainProxyMap[domain]; exists {
		for _, p := range proxies {
			if p == proxy {
				// 该代理曾经处理过该域名，给高分
				return 100.0
			}
		}
	}

	// 检查代理的域名级别统计
	if metrics, exists := s.metrics[proxy]; exists {
		if domainStats, exists := metrics.DomainStats[domain]; exists {
			// 根据该域名的成功率计算亲和性
			totalRequests := domainStats.SuccessCount + domainStats.FailureCount
			if totalRequests > 0 {
				successRate := float64(domainStats.SuccessCount) / float64(totalRequests)
				return successRate * 100
			}
		}
	}

	// 没有该域名的历史记录，给中等评分
	return 50.0
}

// calculateFreshnessScore 计算新鲜度评分
func (s *SmartProxyStrategy) calculateFreshnessScore(proxy string) float64 {
	usage, exists := s.proxyUsageHistory[proxy]
	if !exists {
		return 100.0 // 新代理给满分
	}

	// 基于最后使用时间计算新鲜度
	timeSinceLastUse := time.Since(usage.LastUsed)

	if timeSinceLastUse < 1*time.Minute {
		return 20.0 // 刚刚使用过，新鲜度低
	} else if timeSinceLastUse < 5*time.Minute {
		return 40.0 // 最近使用过
	} else if timeSinceLastUse < 30*time.Minute {
		return 70.0 // 较新鲜
	} else if timeSinceLastUse < 2*time.Hour {
		return 90.0 // 新鲜
	} else {
		return 100.0 // 很久没用，最新鲜
	}
}

// updateAdaptiveWeights 更新自适应权重
func (s *SmartProxyStrategy) updateAdaptiveWeights() {
	// 每10分钟更新一次权重
	if time.Since(s.adaptiveWeights.LastUpdated) < 10*time.Minute {
		return
	}

	// 分析最近的决策效果
	recentDecisions := s.getRecentDecisions(50) // 分析最近50个决策
	if len(recentDecisions) < 10 {
		return // 决策样本不足
	}

	// 计算各种策略的成功率
	qualityBasedSuccess := s.calculateStrategySuccessRate(recentDecisions, "质量优先")
	loadBalanceSuccess := s.calculateStrategySuccessRate(recentDecisions, "负载均衡")
	domainAffinitySuccess := s.calculateStrategySuccessRate(recentDecisions, "域名亲和性")

	// 根据成功率调整权重
	totalSuccess := qualityBasedSuccess + loadBalanceSuccess + domainAffinitySuccess
	if totalSuccess > 0 {
		s.adaptiveWeights.QualityWeight = qualityBasedSuccess / totalSuccess * 0.8 + 0.1
		s.adaptiveWeights.LoadBalanceWeight = loadBalanceSuccess / totalSuccess * 0.6 + 0.1
		s.adaptiveWeights.DomainAffinityWeight = domainAffinitySuccess / totalSuccess * 0.4 + 0.1
		s.adaptiveWeights.FreshnessWeight = 0.1 // 新鲜度权重保持较低
	}

	// 确保权重总和为1
	s.normalizeWeights()
	s.adaptiveWeights.LastUpdated = time.Now()
}

// getRecentDecisions 获取最近的决策记录
func (s *SmartProxyStrategy) getRecentDecisions(count int) []ProxyDecision {
	if len(s.decisionHistory) <= count {
		return s.decisionHistory
	}
	return s.decisionHistory[len(s.decisionHistory)-count:]
}

// calculateStrategySuccessRate 计算特定策略的成功率
func (s *SmartProxyStrategy) calculateStrategySuccessRate(decisions []ProxyDecision, strategy string) float64 {
	var total, success int
	for _, decision := range decisions {
		if decision.Reason == strategy {
			total++
			if decision.Success {
				success++
			}
		}
	}

	if total == 0 {
		return 0.5 // 没有数据时返回中等成功率
	}

	return float64(success) / float64(total)
}

// normalizeWeights 标准化权重
func (s *SmartProxyStrategy) normalizeWeights() {
	total := s.adaptiveWeights.QualityWeight +
		s.adaptiveWeights.LoadBalanceWeight +
		s.adaptiveWeights.DomainAffinityWeight +
		s.adaptiveWeights.FreshnessWeight

	if total > 0 {
		s.adaptiveWeights.QualityWeight /= total
		s.adaptiveWeights.LoadBalanceWeight /= total
		s.adaptiveWeights.DomainAffinityWeight /= total
		s.adaptiveWeights.FreshnessWeight /= total
	}
}

// generateScoreReason 生成评分原因说明
func (s *SmartProxyStrategy) generateScoreReason(quality, loadBalance, domainAffinity, freshness float64, weights *AdaptiveWeights) string {
	maxScore := quality
	reason := "质量优先"

	if loadBalance*weights.LoadBalanceWeight > maxScore*weights.QualityWeight {
		maxScore = loadBalance
		reason = "负载均衡"
	}

	if domainAffinity*weights.DomainAffinityWeight > maxScore*weights.QualityWeight {
		reason = "域名亲和性"
	}

	return reason
}

// isProxyPerformingWell 检查代理是否表现良好
func (s *SmartProxyStrategy) isProxyPerformingWell(proxy, domain string) bool {
	metrics, exists := s.metrics[proxy]
	if !exists {
		return true // 新代理默认认为表现良好
	}

	// 检查总体表现
	if metrics.TotalRequests > constants.MinRequestsForStats {
		successRate := float64(metrics.SuccessCount) / float64(metrics.TotalRequests)
		if successRate < 0.7 { // 成功率低于70%认为表现不佳
			return false
		}
	}

	// 检查域名级别表现
	if domain != "" {
		if domainStats, exists := metrics.DomainStats[domain]; exists {
			totalRequests := domainStats.SuccessCount + domainStats.FailureCount
			if totalRequests > 5 {
				successRate := float64(domainStats.SuccessCount) / float64(totalRequests)
				if successRate < 0.6 { // 域名级别成功率低于60%
					return false
				}
			}
		}
	}

	return true
}

// updateProxyUsage 更新代理使用信息
func (s *SmartProxyStrategy) updateProxyUsage(proxy string) {
	usage, exists := s.proxyUsageHistory[proxy]
	if !exists {
		usage = &ProxyUsageInfo{
			LastUsed:        time.Now(),
			UsageCount:      0,
			ContinuousUse:   0,
			AvgSessionTime:  0,
			RecentFailures:  0,
			LastFailureTime: time.Time{},
		}
		s.proxyUsageHistory[proxy] = usage
	}

	// 更新使用信息
	usage.LastUsed = time.Now()
	usage.UsageCount++

	// 更新连续使用次数
	if s.currentProxy == proxy {
		usage.ContinuousUse++
	} else {
		usage.ContinuousUse = 1
	}
}

// updateDomainProxyMapping 更新域名代理映射
func (s *SmartProxyStrategy) updateDomainProxyMapping(domain, proxy string) {
	if domain == "" {
		return
	}

	proxies, exists := s.domainProxyMap[domain]
	if !exists {
		s.domainProxyMap[domain] = []string{proxy}
		return
	}

	// 检查代理是否已存在
	for _, p := range proxies {
		if p == proxy {
			return
		}
	}

	// 添加新代理，限制最多保存5个
	s.domainProxyMap[domain] = append(proxies, proxy)
	if len(s.domainProxyMap[domain]) > 5 {
		s.domainProxyMap[domain] = s.domainProxyMap[domain][1:]
	}
}

// recordDecision 记录决策
func (s *SmartProxyStrategy) recordDecision(proxy, reason, domain, actionType string) {
	decision := ProxyDecision{
		Timestamp:     time.Now(),
		SelectedProxy: proxy,
		Reason:        reason,
		Domain:        domain,
		ActionType:    actionType,
		Success:       false, // 初始为false，后续通过UpdateProxyMetrics更新
	}

	s.decisionHistory = append(s.decisionHistory, decision)

	// 限制历史记录大小
	if len(s.decisionHistory) > s.maxHistorySize {
		s.decisionHistory = s.decisionHistory[1:]
	}
}

// selectBestProxy 选择最佳代理（基于质量评分）- 保持向后兼容
func (s *SmartProxyStrategy) selectBestProxy(proxies []string, domain string) string {
	if len(proxies) == 1 {
		return proxies[0]
	}

	// 按质量评分排序代理
	type proxyScore struct {
		proxy string
		score float64
	}

	proxyScores := make([]proxyScore, 0, len(proxies))
	for _, proxy := range proxies {
		score := s.getProxyScore(proxy, domain)
		proxyScores = append(proxyScores, proxyScore{proxy: proxy, score: score})
	}

	// 按评分降序排序
	sort.Slice(proxyScores, func(i, j int) bool {
		return proxyScores[i].score > proxyScores[j].score
	})

	// 使用加权随机选择前30%的高质量代理
	topCount := int(math.Max(1, float64(len(proxyScores))*constants.TopProxyRatio))
	topProxies := proxyScores[:topCount]

	// 加权随机选择
	totalWeight := 0.0
	for _, ps := range topProxies {
		totalWeight += ps.score
	}

	if totalWeight == 0 {
		return topProxies[0].proxy
	}

	randomValue := s.random.Float64() * totalWeight
	currentWeight := 0.0

	for _, ps := range topProxies {
		currentWeight += ps.score
		if currentWeight >= randomValue {
			return ps.proxy
		}
	}

	return topProxies[0].proxy
}

// UpdateProxyMetrics 更新代理指标
func (s *SmartProxyStrategy) UpdateProxyMetrics(proxy string, success bool, responseTime time.Duration, domain string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	metrics, exists := s.metrics[proxy]
	if !exists {
		metrics = &ProxyMetrics{
			ProxyURL:    proxy,
			DomainStats: make(map[string]*DomainPerformance),
		}
		s.metrics[proxy] = metrics
	}

	// 使用基础指标管理器更新指标
	s.baseManager.UpdateProxyMetrics(metrics, success, responseTime, domain)

	// 更新使用历史
	s.updateUsageHistory(proxy, success)

	// 更新决策历史中的成功状态
	s.updateDecisionHistory(proxy, success)
}

// updateUsageHistory 更新使用历史
func (s *SmartProxyStrategy) updateUsageHistory(proxy string, success bool) {
	usage, exists := s.proxyUsageHistory[proxy]
	if !exists {
		return
	}

	if !success {
		usage.RecentFailures++
		usage.LastFailureTime = time.Now()

		// 重置连续使用次数
		if usage.RecentFailures > 3 {
			usage.ContinuousUse = 0
		}
	} else {
		// 成功时减少最近失败次数
		if usage.RecentFailures > 0 {
			usage.RecentFailures--
		}
	}
}

// updateDecisionHistory 更新决策历史
func (s *SmartProxyStrategy) updateDecisionHistory(proxy string, success bool) {
	// 更新最近的决策记录
	for i := len(s.decisionHistory) - 1; i >= 0; i-- {
		decision := &s.decisionHistory[i]
		if decision.SelectedProxy == proxy && !decision.Success {
			decision.Success = success
			break
		}
	}
}

// GetName 获取策略名称
func (s *SmartProxyStrategy) GetName() string {
	return "smart"
}

// getProxyScore 获取代理评分（考虑域名特定性能）
func (s *SmartProxyStrategy) getProxyScore(proxy, domain string) float64 {
	metrics, exists := s.metrics[proxy]
	if !exists {
		return constants.DefaultQualityScore // 新代理默认评分
	}

	// 如果有域名特定统计，优先使用
	if domain != "" {
		if domainStats, exists := metrics.DomainStats[domain]; exists {
			return domainStats.QualityScore
		}
	}

	return metrics.QualityScore
}
