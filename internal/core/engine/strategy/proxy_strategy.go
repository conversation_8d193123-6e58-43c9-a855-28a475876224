package strategy

import (
	"context"
	"fmt"
	"time"

	flexerrors "flexproxy/common/errors"
)

// ProxySelectionStrategy 代理选择策略接口
type ProxySelectionStrategy interface {
	// SelectProxy 根据策略选择代理
	SelectProxy(ctx context.Context, proxies []string, domain string) (string, error)
	// GetName 获取策略名称
	GetName() string
	// UpdateProxyMetrics 更新代理指标
	UpdateProxyMetrics(proxy string, success bool, responseTime time.Duration, domain string)
}

// LoadBalancingStrategy 负载均衡策略接口
type LoadBalancingStrategy interface {
	// Balance 执行负载均衡
	Balance(ctx context.Context, targets []Target) (Target, error)
	// GetName 获取策略名称
	GetName() string
	// UpdateTargetHealth 更新目标健康状态
	UpdateTargetHealth(target Target, healthy bool)
}

// Target 负载均衡目标
type Target struct {
	ID       string                 // 目标ID
	Address  string                 // 目标地址
	Weight   int                    // 权重
	Healthy  bool                   // 健康状态
	Metadata map[string]interface{} // 元数据
}

// ProxyMetrics 代理指标
type ProxyMetrics struct {
	ProxyURL        string                        // 代理URL
	SuccessCount    int64                         // 成功次数
	FailureCount    int64                         // 失败次数
	TotalRequests   int64                         // 总请求数
	AvgResponseTime time.Duration                 // 平均响应时间
	LastUsed        time.Time                     // 最后使用时间
	QualityScore    float64                       // 质量评分
	DomainStats     map[string]*DomainPerformance // 域名统计
}

// DomainPerformance 域名性能统计
type DomainPerformance struct {
	Domain          string        // 域名
	SuccessCount    int64         // 成功次数
	FailureCount    int64         // 失败次数
	AvgResponseTime time.Duration // 平均响应时间
	LastUsed        time.Time     // 最后使用时间
	QualityScore    float64       // 质量评分
}

// TargetMetrics 目标服务器指标
type TargetMetrics struct {
	TargetID        string        // 目标ID
	TotalRequests   int64         // 总请求数
	SuccessCount    int64         // 成功次数
	FailureCount    int64         // 失败次数
	AvgResponseTime time.Duration // 平均响应时间
	LastUsed        time.Time     // 最后使用时间
	Healthy         bool          // 健康状态
	Weight          int           // 权重
}

// StrategyManager 策略管理器接口
type StrategyManager interface {
	// RegisterProxyStrategy 注册代理选择策略
	RegisterProxyStrategy(name string, strategy ProxySelectionStrategy) error
	// RegisterLoadBalancingStrategy 注册负载均衡策略
	RegisterLoadBalancingStrategy(name string, strategy LoadBalancingStrategy) error
	// GetProxyStrategy 获取代理选择策略
	GetProxyStrategy(name string) (ProxySelectionStrategy, error)
	// GetLoadBalancingStrategy 获取负载均衡策略
	GetLoadBalancingStrategy(name string) (LoadBalancingStrategy, error)
	// ListProxyStrategies 列出所有代理选择策略
	ListProxyStrategies() []string
	// ListLoadBalancingStrategies 列出所有负载均衡策略
	ListLoadBalancingStrategies() []string
}

// strategyManagerImpl 策略管理器实现
type strategyManagerImpl struct {
	proxyStrategies         map[string]ProxySelectionStrategy
	loadBalancingStrategies map[string]LoadBalancingStrategy
}

// NewStrategyManager 创建新的策略管理器
func NewStrategyManager() StrategyManager {
	sm := &strategyManagerImpl{
		proxyStrategies:         make(map[string]ProxySelectionStrategy),
		loadBalancingStrategies: make(map[string]LoadBalancingStrategy),
	}

	// 注册默认策略
	sm.registerDefaultStrategies()

	return sm
}

// registerDefaultStrategies 注册默认策略
func (sm *strategyManagerImpl) registerDefaultStrategies() {
	// 注册代理选择策略
	sm.proxyStrategies["random"] = NewRandomProxyStrategy()
	sm.proxyStrategies["sequential"] = NewSequentialProxyStrategy()
	sm.proxyStrategies["quality"] = NewQualityBasedProxyStrategy()
	sm.proxyStrategies["smart"] = NewSmartProxyStrategy()

	// 注册负载均衡策略
	sm.loadBalancingStrategies["round_robin"] = NewRoundRobinLoadBalancer()
	sm.loadBalancingStrategies["weighted_round_robin"] = NewWeightedRoundRobinLoadBalancer()
	sm.loadBalancingStrategies["least_connections"] = NewLeastConnectionsLoadBalancer()
	sm.loadBalancingStrategies["response_time"] = NewResponseTimeLoadBalancer()
}

// RegisterProxyStrategy 注册代理选择策略
func (sm *strategyManagerImpl) RegisterProxyStrategy(name string, strategy ProxySelectionStrategy) error {
	sm.proxyStrategies[name] = strategy
	return nil
}

// RegisterLoadBalancingStrategy 注册负载均衡策略
func (sm *strategyManagerImpl) RegisterLoadBalancingStrategy(name string, strategy LoadBalancingStrategy) error {
	sm.loadBalancingStrategies[name] = strategy
	return nil
}

// GetProxyStrategy 获取代理选择策略
func (sm *strategyManagerImpl) GetProxyStrategy(name string) (ProxySelectionStrategy, error) {
	if strategy, exists := sm.proxyStrategies[name]; exists {
		return strategy, nil
	}
	return nil, flexerrors.NewErrorWithDetails(flexerrors.ErrTypeStrategy, flexerrors.ErrCodeStrategyNotFound, "代理选择策略未找到", fmt.Sprintf("策略名称: %s", name))
}

// GetLoadBalancingStrategy 获取负载均衡策略
func (sm *strategyManagerImpl) GetLoadBalancingStrategy(name string) (LoadBalancingStrategy, error) {
	if strategy, exists := sm.loadBalancingStrategies[name]; exists {
		return strategy, nil
	}
	return nil, flexerrors.NewErrorWithDetails(flexerrors.ErrTypeStrategy, flexerrors.ErrCodeStrategyNotFound, "负载均衡策略未找到", fmt.Sprintf("策略名称: %s", name))
}

// ListProxyStrategies 列出所有代理选择策略
func (sm *strategyManagerImpl) ListProxyStrategies() []string {
	var strategies []string
	for name := range sm.proxyStrategies {
		strategies = append(strategies, name)
	}
	return strategies
}

// ListLoadBalancingStrategies 列出所有负载均衡策略
func (sm *strategyManagerImpl) ListLoadBalancingStrategies() []string {
	var strategies []string
	for name := range sm.loadBalancingStrategies {
		strategies = append(strategies, name)
	}
	return strategies
}
