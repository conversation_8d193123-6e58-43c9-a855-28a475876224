package encoding

import (
	"bytes"
	"fmt"
	"io"
	"strings"
	"unicode/utf8"

	"github.com/saintfish/chardet"
	"golang.org/x/text/encoding"
	"golang.org/x/text/encoding/charmap"
	"golang.org/x/text/encoding/japanese"
	"golang.org/x/text/encoding/korean"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/encoding/traditionalchinese"
	"golang.org/x/text/encoding/unicode"
	"golang.org/x/text/transform"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
)

// CharsetConverter 字符编码转换器
type CharsetConverter struct {
	supportedEncodings map[string]encoding.Encoding
	detector          *chardet.Detector // 专业的字符编码检测器
}

// CharsetDetectionResult 字符编码检测结果
type CharsetDetectionResult struct {
	Charset    string                    `json:"charset"`    // 检测到的字符编码
	Confidence int                       `json:"confidence"` // 置信度 (0-100)
	Language   string                    `json:"language"`   // 检测到的语言
	HasBOM     bool                      `json:"has_bom"`    // 是否包含BOM标记
	Candidates []CharsetDetectionCandidate `json:"candidates"` // 候选编码列表
}

// CharsetDetectionCandidate 候选编码信息
type CharsetDetectionCandidate struct {
	Charset    string `json:"charset"`    // 编码名称
	Confidence int    `json:"confidence"` // 置信度
	Language   string `json:"language"`   // 语言
}

// NewCharsetConverter 创建新的字符编码转换器
func NewCharsetConverter() *CharsetConverter {
	converter := &CharsetConverter{
		supportedEncodings: make(map[string]encoding.Encoding),
		detector:          chardet.NewTextDetector(), // 初始化专业编码检测器
	}
	converter.initializeSupportedEncodings()
	return converter
}

// initializeSupportedEncodings 初始化支持的字符编码
func (cc *CharsetConverter) initializeSupportedEncodings() {
	// UTF编码系列
	cc.supportedEncodings["utf-8"] = unicode.UTF8
	cc.supportedEncodings["utf-16"] = unicode.UTF16(unicode.BigEndian, unicode.UseBOM)
	cc.supportedEncodings["utf-16be"] = unicode.UTF16(unicode.BigEndian, unicode.IgnoreBOM)
	cc.supportedEncodings["utf-16le"] = unicode.UTF16(unicode.LittleEndian, unicode.IgnoreBOM)
	// 注意：golang.org/x/text/encoding/unicode 包中没有UTF32，我们暂时不支持UTF-32
	// cc.supportedEncodings["utf-32"] = unicode.UTF32(unicode.BigEndian, unicode.UseBOM)
	// cc.supportedEncodings["utf-32be"] = unicode.UTF32(unicode.BigEndian, unicode.IgnoreBOM)
	// cc.supportedEncodings["utf-32le"] = unicode.UTF32(unicode.LittleEndian, unicode.IgnoreBOM)

	// 中文编码系列
	cc.supportedEncodings["gbk"] = simplifiedchinese.GBK
	cc.supportedEncodings["gb2312"] = simplifiedchinese.HZGB2312
	cc.supportedEncodings["gb18030"] = simplifiedchinese.GB18030
	cc.supportedEncodings["big5"] = traditionalchinese.Big5

	// 日文编码系列
	cc.supportedEncodings["shift_jis"] = japanese.ShiftJIS
	cc.supportedEncodings["euc-jp"] = japanese.EUCJP
	cc.supportedEncodings["iso-2022-jp"] = japanese.ISO2022JP

	// 韩文编码系列
	cc.supportedEncodings["euc-kr"] = korean.EUCKR

	// 西欧编码系列
	cc.supportedEncodings["iso-8859-1"] = charmap.ISO8859_1
	cc.supportedEncodings["iso-8859-2"] = charmap.ISO8859_2
	cc.supportedEncodings["iso-8859-3"] = charmap.ISO8859_3
	cc.supportedEncodings["iso-8859-4"] = charmap.ISO8859_4
	cc.supportedEncodings["iso-8859-5"] = charmap.ISO8859_5
	cc.supportedEncodings["iso-8859-6"] = charmap.ISO8859_6
	cc.supportedEncodings["iso-8859-7"] = charmap.ISO8859_7
	cc.supportedEncodings["iso-8859-8"] = charmap.ISO8859_8
	cc.supportedEncodings["iso-8859-9"] = charmap.ISO8859_9
	cc.supportedEncodings["iso-8859-10"] = charmap.ISO8859_10
	cc.supportedEncodings["iso-8859-13"] = charmap.ISO8859_13
	cc.supportedEncodings["iso-8859-14"] = charmap.ISO8859_14
	cc.supportedEncodings["iso-8859-15"] = charmap.ISO8859_15
	cc.supportedEncodings["iso-8859-16"] = charmap.ISO8859_16

	// Windows编码系列
	cc.supportedEncodings["windows-1250"] = charmap.Windows1250
	cc.supportedEncodings["windows-1251"] = charmap.Windows1251
	cc.supportedEncodings["windows-1252"] = charmap.Windows1252
	cc.supportedEncodings["windows-1253"] = charmap.Windows1253
	cc.supportedEncodings["windows-1254"] = charmap.Windows1254
	cc.supportedEncodings["windows-1255"] = charmap.Windows1255
	cc.supportedEncodings["windows-1256"] = charmap.Windows1256
	cc.supportedEncodings["windows-1257"] = charmap.Windows1257
	cc.supportedEncodings["windows-1258"] = charmap.Windows1258

	// 其他常用编码
	cc.supportedEncodings["koi8-r"] = charmap.KOI8R
	cc.supportedEncodings["koi8-u"] = charmap.KOI8U
}

// Convert 转换字符编码
func (cc *CharsetConverter) Convert(data []byte, fromCharset, toCharset string) ([]byte, error) {
	// 标准化编码名称
	fromCharset = cc.normalizeCharsetName(fromCharset)
	toCharset = cc.normalizeCharsetName(toCharset)

	// 如果编码相同，直接返回
	if fromCharset == toCharset {
		return data, nil
	}

	// 特殊处理UTF-8
	if fromCharset == "utf-8" && toCharset == "utf-8" {
		return data, nil
	}

	// 验证编码是否支持
	if !cc.IsSupported(fromCharset) {
		return nil, errors.NewError(errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf(constants.ErrMsgUnsupportedCharset, fromCharset))
	}
	if !cc.IsSupported(toCharset) {
		return nil, errors.NewError(errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf(constants.ErrMsgUnsupportedCharset, toCharset))
	}

	// 执行转换
	return cc.performConversion(data, fromCharset, toCharset)
}

// performConversion 执行实际的编码转换
func (cc *CharsetConverter) performConversion(data []byte, fromCharset, toCharset string) ([]byte, error) {
	// 如果源编码是UTF-8，直接编码到目标编码
	if fromCharset == "utf-8" {
		return cc.encodeFromUTF8(data, toCharset)
	}

	// 如果目标编码是UTF-8，直接从源编码解码
	if toCharset == "utf-8" {
		return cc.decodeToUTF8(data, fromCharset)
	}

	// 其他情况：先解码到UTF-8，再编码到目标编码
	utf8Data, err := cc.decodeToUTF8(data, fromCharset)
	if err != nil {
		return nil, err
	}

	return cc.encodeFromUTF8(utf8Data, toCharset)
}

// decodeToUTF8 从指定编码解码到UTF-8
func (cc *CharsetConverter) decodeToUTF8(data []byte, fromCharset string) ([]byte, error) {
	if fromCharset == "utf-8" {
		// 验证UTF-8有效性
		if !utf8.Valid(data) {
			return nil, errors.NewError(errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
				"无效的UTF-8数据")
		}
		return data, nil
	}

	enc, exists := cc.supportedEncodings[fromCharset]
	if !exists {
		return nil, errors.NewError(errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf(constants.ErrMsgUnsupportedCharset, fromCharset))
	}

	decoder := enc.NewDecoder()
	reader := transform.NewReader(bytes.NewReader(data), decoder)
	result, err := io.ReadAll(reader)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("从%s解码失败", fromCharset))
	}

	return result, nil
}

// encodeFromUTF8 从UTF-8编码到指定编码
func (cc *CharsetConverter) encodeFromUTF8(data []byte, toCharset string) ([]byte, error) {
	if toCharset == "utf-8" {
		return data, nil
	}

	enc, exists := cc.supportedEncodings[toCharset]
	if !exists {
		return nil, errors.NewError(errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf(constants.ErrMsgUnsupportedCharset, toCharset))
	}

	encoder := enc.NewEncoder()
	reader := transform.NewReader(bytes.NewReader(data), encoder)
	result, err := io.ReadAll(reader)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			fmt.Sprintf("编码到%s失败", toCharset))
	}

	return result, nil
}

// IsSupported 检查是否支持指定的字符编码
func (cc *CharsetConverter) IsSupported(charset string) bool {
	charset = cc.normalizeCharsetName(charset)
	_, exists := cc.supportedEncodings[charset]
	return exists
}

// normalizeCharsetName 标准化字符编码名称
func (cc *CharsetConverter) normalizeCharsetName(charset string) string {
	// 转换为小写
	charset = strings.ToLower(charset)
	
	// 移除常见的前缀和后缀
	charset = strings.TrimSpace(charset)
	
	// 处理常见的别名
	aliases := map[string]string{
		"utf8":        "utf-8",
		"utf16":       "utf-16",
		"utf32":       "utf-32",
		"ascii":       "iso-8859-1",
		"latin1":      "iso-8859-1",
		"latin-1":     "iso-8859-1",
		"cp1252":      "windows-1252",
		"cp1251":      "windows-1251",
		"cp1250":      "windows-1250",
		"sjis":        "shift_jis",
		"shiftjis":    "shift_jis",
		"eucjp":       "euc-jp",
		"euckr":       "euc-kr",
		"gb2312":      "gbk", // GB2312是GBK的子集
	}
	
	if normalized, exists := aliases[charset]; exists {
		return normalized
	}
	
	return charset
}

// GetSupportedCharsets 获取所有支持的字符编码列表
func (cc *CharsetConverter) GetSupportedCharsets() []string {
	charsets := make([]string, 0, len(cc.supportedEncodings))
	for charset := range cc.supportedEncodings {
		charsets = append(charsets, charset)
	}
	return charsets
}

// DetectCharset 检测数据的字符编码
// 使用专业的编码检测器进行准确检测，支持多种语言和编码格式
func (cc *CharsetConverter) DetectCharset(data []byte) string {
	// 空数据检查
	if len(data) == 0 {
		return constants.CharsetUTF8
	}

	// 首先进行快速BOM检测（性能优化）
	if bomCharset := cc.detectBOM(data); bomCharset != "" {
		return bomCharset
	}

	// 快速UTF-8有效性检查
	if utf8.Valid(data) {
		return constants.CharsetUTF8
	}

	// 使用专业的编码检测器进行深度分析
	result, err := cc.detector.DetectBest(data)
	if err != nil {
		// 检测失败时返回默认编码
		return constants.CharsetISO88591 // 兼容ASCII
	}

	// 标准化检测结果的编码名称
	detectedCharset := cc.normalizeCharsetName(result.Charset)

	// 对于亚洲语言编码，进行额外的验证和优化
	detectedCharset = cc.optimizeAsianCharsetDetection(data, detectedCharset, result.Confidence)

	// 验证检测结果是否为项目支持的编码
	if cc.IsSupported(detectedCharset) {
		return detectedCharset
	}

	// 如果检测到的编码不支持，返回默认编码
	return constants.CharsetISO88591
}

// detectBOM 检测字节顺序标记（BOM）
func (cc *CharsetConverter) detectBOM(data []byte) string {
	// UTF-8 BOM: EF BB BF
	if len(data) >= 3 && bytes.Equal(data[:3], []byte{0xEF, 0xBB, 0xBF}) {
		return constants.CharsetUTF8
	}

	// UTF-16 BOM
	if len(data) >= 2 {
		if bytes.Equal(data[:2], []byte{0xFF, 0xFE}) {
			return constants.CharsetUTF16LE
		}
		if bytes.Equal(data[:2], []byte{0xFE, 0xFF}) {
			return constants.CharsetUTF16BE
		}
	}

	// UTF-32 BOM
	if len(data) >= 4 {
		if bytes.Equal(data[:4], []byte{0xFF, 0xFE, 0x00, 0x00}) {
			return "utf-32le" // 项目暂不支持UTF-32，但保留检测
		}
		if bytes.Equal(data[:4], []byte{0x00, 0x00, 0xFE, 0xFF}) {
			return "utf-32be" // 项目暂不支持UTF-32，但保留检测
		}
	}

	return "" // 未检测到BOM
}

// DetectCharsetWithDetails 检测字符编码并返回详细信息
// 包含置信度和候选编码列表，用于需要详细检测结果的场景
func (cc *CharsetConverter) DetectCharsetWithDetails(data []byte) (*CharsetDetectionResult, error) {
	// 空数据检查
	if len(data) == 0 {
		return &CharsetDetectionResult{
			Charset:    constants.CharsetUTF8,
			Confidence: 100,
			Language:   "unknown",
		}, nil
	}

	// BOM检测
	if bomCharset := cc.detectBOM(data); bomCharset != "" {
		return &CharsetDetectionResult{
			Charset:    bomCharset,
			Confidence: 100,
			Language:   "unknown",
			HasBOM:     true,
		}, nil
	}

	// UTF-8有效性检查
	if utf8.Valid(data) {
		return &CharsetDetectionResult{
			Charset:    constants.CharsetUTF8,
			Confidence: 95, // 高置信度但不是100%，因为可能是ASCII
			Language:   "unknown",
		}, nil
	}

	// 使用专业检测器
	results, err := cc.detector.DetectAll(data)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPBodyInvalid,
			constants.ErrMsgCharsetDetectionFailed)
	}

	if len(results) == 0 {
		// 无检测结果时返回默认编码
		return &CharsetDetectionResult{
			Charset:    constants.CharsetISO88591,
			Confidence: 50, // 低置信度
			Language:   "unknown",
		}, nil
	}

	// 返回最佳检测结果
	best := results[0]
	return &CharsetDetectionResult{
		Charset:    cc.normalizeCharsetName(best.Charset),
		Confidence: int(best.Confidence),
		Language:   best.Language,
		Candidates: cc.convertCandidates(results),
	}, nil
}

// convertCandidates 转换候选编码列表
func (cc *CharsetConverter) convertCandidates(results []chardet.Result) []CharsetDetectionCandidate {
	candidates := make([]CharsetDetectionCandidate, 0, len(results))
	for _, result := range results {
		candidates = append(candidates, CharsetDetectionCandidate{
			Charset:    cc.normalizeCharsetName(result.Charset),
			Confidence: int(result.Confidence),
			Language:   result.Language,
		})
	}
	return candidates
}

// optimizeAsianCharsetDetection 优化亚洲语言字符编码检测
// 解决GBK、Big5、Shift_JIS等编码的混淆问题
func (cc *CharsetConverter) optimizeAsianCharsetDetection(data []byte, detected string, confidence int) string {
	// 如果置信度很高，直接返回
	if confidence > 80 {
		return detected
	}

	// 对于可能混淆的亚洲编码，进行额外验证
	switch detected {
	case constants.CharsetShiftJIS:
		// 检查是否更可能是GBK
		if cc.isLikelyGBK(data) {
			return constants.CharsetGBK
		}
	case constants.CharsetGBK:
		// 检查是否更可能是Big5
		if cc.isLikelyBig5(data) {
			return constants.CharsetBig5
		}
	}

	return detected
}

// isLikelyGBK 检查数据是否更可能是GBK编码
func (cc *CharsetConverter) isLikelyGBK(data []byte) bool {
	// GBK编码特征：
	// - 第一字节范围：0x81-0xFE
	// - 第二字节范围：0x40-0x7E, 0x80-0xFE
	gbkCount := 0
	totalBytes := 0

	for i := 0; i < len(data)-1; i++ {
		b1, b2 := data[i], data[i+1]

		// 检查是否符合GBK双字节字符模式
		if (b1 >= 0x81 && b1 <= 0xFE) &&
			((b2 >= 0x40 && b2 <= 0x7E) || (b2 >= 0x80 && b2 <= 0xFE)) {
			gbkCount++
			i++ // 跳过下一个字节
		}
		totalBytes++
	}

	// 如果GBK模式字符占比超过30%，认为是GBK
	return totalBytes > 0 && float64(gbkCount)/float64(totalBytes) > 0.3
}

// isLikelyBig5 检查数据是否更可能是Big5编码
func (cc *CharsetConverter) isLikelyBig5(data []byte) bool {
	// Big5编码特征：
	// - 第一字节范围：0xA1-0xFE
	// - 第二字节范围：0x40-0x7E, 0xA1-0xFE
	big5Count := 0
	totalBytes := 0

	for i := 0; i < len(data)-1; i++ {
		b1, b2 := data[i], data[i+1]

		// 检查是否符合Big5双字节字符模式
		if (b1 >= 0xA1 && b1 <= 0xFE) &&
			((b2 >= 0x40 && b2 <= 0x7E) || (b2 >= 0xA1 && b2 <= 0xFE)) {
			big5Count++
			i++ // 跳过下一个字节
		}
		totalBytes++
	}

	// 如果Big5模式字符占比超过30%，认为是Big5
	return totalBytes > 0 && float64(big5Count)/float64(totalBytes) > 0.3
}
