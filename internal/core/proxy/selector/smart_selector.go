package selector

import (
	"context"
	"math"
	"sort"
	"sync"
	"time"

	"flexproxy/common/errors"
	"flexproxy/common/logger"
)

// ProxyMetrics 代理指标
type ProxyMetrics struct {
	ProxyURL         string        `json:"proxy_url"`
	SuccessRate      float64       `json:"success_rate"`      // 成功率 (0-1)
	AvgResponseTime  time.Duration `json:"avg_response_time"` // 平均响应时间
	TotalRequests    int64         `json:"total_requests"`    // 总请求数
	SuccessRequests  int64         `json:"success_requests"`  // 成功请求数
	FailedRequests   int64         `json:"failed_requests"`   // 失败请求数
	LastUsed         time.Time     `json:"last_used"`         // 最后使用时间
	LastSuccess      time.Time     `json:"last_success"`      // 最后成功时间
	LastFailure      time.Time     `json:"last_failure"`      // 最后失败时间
	ConsecutiveFails int           `json:"consecutive_fails"` // 连续失败次数
	QualityScore     float64       `json:"quality_score"`     // 综合质量评分 (0-100)
	LoadFactor       float64       `json:"load_factor"`       // 负载因子 (0-1)
	DomainStats      map[string]*DomainMetrics `json:"domain_stats"` // 域名级别统计
}

// DomainMetrics 域名级别指标
type DomainMetrics struct {
	Domain          string        `json:"domain"`
	SuccessRate     float64       `json:"success_rate"`
	AvgResponseTime time.Duration `json:"avg_response_time"`
	TotalRequests   int64         `json:"total_requests"`
	SuccessRequests int64         `json:"success_requests"`
	LastUsed        time.Time     `json:"last_used"`
}

// SmartSelector 智能代理选择器
type SmartSelector struct {
	mu              sync.RWMutex
	metrics         map[string]*ProxyMetrics
	logger          logger.Logger
	config          *SmartSelectorConfig
	lastCleanup     time.Time
	selectionHistory []SelectionRecord
}

// SmartSelectorConfig 智能选择器配置
type SmartSelectorConfig struct {
	// 权重配置
	SuccessRateWeight    float64 `json:"success_rate_weight"`    // 成功率权重
	ResponseTimeWeight   float64 `json:"response_time_weight"`   // 响应时间权重
	LoadBalanceWeight    float64 `json:"load_balance_weight"`    // 负载均衡权重
	FreshnessWeight      float64 `json:"freshness_weight"`       // 新鲜度权重
	
	// 阈值配置
	MinSuccessRate       float64       `json:"min_success_rate"`       // 最小成功率阈值
	MaxResponseTime      time.Duration `json:"max_response_time"`      // 最大响应时间阈值
	MaxConsecutiveFails  int           `json:"max_consecutive_fails"`  // 最大连续失败次数
	
	// 学习配置
	LearningRate         float64       `json:"learning_rate"`          // 学习率
	AdaptiveWeights      bool          `json:"adaptive_weights"`       // 是否启用自适应权重
	HistorySize          int           `json:"history_size"`           // 历史记录大小
	
	// 清理配置
	CleanupInterval      time.Duration `json:"cleanup_interval"`       // 清理间隔
	MetricsRetention     time.Duration `json:"metrics_retention"`      // 指标保留时间
}

// SelectionRecord 选择记录
type SelectionRecord struct {
	Timestamp    time.Time `json:"timestamp"`
	SelectedProxy string   `json:"selected_proxy"`
	Domain       string    `json:"domain"`
	Reason       string    `json:"reason"`
	QualityScore float64   `json:"quality_score"`
	Success      bool      `json:"success"`
}

// NewSmartSelector 创建智能代理选择器
func NewSmartSelector(logger logger.Logger, config *SmartSelectorConfig) *SmartSelector {
	if config == nil {
		config = DefaultSmartSelectorConfig()
	}
	
	return &SmartSelector{
		metrics:          make(map[string]*ProxyMetrics),
		logger:           logger,
		config:           config,
		lastCleanup:      time.Now(),
		selectionHistory: make([]SelectionRecord, 0, config.HistorySize),
	}
}

// DefaultSmartSelectorConfig 默认配置
func DefaultSmartSelectorConfig() *SmartSelectorConfig {
	return &SmartSelectorConfig{
		SuccessRateWeight:   0.4,
		ResponseTimeWeight:  0.3,
		LoadBalanceWeight:   0.2,
		FreshnessWeight:     0.1,
		MinSuccessRate:      0.7,
		MaxResponseTime:     time.Second * 10,
		MaxConsecutiveFails: 5,
		LearningRate:        0.1,
		AdaptiveWeights:     true,
		HistorySize:         1000,
		CleanupInterval:     time.Hour,
		MetricsRetention:    time.Hour * 24,
	}
}

// SelectBestProxy 选择最佳代理
func (ss *SmartSelector) SelectBestProxy(ctx context.Context, availableProxies []string, domain string) (string, error) {
	if len(availableProxies) == 0 {
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "没有可用的代理")
	}
	
	if len(availableProxies) == 1 {
		return availableProxies[0], nil
	}
	
	ss.mu.RLock()
	defer ss.mu.RUnlock()
	
	// 执行定期清理
	ss.performCleanupIfNeeded()
	
	// 计算每个代理的综合评分
	proxyScores := ss.calculateProxyScores(availableProxies, domain)
	
	// 根据评分选择最佳代理
	selectedProxy, reason := ss.selectProxyByScore(proxyScores)
	
	// 记录选择历史
	ss.recordSelection(selectedProxy, domain, reason, proxyScores[selectedProxy])
	
	ss.logger.Debugf("智能代理选择完成: 代理=%s, 域名=%s, 评分=%.2f, 原因=%s",
		selectedProxy, domain, proxyScores[selectedProxy], reason)
	
	return selectedProxy, nil
}

// calculateProxyScores 计算代理评分
func (ss *SmartSelector) calculateProxyScores(proxies []string, domain string) map[string]float64 {
	scores := make(map[string]float64)
	
	for _, proxy := range proxies {
		metrics := ss.getOrCreateMetrics(proxy)
		score := ss.calculateComprehensiveScore(metrics, domain)
		scores[proxy] = score
	}
	
	return scores
}

// calculateComprehensiveScore 计算综合评分
func (ss *SmartSelector) calculateComprehensiveScore(metrics *ProxyMetrics, domain string) float64 {
	// 成功率评分 (0-100)
	successScore := metrics.SuccessRate * 100
	
	// 响应时间评分 (0-100，响应时间越短评分越高)
	responseScore := ss.calculateResponseTimeScore(metrics.AvgResponseTime)
	
	// 负载均衡评分 (0-100，负载越低评分越高)
	loadScore := (1.0 - metrics.LoadFactor) * 100
	
	// 新鲜度评分 (0-100，最近使用的代理评分稍低以实现负载均衡)
	freshnessScore := ss.calculateFreshnessScore(metrics.LastUsed)
	
	// 域名特定评分
	domainScore := ss.calculateDomainSpecificScore(metrics, domain)
	
	// 综合评分
	totalScore := successScore*ss.config.SuccessRateWeight +
		responseScore*ss.config.ResponseTimeWeight +
		loadScore*ss.config.LoadBalanceWeight +
		freshnessScore*ss.config.FreshnessWeight +
		domainScore*0.1 // 域名特定权重
	
	// 应用惩罚因子
	totalScore = ss.applyPenaltyFactors(totalScore, metrics)
	
	return math.Max(0, math.Min(100, totalScore))
}

// calculateResponseTimeScore 计算响应时间评分
func (ss *SmartSelector) calculateResponseTimeScore(responseTime time.Duration) float64 {
	if responseTime <= 0 {
		return 100 // 没有历史数据时给满分
	}
	
	// 使用对数函数，响应时间越短评分越高
	maxTime := float64(ss.config.MaxResponseTime.Milliseconds())
	currentTime := float64(responseTime.Milliseconds())
	
	if currentTime >= maxTime {
		return 0
	}
	
	// 对数评分函数
	score := 100 * (1 - math.Log(currentTime+1)/math.Log(maxTime+1))
	return math.Max(0, score)
}

// calculateFreshnessScore 计算新鲜度评分
func (ss *SmartSelector) calculateFreshnessScore(lastUsed time.Time) float64 {
	if lastUsed.IsZero() {
		return 100 // 从未使用过的代理给高分
	}
	
	timeSinceLastUse := time.Since(lastUsed)
	
	// 5分钟内使用过的代理评分较低，促进负载均衡
	if timeSinceLastUse < 5*time.Minute {
		return 50
	}
	
	// 超过5分钟的代理逐渐恢复评分
	hours := timeSinceLastUse.Hours()
	score := 50 + 50*(1-math.Exp(-hours/2)) // 指数恢复函数
	
	return math.Min(100, score)
}

// calculateDomainSpecificScore 计算域名特定评分
func (ss *SmartSelector) calculateDomainSpecificScore(metrics *ProxyMetrics, domain string) float64 {
	if domain == "" || metrics.DomainStats == nil {
		return 50 // 中性评分
	}
	
	domainMetrics, exists := metrics.DomainStats[domain]
	if !exists {
		return 60 // 新域名稍微加分
	}
	
	// 基于域名特定的成功率和响应时间
	domainSuccessScore := domainMetrics.SuccessRate * 100
	domainResponseScore := ss.calculateResponseTimeScore(domainMetrics.AvgResponseTime)
	
	return (domainSuccessScore + domainResponseScore) / 2
}

// applyPenaltyFactors 应用惩罚因子
func (ss *SmartSelector) applyPenaltyFactors(score float64, metrics *ProxyMetrics) float64 {
	// 连续失败惩罚
	if metrics.ConsecutiveFails > 0 {
		penalty := float64(metrics.ConsecutiveFails) * 10
		score -= penalty
	}
	
	// 成功率过低惩罚
	if metrics.SuccessRate < ss.config.MinSuccessRate {
		penalty := (ss.config.MinSuccessRate - metrics.SuccessRate) * 100
		score -= penalty
	}
	
	// 最近失败惩罚
	if !metrics.LastFailure.IsZero() && time.Since(metrics.LastFailure) < 5*time.Minute {
		score -= 20
	}
	
	return score
}

// selectProxyByScore 根据评分选择代理
func (ss *SmartSelector) selectProxyByScore(scores map[string]float64) (string, string) {
	if len(scores) == 0 {
		return "", "无可用代理"
	}
	
	// 按评分排序
	type proxyScore struct {
		proxy string
		score float64
	}
	
	var sortedProxies []proxyScore
	for proxy, score := range scores {
		sortedProxies = append(sortedProxies, proxyScore{proxy, score})
	}
	
	sort.Slice(sortedProxies, func(i, j int) bool {
		return sortedProxies[i].score > sortedProxies[j].score
	})
	
	// 选择最高评分的代理，但引入一定随机性避免过度集中
	topCount := len(sortedProxies)
	if topCount > 3 {
		topCount = 3 // 从前3名中选择
	}
	
	// 加权随机选择
	totalWeight := 0.0
	for i := 0; i < topCount; i++ {
		totalWeight += sortedProxies[i].score + 1 // +1避免零权重
	}
	
	if totalWeight <= 0 {
		return sortedProxies[0].proxy, "最高评分"
	}
	
	// 使用加权随机选择
	randomValue := ss.generateRandomFloat() * totalWeight
	currentWeight := 0.0
	
	for i := 0; i < topCount; i++ {
		currentWeight += sortedProxies[i].score + 1
		if randomValue <= currentWeight {
			reason := "加权随机选择"
			if i == 0 {
				reason = "最高评分"
			}
			return sortedProxies[i].proxy, reason
		}
	}
	
	return sortedProxies[0].proxy, "默认最高评分"
}

// UpdateMetrics 更新代理指标
func (ss *SmartSelector) UpdateMetrics(proxy string, domain string, success bool, responseTime time.Duration) {
	ss.mu.Lock()
	defer ss.mu.Unlock()
	
	metrics := ss.getOrCreateMetrics(proxy)
	
	// 更新基础指标
	metrics.TotalRequests++
	metrics.LastUsed = time.Now()
	
	if success {
		metrics.SuccessRequests++
		metrics.LastSuccess = time.Now()
		metrics.ConsecutiveFails = 0
		
		// 更新平均响应时间（指数移动平均）
		if metrics.AvgResponseTime == 0 {
			metrics.AvgResponseTime = responseTime
		} else {
			alpha := 0.3 // 平滑因子
			metrics.AvgResponseTime = time.Duration(
				float64(metrics.AvgResponseTime)*(1-alpha) + float64(responseTime)*alpha,
			)
		}
	} else {
		metrics.FailedRequests++
		metrics.LastFailure = time.Now()
		metrics.ConsecutiveFails++
	}
	
	// 重新计算成功率
	if metrics.TotalRequests > 0 {
		metrics.SuccessRate = float64(metrics.SuccessRequests) / float64(metrics.TotalRequests)
	}
	
	// 更新域名特定指标
	if domain != "" {
		ss.updateDomainMetrics(metrics, domain, success, responseTime)
	}
	
	// 重新计算质量评分
	metrics.QualityScore = ss.calculateComprehensiveScore(metrics, domain)
	
	ss.logger.Debugf("代理指标已更新: %s, 成功率: %.2f, 平均响应时间: %v, 质量评分: %.2f",
		proxy, metrics.SuccessRate, metrics.AvgResponseTime, metrics.QualityScore)
}

// getOrCreateMetrics 获取或创建代理指标
func (ss *SmartSelector) getOrCreateMetrics(proxy string) *ProxyMetrics {
	if metrics, exists := ss.metrics[proxy]; exists {
		return metrics
	}
	
	metrics := &ProxyMetrics{
		ProxyURL:         proxy,
		SuccessRate:      1.0, // 新代理给予较高初始评分
		AvgResponseTime:  0,
		TotalRequests:    0,
		SuccessRequests:  0,
		FailedRequests:   0,
		LastUsed:         time.Time{},
		LastSuccess:      time.Time{},
		LastFailure:      time.Time{},
		ConsecutiveFails: 0,
		QualityScore:     80.0, // 新代理初始评分
		LoadFactor:       0.0,
		DomainStats:      make(map[string]*DomainMetrics),
	}
	
	ss.metrics[proxy] = metrics
	return metrics
}

// updateDomainMetrics 更新域名特定指标
func (ss *SmartSelector) updateDomainMetrics(metrics *ProxyMetrics, domain string, success bool, responseTime time.Duration) {
	if metrics.DomainStats == nil {
		metrics.DomainStats = make(map[string]*DomainMetrics)
	}
	
	domainMetrics, exists := metrics.DomainStats[domain]
	if !exists {
		domainMetrics = &DomainMetrics{
			Domain:          domain,
			SuccessRate:     1.0,
			AvgResponseTime: 0,
			TotalRequests:   0,
			SuccessRequests: 0,
			LastUsed:        time.Time{},
		}
		metrics.DomainStats[domain] = domainMetrics
	}
	
	domainMetrics.TotalRequests++
	domainMetrics.LastUsed = time.Now()
	
	if success {
		domainMetrics.SuccessRequests++
		
		// 更新域名特定的平均响应时间
		if domainMetrics.AvgResponseTime == 0 {
			domainMetrics.AvgResponseTime = responseTime
		} else {
			alpha := 0.3
			domainMetrics.AvgResponseTime = time.Duration(
				float64(domainMetrics.AvgResponseTime)*(1-alpha) + float64(responseTime)*alpha,
			)
		}
	}
	
	// 重新计算域名特定成功率
	if domainMetrics.TotalRequests > 0 {
		domainMetrics.SuccessRate = float64(domainMetrics.SuccessRequests) / float64(domainMetrics.TotalRequests)
	}
}

// 其他辅助方法...
func (ss *SmartSelector) generateRandomFloat() float64 {
	// 简单的随机数生成，实际应用中可以使用更好的随机数生成器
	return float64(time.Now().UnixNano()%1000) / 1000.0
}

func (ss *SmartSelector) performCleanupIfNeeded() {
	if time.Since(ss.lastCleanup) < ss.config.CleanupInterval {
		return
	}
	
	// 清理过期指标
	cutoff := time.Now().Add(-ss.config.MetricsRetention)
	for proxy, metrics := range ss.metrics {
		if metrics.LastUsed.Before(cutoff) && metrics.TotalRequests == 0 {
			delete(ss.metrics, proxy)
		}
	}
	
	ss.lastCleanup = time.Now()
}

func (ss *SmartSelector) recordSelection(proxy, domain, reason string, score float64) {
	record := SelectionRecord{
		Timestamp:     time.Now(),
		SelectedProxy: proxy,
		Domain:        domain,
		Reason:        reason,
		QualityScore:  score,
		Success:       false, // 将在后续更新
	}
	
	ss.selectionHistory = append(ss.selectionHistory, record)
	
	// 限制历史记录大小
	if len(ss.selectionHistory) > ss.config.HistorySize {
		ss.selectionHistory = ss.selectionHistory[1:]
	}
}

// GetMetrics 获取所有代理指标
func (ss *SmartSelector) GetMetrics() map[string]*ProxyMetrics {
	ss.mu.RLock()
	defer ss.mu.RUnlock()
	
	result := make(map[string]*ProxyMetrics)
	for k, v := range ss.metrics {
		result[k] = v
	}
	return result
}

// GetSelectionHistory 获取选择历史
func (ss *SmartSelector) GetSelectionHistory() []SelectionRecord {
	ss.mu.RLock()
	defer ss.mu.RUnlock()
	
	result := make([]SelectionRecord, len(ss.selectionHistory))
	copy(result, ss.selectionHistory)
	return result
}
