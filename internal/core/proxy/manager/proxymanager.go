package manager

import (
	"bufio"
	"context" // 为 StartBanCleaner 添加
	"errors"
	"fmt"
	"math/rand"
	"net"
	"os"
	"strconv" // 为 ParseInt 添加
	"strings"
	"sync"
	"time"

	"flexproxy/common/constants"
	flexerrors "flexproxy/common/errors"
	"flexproxy/common/logger"
	configpkg "flexproxy/internal/config"
	"flexproxy/internal/core/proxy/selector"
	"flexproxy/internal/core/validation"
	"flexproxy/pkg/flexproxy" // 假设仍需要用于 placeholder.ReplaceAllString 和 Transport
	"flexproxy/pkg/helper"    // 假设仍需要用于 helper.Eval
	"github.com/fsnotify/fsnotify"         // 为 Watch 方法添加
)

// var placeholder = strings.NewReplacer("https", "http", "socks5", "http", "socks4", "http") // 已移除，将使用 vars.go 中的 placeholder

// IPBanInfo 存储IP封禁信息
type IPBanInfo struct {
	ExpiresAt   time.Time // 封禁到期时间，零值表示永久
	IsPermanent bool      // 是否永久封禁
}

// IsActive 检查封禁是否仍然有效
func (bi *IPBanInfo) IsActive() bool {
	if bi.IsPermanent {
		return true
	}
	return !bi.ExpiresAt.IsZero() && time.Now().Before(bi.ExpiresAt)
}

// ProxyQualityInfo 代理质量信息
type ProxyQualityInfo struct {
	ProxyURL        string                        // 代理URL
	SuccessCount    int64                         // 成功请求数
	FailureCount    int64                         // 失败请求数
	TotalRequests   int64                         // 总请求数
	AvgResponseTime time.Duration                 // 平均响应时间
	LastUsed        time.Time                     // 最后使用时间
	QualityScore    float64                       // 质量评分 (0-100)
	QualityTier     string                        // 质量等级: "premium", "standard", "backup"
	DomainStats     map[string]*DomainPerformance // 域名级别性能统计
	CreatedAt       time.Time                     // 创建时间
	UpdatedAt       time.Time                     // 更新时间
}

// DomainPerformance 域名级别性能统计
type DomainPerformance struct {
	Domain          string        // 域名
	SuccessCount    int64         // 成功请求数
	FailureCount    int64         // 失败请求数
	AvgResponseTime time.Duration // 平均响应时间
	LastUsed        time.Time     // 最后使用时间
	QualityScore    float64       // 该域名下的质量评分
}

// ProxyPool 分层代理池
type ProxyPool struct {
	PremiumProxies  []string            // 高质量代理池 (成功率>90%, 延迟<500ms)
	StandardProxies []string            // 标准代理池 (成功率60-90%)
	BackupProxies   []string            // 备用代理池 (成功率30-60%)
	DomainPools     map[string][]string // 域名专用代理池
}

// ProxyManager 定义代理列表和当前代理位置
type ProxyManager struct {
	mu                   sync.Mutex
	CurrentIndex         int                              // 当前代理索引
	filepath             string                           // 代理文件路径
	Length               int                              // 代理数量
	Proxies              []string                         // 代理列表
	GlobalBannedIPs      map[string]*IPBanInfo            // 全局封禁的IP
	DomainBannedIPs      map[string]map[string]*IPBanInfo // 域名级别封禁的IP，格式为：domain -> ip -> ban info
	URLBannedIPs         map[string]map[string]*IPBanInfo // URL级别封禁的IP，格式为：url -> ip -> ban info
	PermanentBlockedIPs  map[string]bool                  // 永久封禁的IP或域名
	TimedDomainBans      map[string]*IPBanInfo            // 定时域名封禁，格式为：domain -> ban info
	TrustedIPs           map[string]bool                  // 受信任的IP或域名
	Config               *configpkg.Config                   // 配置信息
	banSystemInitialized bool                             // 封禁系统是否已初始化
	proxyCache           struct {                         // 代理缓存
		proxy    string    // 缓存的代理
		lastUsed time.Time // 最后使用时间
	}
	failedProxies    map[string]time.Time         // 失败代理跟踪，记录失败时间
	failedProxyTTL   time.Duration                // 失败代理的TTL，默认5分钟
	ProxyQuality     map[string]*ProxyQualityInfo // 代理质量信息映射
	QualityPools     *ProxyPool                   // 质量分层代理池
	qualityMutex     sync.RWMutex                 // 质量信息读写锁
	watcher          *fsnotify.Watcher            // 用于监控代理文件变更
	smartModeEnabled bool                         // 智能模式是否启用
	retryPolicy      string                       // 重试策略
	smartSelector    *selector.SmartSelector      // 智能代理选择器
	networkValidator *validation.NetworkValidator // 网络地址验证器
}

func init() {
	rand.Seed(time.Now().UnixNano())
}

// New 初始化代理管理器
func New(filename string, cfg *configpkg.Config) (*ProxyManager, error) {
	pm := &ProxyManager{
		filepath:            filename,
		Proxies:             []string{},
		GlobalBannedIPs:     make(map[string]*IPBanInfo),
		DomainBannedIPs:     make(map[string]map[string]*IPBanInfo),
		URLBannedIPs:        make(map[string]map[string]*IPBanInfo),
		PermanentBlockedIPs: make(map[string]bool),
		TimedDomainBans:     make(map[string]*IPBanInfo),
		TrustedIPs:          make(map[string]bool),
		Config:              cfg, // Store common.Config
		failedProxies:       make(map[string]time.Time),
		failedProxyTTL:      5 * time.Minute, // 默认5分钟TTL
		ProxyQuality:        make(map[string]*ProxyQualityInfo),
		QualityPools: &ProxyPool{
			PremiumProxies:  make([]string, 0),
			StandardProxies: make([]string, 0),
			BackupProxies:   make([]string, 0),
			DomainPools:     make(map[string][]string),
		},
	}

	if err := pm.loadProxiesFromFile(); err != nil {
		return nil, flexerrors.WrapErrorWithDetails(err, flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerLoadFailed, "代理加载失败", "文件名: "+filename)
	}

	// 移除自动初始化封禁系统，改为显式调用
	// 这样可以避免重复初始化和重复日志输出
	pm.InitQualitySystem() // 在代理加载后初始化质量系统

	// 初始化智能选择器
	pm.initSmartSelector()

	// 初始化网络验证器
	pm.networkValidator = validation.NewNetworkValidator()

	return pm, nil
}

// NewWithConfigService 创建代理管理器并注册配置热重载回调
// 这个方法是 New 方法的扩展版本，支持配置服务回调注册
func NewWithConfigService(filename string, cfg *configpkg.Config, configService interface{}) (*ProxyManager, error) {
	// 使用标准的 New 方法创建 ProxyManager
	pm, err := New(filename, cfg)
	if err != nil {
		return nil, err
	}

	// 初始化封禁系统
	pm.InitBanSystem(cfg)

	// 注册配置热重载回调
	if configService != nil {
		pm.RegisterConfigCallback(configService)
	}

	proxyManagerLogger.GetRawLogger().Info("代理管理器已创建并注册配置热重载回调")
	return pm, nil
}

// 使用统一的日志管理器
var proxyManagerLogger = logger.GetProxyManagerLogger()

// loadProxiesFromFile 从文件加载代理列表
func (p *ProxyManager) loadProxiesFromFile() error {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.Proxies = []string{} // 清空现有代理列表
	keys := make(map[string]bool)

	file, err := os.Open(p.filepath)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		proxy := helper.Eval(scanner.Text())
		if _, value := keys[proxy]; !value {
			normalizedProxy := placeholder.ReplaceAllString(proxy, "") // 确保占位符已定义
			_, err = flexproxy.Transport(normalizedProxy, "", nil, "") // 使用标准化代理
			if err == nil || errors.Is(err, flexproxy.ErrSwitchTransportAWSProtocolScheme) {
				keys[proxy] = true                   // 使用原始代理字符串作为键
				p.Proxies = append(p.Proxies, proxy) // 存储原始代理字符串
			} else {
				proxyManagerLogger.GetRawLogger().Warnf("无效的代理: %s, 错误: %v", proxy, err)
			}
		}
	}
	p.Length = len(p.Proxies)
	if p.Length < 1 {
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoValidProxies, "没有有效的代理URL", "文件路径: "+p.filepath)
	}
	proxyManagerLogger.GetRawLogger().Infof("从 %s 加载了 %d 个代理", p.filepath, p.Length)
	return scanner.Err()
}

// Count 返回可用代理的数量
func (p *ProxyManager) Count() int {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.Length = len(p.Proxies)
	return p.Length
}

// GetProxies 返回代理列表的副本
func (p *ProxyManager) GetProxies() []string {
	p.mu.Lock()
	defer p.mu.Unlock()
	proxies := make([]string, len(p.Proxies))
	copy(proxies, p.Proxies)
	return proxies
}

// GetProxy (已弃用，请使用 RotateProxy 或 GetProxyForDomain)
// 为了接口兼容性，它可以调用 RotateProxy。
func (p *ProxyManager) GetProxy(mode string) (string, error) {
	return p.RotateProxy() // 默认使用通用轮询
}

// GetRandomProxy 从列表中返回一个随机代理
func (p *ProxyManager) GetRandomProxy() (string, error) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.Length == 0 {
		return "", flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoValidProxies, "没有可用的代理", "代理列表为空")
	}

	index := rand.Intn(p.Length)
	return p.Proxies[index], nil
}

// GetCachedProxy 返回缓存的代理（如果可用且未过期）
func (p *ProxyManager) GetCachedProxy() (string, bool) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.proxyCache.proxy != "" && time.Since(p.proxyCache.lastUsed) < 5*time.Minute {
		return p.proxyCache.proxy, true
	}
	return "", false
}

// SetCachedProxy 在缓存中设置代理
func (p *ProxyManager) SetCachedProxy(proxy string) {
	p.mu.Lock()
	defer p.mu.Unlock()

	p.proxyCache.proxy = proxy
	p.proxyCache.lastUsed = time.Now()
}

// RotateProxy 实现 ProxyManagerInterface 方法。
// 它提供通用的代理轮询机制。
func (p *ProxyManager) RotateProxy(domain ...string) (string, error) {
	// 如果提供了域名，使用 GetProxyForDomain，否则使用通用轮询。
	// 'mode'（随机/顺序）可以是全局配置或传递的参数。
	// 为简单起见，如果未指定则假设默认模式。
	rotationMode := "random" // 默认或来自配置
	if p.Config != nil && p.Config.Global.IPRotationMode != "" {
		rotationMode = p.Config.Global.IPRotationMode
	}

	var targetDomain string
	if len(domain) > 0 {
		targetDomain = domain[0]
	}
	return p.GetProxyForDomain(rotationMode, targetDomain)
}

// GetProxyForDomain 为特定域名获取代理，支持域名专用池优先选择
func (p *ProxyManager) GetProxyForDomain(mode string, domain string) (string, error) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if time.Since(p.proxyCache.lastUsed) < 5*time.Second && p.proxyCache.proxy != "" {
		return p.proxyCache.proxy, nil
	}

	var proxy string
	var err error

	if domain != "" {
		proxy, err = p.getProxyFromDomainPool(domain, mode)
		if err == nil && proxy != "" {
			p.proxyCache.proxy = proxy
			p.proxyCache.lastUsed = time.Now()
			return proxy, nil
		}
	}

	switch strings.ToLower(mode) {
	case "random":
		proxy, err = p.randomProxy()
	case "quality":
		proxy, err = p.qualityProxy()
	case "smart":
		proxy, err = p.smartProxy(domain)
	default:
		proxy, err = p.nextProxy()
	}

	if err == nil && proxy != "" {
		p.proxyCache.proxy = proxy
		p.proxyCache.lastUsed = time.Now()
	}
	return proxy, err
}

func (p *ProxyManager) getProxyFromDomainPool(domain string, mode string) (string, error) {
	p.qualityMutex.RLock()
	defer p.qualityMutex.RUnlock()

	if p.QualityPools == nil || p.QualityPools.DomainPools == nil {
		return "", flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerQualityPoolNotInitialized, "质量池未初始化", "域名: "+domain)
	}
	domainProxies, exists := p.QualityPools.DomainPools[domain]
	if !exists || len(domainProxies) == 0 {
		return "", flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoDomainProxies, "没有可用的域名专用代理", "域名: "+domain)
	}
	var availableProxies []string
	for _, proxy := range domainProxies {
		if !p.isProxyBannedInternal(proxy, domain, "domain") { // 使用内部检查
			availableProxies = append(availableProxies, proxy)
		}
	}
	if len(availableProxies) == 0 {
		return "", flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoAvailableProxies, "没有可用的未封禁域名专用代理", "域名: "+domain)
	}
	var selectedProxy string
	switch strings.ToLower(mode) {
	case "random":
		selectedProxy = availableProxies[rand.Intn(len(availableProxies))]
	case "quality":
		// 质量模式：选择第一个（假设已按质量排序）
		selectedProxy = availableProxies[0]
	case "smart":
		// 智能模式：使用多维度评估选择最佳代理
		smartSelector := p.getSmartSelector()
		if smartSelector != nil {
			ctx := context.Background()
			selected, err := smartSelector.SelectBestProxy(ctx, availableProxies, domain)
			if err != nil {
				proxyManagerLogger.GetRawLogger().Warnf("智能选择失败，回退到质量模式: %v", err)
				selectedProxy = availableProxies[0] // 回退到第一个
			} else {
				selectedProxy = selected
			}
		} else {
			// 回退到简化实现
			if len(availableProxies) > 1 {
				topCount := (len(availableProxies) + 1) / 2
				selectedProxy = availableProxies[rand.Intn(topCount)]
			} else {
				selectedProxy = availableProxies[0]
			}
		}
	default:
		// 顺序选择
		selectedProxy = availableProxies[0]
	}
	proxyManagerLogger.GetRawLogger().Debugf("从域名 %s 专用池选择代理: %s", domain, selectedProxy)
	return selectedProxy, nil
}

func (p *ProxyManager) nextProxy() (string, error) {
	count := len(p.Proxies)
	if count == 0 {
		return "", flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoAvailableProxies, "没有可用的代理")
	}

	maxAttempts := count
	if p.Config != nil && p.Config.Global.MaxProxyFetchAttempts > 0 {
		maxAttempts = p.Config.Global.MaxProxyFetchAttempts
		if maxAttempts > count {
			maxAttempts = count
		}
	}

	startIndex := p.CurrentIndex
	for i := 0; i < maxAttempts; i++ {
		p.CurrentIndex = (p.CurrentIndex + 1) % count
		proxy := p.Proxies[p.CurrentIndex]
		if !p.isProxyBannedInternal(proxy, "", "global") { // 检查全局禁用
			return proxy, nil
		}
		if p.CurrentIndex == startIndex && i > 0 {
			break
		} // 完整循环
	}
	return "", flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerAllProxiesBanned, "所有尝试的代理都被封禁或不可用（顺序模式）")
}

func (p *ProxyManager) randomProxy() (string, error) {
	count := len(p.Proxies)
	if count == 0 {
		return "", flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoAvailableProxies, "没有可用的代理")
	}

	maxAttempts := count
	if p.Config != nil && p.Config.Global.MaxProxyFetchAttempts > 0 {
		maxAttempts = p.Config.Global.MaxProxyFetchAttempts
		if maxAttempts > count {
			maxAttempts = count
		}
	}

	triedIndices := make(map[int]bool)
	for i := 0; i < maxAttempts; i++ {
		if len(triedIndices) >= count {
			break
		} // 所有代理都已尝试

		var randomIndex int
		for {
			randomIndex = rand.Intn(count)
			if !triedIndices[randomIndex] {
				triedIndices[randomIndex] = true
				break
			}
		}
		proxy := p.Proxies[randomIndex]
		if !p.isProxyBannedInternal(proxy, "", "global") { // 检查全局禁用
			return proxy, nil
		}
	}
	return "", flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerAllProxiesBanned, "所有尝试的代理都被封禁或不可用（随机模式）")
}

// qualityProxy 基于质量选择代理
func (p *ProxyManager) qualityProxy() (string, error) {
	count := len(p.Proxies)
	if count == 0 {
		return "", flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoAvailableProxies, "没有可用的代理")
	}

	// 如果质量池已初始化，使用质量池选择
	if p.QualityPools != nil && len(p.QualityPools.PremiumProxies) > 0 {
		return p.GetQualityProxy("premium", "")
	}

	// 否则回退到随机选择
	return p.randomProxy()
}

// smartProxy 智能代理选择
func (p *ProxyManager) smartProxy(domain string) (string, error) {
	count := len(p.Proxies)
	if count == 0 {
		return "", flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoAvailableProxies, "没有可用的代理")
	}

	// 智能模式的核心逻辑：
	// 1. 检查是否有缓存的代理且仍然可用
	// 2. 如果没有明确的重试指示，继续使用当前代理
	// 3. 如果需要重试，根据重试类型决定是否切换代理

	// 检查缓存的代理是否仍然可用
	if p.proxyCache.proxy != "" && time.Since(p.proxyCache.lastUsed) < 30*time.Second {
		// 检查缓存的代理是否被封禁或标记为失败
		if !p.isProxyBannedInternal(p.proxyCache.proxy, domain, "domain") &&
			!p.isProxyBannedInternal(p.proxyCache.proxy, "", "global") &&
			!p.isProxyMarkedAsFailed(p.proxyCache.proxy) {
			proxyManagerLogger.GetRawLogger().Debugf("智能模式：继续使用缓存代理 %s", p.proxyCache.proxy)
			return p.proxyCache.proxy, nil
		} else {
			// 缓存的代理不可用，清除缓存
			proxyManagerLogger.GetRawLogger().Debugf("智能模式：清除不可用的缓存代理 %s", p.proxyCache.proxy)
			p.proxyCache.proxy = ""
			p.proxyCache.lastUsed = time.Time{}
		}
	}

	// 如果缓存代理不可用，选择新代理
	// 优先使用质量池，如果没有则使用随机选择
	if p.QualityPools != nil && len(p.QualityPools.PremiumProxies) > 0 {
		proxy, err := p.GetQualityProxy("premium", domain)
		if err == nil && proxy != "" {
			proxyManagerLogger.GetRawLogger().Debugf("智能模式：从质量池选择代理 %s", proxy)
			return proxy, nil
		}
	}

	// 回退到随机选择
	proxy, err := p.randomProxy()
	if err == nil && proxy != "" {
		proxyManagerLogger.GetRawLogger().Debugf("智能模式：随机选择代理 %s", proxy)
	}
	return proxy, err
}

// InitBanSystem 初始化封禁系统
// 这是一个独立的方法，需要在适当的时机显式调用
// 避免在ProxyManager创建时自动调用，防止重复初始化
func (p *ProxyManager) InitBanSystem(cfg interface{}) {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 检查是否已经初始化，避免重复初始化
	if p.banSystemInitialized {
		proxyManagerLogger.GetRawLogger().Debug("封禁系统已经初始化，跳过重复初始化")
		return
	}

	// 在加载新配置前清除现有的封禁信息
	p.GlobalBannedIPs = make(map[string]*IPBanInfo)
	p.DomainBannedIPs = make(map[string]map[string]*IPBanInfo)
	p.URLBannedIPs = make(map[string]map[string]*IPBanInfo)
	p.PermanentBlockedIPs = make(map[string]bool)
	p.TimedDomainBans = make(map[string]*IPBanInfo)
	p.TrustedIPs = make(map[string]bool)

	// 类型断言获取具体的配置类型
	if config, ok := cfg.(*configpkg.Config); ok && config != nil && config.Global.Enable {
		for _, ipOrDomain := range config.Global.BlockedIPs {
			p.PermanentBlockedIPs[ipOrDomain] = true
		}
		for _, ipOrDomain := range config.Global.TrustedIPs {
			p.TrustedIPs[ipOrDomain] = true
		}
		for _, banCfg := range config.Global.GlobalBannedIPs {
			durationStr := fmt.Sprintf("%v", banCfg.Duration)
			p.banIPInternal(banCfg.IP, durationStr, "global", "")
		}
		for _, banCfg := range config.Global.BannedDomains {
			durationStr := fmt.Sprintf("%v", banCfg.Duration)
			p.banDomainInternal(banCfg.Domain, durationStr)
		}
		proxyManagerLogger.GetRawLogger().Infof("封禁系统初始化完成：永久封禁 %d 个IP/域名，信任 %d 个IP/域名",
			len(p.PermanentBlockedIPs), len(p.TrustedIPs))
	}

	// 标记为已初始化
	p.banSystemInitialized = true
}

// UpdateBanSystemFromConfig 增量更新封禁系统配置
// 保留运行时状态，只更新配置文件中的静态封禁项
// 这个方法专门用于热重载场景，确保不会清空现有封禁状态
func (p *ProxyManager) UpdateBanSystemFromConfig(cfg *configpkg.Config) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if cfg == nil || !cfg.Global.Enable {
		proxyManagerLogger.GetRawLogger().Debug("配置无效或未启用，跳过封禁系统更新")
		return
	}

	proxyManagerLogger.GetRawLogger().Info("开始增量更新封禁系统配置")

	// 记录更新前的状态
	beforePermanentCount := len(p.PermanentBlockedIPs)
	beforeTrustedCount := len(p.TrustedIPs)
	beforeGlobalBanCount := len(p.GlobalBannedIPs)

	// 增量更新永久封禁IP/域名列表（不清空现有数据）
	for _, ipOrDomain := range cfg.Global.BlockedIPs {
		if _, exists := p.PermanentBlockedIPs[ipOrDomain]; !exists {
			p.PermanentBlockedIPs[ipOrDomain] = true
			proxyManagerLogger.GetRawLogger().Debugf("新增永久封禁IP/域名: %s", ipOrDomain)
		}
	}

	// 增量更新信任IP列表（不清空现有数据）
	for _, ipOrDomain := range cfg.Global.TrustedIPs {
		if _, exists := p.TrustedIPs[ipOrDomain]; !exists {
			p.TrustedIPs[ipOrDomain] = true
			proxyManagerLogger.GetRawLogger().Debugf("新增信任IP/域名: %s", ipOrDomain)
		}
	}

	// 增量更新全局封禁IP（不清空现有数据）
	for _, banCfg := range cfg.Global.GlobalBannedIPs {
		// 检查是否已存在
		if existingBan, exists := p.GlobalBannedIPs[banCfg.IP]; exists {
			// 如果已存在，检查是否需要更新封禁时间
			durationStr := fmt.Sprintf("%v", banCfg.Duration)
			if newBanInfo := p.parseBanDuration(durationStr); newBanInfo != nil {
				if !existingBan.IsPermanent && newBanInfo.ExpiresAt.After(existingBan.ExpiresAt) {
					// 如果新的封禁时间更长，则更新
					p.GlobalBannedIPs[banCfg.IP] = newBanInfo
					proxyManagerLogger.GetRawLogger().Debugf("更新全局封禁IP: %s, 新到期时间: %v", banCfg.IP, newBanInfo.ExpiresAt)
				}
			}
		} else {
			// 新增封禁
			durationStr := fmt.Sprintf("%v", banCfg.Duration)
			if err := p.banIPInternal(banCfg.IP, durationStr, "global", ""); err != nil {
				proxyManagerLogger.GetRawLogger().Warnf("新增全局封禁IP失败: %s, 错误: %v", banCfg.IP, err)
			} else {
				proxyManagerLogger.GetRawLogger().Debugf("新增全局封禁IP: %s", banCfg.IP)
			}
		}
	}

	// 增量更新封禁域名（不清空现有数据）
	for _, banCfg := range cfg.Global.BannedDomains {
		// 检查是否已在永久封禁列表或定时封禁列表中
		permanentExists := p.PermanentBlockedIPs[banCfg.Domain]
		timedExists := false
		if banInfo, exists := p.TimedDomainBans[banCfg.Domain]; exists && banInfo.IsActive() {
			timedExists = true
		}

		if !permanentExists && !timedExists {
			durationStr := fmt.Sprintf("%v", banCfg.Duration)
			if err := p.banDomainInternal(banCfg.Domain, durationStr); err != nil {
				proxyManagerLogger.GetRawLogger().Warnf("新增封禁域名失败: %s, 错误: %v", banCfg.Domain, err)
			} else {
				proxyManagerLogger.GetRawLogger().Debugf("新增封禁域名: %s", banCfg.Domain)
			}
		}
	}

	// 记录更新结果
	afterPermanentCount := len(p.PermanentBlockedIPs)
	afterTrustedCount := len(p.TrustedIPs)
	afterGlobalBanCount := len(p.GlobalBannedIPs)

	proxyManagerLogger.GetRawLogger().Infof(
		"封禁系统配置增量更新完成 - 永久封禁: %d->%d (+%d), 信任IP: %d->%d (+%d), 全局封禁: %d->%d (+%d)",
		beforePermanentCount, afterPermanentCount, afterPermanentCount-beforePermanentCount,
		beforeTrustedCount, afterTrustedCount, afterTrustedCount-beforeTrustedCount,
		beforeGlobalBanCount, afterGlobalBanCount, afterGlobalBanCount-beforeGlobalBanCount)
}

// parseBanDuration 解析封禁持续时间，返回 IPBanInfo
func (p *ProxyManager) parseBanDuration(durationStr string) *IPBanInfo {
	var expiresAt time.Time
	var isPermanent bool

	if strings.ToLower(durationStr) == "reboot" || durationStr == "0" || durationStr == "" {
		isPermanent = true
	} else {
		if duration, err := time.ParseDuration(durationStr); err == nil {
			expiresAt = time.Now().Add(duration)
		} else if seconds, err := strconv.Atoi(durationStr); err == nil {
			expiresAt = time.Now().Add(time.Duration(seconds) * time.Second)
		} else {
			return nil
		}
	}

	return &IPBanInfo{ExpiresAt: expiresAt, IsPermanent: isPermanent}
}

// ResetBanSystem 重置封禁系统初始化状态
// 用于需要重新初始化封禁系统的场景（如配置重载）
func (p *ProxyManager) ResetBanSystem() {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.banSystemInitialized = false
	proxyManagerLogger.GetRawLogger().Debug("封禁系统初始化状态已重置")
}

// IsBanSystemInitialized 检查封禁系统是否已初始化
func (p *ProxyManager) IsBanSystemInitialized() bool {
	p.mu.Lock()
	defer p.mu.Unlock()
	return p.banSystemInitialized
}

// RegisterConfigCallback 注册配置热重载回调
// 这个方法用于将 ProxyManager 的增量更新方法注册到配置服务的回调列表中
// 确保热重载时能够更新封禁配置而不清空运行时状态
func (p *ProxyManager) RegisterConfigCallback(configService interface{}) {
	// 检查配置服务是否实现了 RegisterCallback 方法
	if cs, ok := configService.(interface{ RegisterCallback(func(interface{})) }); ok {
		cs.RegisterCallback(func(cfg interface{}) {
			// 类型断言，确保配置类型正确
			if config, ok := cfg.(*configpkg.Config); ok {
				// 使用增量更新方法，而不是重置和重新初始化
				p.UpdateBanSystemFromConfig(config)
			}
		})
		proxyManagerLogger.GetRawLogger().Info("已注册封禁系统配置热重载回调")
	} else {
		proxyManagerLogger.GetRawLogger().Warn("配置服务不支持回调注册，封禁系统热重载将不可用")
	}
}

// BanIP 在给定持续时间、范围和资源下封禁IP
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) BanIP(ip, durationStr, scope, resource string) error {
	p.mu.Lock()
	defer p.mu.Unlock()
	return p.banIPInternal(ip, durationStr, scope, resource)
}

// banIPInternal 是封禁IP的内部实现
func (p *ProxyManager) banIPInternal(ip, durationStr, scope, resource string) error {
	if p.TrustedIPs[ip] {
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerTrustedIPCannotBan, "受信任的IP无法封禁", "IP: "+ip)
	}
	if p.PermanentBlockedIPs[ip] {
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerIPAlreadyBlocked, "IP已被永久封禁", "IP: "+ip)
	}

	var expiresAt time.Time
	isPermanent := false
	if strings.ToLower(durationStr) == "reboot" || durationStr == "0" || durationStr == "" {
		isPermanent = true // 或者如果""或"0"表示其他含义，则视为仅会话有效
	} else {
		duration, err := time.ParseDuration(durationStr) // 假设durationStr格式如"300ms", "2s", "1h"
		if err != nil {
			// 尝试解析为毫秒整数
			ms, intErr := strconv.ParseInt(durationStr, 10, 64)
			if intErr != nil {
				return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerInvalidDuration, "无效的封禁持续时间", "持续时间: "+durationStr+", 解析错误: "+err.Error())
			}
			duration = time.Duration(ms) * time.Millisecond
		}
		if duration <= 0 { // 如果解析的持续时间为0或负数，则视为永久/重启
			isPermanent = true
		} else {
			expiresAt = time.Now().Add(duration)
		}
	}
	banInfo := &IPBanInfo{ExpiresAt: expiresAt, IsPermanent: isPermanent}

	switch strings.ToLower(scope) {
	case "global":
		p.GlobalBannedIPs[ip] = banInfo
	case "domain":
		if resource == "" {
			return flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeInvalidResource, "域名范围需要域名资源")
		}
		if p.DomainBannedIPs[resource] == nil {
			p.DomainBannedIPs[resource] = make(map[string]*IPBanInfo)
		}
		p.DomainBannedIPs[resource][ip] = banInfo
	case "url":
		if resource == "" {
			return flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeInvalidResource, "URL范围需要URL字符串资源")
		}
		if p.URLBannedIPs[resource] == nil {
			p.URLBannedIPs[resource] = make(map[string]*IPBanInfo)
		}
		p.URLBannedIPs[resource][ip] = banInfo
	default:
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeInvalidScope, "未知的封禁范围", "范围: "+scope)
	}
	proxyManagerLogger.GetRawLogger().Infof("已封禁IP %s (范围: %s, 资源: %s, 持续时间: %s, 永久: %v)", ip, scope, resource, durationStr, isPermanent)
	return nil
}

// BanDomain 封禁整个域名（不是域名下的特定IP）
// 将域名添加到封禁域名列表中
func (p *ProxyManager) BanDomain(domain, durationStr string) error {
	p.mu.Lock()
	defer p.mu.Unlock()
	return p.banDomainInternal(domain, durationStr)
}
func (p *ProxyManager) banDomainInternal(domain, durationStr string) error {
	if p.TrustedIPs[domain] {
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeTrustedResource, "域名是受信任的，不能封禁", "域名: "+domain)
	}
	if p.PermanentBlockedIPs[domain] {
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodePermanentlyBlocked, "域名已永久封禁", "域名: "+domain)
	}

	// 检查是否已存在定时封禁
	if existingBan, exists := p.TimedDomainBans[domain]; exists && existingBan.IsActive() {
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodePermanentlyBlocked, "域名已被定时封禁", "域名: "+domain)
	}

	// 解析封禁持续时间
	banInfo := p.parseBanDuration(durationStr)
	if banInfo == nil {
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerInvalidDuration, "无效的封禁持续时间", "持续时间: "+durationStr)
	}

	// 根据封禁类型选择存储位置
	if banInfo.IsPermanent {
		// 永久封禁存储在PermanentBlockedIPs中
		p.PermanentBlockedIPs[domain] = true
		proxyManagerLogger.GetRawLogger().Infof("已永久封禁域名: %s", domain)
	} else {
		// 定时封禁存储在TimedDomainBans中
		p.TimedDomainBans[domain] = banInfo
		proxyManagerLogger.GetRawLogger().Infof("已定时封禁域名: %s, 到期时间: %v", domain, banInfo.ExpiresAt)
	}
	return nil
}

// isProxyBannedInternal 是用于封禁检查的内部无锁版本
// 假设调用者已持有锁
func (p *ProxyManager) isProxyBannedInternal(proxy, resource, scope string) bool {
	if p.PermanentBlockedIPs[proxy] {
		return true
	}
	if resource != "" && p.PermanentBlockedIPs[resource] {
		return true
	} // 如果资源（域名/URL）本身被永久封禁

	// 检查定时域名封禁
	if resource != "" {
		if banInfo, exists := p.TimedDomainBans[resource]; exists && banInfo.IsActive() {
			return true
		}
	}

	if p.TrustedIPs[proxy] {
		return false
	}
	if resource != "" && p.TrustedIPs[resource] {
		return false
	}

	switch strings.ToLower(scope) {
	case "global":
		if banInfo, ok := p.GlobalBannedIPs[proxy]; ok && banInfo.IsActive() {
			return true
		}
	case "domain":
		if resource != "" {
			if domainBans, ok := p.DomainBannedIPs[resource]; ok {
				if banInfo, ok := domainBans[proxy]; ok && banInfo.IsActive() {
					return true
				}
			}
		}
	case "url":
		if resource != "" {
			if urlBans, ok := p.URLBannedIPs[resource]; ok {
				if banInfo, ok := urlBans[proxy]; ok && banInfo.IsActive() {
					return true
				}
			}
		}
	}
	return false
}

// IsIPBanned 根据给定的范围和资源检查IP是否被封禁
// 此方法实现 ProxyManagerInterface 接口
func (p *ProxyManager) IsIPBanned(ip, resource, scope string) bool {
	p.mu.Lock() // 如果成为瓶颈，考虑使用RLock
	defer p.mu.Unlock()
	return p.isProxyBannedInternal(ip, resource, scope)
}

// IsIPPermanentlyBlocked 检查IP或域名是否在永久封禁列表中
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) IsIPPermanentlyBlocked(ipOrDomain string) bool {
	p.mu.Lock() // 考虑使用RLock
	defer p.mu.Unlock()
	return p.PermanentBlockedIPs[ipOrDomain]
}

// IsTrustedIP 检查IP或域名是否在信任列表中
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) IsTrustedIP(ipOrDomain string) bool {
	p.mu.Lock() // 考虑使用RLock
	defer p.mu.Unlock()
	return p.TrustedIPs[ipOrDomain]
}

// StartBanCleaner 启动一个协程定期清理过期的封禁
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) StartBanCleaner(ctx context.Context) {
	go func() {
		ticker := time.NewTicker(1 * time.Minute) // 每分钟清理一次
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				p.cleanupExpiredBans()
				p.cleanupExpiredFailedProxies() // 同时清理过期的失败代理
			case <-ctx.Done():
				return
			}
		}
	}()
}

// cleanupExpiredBans 从所有范围中移除过期的IP封禁和域名封禁
func (p *ProxyManager) cleanupExpiredBans() {
	p.mu.Lock()
	defer p.mu.Unlock()
	now := time.Now()
	cleanedCount := 0

	// 清理过期的全局IP封禁
	for ip, banInfo := range p.GlobalBannedIPs {
		if !banInfo.IsPermanent && !banInfo.ExpiresAt.IsZero() && now.After(banInfo.ExpiresAt) {
			delete(p.GlobalBannedIPs, ip)
			cleanedCount++
		}
	}

	// 清理过期的域名级别IP封禁
	for domain, ipMap := range p.DomainBannedIPs {
		for ip, banInfo := range ipMap {
			if !banInfo.IsPermanent && !banInfo.ExpiresAt.IsZero() && now.After(banInfo.ExpiresAt) {
				delete(p.DomainBannedIPs[domain], ip)
				cleanedCount++
			}
		}
		if len(p.DomainBannedIPs[domain]) == 0 {
			delete(p.DomainBannedIPs, domain)
		}
	}

	// 清理过期的URL级别IP封禁
	for url, ipMap := range p.URLBannedIPs {
		for ip, banInfo := range ipMap {
			if !banInfo.IsPermanent && !banInfo.ExpiresAt.IsZero() && now.After(banInfo.ExpiresAt) {
				delete(p.URLBannedIPs[url], ip)
				cleanedCount++
			}
		}
		if len(p.URLBannedIPs[url]) == 0 {
			delete(p.URLBannedIPs, url)
		}
	}

	// 清理过期的定时域名封禁
	domainCleanedCount := 0
	for domain, banInfo := range p.TimedDomainBans {
		if !banInfo.IsPermanent && !banInfo.ExpiresAt.IsZero() && now.After(banInfo.ExpiresAt) {
			delete(p.TimedDomainBans, domain)
			domainCleanedCount++
			proxyManagerLogger.GetRawLogger().Infof("域名 %s 的定时封禁已到期，自动解封", domain)
		}
	}

	totalCleaned := cleanedCount + domainCleanedCount
	if totalCleaned > 0 {
		proxyManagerLogger.GetRawLogger().Infof("清理了 %d 个过期的IP封禁和 %d 个过期的域名封禁", cleanedCount, domainCleanedCount)
	}
}

// RemoveProxy 从列表中移除代理
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) RemoveProxy(target string) error {
	p.mu.Lock()
	defer p.mu.Unlock()
	found := false
	for i, proxy := range p.Proxies {
		if proxy == target {
			p.Proxies = append(p.Proxies[:i], p.Proxies[i+1:]...)
			p.Length = len(p.Proxies)
			proxyManagerLogger.GetRawLogger().Infof("已移除代理: %s。剩余代理数: %d", target, p.Length)
			found = true
			break
		}
	}
	if !found {
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyNotFound, "代理未在列表中找到", "代理: "+target)
	}
	// 同时从质量跟踪和池中移除
	p.qualityMutex.Lock()
	delete(p.ProxyQuality, target)
	p.removeFromAllQualityPools(target)
	p.qualityMutex.Unlock()
	return nil
}

func (p *ProxyManager) removeFromAllQualityPools(proxyURL string) {
	// 假设调用者在必要时持有qualityMutex
	p.QualityPools.PremiumProxies = p.removeFromSlice(p.QualityPools.PremiumProxies, proxyURL)
	p.QualityPools.StandardProxies = p.removeFromSlice(p.QualityPools.StandardProxies, proxyURL)
	p.QualityPools.BackupProxies = p.removeFromSlice(p.QualityPools.BackupProxies, proxyURL)
	for domain, proxies := range p.QualityPools.DomainPools {
		p.QualityPools.DomainPools[domain] = p.removeFromSlice(proxies, proxyURL)
		if len(p.QualityPools.DomainPools[domain]) == 0 {
			delete(p.QualityPools.DomainPools, domain)
		}
	}
}

// Watch 监控代理文件变更
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) Watch() (*fsnotify.Watcher, error) {
	var err error
	p.watcher, err = fsnotify.NewWatcher()
	if err != nil {
		return nil, flexerrors.WrapErrorWithDetails(err, flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeWatcherCreationFailed, "创建文件监视器失败", "路径: "+p.filepath)
	}

	go func() {
		for {
			select {
			case event, ok := <-p.watcher.Events:
				if !ok {
					return
				}
				if event.Op&fsnotify.Write == fsnotify.Write || event.Op&fsnotify.Create == fsnotify.Create {
					proxyManagerLogger.GetRawLogger().Infof("代理文件 %s 已修改，正在重新加载。", p.filepath)
					if loadErr := p.loadProxiesFromFile(); loadErr != nil {
						proxyManagerLogger.GetRawLogger().Errorf("重新加载代理文件 %s 时出错: %v", p.filepath, loadErr)
					} else {
						// 为新增/移除的代理重新初始化质量系统
						p.InitQualitySystem()
					}
				}
			case err, ok := <-p.watcher.Errors:
				if !ok {
					return
				}
				proxyManagerLogger.GetRawLogger().Errorf("文件监视器错误: %v", err)
			}
		}
	}()

	err = p.watcher.Add(p.filepath)
	if err != nil {
		p.watcher.Close()
		return nil, flexerrors.WrapErrorWithDetails(err, flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeWatcherAddFailed, "添加代理文件到监视器失败", "路径: "+p.filepath)
	}

	// 直接返回底层的fsnotify.Watcher
	return p.watcher, nil
}

// InitQualitySystem 初始化或重新初始化代理质量管理系统
func (p *ProxyManager) InitQualitySystem() {
	p.qualityMutex.Lock()
	defer p.qualityMutex.Unlock()

	p.ProxyQuality = make(map[string]*ProxyQualityInfo)
	p.QualityPools = &ProxyPool{
		PremiumProxies:  make([]string, 0),
		StandardProxies: make([]string, 0),
		BackupProxies:   make([]string, 0),
		DomainPools:     make(map[string][]string),
	}
	now := time.Now()
	for _, proxy := range p.Proxies { // 假设p.Proxies是最新的
		p.ProxyQuality[proxy] = &ProxyQualityInfo{
			ProxyURL: proxy, SuccessCount: 0, FailureCount: 0, TotalRequests: 0,
			AvgResponseTime: 0, LastUsed: time.Time{}, QualityScore: 50.0,
			QualityTier: "standard", DomainStats: make(map[string]*DomainPerformance),
			CreatedAt: now, UpdatedAt: now,
		}
		p.QualityPools.StandardProxies = append(p.QualityPools.StandardProxies, proxy)
	}
	proxyManagerLogger.GetRawLogger().Infof("代理质量系统已为 %d 个代理初始化/重新初始化。", len(p.Proxies))
}

// UpdateProxyQuality 更新指定代理的质量指标
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) UpdateProxyQuality(proxyURL string, success bool, responseTime time.Duration) {
	p.qualityMutex.Lock()
	defer p.qualityMutex.Unlock()

	quality, exists := p.ProxyQuality[proxyURL]
	if !exists {
		now := time.Now()
		quality = &ProxyQualityInfo{
			ProxyURL: proxyURL, SuccessCount: 0, FailureCount: 0, TotalRequests: 0,
			AvgResponseTime: 0, LastUsed: now, QualityScore: 50.0, QualityTier: "standard",
			DomainStats: make(map[string]*DomainPerformance), CreatedAt: now, UpdatedAt: now,
		}
		p.ProxyQuality[proxyURL] = quality
		// 如果是通过质量更新看到的新代理，则添加到标准池
		if !p.isProxyInAnyPool(proxyURL) {
			p.QualityPools.StandardProxies = append(p.QualityPools.StandardProxies, proxyURL)
		}
	}

	quality.TotalRequests++
	quality.LastUsed = time.Now()
	quality.UpdatedAt = time.Now()
	if success {
		quality.SuccessCount++
	} else {
		quality.FailureCount++
	}

	if quality.AvgResponseTime == 0 {
		quality.AvgResponseTime = responseTime
	} else {
		quality.AvgResponseTime = time.Duration(float64(quality.AvgResponseTime)*constants.ProxyResponseTimeSmoothingFactor + float64(responseTime)*constants.ProxyLatencySmoothingFactor)
	}

	// 域名统计功能已移除，因为接口不支持domain参数
	// 如需域名级别统计，可以通过其他方法实现

	quality.QualityScore = p.calculateQualityScore(quality.SuccessCount, quality.FailureCount, quality.AvgResponseTime)
	oldTier := quality.QualityTier
	quality.QualityTier = p.determineQualityTier(quality.QualityScore, quality.AvgResponseTime)
	if oldTier != quality.QualityTier {
		p.redistributeProxy(proxyURL, oldTier, quality.QualityTier)
	}
}
func (p *ProxyManager) isProxyInAnyPool(proxyURL string) bool {
	// 假设调用者已经对qualityMutex进行了RLock或Lock
	for _, p := range p.QualityPools.PremiumProxies {
		if p == proxyURL {
			return true
		}
	}
	for _, proxy := range p.QualityPools.StandardProxies {
		if proxy == proxyURL {
			return true
		}
	}
	for _, proxy := range p.QualityPools.BackupProxies {
		if proxy == proxyURL {
			return true
		}
	}
	for _, proxies := range p.QualityPools.DomainPools {
		for _, proxy := range proxies {
			if proxy == proxyURL {
				return true
			}
		}
	}
	return false
}

func (p *ProxyManager) calculateQualityScore(successCount, failureCount int64, avgResponseTime time.Duration) float64 {
	if successCount+failureCount == 0 {
		return 50.0
	}
	successRate := float64(successCount) / float64(successCount+failureCount) * 100
	responseTimeScore := 100.0
	if avgResponseTime > 0 {
		responseTimeMs := float64(avgResponseTime.Milliseconds())
		if responseTimeMs > 500 { // 500ms是良好的响应时间
			responseTimeScore = 100 - (responseTimeMs-500)/15 // 分数下降更快，2秒时为0
			if responseTimeScore < 0 {
				responseTimeScore = 0
			}
		}
	}
	// 加权分数：成功率70%，响应时间30%
	finalScore := successRate*constants.ProxySuccessRateWeight + responseTimeScore*constants.ProxyResponseTimeWeight
	return finalScore
}

func (p *ProxyManager) determineQualityTier(score float64, avgResponseTime time.Duration) string {
	if score >= 90 && avgResponseTime.Milliseconds() <= 500 {
		return "premium"
	}
	if score >= 70 && avgResponseTime.Milliseconds() <= 1000 {
		return "standard"
	} // 调整后的标准层级
	if score >= 50 {
		return "standard"
	} // 更宽泛的标准层级
	return "backup"
}

func (p *ProxyManager) redistributeProxy(proxyURL, oldTier, newTier string) {
	// 假设调用者持有qualityMutex
	// 从旧层级移除
	switch oldTier {
	case "premium":
		p.QualityPools.PremiumProxies = p.removeFromSlice(p.QualityPools.PremiumProxies, proxyURL)
	case "standard":
		p.QualityPools.StandardProxies = p.removeFromSlice(p.QualityPools.StandardProxies, proxyURL)
	case "backup":
		p.QualityPools.BackupProxies = p.removeFromSlice(p.QualityPools.BackupProxies, proxyURL)
	}
	// 添加到新层级
	switch newTier {
	case "premium":
		p.QualityPools.PremiumProxies = append(p.QualityPools.PremiumProxies, proxyURL)
	case "standard":
		p.QualityPools.StandardProxies = append(p.QualityPools.StandardProxies, proxyURL)
	case "backup":
		p.QualityPools.BackupProxies = append(p.QualityPools.BackupProxies, proxyURL)
	}
}

func (p *ProxyManager) removeFromSlice(slice []string, item string) []string {
	for i, v := range slice {
		if v == item {
			return append(slice[:i], slice[i+1:]...)
		}
	}
	return slice // 未找到，返回原始切片
}

// GetQualityProxy 根据首选质量等级和域名选择代理
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) GetQualityProxy(preferredTier string, domain string) (string, error) {
	p.qualityMutex.RLock() // 使用RLock读取池数据
	defer p.qualityMutex.RUnlock()

	tryTier := func(tierProxies []string) (string, bool) {
		if len(tierProxies) == 0 {
			return "", false
		}
		// 目前从该层级进行简单的随机选择
		// 可以添加更复杂的选择逻辑（例如，该层级中最近最少使用的）
		var available []string
		for _, proxy := range tierProxies {
			if !p.isProxyBannedInternal(proxy, domain, "domain") && !p.isProxyBannedInternal(proxy, "", "global") {
				available = append(available, proxy)
			}
		}
		if len(available) > 0 {
			return available[rand.Intn(len(available))], true
		}
		return "", false
	}

	var proxy string
	var found bool

	switch strings.ToLower(preferredTier) {
	case "premium":
		if proxy, found = tryTier(p.QualityPools.PremiumProxies); found {
			return proxy, nil
		}
		if proxy, found = tryTier(p.QualityPools.StandardProxies); found {
			return proxy, nil
		}
		if proxy, found = tryTier(p.QualityPools.BackupProxies); found {
			return proxy, nil
		}
	case "standard":
		if proxy, found = tryTier(p.QualityPools.StandardProxies); found {
			return proxy, nil
		}
		if proxy, found = tryTier(p.QualityPools.PremiumProxies); found {
			return proxy, nil
		} // 如果标准层级失败，尝试高级层级
		if proxy, found = tryTier(p.QualityPools.BackupProxies); found {
			return proxy, nil
		}
	case "backup":
		if proxy, found = tryTier(p.QualityPools.BackupProxies); found {
			return proxy, nil
		}
		if proxy, found = tryTier(p.QualityPools.StandardProxies); found {
			return proxy, nil
		}
		if proxy, found = tryTier(p.QualityPools.PremiumProxies); found {
			return proxy, nil
		}
	default: // "auto"或未知，尝试最佳可用
		if proxy, found = tryTier(p.QualityPools.PremiumProxies); found {
			return proxy, nil
		}
		if proxy, found = tryTier(p.QualityPools.StandardProxies); found {
			return proxy, nil
		}
		if proxy, found = tryTier(p.QualityPools.BackupProxies); found {
			return proxy, nil
		}
	}
	return "", flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeNoSuitableProxy, "没有合适的质量代理可用")
}

// AddToDomainPool 将代理添加到域名专用池
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) AddToDomainPool(proxyURL, domain string) {
	p.qualityMutex.Lock()
	defer p.qualityMutex.Unlock()
	if p.QualityPools.DomainPools == nil {
		p.QualityPools.DomainPools = make(map[string][]string)
	}
	if p.QualityPools.DomainPools[domain] == nil {
		p.QualityPools.DomainPools[domain] = make([]string, 0)
	}
	for _, existing := range p.QualityPools.DomainPools[domain] {
		if existing == proxyURL {
			return
		} // 已存在
	}
	p.QualityPools.DomainPools[domain] = append(p.QualityPools.DomainPools[domain], proxyURL)
	proxyManagerLogger.GetRawLogger().Debugf("已将代理 %s 添加到域名 %s 的专用池", proxyURL, domain)
}

// GetProxyQualityInfo 获取特定代理的质量信息
func (p *ProxyManager) GetProxyQualityInfo(proxyURL string) (*ProxyQualityInfo, bool) {
	p.qualityMutex.RLock()
	defer p.qualityMutex.RUnlock()
	info, exists := p.ProxyQuality[proxyURL]
	return info, exists
}

// GetQualityStats 提供代理质量的摘要统计
func (p *ProxyManager) GetQualityStats() map[string]interface{} {
	p.qualityMutex.RLock()
	defer p.qualityMutex.RUnlock()
	stats := make(map[string]interface{})
	tierCounts := make(map[string]int)
	for _, q := range p.ProxyQuality {
		tierCounts[q.QualityTier]++
	}
	stats["tier_counts"] = tierCounts
	// 如需要可添加更多统计信息
	return stats
}

// GetAllProxies 实现 ProxyManagerInterface 接口方法
// 返回当前加载的所有代理字符串切片
func (p *ProxyManager) GetAllProxies() []string {
	p.mu.Lock()
	defer p.mu.Unlock()
	// 返回副本以防止外部修改内部切片
	proxiesCopy := make([]string, len(p.Proxies))
	copy(proxiesCopy, p.Proxies)
	return proxiesCopy
}

// Reload 实现 ProxyManagerInterface 接口方法
// 从配置文件重新加载代理
func (p *ProxyManager) Reload() error {
	proxyManagerLogger.GetRawLogger().Infof("正在从文件重新加载代理: %s", p.filepath)
	if err := p.loadProxiesFromFile(); err != nil {
		proxyManagerLogger.GetRawLogger().Errorf("重新加载代理文件 %s 时出错: %v", p.filepath, err)
		return err
	}
	// 为新增/移除的代理重新初始化质量系统
	p.InitQualitySystem()
	proxyManagerLogger.GetRawLogger().Infof("成功重新加载了 %d 个代理。", p.Length)
	return nil
}

// Start 实现 ProxyManagerInterface 接口方法
// 启动代理管理器和任何后台进程
func (p *ProxyManager) Start(ctx context.Context) error {
	proxyManagerLogger.GetRawLogger().Info("正在启动代理管理器")
	// 启动封禁清理器
	p.StartBanCleaner(ctx)
	return nil
}

// Stop 实现 ProxyManagerInterface 接口方法
// 停止代理管理器并清理资源
func (p *ProxyManager) Stop(ctx context.Context) error {
	proxyManagerLogger.GetRawLogger().Info("正在停止代理管理器")

	// 关闭文件监视器
	if p.watcher != nil {
		if err := p.watcher.Close(); err != nil {
			proxyManagerLogger.GetRawLogger().Warnf("关闭文件监视器时出错: %v", err)
		}
	}

	return nil
}

// UnbanIP 从所有封禁列表中移除IP
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) UnbanIP(ip string) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 从全局封禁中移除
	delete(p.GlobalBannedIPs, ip)

	// 从域名特定封禁中移除
	for domain, ipMap := range p.DomainBannedIPs {
		delete(ipMap, ip)
		if len(ipMap) == 0 {
			delete(p.DomainBannedIPs, domain)
		}
	}

	// 从URL特定封禁中移除
	for url, ipMap := range p.URLBannedIPs {
		delete(ipMap, ip)
		if len(ipMap) == 0 {
			delete(p.URLBannedIPs, url)
		}
	}

	// 如果存在，从永久封禁中移除
	delete(p.PermanentBlockedIPs, ip)

	proxyManagerLogger.GetRawLogger().Infof("已解除封禁IP: %s", ip)
	return nil
}

// EnableSmartMode 启用或禁用智能模式
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) EnableSmartMode(enabled bool) {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.smartModeEnabled = enabled
	proxyManagerLogger.GetRawLogger().Infof("智能模式已%s", map[bool]string{true: "启用", false: "禁用"}[enabled])
}

// IsSmartModeEnabled 检查智能模式是否启用
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) IsSmartModeEnabled() bool {
	p.mu.Lock()
	defer p.mu.Unlock()
	return p.smartModeEnabled
}

// GetAvailableProxies 获取可用的代理列表
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) GetAvailableProxies() []string {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 返回所有代理的副本，避免外部修改
	availableProxies := make([]string, len(p.Proxies))
	copy(availableProxies, p.Proxies)
	return availableProxies
}

// GetBannedDomains 获取被封禁的域名列表
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) GetBannedDomains() []string {
	p.mu.Lock()
	defer p.mu.Unlock()

	domainSet := make(map[string]bool)

	// 从PermanentBlockedIPs中提取域名
	for ipOrDomain := range p.PermanentBlockedIPs {
		// 使用专业的网络验证器判断是否为域名
		if p.networkValidator != nil {
			result := p.networkValidator.ValidateAddress(ipOrDomain)
			if result.IsValid && result.Type == "domain" {
				domainSet[result.Normalized] = true
			}
		} else {
			// 回退到简单判断
			validator := validation.NewNetworkValidator()
			result := validator.ValidateAddress(ipOrDomain)
			if result.IsValid && result.Type == "domain" {
				domainSet[ipOrDomain] = true
			}
		}
	}

	// 从TimedDomainBans中提取仍然有效的域名
	for domain, banInfo := range p.TimedDomainBans {
		if banInfo.IsActive() {
			domainSet[domain] = true
		}
	}

	// 转换为切片
	var bannedDomains []string
	for domain := range domainSet {
		bannedDomains = append(bannedDomains, domain)
	}

	return bannedDomains
}

// GetBannedIPs 获取被封禁的IP列表
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) GetBannedIPs() []string {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 从PermanentBlockedIPs中提取IP地址
	var bannedIPs []string
	for ipOrDomain := range p.PermanentBlockedIPs {
		// 使用专业的网络验证器判断是否为IP地址
		if p.networkValidator != nil {
			result := p.networkValidator.ValidateAddress(ipOrDomain)
			if result.IsValid && (result.Type == "ipv4" || result.Type == "ipv6") {
				bannedIPs = append(bannedIPs, result.Normalized)
			}
		} else {
			// 回退到标准库验证
			if net.ParseIP(ipOrDomain) != nil {
				bannedIPs = append(bannedIPs, ipOrDomain)
			}
		}
	}
	return bannedIPs
}

// GetConfig 获取代理管理器配置
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) GetConfig() interface{} {
	p.mu.Lock()
	defer p.mu.Unlock()
	return p.Config
}

// GetProxyCount 获取代理数量
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) GetProxyCount() int {
	p.mu.Lock()
	defer p.mu.Unlock()
	return len(p.Proxies)
}

// GetProxyQuality 获取代理质量分数
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) GetProxyQuality(proxy string) float64 {
	p.qualityMutex.RLock()
	defer p.qualityMutex.RUnlock()

	if info, exists := p.ProxyQuality[proxy]; exists {
		return p.calculateQualityScore(info.SuccessCount, info.FailureCount, info.AvgResponseTime)
	}
	return 50.0 // 默认分数
}

// SetRetryPolicy 设置重试策略
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) SetRetryPolicy(policy string) error {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.retryPolicy = policy
	proxyManagerLogger.GetRawLogger().Infof("重试策略已设置为: %s", policy)
	return nil
}

// GetRetryPolicy 获取重试策略
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) GetRetryPolicy() string {
	p.mu.Lock()
	defer p.mu.Unlock()
	if p.retryPolicy == "" {
		return "default" // 默认策略
	}
	return p.retryPolicy
}

// GetStats 获取代理管理器统计信息
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) GetStats() map[string]interface{} {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 计算活跃的定时域名封禁数量
	activeTimedDomainBans := 0
	for _, banInfo := range p.TimedDomainBans {
		if banInfo.IsActive() {
			activeTimedDomainBans++
		}
	}

	stats := map[string]interface{}{
		"total_proxies":              len(p.Proxies),
		"current_index":              p.CurrentIndex,
		"banned_ips_count":           len(p.PermanentBlockedIPs),
		"timed_domain_bans_count":    activeTimedDomainBans,
		"trusted_ips_count":          len(p.TrustedIPs),
		"smart_mode_enabled":         p.smartModeEnabled,
		"retry_policy":               p.retryPolicy,
		"ban_system_initialized":     p.banSystemInitialized,
	}

	// 添加质量统计
	if p.QualityPools != nil {
		stats["premium_proxies_count"] = len(p.QualityPools.PremiumProxies)
		stats["standard_proxies_count"] = len(p.QualityPools.StandardProxies)
		stats["backup_proxies_count"] = len(p.QualityPools.BackupProxies)
		stats["domain_pools_count"] = len(p.QualityPools.DomainPools)
	}

	return stats
}

// GetUsageStats 获取使用统计信息
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) GetUsageStats() map[string]interface{} {
	p.qualityMutex.RLock()
	defer p.qualityMutex.RUnlock()

	usageStats := map[string]interface{}{
		"total_requests": 0,
		"successful_requests": 0,
		"failed_requests": 0,
		"average_response_time": 0.0,
	}

	var totalRequests, successfulRequests, failedRequests int64
	var totalResponseTime time.Duration

	for _, info := range p.ProxyQuality {
		totalRequests += info.SuccessCount + info.FailureCount
		successfulRequests += info.SuccessCount
		failedRequests += info.FailureCount
		totalResponseTime += info.AvgResponseTime
	}

	usageStats["total_requests"] = totalRequests
	usageStats["successful_requests"] = successfulRequests
	usageStats["failed_requests"] = failedRequests

	if len(p.ProxyQuality) > 0 {
		usageStats["average_response_time"] = float64(totalResponseTime) / float64(len(p.ProxyQuality)) / float64(time.Millisecond)
	}

	return usageStats
}

// Health 检查代理管理器健康状态
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) Health() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if len(p.Proxies) == 0 {
		return errors.New("代理池为空")
	}

	if !p.banSystemInitialized {
		return errors.New("封禁系统未初始化")
	}

	return nil
}

// IsDomainBanned 检查域名是否被封禁
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) IsDomainBanned(domain string) bool {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 检查是否在永久封禁列表中
	if banned, exists := p.PermanentBlockedIPs[domain]; exists && banned {
		return true
	}

	// 检查是否在定时封禁列表中且仍然有效
	if banInfo, exists := p.TimedDomainBans[domain]; exists && banInfo.IsActive() {
		return true
	}

	return false
}

// IsRunning 检查代理管理器是否正在运行
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) IsRunning() bool {
	p.mu.Lock()
	defer p.mu.Unlock()
	return p.banSystemInitialized && len(p.Proxies) > 0
}

// RefreshProxyPool 刷新代理池
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) RefreshProxyPool() error {
	proxyManagerLogger.GetRawLogger().Info("开始刷新代理池")

	// 重新加载代理文件
	if err := p.loadProxiesFromFile(); err != nil {
		proxyManagerLogger.GetRawLogger().Errorf("刷新代理池失败: %v", err)
		return err
	}

	// 重新初始化质量池
	p.qualityMutex.Lock()
	if p.QualityPools == nil {
		p.QualityPools = &ProxyPool{
			PremiumProxies:  []string{},
			StandardProxies: []string{},
			BackupProxies:   []string{},
			DomainPools:     make(map[string][]string),
		}
	}
	p.qualityMutex.Unlock()

	proxyManagerLogger.GetRawLogger().Infof("代理池刷新完成，当前代理数量: %d", len(p.Proxies))
	return nil
}

// ReloadConfig 重新加载配置
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) ReloadConfig() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 重新加载配置逻辑
	proxyManagerLogger.GetRawLogger().Info("代理管理器配置已重新加载")
	return nil
}

// ResetStats 重置统计信息
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) ResetStats() {
	p.qualityMutex.Lock()
	defer p.qualityMutex.Unlock()

	// 重置所有代理的质量信息
	for proxy := range p.ProxyQuality {
		p.ProxyQuality[proxy] = &ProxyQualityInfo{
			SuccessCount:    0,
			FailureCount:    0,
			AvgResponseTime: 0,
			LastUsed:        time.Now(),
			QualityScore:    50.0, // 默认分数
		}
	}

	proxyManagerLogger.GetRawLogger().Info("代理统计信息已重置")
}

// MarkProxyAsFailed 标记代理为失败
func (p *ProxyManager) MarkProxyAsFailed(proxy string) {
	p.mu.Lock()
	defer p.mu.Unlock()

	p.failedProxies[proxy] = time.Now()
	proxyManagerLogger.GetRawLogger().Debugf("代理 %s 已标记为失败", proxy)
}

// isProxyMarkedAsFailed 检查代理是否被标记为失败且仍在TTL内
func (p *ProxyManager) isProxyMarkedAsFailed(proxy string) bool {
	p.mu.Lock()
	defer p.mu.Unlock()

	failedTime, exists := p.failedProxies[proxy]
	if !exists {
		return false
	}

	// 检查是否超过TTL
	if time.Since(failedTime) > p.failedProxyTTL {
		// 超过TTL，移除失败标记
		delete(p.failedProxies, proxy)
		proxyManagerLogger.GetRawLogger().Debugf("代理 %s 失败标记已过期，已移除", proxy)
		return false
	}

	return true
}

// ClearProxyFailureStatus 清除代理的失败状态
func (p *ProxyManager) ClearProxyFailureStatus(proxy string) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if _, exists := p.failedProxies[proxy]; exists {
		delete(p.failedProxies, proxy)
		proxyManagerLogger.GetRawLogger().Debugf("代理 %s 失败状态已清除", proxy)
	}
}

// ClearProxyCacheIfFailed 如果代理失败则清除缓存
func (p *ProxyManager) ClearProxyCacheIfFailed(proxy string) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.proxyCache.proxy == proxy {
		p.proxyCache.proxy = ""
		p.proxyCache.lastUsed = time.Time{}
		proxyManagerLogger.GetRawLogger().Debugf("已清除失败代理 %s 的缓存", proxy)
	}
}

// cleanupExpiredFailedProxies 清理过期的失败代理记录
func (p *ProxyManager) cleanupExpiredFailedProxies() {
	p.mu.Lock()
	defer p.mu.Unlock()

	now := time.Now()
	expiredProxies := make([]string, 0)

	for proxy, failedTime := range p.failedProxies {
		if now.Sub(failedTime) > p.failedProxyTTL {
			expiredProxies = append(expiredProxies, proxy)
		}
	}

	for _, proxy := range expiredProxies {
		delete(p.failedProxies, proxy)
	}

	if len(expiredProxies) > 0 {
		proxyManagerLogger.GetRawLogger().Debugf("清理了 %d 个过期的失败代理记录", len(expiredProxies))
	}
}

// UnbanDomain 解除域名封禁
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) UnbanDomain(domain string) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	removedFromPermanent := false
	removedFromTimed := false

	// 从永久封禁列表中移除
	if _, exists := p.PermanentBlockedIPs[domain]; exists {
		delete(p.PermanentBlockedIPs, domain)
		removedFromPermanent = true
	}

	// 从定时封禁列表中移除
	if _, exists := p.TimedDomainBans[domain]; exists {
		delete(p.TimedDomainBans, domain)
		removedFromTimed = true
	}

	if removedFromPermanent || removedFromTimed {
		banType := ""
		if removedFromPermanent && removedFromTimed {
			banType = "永久和定时"
		} else if removedFromPermanent {
			banType = "永久"
		} else {
			banType = "定时"
		}
		proxyManagerLogger.GetRawLogger().Infof("已解除%s封禁域名: %s", banType, domain)
	} else {
		proxyManagerLogger.GetRawLogger().Warnf("尝试解封未被封禁的域名: %s", domain)
	}

	return nil
}

// UpdateConfig 更新配置
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) UpdateConfig(config interface{}) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if cfg, ok := config.(*configpkg.Config); ok {
		p.Config = cfg
		proxyManagerLogger.GetRawLogger().Info("代理管理器配置已更新")
		return nil
	}

	return errors.New("无效的配置类型")
}

// ValidateProxy 验证代理是否有效
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) ValidateProxy(proxy string) bool {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 检查代理格式是否正确
	if proxy == "" {
		return false
	}

	// 检查是否在代理列表中
	for _, existingProxy := range p.Proxies {
		if existingProxy == proxy {
			return true
		}
	}

	// 检查是否被封禁
	if banned, exists := p.PermanentBlockedIPs[proxy]; exists && banned {
		return false
	}

	return true // 基本验证通过
}



// initSmartSelector 初始化智能选择器
func (p *ProxyManager) initSmartSelector() {
	// 创建智能选择器配置
	config := selector.DefaultSmartSelectorConfig()

	// 根据全局配置调整智能选择器参数
	if p.Config != nil {
		// 可以根据全局配置调整智能选择器的参数
		if p.Config.Global.IPRotationMode == constants.StrategySmart {
			p.smartModeEnabled = true
		}
	}

	// 初始化智能选择器，直接使用原始logger
	p.smartSelector = selector.NewSmartSelector(proxyManagerLogger.GetRawLogger(), config)

	proxyManagerLogger.GetRawLogger().Debugf("智能代理选择器已初始化")
}

// getSmartSelector 获取智能选择器
func (p *ProxyManager) getSmartSelector() *selector.SmartSelector {
	return p.smartSelector
}

// UpdateProxyMetrics 更新代理指标（供外部调用）
func (p *ProxyManager) UpdateProxyMetrics(proxy string, domain string, success bool, responseTime time.Duration) {
	if p.smartSelector != nil {
		p.smartSelector.UpdateMetrics(proxy, domain, success, responseTime)
	}
}


