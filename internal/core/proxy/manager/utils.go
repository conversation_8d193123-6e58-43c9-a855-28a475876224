package manager

import (
	// "fmt" // 注释掉方法后不再直接使用
	"math/rand"

	// "github.com/fsnotify/fsnotify" // 注释掉 Watch 后不再直接使用
	"flexproxy/common/errors"
)

// Count 在 proxymanager.go 中定义
// func (p *ProxyManager) Count() int {
//	p.Length = len(p.Proxies)
//	return p.Length
// }

// NextProxy 导航到下一个要使用的代理
func (p *ProxyManager) NextProxy() (string, error) {
	p.mu.Lock()
	defer p.mu.Unlock()

	var proxy string

	count := p.Count()
	if count <= 0 {
		return proxy, errors.ErrNoProxyLeft
	}

	// 获取最大尝试次数
	maxAttempts := count
	if p.Config != nil && p.Config.Global.MaxProxyFetchAttempts > 0 {
		maxAttempts = p.Config.Global.MaxProxyFetchAttempts
	}

	// 防止无限循环，确保尝试次数不超过代理总数
	if maxAttempts > count {
		maxAttempts = count
	}

	// 记录起始索引，避免循环遍历
	startIndex := p.CurrentIndex
	triedCount := 0

	// 尝试最多maxAttempts次，确保能找到未被封禁的代理
	for triedCount < maxAttempts {
		p.CurrentIndex++
		if p.CurrentIndex > count-1 {
			p.CurrentIndex = 0
		}

		// 如果已经遍历了一圈还没找到可用代理，退出循环
		if p.CurrentIndex == startIndex && triedCount > 0 {
			break
		}

		proxy = p.Proxies[p.CurrentIndex]
		triedCount++

		// 检查代理是否被全局封禁
		if _, banned := p.GlobalBannedIPs[proxy]; !banned {
			return proxy, nil
		}
	}

	// 如果所有代理都被封禁，返回错误
	return "", errors.ErrNoProxyLeft
}

// RandomProxy 从列表中随机选择一个代理
func (p *ProxyManager) RandomProxy() (string, error) {
	p.mu.Lock()
	defer p.mu.Unlock()

	var proxy string

	count := p.Count()
	if count <= 0 {
		return proxy, errors.ErrNoProxyLeft
	}

	// 获取最大尝试次数
	maxAttempts := count
	if p.Config != nil && p.Config.Global.MaxProxyFetchAttempts > 0 {
		maxAttempts = p.Config.Global.MaxProxyFetchAttempts
	}

	// 防止无限循环，确保尝试次数不超过代理总数
	if maxAttempts > count {
		maxAttempts = count
	}

	// 记录已尝试过的代理，避免重复尝试
	triedProxies := make(map[string]bool)

	// 尝试最多maxAttempts次，确保能找到未被封禁的代理
	for i := 0; i < maxAttempts; i++ {
		// 随机选择一个未尝试过的代理
		for {
			proxy = p.Proxies[rand.Intn(count)]
			if !triedProxies[proxy] {
				triedProxies[proxy] = true
				break
			}
			// 如果所有代理都已尝试过，跳出循环
			if len(triedProxies) >= count {
				break
			}
		}

		// 检查代理是否被全局封禁
		if _, banned := p.GlobalBannedIPs[proxy]; !banned {
			return proxy, nil
		}
	}

	// 如果所有代理都被封禁，返回错误
	return "", errors.ErrNoProxyLeft
}

// RemoveProxy 在 proxymanager.go 中定义
// func (p *ProxyManager) RemoveProxy(target string) error {
//	p.mu.Lock()
//	defer p.mu.Unlock()
//
//	for i, v := range p.Proxies {
//		if v == target {
//			p.Proxies = append(p.Proxies[:i], p.Proxies[i+1:]...)
//			p.Length = len(p.Proxies)
//			return nil
//		}
//	}
//	return fmt.Errorf("could not find %q in the proxy pool", target)
// }

// Watch 在 proxymanager.go 中定义
// func (p *ProxyManager) Watch() (*fsnotify.Watcher, error) {
//	watcher, err := fsnotify.NewWatcher()
//	if err != nil {
//		return watcher, err
//	}
//
//	if err := watcher.Add(p.filepath); err != nil {
//		return watcher, err
//	}
//
//	return watcher, nil
// }

// 重新加载代理池 - 这需要重构，因为 New() 现在需要配置。
// 暂时注释掉以解决即时编译问题。
/*
func (p *ProxyManager) Reload() error {
	i := p.CurrentIndex

	// p, err := New(p.filepath) // ERROR: New now requires *config.Config
	// if err != nil {
	// 	return err
	// }
	// p.CurrentIndex = i

	return errors.New("Reload method needs refactoring")
}
*/
