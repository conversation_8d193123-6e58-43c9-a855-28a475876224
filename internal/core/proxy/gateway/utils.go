package gateway

import (
	"net/url"

	"flexproxy/internal/core/validation"
)

// isHTTPURL 检查给定的 URL 字符串是否为 HTTP 或 HTTPS
// 使用项目统一的网络验证器进行URL验证
func isHTTPURL(s string) bool {
	validator := validation.NewNetworkValidator()
	return validator.IsHTTPURL(s)
}

// GetBaseURL 从给定的 URL 字符串返回基础 URL 和解析后的 URL
// 使用项目统一的网络验证器进行URL解析和验证
func GetBaseURL(s string) (string, *url.URL, error) {
	validator := validation.NewNetworkValidator()
	return validator.GetBaseURL(s)
}
