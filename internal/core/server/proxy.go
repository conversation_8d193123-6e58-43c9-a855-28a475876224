package server

import (
	"context"
	"sync"
	"time"

	"github.com/elazarl/goproxy"
	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/internal/config"
	"flexproxy/internal/core/engine/action"
	"flexproxy/internal/core/proxy/gateway"
	"flexproxy/internal/core/engine/trigger"
)

// Proxy as ServeMux in proxy server handler.
type Proxy struct {
	HTTPProxy          *goproxy.ProxyHttpServer
	Options            *config.Options
	Gateways           map[string]*gateway.ProxyGateway
	triggerManager     *trigger.TriggerManager // 触发器管理器
	actionManager      *action.ActionManager   // 动作管理器
	mu                 sync.RWMutex
	dnsResolver        DNSResolver
	reverseDNSResolver *ReverseDNSResolver
	currentProxy       string // 当前使用的代理
	proxyOkCount       int    // 当前代理成功次数

	// 并发访问统计（用于性能监控）
	proxyReadCount  int64 // 代理读取次数
	proxyWriteCount int64 // 代理写入次数
}

// ResetProxyState 重置当前代理状态
func (p *Proxy) ResetProxyState() {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.currentProxy = ""
	p.proxyOkCount = 0
	serverLogger.GetRawLogger().Debugf("Proxy state (currentProxy, proxyOkCount) reset.")
}

// ============================================================================
// DNSConfigProvider接口实现
// 使Proxy能够为DNSManager提供DNS配置信息
// ============================================================================

// GetDNSConfig 实现DNSConfigProvider接口，获取DNS服务配置
func (p *Proxy) GetDNSConfig() (*config.DNSServiceConfig, error) {
	p.mu.RLock()
	defer p.mu.RUnlock()

	if p.Options == nil || p.Options.RuleConfig == nil {
		return nil, errors.NewFlexProxyError(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigNotLoaded,
			"代理配置未初始化",
		)
	}

	if p.Options.RuleConfig.DNSService == nil {
		return nil, errors.NewFlexProxyError(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigNotLoaded,
			"DNS服务配置未找到",
		)
	}

	return p.Options.RuleConfig.DNSService, nil
}

// IsDNSServiceEnabled 实现DNSConfigProvider接口，检查DNS服务是否启用
func (p *Proxy) IsDNSServiceEnabled() bool {
	p.mu.RLock()
	defer p.mu.RUnlock()

	// 检查全局配置是否启用
	if p.Options == nil || p.Options.RuleConfig == nil || !p.Options.RuleConfig.Global.Enable {
		return false
	}

	// 检查DNS服务配置是否存在
	return p.Options.RuleConfig.DNSService != nil
}

// GetDNSTimeout 实现DNSConfigProvider接口，获取DNS超时配置
func (p *Proxy) GetDNSTimeout() time.Duration {
	p.mu.RLock()
	defer p.mu.RUnlock()

	// 从DNS服务配置获取超时时间
	if p.Options != nil && p.Options.RuleConfig != nil && p.Options.RuleConfig.DNSService != nil {
		if p.Options.RuleConfig.DNSService.Timeout != "" {
			if duration, err := time.ParseDuration(p.Options.RuleConfig.DNSService.Timeout); err == nil {
				return duration
			}
		}
	}

	// 返回默认超时时间
	return constants.DefaultDNSTimeout
}

func (p *Proxy) Close() {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	p.mu.Lock()
	defer p.mu.Unlock()

	for _, gateway := range p.Gateways {
		if err := gateway.Close(ctx); err != nil {
			wrappedErr := errors.WrapError(err, errors.ErrTypeProxy, errors.ErrCodeProxyShutdownFailed, "无法关闭网关")
			serverLogger.Error(wrappedErr.Error())
		}
	}
}
