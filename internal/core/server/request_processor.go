package server

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"time"

	"flexproxy/common/constants"
)

// RequestProcessor 请求处理器
// 负责处理HTTP请求的验证、配置检查、上下文设置等功能
type RequestProcessor struct {
	proxy *Proxy
}

// NewRequestProcessor 创建新的请求处理器
func NewRequestProcessor(proxy *Proxy) *RequestProcessor {
	return &RequestProcessor{
		proxy: proxy,
	}
}

// ValidateGlobalConfig 验证全局配置是否启用
// 返回：true表示全局配置已启用，false表示未启用
func (rp *RequestProcessor) ValidateGlobalConfig() bool {
	if rp.proxy.Options.RuleConfig == nil || !rp.proxy.Options.RuleConfig.Global.Enable {
		serverLogger.Debug("全局规则未启用，跳过规则检查")
		return false
	}
	serverLogger.Debug("全局规则已启用，检查请求是否符合规则")
	return true
}

// SetupRequestContext 设置请求上下文
// 为请求添加必要的上下文信息，包括DNS缓存参数、IP版本优先级、开始时间等
// 参数：
//   - req: HTTP请求对象
// 返回：设置了上下文的HTTP请求对象
func (rp *RequestProcessor) SetupRequestContext(req *http.Request) *http.Request {
	reqCtx := req.Context()

	// 设置DNS缓存参数到上下文
	if rp.proxy.Options.RuleConfig != nil && rp.proxy.Options.RuleConfig.Global.Enable {
		// 使用全局标志控制配置日志只输出一次
		globalConfigMutex.Lock()
		_ = !globalConfigLogged // shouldLogConfig 暂时不使用
		if !globalConfigLogged {
			globalConfigLogged = true
		}
		globalConfigMutex.Unlock()

		// 从DNSService配置中获取DNS相关设置
		if dnsConfig, err := rp.proxy.GetDNSConfig(); err == nil && dnsConfig != nil {
			// 设置DNS缓存配置
			if dnsConfig.Cache != nil {
				// 设置缓存启用状态
				reqCtx = context.WithValue(reqCtx, constants.ContextKeyDNSCacheEnabled, dnsConfig.Cache.Enabled)

				// 只有在缓存启用时才设置TTL
				if dnsConfig.Cache.Enabled && dnsConfig.Cache.TTL != "" {
					if duration, err := time.ParseDuration(dnsConfig.Cache.TTL); err == nil {
						reqCtx = context.WithValue(reqCtx, constants.ContextKeyDNSCacheTTL, duration)
					}
				}
			} else {
				// 如果没有缓存配置，使用默认值
				reqCtx = context.WithValue(reqCtx, constants.ContextKeyDNSCacheEnabled, constants.DefaultDNSCacheEnabled)
			}

			// 设置IP版本优先级
			if dnsConfig.IPVersionPriority != "" {
				reqCtx = context.WithValue(reqCtx, constants.ContextKeyIPVersionPriority, dnsConfig.IPVersionPriority)
			} else {
				reqCtx = context.WithValue(reqCtx, constants.ContextKeyIPVersionPriority, "ipv4")
			}
		} else {
			// 配置获取失败时使用默认值
			reqCtx = context.WithValue(reqCtx, constants.ContextKeyDNSCacheEnabled, constants.DefaultDNSCacheEnabled)
			reqCtx = context.WithValue(reqCtx, constants.ContextKeyIPVersionPriority, "ipv4")
		}
	}

	// 添加请求开始时间到上下文
	reqCtx = context.WithValue(reqCtx, constants.ContextKeyStartTime, time.Now())

	return req.WithContext(reqCtx)
}

// CheckExclusionRules 检查排除规则
// 检查请求URL是否匹配配置的排除规则
// 参数：
//   - req: HTTP请求对象
// 返回：如果请求被排除则返回相应的HTTP响应，否则返回nil
func (rp *RequestProcessor) CheckExclusionRules(req *http.Request) *http.Response {
	if rp.proxy.Options.RuleConfig != nil && rp.proxy.Options.RuleConfig.Global.Enable {
		urlString := req.URL.String()
		serverLogger.GetRawLogger().Debugf("检查排除规则: URL:%s, 规则数量:%d, 范围:%s", 
			urlString, 
			len(rp.proxy.Options.RuleConfig.Global.ExcludedPatterns), 
			rp.proxy.Options.RuleConfig.Global.ExcludedScope)
		
		if isExcluded(urlString, rp.proxy.Options.RuleConfig.Global.ExcludedPatterns, rp.proxy.Options.RuleConfig.Global.ExcludedScope) {
			serverLogger.GetRawLogger().Infof("%s URL被排除规则阻止: %s", req.RemoteAddr, urlString)
			
			// 使用响应构建器创建排除响应
			responseBuilder := NewResponseBuilder()
			return responseBuilder.CreateExcludedResponse(req)
		}
	}
	return nil
}

// CheckIPBans 检查IP封禁状态
// 检查请求的域名或IP是否被永久封禁
// 参数：
//   - req: HTTP请求对象
// 返回：如果IP被封禁则返回相应的HTTP响应，否则返回nil
func (rp *RequestProcessor) CheckIPBans(req *http.Request) *http.Response {
	if rp.proxy.Options.RuleConfig != nil && rp.proxy.Options.RuleConfig.Global.Enable {
		domain := req.URL.Hostname()
		responseBuilder := NewResponseBuilder()
		
		// 检查域名是否被永久封禁
		if rp.proxy.Options.ProxyManager.IsIPPermanentlyBlocked(domain) {
			serverLogger.GetRawLogger().Warnf("域名 %s 被永久封禁，直接丢弃请求", domain)
			return responseBuilder.CreateProxyBlockedResponse(req)
		}
		
		// 检查IP是否被永久封禁
		ip := net.ParseIP(domain)
		if ip != nil && rp.proxy.Options.ProxyManager.IsIPPermanentlyBlocked(ip.String()) {
			serverLogger.GetRawLogger().Warnf("IP %s 被永久封禁，直接丢弃请求", ip)
			return responseBuilder.CreateProxyBlockedResponse(req)
		}
	}
	return nil
}

// ValidateURLScheme 验证URL协议
// 检查请求URL的协议是否为支持的HTTP或HTTPS
// 参数：
//   - req: HTTP请求对象
// 返回：如果协议不支持则返回错误响应，否则返回nil
func (rp *RequestProcessor) ValidateURLScheme(req *http.Request) *http.Response {
	if (req.URL.Scheme != "http") && (req.URL.Scheme != "https") {
		return serverErr(req)
	}
	return nil
}

// ProcessSyncMode 处理同步模式
// 如果启用了同步模式，则获取互斥锁
// 返回：解锁函数，调用者需要在适当时候调用此函数释放锁
func (rp *RequestProcessor) ProcessSyncMode() func() {
	if rp.proxy.Options.Sync {
		mutex.Lock()
		return func() {
			mutex.Unlock()
		}
	}
	return func() {} // 返回空函数，避免nil检查
}

// LogRequestEntry 记录请求入口信息
// 记录进入onRequest时的配置状态，用于调试
// 参数：
//   - req: HTTP请求对象
func (rp *RequestProcessor) LogRequestEntry(req *http.Request) {
	// 打印进入 onRequest 时 RuleConfig 和 Global.Enable 的状态
	if rp.proxy.Options.RuleConfig != nil {
		serverLogger.Debug(fmt.Sprintf("[onRequest入口] p.Options.RuleConfig.Global.Enable 的值为: %v", 
			rp.proxy.Options.RuleConfig.Global.Enable))
	} else {
		serverLogger.Debug("[onRequest入口] p.Options.RuleConfig 为 nil")
	}
}

// ProcessRequest 处理请求的完整流程
// 这是一个便捷方法，按顺序执行所有请求处理步骤
// 参数：
//   - req: HTTP请求对象
// 返回：处理后的请求对象和可能的响应对象（如果需要提前返回）
func (rp *RequestProcessor) ProcessRequest(req *http.Request) (*http.Request, *http.Response) {
	// 1. 记录请求入口信息
	rp.LogRequestEntry(req)

	// 2. 验证全局配置
	if !rp.ValidateGlobalConfig() {
		// 全局配置未启用，继续基本处理流程
	}

	// 3. 设置请求上下文
	req = rp.SetupRequestContext(req)

	// 4. 检查排除规则
	if resp := rp.CheckExclusionRules(req); resp != nil {
		return req, resp
	}

	// 5. 验证URL协议
	if resp := rp.ValidateURLScheme(req); resp != nil {
		return req, resp
	}

	// 6. 检查IP封禁状态
	if resp := rp.CheckIPBans(req); resp != nil {
		return req, resp
	}

	return req, nil
}
