package server

import (
	"context"
	"fmt"
	"net/http"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
)

// ProxyManager 代理管理器
// 负责代理选择、验证、执行等功能
type ProxyManager struct {
	proxy *Proxy
}

// NewProxyManager 创建新的代理管理器
func NewProxyManager(proxy *Proxy) *ProxyManager {
	return &ProxyManager{
		proxy: proxy,
	}
}

// SelectAndValidateProxy 选择并验证代理
// 从代理池中选择合适的代理，并验证其是否可用
// 参数：
//   - req: HTTP请求对象
//   - triedCount: 已尝试的代理数量
// 返回：选中的代理地址、更新后的尝试计数、可能的错误
func (pm *ProxyManager) SelectAndValidateProxy(req *http.Request, triedCount int) (string, int, error) {
	domain := req.URL.Hostname()
	url := req.URL.String()

	// 检查上下文中是否已有代理信息
	proxyFromCtx := req.Context().Value(constants.ContextKeyCurrentProxy)
	var proxy string
	var rotateErr error

	if proxyFromCtx != nil {
		// 使用上下文中的代理
		proxy = proxyFromCtx.(string)
		serverLogger.GetRawLogger().Debugf("从上下文获取代理: %s", proxy)
	} else {
		// 获取新代理并存储到上下文
		proxy, rotateErr = pm.proxy.rotateProxy(domain, url)
		if rotateErr != nil || proxy == "" {
			return "", triedCount, errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理池无可用代理")
		}
		serverLogger.GetRawLogger().Debugf("将代理 %s 存储到请求上下文", proxy)
	}

	// 检查是否还有可用代理，避免死循环
	availableProxies := pm.proxy.Options.ProxyManager.Count()
	if availableProxies <= 0 {
		serverLogger.GetRawLogger().Warnf("代理池中没有可用代理，放弃请求")
		return "", triedCount, errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理池中没有可用代理")
	}

	// 检查URL级别的IP封禁
	if pm.proxy.Options.RuleConfig != nil && pm.proxy.Options.RuleConfig.Global.Enable {
		if pm.proxy.Options.ProxyManager.IsIPBanned(proxy, url, "url") {
			serverLogger.GetRawLogger().Debugf("禁止 URL %s 使用代理 %s ", url, proxy)
			// 记录已尝试的代理数量，避免死循环
			triedCount++
			if triedCount >= availableProxies {
				serverLogger.GetRawLogger().Warnf("所有代理都被URL %s 封禁，放弃请求", url)
				return "", triedCount, errors.NewError(errors.ErrTypeProxy, errors.ErrCodeProxyBanned, "所有代理都被URL封禁")
			}
			return "", triedCount, errors.NewError(errors.ErrTypeProxy, errors.ErrCodeProxyBanned, "代理被URL封禁，需要重试")
		}
	}

	// 检查域名级别的IP封禁
	if pm.proxy.Options.RuleConfig != nil && pm.proxy.Options.RuleConfig.Global.Enable {
		if pm.proxy.Options.ProxyManager.IsIPBanned(proxy, domain, "domain") {
			serverLogger.GetRawLogger().Debugf("禁止 %s 域名使用代理 %s ", domain, proxy)
			// 记录已尝试的代理数量，避免死循环
			triedCount++
			if triedCount >= availableProxies {
				serverLogger.GetRawLogger().Warnf("所有代理都被域名 %s 封禁，放弃请求", domain)
				return "", triedCount, errors.NewError(errors.ErrTypeProxy, errors.ErrCodeProxyBanned, "所有代理都被域名封禁")
			}
			return "", triedCount, errors.NewError(errors.ErrTypeProxy, errors.ErrCodeProxyBanned, "代理被域名封禁，需要重试")
		}
	}

	return proxy, triedCount, nil
}

// ExecuteRequestWithProxy 使用代理执行请求
// 使用指定的代理发送HTTP请求，包含重试和错误处理逻辑
// 参数：
//   - req: HTTP请求对象
//   - proxy: 代理地址
// 返回：HTTP响应对象和可能的错误
func (pm *ProxyManager) ExecuteRequestWithProxy(req *http.Request, proxy string) (*http.Response, error) {
	domain := req.URL.Hostname()
	
	// 尝试使用当前代理发送请求，如果失败则重试其他代理
	resp, err := pm.proxy.tryProxyRequest(req, domain, proxy)
	if err != nil {
		// 检查是否启用了代理连接重试
		if pm.proxy.Options.RuleConfig != nil && pm.proxy.Options.RuleConfig.Global.ProxyConnectionRetryEnabled {
			errorType := classifyProxyError(err)
			serverLogger.GetRawLogger().Warnf("代理 %s 连接失败 [%s]，启动重试机制: %v", proxy, errorType, err)

			// 标记当前代理为失败
			pm.proxy.Options.ProxyManager.MarkProxyAsFailed(proxy)
			serverLogger.GetRawLogger().Debugf("已标记代理 %s 为失败状态", proxy)

			// 如果使用Smart模式，清除缓存
			if pm.proxy.Options.RuleConfig.Global.IPRotationMode == "smart" {
				pm.proxy.Options.ProxyManager.ClearProxyCacheIfFailed(proxy)
				pm.proxy.clearCurrentProxy()
				serverLogger.GetRawLogger().Debugf("Smart模式：已清除失败代理 %s 的缓存", proxy)
			}

			// 尝试重试其他代理
			retryResp := pm.proxy.retryWithOtherProxies(req, domain, proxy, err)
			if retryResp != nil {
				return retryResp, nil
			}

			serverLogger.GetRawLogger().Warnf("代理连接重试全部失败，返回原始错误")
		} else {
			serverLogger.GetRawLogger().Debugf("代理连接重试未启用，直接返回错误: %v", err)
		}

		// 如果重试失败或未启用重试，返回原始错误
		return nil, err
	}

	return resp, nil
}

// StoreProxyInContext 将代理信息存储到请求上下文中
// 参数：
//   - req: HTTP请求对象
//   - proxy: 代理地址
// 返回：更新了上下文的HTTP请求对象
func (pm *ProxyManager) StoreProxyInContext(req *http.Request, proxy string) *http.Request {
	return req.WithContext(context.WithValue(req.Context(), constants.ContextKeyCurrentProxy, proxy))
}

// IsProxyRetryError 检查是否是需要重试的代理错误
// 参数：
//   - err: 错误对象
// 返回：true表示需要重试，false表示不需要重试
func (pm *ProxyManager) IsProxyRetryError(err error) bool {
	if err == nil {
		return false
	}
	
	errMsg := err.Error()
	return errMsg == "代理被URL封禁，需要重试" || errMsg == "代理被域名封禁，需要重试"
}

// GetProxyPoolStatus 获取代理池状态信息
// 返回：代理池的状态信息
func (pm *ProxyManager) GetProxyPoolStatus() map[string]interface{} {
	status := make(map[string]interface{})
	
	if pm.proxy.Options.ProxyManager != nil {
		status["total_proxies"] = pm.proxy.Options.ProxyManager.Count()
		status["current_proxy"] = pm.proxy.currentProxy
		status["proxy_ok_count"] = pm.proxy.proxyOkCount
	}
	
	// 添加配置信息
	if pm.proxy.Options.RuleConfig != nil && pm.proxy.Options.RuleConfig.Global.Enable {
		status["ip_rotation_mode"] = pm.proxy.Options.RuleConfig.Global.IPRotationMode
		status["max_proxy_fetch_attempts"] = pm.proxy.Options.RuleConfig.Global.MaxProxyFetchAttempts
		status["min_proxy_pool_size"] = pm.proxy.Options.RuleConfig.Global.MinProxyPoolSize
		status["proxy_connection_retry_enabled"] = pm.proxy.Options.RuleConfig.Global.ProxyConnectionRetryEnabled
	}
	
	return status
}

// ValidateProxyPool 验证代理池状态
// 检查代理池是否满足最小要求
// 返回：验证结果和错误信息
func (pm *ProxyManager) ValidateProxyPool() error {
	if pm.proxy.Options.ProxyManager == nil {
		return errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理管理器未初始化")
	}
	
	totalProxies := pm.proxy.Options.ProxyManager.Count()
	if totalProxies <= 0 {
		return errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理池为空")
	}
	
	// 检查最小代理池大小要求
	minPool := 1
	if pm.proxy.Options.RuleConfig != nil && pm.proxy.Options.RuleConfig.Global.MinProxyPoolSize > 0 {
		minPool = pm.proxy.Options.RuleConfig.Global.MinProxyPoolSize
	}
	
	if totalProxies < minPool {
		return errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable,
			fmt.Sprintf("代理池数量不足，当前: %d，最小要求: %d", totalProxies, minPool))
	}
	
	return nil
}
