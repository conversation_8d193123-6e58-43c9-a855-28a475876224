package server

import (
	"bytes"
	"io"
	"net/http"

	"flexproxy/common/constants"
	"flexproxy/pkg/flexproxy"
)

// ResponseBuilder 响应构建器
// 提供统一的HTTP响应创建和处理功能
type ResponseBuilder struct{}

// NewResponseBuilder 创建新的响应构建器
func NewResponseBuilder() *ResponseBuilder {
	return &ResponseBuilder{}
}

// CreateErrorResponse 创建统一的错误响应
// 参数：
//   - req: HTTP请求对象
//   - statusCode: HTTP状态码
//   - statusMessage: HTTP状态消息
//   - body: 响应体内容
//   - headerKey: 要设置的特殊头部键名（可为空）
// 返回：构建好的HTTP响应对象
func (rb *ResponseBuilder) CreateErrorResponse(req *http.Request, statusCode int, statusMessage, body, headerKey string) *http.Response {
	resp := &http.Response{
		StatusCode: statusCode,
		Status:     statusMessage,
		Body:       io.NopCloser(bytes.NewBufferString(body)),
		Header:     make(http.Header),
		Request:    req,
	}
	if headerKey != "" {
		resp.Header.Set(headerKey, "1")
	}
	return resp
}

// CreateSuccessResponse 创建统一的成功响应
// 参数：
//   - req: HTTP请求对象
//   - body: 响应体内容
// 返回：构建好的HTTP响应对象
func (rb *ResponseBuilder) CreateSuccessResponse(req *http.Request, body string) *http.Response {
	return &http.Response{
		StatusCode: constants.HTTPStatusOK,
		Status:     constants.HTTPStatusOKMessage,
		Body:       io.NopCloser(bytes.NewBufferString(body)),
		Header:     make(http.Header),
		Request:    req,
	}
}

// CreateNullResponse 创建空响应
// 参数：
//   - req: HTTP请求对象
// 返回：构建好的空HTTP响应对象
func (rb *ResponseBuilder) CreateNullResponse(req *http.Request) *http.Response {
	return &http.Response{
		StatusCode: constants.HTTPStatusOK,
		Status:     constants.HTTPStatusOKMessage,
		Body:       http.NoBody,
		Request:    req,
		Header:     make(http.Header),
	}
}

// CleanHopByHopHeaders 清理hop-by-hop headers
// 这些头部不应该被代理转发到下游服务器
// 参数：
//   - resp: HTTP响应对象
func (rb *ResponseBuilder) CleanHopByHopHeaders(resp *http.Response) {
	for _, h := range flexproxy.HopHeaders {
		resp.Header.Del(h)
	}
}

// CreateProxyBlockedResponse 创建代理被阻止的响应
// 参数：
//   - req: HTTP请求对象
// 返回：构建好的阻止响应对象
func (rb *ResponseBuilder) CreateProxyBlockedResponse(req *http.Request) *http.Response {
	return rb.CreateErrorResponse(
		req,
		constants.HTTPStatusForbidden,
		constants.HTTPStatusForbiddenMessage,
		constants.MessageProxyBlocked,
		constants.HeaderXFlexProxyBlocked,
	)
}

// CreateProxyPoolExhaustedResponse 创建代理池耗尽的响应
// 参数：
//   - req: HTTP请求对象
// 返回：构建好的代理池耗尽响应对象
func (rb *ResponseBuilder) CreateProxyPoolExhaustedResponse(req *http.Request) *http.Response {
	return rb.CreateErrorResponse(
		req,
		constants.HTTPStatusServiceUnavailable,
		constants.HTTPStatusServiceUnavailableMessage,
		constants.MessageProxyPoolExhausted,
		constants.HeaderXFlexProxyProxyPool,
	)
}

// CreateProxyConnectionFailedResponse 创建代理连接失败的响应
// 参数：
//   - req: HTTP请求对象
//   - errorMsg: 错误消息
//   - errorType: 错误类型
// 返回：构建好的连接失败响应对象
func (rb *ResponseBuilder) CreateProxyConnectionFailedResponse(req *http.Request, errorMsg, errorType string) *http.Response {
	resp := rb.CreateErrorResponse(
		req,
		constants.HTTPStatusBadGateway,
		constants.HTTPStatusBadGatewayMessage,
		constants.MessageProxyConnectionFailed+": "+errorMsg,
		"",
	)
	resp.Header.Set(constants.HeaderXFlexProxyErrorType, errorType)
	return resp
}

// CreateExcludedResponse 创建被排除的响应
// 参数：
//   - req: HTTP请求对象
// 返回：构建好的排除响应对象
func (rb *ResponseBuilder) CreateExcludedResponse(req *http.Request) *http.Response {
	resp := rb.CreateErrorResponse(
		req,
		constants.HTTPStatusOK,
		constants.HTTPStatusOKMessage,
		"",
		constants.HeaderXFlexProxyExcluded,
	)
	rb.CleanHopByHopHeaders(resp)
	return resp
}

// ProcessSpecialResponseHeaders 处理特殊响应头
// 检查并处理FlexProxy的特殊响应头，如排除、阻止、临时封禁等
// 参数：
//   - resp: HTTP响应对象
// 返回：处理后的响应对象，如果需要特殊处理则返回处理后的响应，否则返回nil
func (rb *ResponseBuilder) ProcessSpecialResponseHeaders(resp *http.Response) *http.Response {
	if resp == nil {
		return nil
	}

	// 处理被排除的请求响应
	if resp.Header.Get(constants.HeaderXFlexProxyExcluded) == "1" {
		serverLogger.GetRawLogger().Debugf("处理被排除的请求响应")
		rb.CleanHopByHopHeaders(resp)
		resp.Header.Del(constants.HeaderXFlexProxyExcluded)
		return resp
	}

	// 处理被阻止的请求响应
	if resp.Header.Get(constants.HeaderXFlexProxyBlocked) == "1" {
		serverLogger.GetRawLogger().Debugf("处理被阻止的请求响应")
		rb.CleanHopByHopHeaders(resp)
		resp.Header.Del(constants.HeaderXFlexProxyBlocked)
		return resp
	}

	// 处理临时封禁的请求响应
	if resp.Header.Get(constants.HeaderXFlexProxyTempBanned) == "1" {
		serverLogger.GetRawLogger().Debugf("处理临时封禁的请求响应")
		rb.CleanHopByHopHeaders(resp)
		resp.Header.Del(constants.HeaderXFlexProxyTempBanned)
		return resp
	}

	// 处理代理池耗尽的请求响应
	if resp.Header.Get(constants.HeaderXFlexProxyProxyPool) == "exhausted" {
		serverLogger.GetRawLogger().Debugf("处理代理池耗尽的请求响应")
		rb.CleanHopByHopHeaders(resp)
		resp.Header.Del(constants.HeaderXFlexProxyProxyPool)
		return resp
	}

	return nil
}
