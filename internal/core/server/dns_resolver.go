package server

import (
	"context"
	"net"
	"net/http"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/internal/config"
)

// DNSResolver接口已在dns.go中定义

// DNSConfigProvider DNS配置提供者接口
// 遵循依赖倒置原则，DNSManager只依赖此接口而非具体实现
type DNSConfigProvider interface {
	// GetDNSConfig 获取DNS服务配置
	GetDNSConfig() (*config.DNSServiceConfig, error)

	// IsDNSServiceEnabled 检查DNS服务是否启用
	IsDNSServiceEnabled() bool

	// GetDNSTimeout 获取DNS超时配置
	GetDNSTimeout() time.Duration
}

// DNSManager DNS管理器
// 负责DNS解析相关的功能
// 使用依赖注入模式，降低与Proxy的耦合度
type DNSManager struct {
	configProvider DNSConfigProvider // DNS配置提供者
	proxy          *Proxy            // 保留proxy引用用于其他功能
}

// NewDNSManager 创建新的DNS管理器
// 使用依赖注入模式，接受配置提供者接口
func NewDNSManager(configProvider DNSConfigProvider, proxy *Proxy) *DNSManager {
	return &DNSManager{
		configProvider: configProvider,
		proxy:          proxy,
	}
}

// NewDNSManagerWithProxy 兼容性构造函数（向后兼容）
// 从Proxy中提取配置提供者，逐步迁移到NewDNSManager
func NewDNSManagerWithProxy(proxy *Proxy) *DNSManager {
	return &DNSManager{
		configProvider: proxy, // Proxy需要实现DNSConfigProvider接口
		proxy:          proxy,
	}
}

// getDNSConfig 获取DNS配置的内部方法
// 包含错误处理和默认值回退逻辑
func (dm *DNSManager) getDNSConfig() (*config.DNSServiceConfig, error) {
	if dm.configProvider == nil {
		return nil, errors.NewFlexProxyError(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigNotLoaded,
			"DNS配置提供者未初始化",
		)
	}

	dnsConfig, err := dm.configProvider.GetDNSConfig()
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeConfig, errors.ErrCodeConfigNotLoaded, "获取DNS配置失败")
	}

	return dnsConfig, nil
}

// getDNSMode 获取DNS解析模式，包含默认值处理
func (dm *DNSManager) getDNSMode() string {
	dnsConfig, err := dm.getDNSConfig()
	if err != nil {
		return constants.DNSModeLocal // 默认值
	}

	// 使用LookupMode字段
	if dnsConfig.LookupMode != "" {
		return dnsConfig.LookupMode
	}

	return constants.DNSModeLocal // 默认值
}

// getCustomDNSServers 获取自定义DNS服务器列表
func (dm *DNSManager) getCustomDNSServers() []string {
	dnsConfig, err := dm.getDNSConfig()
	if err != nil {
		return []string{} // 默认为空
	}

	return dnsConfig.CustomDNSServers
}

// getDNSCacheConfig 获取DNS缓存配置
func (dm *DNSManager) getDNSCacheConfig() (bool, time.Duration) {
	dnsConfig, err := dm.getDNSConfig()
	if err != nil {
		return true, time.Duration(constants.DefaultDNSCacheTTLSeconds) * time.Second // 默认启用缓存，5分钟TTL
	}

	// 检查是否禁用缓存
	if dnsConfig.DNSNoCache {
		return false, 0
	}

	// 获取缓存TTL
	var cacheTTL time.Duration
	if dnsConfig.Cache != nil && dnsConfig.Cache.TTL != "" {
		if duration, err := time.ParseDuration(dnsConfig.Cache.TTL); err == nil {
			cacheTTL = duration
		} else {
			cacheTTL = time.Duration(constants.DefaultDNSCacheTTLSeconds) * time.Second
		}
	} else {
		cacheTTL = time.Duration(constants.DefaultDNSCacheTTLSeconds) * time.Second
	}

	return true, cacheTTL
}

// SetupDNSResolution 设置DNS解析
// 为请求设置DNS解析相关的上下文信息，并进行预解析
// 参数：
//   - req: HTTP请求对象
// 返回：设置了DNS解析信息的HTTP请求对象
func (dm *DNSManager) SetupDNSResolution(req *http.Request) *http.Request {
	// 获取请求的实际域名，用于DNS解析
	requestHost := req.URL.Hostname()

	// 检查是否是IP地址，如果是则跳过DNS解析
	isIP := false
	if net.ParseIP(requestHost) != nil {
		serverLogger.GetRawLogger().Debugf("主机名 %s 已经是IP地址，跳过DNS解析", requestHost)
		isIP = true
	}

	// 创建DNS解析器并预先解析域名
	if !isIP && dm.configProvider.IsDNSServiceEnabled() {
		//从DNSService配置中获取DNS相关设置
		dnsMode := dm.getDNSMode()
		customDNSServers := dm.getCustomDNSServers()
		cacheEnabled, cacheTTL := dm.getDNSCacheConfig()

		// 创建DNS上下文
		dnsCtx := req.Context()

		// 记录DNS配置信息
		serverLogger.GetRawLogger().Debugf("DNS解析配置: mode=%s, cache_enabled=%v, cache_ttl=%v, custom_servers=%v",
			dnsMode, cacheEnabled, cacheTTL, customDNSServers)
		dnsCtx = context.WithValue(dnsCtx, constants.ContextKeyDNSCacheTTL, cacheTTL)
		// 将DNS配置提供者添加到上下文中，供getDefaultDNSTimeout使用
		dnsCtx = context.WithValue(dnsCtx, "dns_config_provider", dm.configProvider)
		// 设置默认DNS超时时间
		dnsTimeout := time.Duration(constants.DefaultDNSTimeoutSeconds) * time.Second // 默认超时
		dnsCtx = context.WithValue(dnsCtx, constants.ContextKeyDNSTimeout, dnsTimeout)
		dnsCtx = context.WithValue(dnsCtx, constants.ContextKeyDNSCacheEnabled, true) // 默认启用缓存

		// 检查DNS缓存中是否已有结果
		dnsCacheLock.RLock()
		cached, exists := dnsCache[requestHost]
		dnsCacheLock.RUnlock()

		// 如果缓存中没有结果或已过期，则创建解析器并解析
		if !exists || time.Now().After(cached.ExpireAt) {
			// 根据配置创建DNS解析器
			var dnsResolver DNSResolver
			if dnsMode == "remote" {
				// 获取代理用于远程DNS解析
				proxy, err := dm.proxy.rotateProxy()
				if err == nil && proxy != "" {
					dnsResolver = NewRemoteDNSResolver(proxy, "http") // 使用HTTP代理模式
				}
			} else if len(customDNSServers) > 0 { // 检查切片是否有元素
				// Use the first custom DNS server's address
				firstCustomDNSServer := customDNSServers[0]
				customDNSResolver := NewCustomDNSResolver(firstCustomDNSServer) // customDNSServers是[]string类型

				// 设置DNS缓存参数
				customDNSResolver.NoCache = false                                                                   // 默认启用缓存
				customDNSResolver.CacheTTL = time.Duration(constants.DefaultDNSCacheTTLSeconds) * time.Second // 默认5分钟缓存

				dnsResolver = customDNSResolver
			} else {
				dnsResolver = &LocalDNSResolver{}
			}

			// 如果创建了DNS解析器，预先解析域名并存储结果
			if dnsResolver != nil {
				serverLogger.GetRawLogger().Debugf("预先解析域名 %s 并存入缓存", requestHost)
				ips, err := dnsResolver.LookupIP(dnsCtx, requestHost)
				if err == nil && len(ips) > 0 {
					// 将DNS解析结果存储到上下文中
					req = req.WithContext(context.WithValue(req.Context(), constants.ContextKeyDNSResult+requestHost, ips))
					// 将DNS解析器存储到上下文中
					req = req.WithContext(context.WithValue(req.Context(), constants.ContextKeyDNSResolver, dnsResolver))
					serverLogger.GetRawLogger().Debugf("成功预解析域名 %s，找到 %d 个IP地址", requestHost, len(ips))
				} else if err != nil {
					serverLogger.GetRawLogger().Warnf("预解析域名 %s 失败: %v", requestHost, err)
				}
			}
		} else {
			serverLogger.GetRawLogger().Debugf("域名 %s 已在DNS缓存中，跳过预解析", requestHost)
			// 从缓存中获取结果并存入上下文
			req = req.WithContext(context.WithValue(req.Context(), constants.ContextKeyDNSResult+requestHost, cached.IPs))
			// 确保DNS解析器也存储到上下文中
			if dm.proxy.dnsResolver != nil {
				req = req.WithContext(context.WithValue(req.Context(), constants.ContextKeyDNSResolver, dm.proxy.dnsResolver))
			}
		}
	}

	return req
}

// CreateDNSResolverForEvent 为事件创建DNS解析器
// 根据请求上下文和配置创建合适的DNS解析器
// 参数：
//   - req: HTTP请求对象
// 返回：创建的DNS解析器
func (dm *DNSManager) CreateDNSResolverForEvent(req *http.Request) DNSResolver {
	// 从DNSService配置中获取DNS相关设置
	dnsMode := dm.getDNSMode()
	customDNSServers := dm.getCustomDNSServers()
	cacheEnabled, cacheTTL := dm.getDNSCacheConfig()
	noCache := !cacheEnabled // 转换为noCache标志

	// 记录DNS解析器创建信息
	serverLogger.GetRawLogger().Debugf("为事件创建DNS解析器: mode=%s, cache_enabled=%v, cache_ttl=%v, custom_servers=%v",
		dnsMode, cacheEnabled, cacheTTL, customDNSServers)

	// 检查是否启用DNS缓存
	if ctxVal := req.Context().Value(constants.ContextKeyDNSCacheEnabled); ctxVal != nil {
		if enabled, ok := ctxVal.(bool); ok && !enabled {
			noCache = true
			serverLogger.Debug("从上下文中检测到DNS缓存已禁用")
		}
	}

	// 获取DNS缓存TTL
	if ctxVal := req.Context().Value(constants.ContextKeyDNSCacheTTL); ctxVal != nil {
		if ttl, ok := ctxVal.(time.Duration); ok && ttl > 0 {
			cacheTTL = ttl
			serverLogger.GetRawLogger().Debugf("从上下文中获取DNS缓存TTL: %s", cacheTTL)
		}
	}

	// 检查是否强制刷新DNS缓存
	if ctxVal := req.Context().Value(constants.ContextKeyDNSForceRefresh); ctxVal != nil {
		if refresh, ok := ctxVal.(bool); ok && refresh {
			serverLogger.Debug("从上下文中检测到强制刷新DNS缓存标志")
		}
	}

	// 根据配置创建DNS解析器
	var dnsResolver DNSResolver

	switch dnsMode {
	case "remote":
		// 获取代理用于远程DNS解析
		proxy, err := dm.proxy.rotateProxy()
		if err == nil && proxy != "" {
			dnsResolver = NewRemoteDNSResolver(proxy, "http")
		} else {
			// 如果获取代理失败，回退到本地DNS
			dnsResolver = &LocalDNSResolver{}
		}
	case "custom":
		if len(customDNSServers) > 0 {
			// 使用第一个自定义DNS服务器
			customDNSResolver := NewCustomDNSResolver(customDNSServers[0])
			customDNSResolver.NoCache = noCache
			customDNSResolver.CacheTTL = cacheTTL
			dnsResolver = customDNSResolver
		} else {
			// 如果没有自定义DNS服务器，回退到本地DNS
			dnsResolver = &LocalDNSResolver{}
		}
	default:
		// 默认使用本地DNS解析器
		dnsResolver = &LocalDNSResolver{}
	}

	return dnsResolver
}

// GetDNSCacheStatus 获取DNS缓存状态
// 返回：DNS缓存的状态信息
func (dm *DNSManager) GetDNSCacheStatus() map[string]interface{} {
	status := make(map[string]interface{})

	dnsCacheLock.RLock()
	defer dnsCacheLock.RUnlock()

	status["cache_size"] = len(dnsCache)
	status["cache_entries"] = make([]map[string]interface{}, 0, len(dnsCache))

	for host, cached := range dnsCache {
		entry := map[string]interface{}{
			"host":      host,
			"ips":       cached.IPs,
			"expire_at": cached.ExpireAt,
			"expired":   time.Now().After(cached.ExpireAt),
		}
		status["cache_entries"] = append(status["cache_entries"].([]map[string]interface{}), entry)
	}

	return status
}

// ClearDNSCache 清理DNS缓存
// 清理过期的DNS缓存条目
// 返回：清理的条目数量
func (dm *DNSManager) ClearDNSCache() int {
	dnsCacheLock.Lock()
	defer dnsCacheLock.Unlock()

	now := time.Now()
	clearedCount := 0

	for host, cached := range dnsCache {
		if now.After(cached.ExpireAt) {
			delete(dnsCache, host)
			clearedCount++
		}
	}

	if clearedCount > 0 {
		serverLogger.GetRawLogger().Debugf("清理了 %d 个过期的DNS缓存条目", clearedCount)
	}

	return clearedCount
}

// IsIPAddress 检查字符串是否为IP地址
// 参数：
//   - host: 主机名或IP地址字符串
// 返回：true表示是IP地址，false表示是域名
func (dm *DNSManager) IsIPAddress(host string) bool {
	return net.ParseIP(host) != nil
}
