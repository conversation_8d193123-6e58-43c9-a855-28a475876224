package server

import (
	"net/http"
	"sync"
	"time"

	"flexproxy/common/constants"
	"flexproxy/common/logger"
	"github.com/henvic/httpretty"
)

var (
	handler      *Proxy
	server       *http.Server
	dump         *httpretty.Logger
	mime         = constants.MimeTextPlain
	log          logger.Logger
	ok           int64 = 1
	currentProxy string
	mutex        = sync.Mutex{}
	// 用于控制全局配置日志只输出一次
	globalConfigLogged bool = false
	globalConfigMutex  sync.Mutex
)

// 初始化日志器
func init() {
	// 不再需要初始化log变量，直接使用logger.ServerXXX()函数
}

// GlobalProxyUsage 全局代理使用跟踪
type GlobalProxyUsage struct {
	usedProxies map[string]time.Time // 代理IP -> 最后使用时间
	cooldown    time.Duration        // 冷却时间
	enabled     bool                 // 是否启用全局跟踪
	mu          sync.RWMutex
}

// 全局代理使用跟踪实例
var globalProxyUsage = &GlobalProxyUsage{
	usedProxies: make(map[string]time.Time),
	cooldown:    constants.DefaultProxyCooldownMinutes * time.Minute, // 默认5分钟
	enabled:     true,                                                // 默认启用
}

// InitGlobalProxyUsage 初始化全局代理使用跟踪
func InitGlobalProxyUsage(enabled bool, cooldownSeconds int) {
	globalProxyUsage.mu.Lock()
	defer globalProxyUsage.mu.Unlock()

	globalProxyUsage.enabled = enabled
	if cooldownSeconds > 0 {
		globalProxyUsage.cooldown = time.Duration(cooldownSeconds) * time.Second
	}

	serverLogger.GetRawLogger().Infof("全局代理使用跟踪初始化: 启用:%v, 冷却时间:%v", enabled, globalProxyUsage.cooldown)
}

// IsRecentlyUsed 检查代理是否在冷却期内被使用过
func (gpu *GlobalProxyUsage) IsRecentlyUsed(proxy string) bool {
	if !gpu.enabled {
		return false
	}

	gpu.mu.RLock()
	defer gpu.mu.RUnlock()

	lastUsed, exists := gpu.usedProxies[proxy]
	if !exists {
		return false
	}

	return time.Since(lastUsed) < gpu.cooldown
}

// MarkAsUsed 标记代理为已使用
func (gpu *GlobalProxyUsage) MarkAsUsed(proxy string) {
	if !gpu.enabled {
		return
	}

	gpu.mu.Lock()
	defer gpu.mu.Unlock()

	gpu.usedProxies[proxy] = time.Now()
	serverLogger.GetRawLogger().Debugf("标记代理 %s 为已使用，冷却时间: %v", proxy, gpu.cooldown)
}

// CleanupExpired 清理过期的使用记录
func (gpu *GlobalProxyUsage) CleanupExpired() {
	if !gpu.enabled {
		return
	}

	gpu.mu.Lock()
	defer gpu.mu.Unlock()

	now := time.Now()
	for proxy, lastUsed := range gpu.usedProxies {
		if now.Sub(lastUsed) > gpu.cooldown {
			delete(gpu.usedProxies, proxy)
		}
	}
}
