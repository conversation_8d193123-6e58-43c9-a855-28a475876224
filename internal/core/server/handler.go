package server

import (
	"bytes"
	"context"
	"encoding/base64"
	stderrors "errors" // 标准库errors包
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"path/filepath"
	"strings"
	"time"

	"github.com/elazarl/goproxy"
	"flexproxy/common"
	"flexproxy/common/constants"
	"flexproxy/common/errors"
	"flexproxy/common/logger"
	"flexproxy/internal/config"
	"flexproxy/internal/core/proxy/gateway"
	"flexproxy/internal/core/engine/trigger"
	"flexproxy/internal/core/validation"
	"github.com/hashicorp/go-retryablehttp"

	"flexproxy/pkg/flexproxy"
	"flexproxy/pkg/helper/awsurl"
	"flexproxy/pkg/utils/header"
)

// 模块级别的日志器

// getGatewayKey 根据baseURL和region生成网关映射的唯一键
func getGatewayKey(baseURL, region string) string {
	return baseURL + constants.GatewayKeySeparator + region
}

// 模块化组件实例
var (
	responseBuilder *ResponseBuilder
	requestProcessor *RequestProcessor
	proxyManager *ProxyManager
	dnsManager *DNSManager
)

// initModules 初始化模块化组件
func (p *Proxy) initModules() {
	responseBuilder = NewResponseBuilder()
	requestProcessor = NewRequestProcessor(p)
	proxyManager = NewProxyManager(p)
	dnsManager = NewDNSManagerWithProxy(p) // 使用兼容性构造函数
}

// onRequest 处理客户端请求
func (p *Proxy) onRequest(req *http.Request, ctx *goproxy.ProxyCtx) (*http.Request, *http.Response) {
	// 初始化模块化组件（如果尚未初始化）
	if requestProcessor == nil {
		p.initModules()
	}

	// 使用请求处理器处理请求
	processedReq, earlyResp := requestProcessor.ProcessRequest(req)
	if earlyResp != nil {
		return processedReq, earlyResp
	}
	req = processedReq

	// 更新上下文到goproxy
	ctx.Req = req

	// 处理同步模式
	unlock := requestProcessor.ProcessSyncMode()
	defer unlock()

	// 设置DNS解析
	req = dnsManager.SetupDNSResolution(req)

	resChan := make(chan interface{})

	go func(r *http.Request) {
		serverLogger.GetRawLogger().Debugf(constants.LogFormatRequestInfo, r.RemoteAddr, r.Method, r.URL)

		// 8. 执行请求前处理阶段触发器
		if p.triggerManager != nil && p.triggerManager.HasPreRequestTriggers {
			resp := p.executeTriggers(trigger.PreRequest, r, nil, 0)
			if resp != nil {
				resChan <- resp
				return
			}
		}

		// 9. 初始化已尝试的代理计数器
		var triedCount int = 0

		for {
			serverLogger.GetRawLogger().Infof(constants.LogMsgCurrentProxyPoolCount, p.Options.ProxyManager.Count())

			// 10. 选择并验证代理
			proxy, newTriedCount, err := proxyManager.SelectAndValidateProxy(r, triedCount)
			triedCount = newTriedCount
			if err != nil {
				// 检查是否是需要重试的错误
				if proxyManager.IsProxyRetryError(err) {
					continue // 继续尝试下一个代理
				}
				// 其他错误直接返回
				resChan <- err
				return
			}

			// 11. 将代理信息存储到上下文中
			r = proxyManager.StoreProxyInContext(r, proxy)

			// 12. 使用代理执行请求
			resp, err := proxyManager.ExecuteRequestWithProxy(r, proxy)
			if err != nil {
				resChan <- err
				return
			}
			defer resp.Body.Close()

			// 13. 读取响应体
			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				resChan <- err
				return
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(buf))

			resChan <- resp
			return // 成功处理，退出循环
		}
	}(req)

	var resp *http.Response

	res := <-resChan
	switch res := res.(type) {
	case *http.Response:
		resp = res
		serverLogger.Debug(req.RemoteAddr + constants.SpaceSeparator + resp.Status)

	case error:
		err := res

		// 区分不同类型的错误并记录详细日志
		if isProxyPoolExhaustedError(err) {
			// 真正的代理池耗尽错误
			serverLogger.GetRawLogger().Errorf(constants.LogMsgProxyPoolExhaustedError, req.RemoteAddr, err)
			serverLogger.GetRawLogger().Infof(constants.LogMsgHandleProxyPoolExhausted)
			resp = responseBuilder.CreateProxyPoolExhaustedResponse(req)
		} else {
			// 其他类型的错误（连接失败、超时等）
			errorType := classifyProxyError(err)
			serverLogger.GetRawLogger().Errorf(constants.LogMsgProxyConnectionError, errorType, req.RemoteAddr, err)
			resp = responseBuilder.CreateProxyConnectionFailedResponse(req, err.Error(), errorType)
		}
	}
	return req, resp
}

// onConnect 处理CONNECT方法
func (p *Proxy) onConnect(host string, ctx *goproxy.ProxyCtx) (*goproxy.ConnectAction, string) {
	if p.Options.Auth != "" {
		auth := ctx.Req.Header.Get(constants.ProxyAuthHeaderName)
		if auth != "" {
			creds := strings.SplitN(auth, constants.SpaceSeparator, constants.ProxyAuthCredentialsParts)
			if len(creds) != constants.ProxyAuthCredentialsParts {
				return goproxy.RejectConnect, host
			}

			auth, err := base64.StdEncoding.DecodeString(creds[1])
			if err != nil {
				serverLogger.GetRawLogger().Warnf(constants.ErrMsgProxyAuthDecodeFailed, ctx.Req.RemoteAddr)
				return goproxy.RejectConnect, host
			}

			if string(auth) != p.Options.Auth {
				serverLogger.GetRawLogger().Errorf(constants.ErrMsgProxyAuthInvalid, ctx.Req.RemoteAddr)
				return goproxy.RejectConnect, host
			}
		} else {
			serverLogger.GetRawLogger().Warnf(constants.ErrMsgProxyAuthRequired, ctx.Req.RemoteAddr, host)
			return goproxy.RejectConnect, host
		}
	}

	return goproxy.MitmConnect, host
}

// onResponse handles backend responses, and removing hop-by-hop headers
func (p *Proxy) onResponse(resp *http.Response, ctx *goproxy.ProxyCtx) *http.Response {

	// 处理特殊响应头
	if specialResp := responseBuilder.ProcessSpecialResponseHeaders(resp); specialResp != nil {
		return specialResp
	}

	// 删除hop-by-hop headers
	responseBuilder.CleanHopByHopHeaders(resp)

	// 添加响应体处理阶段触发器执行
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable && resp != nil {
		// 计算请求时间
		// 健壮性处理，防止 context 没有 startTime 时 panic
		startTimeVal := ctx.Req.Context().Value(constants.ContextKeyStartTime)
		var requestTime time.Duration
		if t, ok := startTimeVal.(time.Time); ok {
			requestTime = time.Since(t)
			serverLogger.GetRawLogger().Debugf(constants.LogMsgRequestTotalTime, requestTime)
		} else {
			requestTime = 0
			serverLogger.Warn(constants.LogMsgRequestContextMissingStartTime)
		}

		// 执行post_header阶段触发器
		if p.triggerManager != nil && p.triggerManager.HasPostHeaderTriggers {
			serverLogger.GetRawLogger().Debugf(constants.LogMsgStartPostHeaderTrigger, ctx.Req.URL)
			triggerStartTime := time.Now()
			newResp := p.executeTriggers(trigger.PostHeader, ctx.Req, resp, requestTime)
			serverLogger.GetRawLogger().Debugf(constants.LogMsgPostHeaderTriggerComplete, time.Since(triggerStartTime))
			if newResp != nil {
				serverLogger.GetRawLogger().Infof(constants.LogMsgPostHeaderNewResponse, newResp.StatusCode)
				return newResp
			}
		} else {
			serverLogger.Debug(constants.LogMsgNoPostHeaderTrigger)
		}

		// 新增：触发器后判断代理池数量
		minPool := constants.DefaultMinProxyPoolSize
		if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.MinProxyPoolSize > 0 {
			minPool = p.Options.RuleConfig.Global.MinProxyPoolSize
		}
		if p.Options.ProxyManager.Count() < minPool {
			serverLogger.GetRawLogger().Warnf(constants.LogFormatProxyPoolInfo, p.Options.ProxyManager.Count(), minPool)
			return resp
		}

		// 执行post_body阶段触发器
		if p.triggerManager != nil && p.triggerManager.HasPostBodyTriggers {
			serverLogger.GetRawLogger().Debugf(constants.LogMsgStartPostBodyTrigger, ctx.Req.URL)
			triggerStartTime := time.Now()
			newResp := p.executeTriggers(trigger.PostBody, ctx.Req, resp, requestTime)
			serverLogger.GetRawLogger().Debugf(constants.LogMsgPostBodyTriggerComplete, time.Since(triggerStartTime))
			if newResp != nil {
				serverLogger.GetRawLogger().Infof(constants.LogMsgPostBodyNewResponse, newResp.StatusCode)
				return newResp
			}
		} else {
			serverLogger.Debug(constants.LogMsgNoPostBodyTrigger)
		}

	}

	return resp
}

func (p *Proxy) rotateProxy(domain ...string) (string, error) {
	serverLogger.Debug(constants.LogMsgStartRotateProxy)

	// 获取锁
	serverLogger.Debug(constants.LogMsgTryAcquireMutex)
	p.mu.Lock()
	defer func() {
		serverLogger.Debug(constants.LogMsgReleaseMutex)
		p.mu.Unlock()
	}()
	serverLogger.Debug(constants.LogMsgAcquiredMutexStartProxy)

	// 读取全局IP轮询方式，默认sequential
	ipRotationMode := constants.DefaultIPRotationMode
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.IPRotationMode != "" {
		ipRotationMode = p.Options.RuleConfig.Global.IPRotationMode
	}
	serverLogger.GetRawLogger().Debugf(constants.LogFormatIPRotationMode, ipRotationMode)

	// 新增：读取最大尝试次数配置
	maxAttempts := constants.DefaultMaxProxyAttempts
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.MaxProxyFetchAttempts > 0 {
		maxAttempts = p.Options.RuleConfig.Global.MaxProxyFetchAttempts
	}
	serverLogger.GetRawLogger().Debugf(constants.LogFormatMaxAttempts, maxAttempts)

	// 新增：代理池最小可用数量判断
	minPool := constants.DefaultMinProxyPoolSize
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.MinProxyPoolSize > 0 {
		minPool = p.Options.RuleConfig.Global.MinProxyPoolSize
	}
	serverLogger.GetRawLogger().Debugf(constants.LogFormatProxyPoolInfo, minPool, p.Options.ProxyManager.Count())

	if p.Options.ProxyManager.Count() < minPool {
		serverLogger.Warn(constants.ErrMsgProxyPoolInsufficient)
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, constants.ErrMsgProxyPoolExhaustedMsg)
	}

	// 如果当前没有代理，获取新代理
	if p.currentProxy == "" {
		serverLogger.Debug(constants.ErrMsgNoCurrentProxy)

		// 使用新的GetProxy方法获取代理，该方法内部已实现缓存机制
		proxy, err := p.Options.ProxyManager.GetProxy(ipRotationMode)
		if err != nil {
			serverLogger.GetRawLogger().Warnf(constants.ErrMsgGetProxyIPFailed, err)
			return "", err
		}
		serverLogger.GetRawLogger().Debugf(constants.ErrMsgProxyObtained, proxy)

		// 如果启用了规则系统，检查代理IP是否被封禁
		if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
			serverLogger.Debug(constants.ErrMsgRuleSystemEnabled)
			// 这里使用空字符串作为域名，只检查全局封禁
			isBanned := p.Options.ProxyManager.IsIPBanned(proxy, "", "")
			serverLogger.GetRawLogger().Debugf(constants.ErrMsgProxyBannedCheck, proxy, isBanned)

			if isBanned {
				serverLogger.GetRawLogger().Debugf(constants.ErrMsgSkipBannedProxy, proxy)
				return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeProxyBanned, constants.ErrMsgProxyBanned)
			}
		}

		serverLogger.GetRawLogger().Infof(constants.ErrMsgFoundAvailableProxy, proxy)
		p.currentProxy = proxy
		p.proxyOkCount = constants.ProxyOkCountInitial
	} else {
		serverLogger.GetRawLogger().Debugf(constants.ErrMsgContinueCurrentProxy, p.currentProxy)
		p.proxyOkCount++
		serverLogger.GetRawLogger().Debugf(constants.ErrMsgProxyUsageIncreased, p.proxyOkCount)
	}

	serverLogger.GetRawLogger().Debugf(constants.ErrMsgRotateProxyComplete, p.currentProxy)
	return p.currentProxy, nil
}

func (p *Proxy) removeProxy(target string) {
	err := p.Options.ProxyManager.RemoveProxy(target)
	if err != nil {
		serverLogger.Error(err.Error())
	}
}

func (p *Proxy) getClient(req *http.Request, proxyAddr string) (*retryablehttp.Client, error) {
	// 从请求上下文中获取代理信息
	proxyFromCtx := req.Context().Value(constants.ContextKeyCurrentProxy)
	//var proxyAddr string
	proxyAddr = proxyFromCtx.(string)

	// 获取请求的实际域名，用于DNS解析
	requestHost := ""
	if req != nil && req.URL != nil {
		requestHost = req.URL.Hostname()
		serverLogger.GetRawLogger().Debugf(constants.LogMsgGetDomainForDNS, requestHost)
	}

	// 从上下文中获取DNS解析器（如果已经创建）
	dnsResolverFromCtx := req.Context().Value(constants.ContextKeyDNSResolver)
	var dnsResolver DNSResolver
	if dnsResolverFromCtx != nil {
		dnsResolver = dnsResolverFromCtx.(DNSResolver)
		serverLogger.Debug(constants.LogMsgGetDNSResolverFromContext)
	}

	// 获取DNS模式和配置
	dnsMode := constants.DNSModeLocal // 默认值
	httpProxyDNS := constants.DefaultHTTPProxyDNSMode
	httpProxyDNSConfig := ""

	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
		if p.Options.RuleConfig.DNSService != nil && p.Options.RuleConfig.DNSService.DNSLookupMode != "" {
			dnsMode = p.Options.RuleConfig.DNSService.DNSLookupMode
		}
		if p.Options.RuleConfig.DNSService != nil && p.Options.RuleConfig.DNSService.HTTPProxyDNS {
			httpProxyDNS = "true"
		}
		if p.Options.RuleConfig.DNSService != nil && p.Options.RuleConfig.DNSService.HTTPProxyDNSConfig != "" {
			httpProxyDNSConfig = p.Options.RuleConfig.DNSService.HTTPProxyDNSConfig
		}
	}

	// 创建HTTPProxyDNS解析器（如果配置了）
	var httpProxyDNSResolver DNSResolver
	if httpProxyDNSConfig != "" {
		dnsConfig := ParseHTTPProxyDNSConfig(httpProxyDNSConfig)
		httpProxyDNSResolver = CreateDNSResolverFromHTTPProxyConfig(dnsConfig, proxyAddr)
		serverLogger.GetRawLogger().Debugf("创建HTTPProxyDNS解析器: %s", httpProxyDNSConfig)
	}

	// 使用新的DNS架构创建Transport
	tr, err := flexproxy.TransportWithDNSResolver(proxyAddr, dnsMode, p.reverseDNSResolver, httpProxyDNSResolver, httpProxyDNS)
	if err != nil && !stderrors.Is(err, flexproxy.ErrSwitchTransportAWSProtocolScheme) {
		return nil, err
	}

	// 如果使用本地模式和自定义DNS服务器，覆盖Transport的DialContext
	if dnsMode == constants.DNSModeLocal && dnsResolver != nil {
		// 设置HTTP代理
		if proxyAddr != "" && strings.HasPrefix(proxyAddr, "http") {
			proxyURL, err := url.Parse(proxyAddr)
			if err == nil {
				tr.Proxy = http.ProxyURL(proxyURL)
				serverLogger.GetRawLogger().Debugf(constants.LogMsgSetHTTPProxy, proxyAddr)
			}
		}

		// 保存原始的DialContext函数，用于直接连接
		originalDialContext := (&net.Dialer{}).DialContext

		tr.DialContext = func(ctx context.Context, network, address string) (net.Conn, error) {
			// 解析主机名和端口
			host, port, err := net.SplitHostPort(address)
			if err != nil {
				return nil, err
			}

			// 获取请求的实际域名
			var requestHost string
			if req != nil && req.URL != nil {
				requestHost = req.URL.Hostname()
			}

			// 从上下文中获取当前代理信息
			proxyFromCtx := ctx.Value(constants.ContextKeyCurrentProxy)
			if proxyFromCtx != nil {
				proxyStr := proxyFromCtx.(string)
				// 解析代理URL获取主机名
				proxyURL, err := url.Parse(proxyStr)
				if err == nil {
					proxyHost := proxyURL.Hostname()
					// 如果当前连接的是代理服务器本身，直接使用原始拨号器连接
					if host == proxyHost {
						serverLogger.GetRawLogger().Debugf(constants.LogMsgDetectDirectProxyConn, host)
						return originalDialContext(ctx, network, address)
					}
				}
			}

			// 检查是否是IP地址，如果是则跳过DNS解析
			if net.ParseIP(host) != nil {
				serverLogger.GetRawLogger().Debugf(constants.LogMsgHostIsIPAddress, host)
				return originalDialContext(ctx, network, address)
			}

			// 如果host不是请求的实际域名，可能是代理地址，直接连接
			if requestHost != "" && host != requestHost {
				serverLogger.GetRawLogger().Debugf(constants.LogMsgDetectProxyAddress, host, requestHost)
				return originalDialContext(ctx, network, address)
			}

			// 确定要解析的主机名
			resolveHost := requestHost
			if resolveHost == "" {
				resolveHost = host
			}

			// 从上下文中获取DNS解析结果缓存
			dnsResultFromCtx := ctx.Value(constants.ContextKeyDNSResult + resolveHost)
			var ips []net.IP

			if dnsResultFromCtx != nil {
				// 使用缓存的DNS解析结果
				ips = dnsResultFromCtx.([]net.IP)
				serverLogger.GetRawLogger().Debugf(constants.LogMsgUseCachedDNSResult, resolveHost, ips)
			} else {
				// 使用自定义DNS服务器解析主机名
				var err error
				serverLogger.GetRawLogger().Debugf(constants.LogMsgUseDNSResolver, resolveHost)
				ips, err = dnsResolver.LookupIP(ctx, resolveHost)
				if err != nil {
					serverLogger.GetRawLogger().Warnf(constants.LogMsgDNSResolveFallback, err)
					// 尝试使用系统默认解析器作为备选
					ips, err = net.DefaultResolver.LookupIP(ctx, "ip", resolveHost)
					if err != nil {
						return nil, errors.WrapErrorWithDetails(err, errors.ErrTypeDNS, errors.ErrCodeDNSQueryFailed, "无法解析主机名", fmt.Sprintf("主机名: %s", resolveHost))
					}
				}

				if len(ips) > 0 {
					// 将DNS解析结果存储到上下文中
					if req != nil {
						req = req.WithContext(context.WithValue(req.Context(), constants.ContextKeyDNSResult+resolveHost, ips))
					}
				}
			}

			if len(ips) == 0 {
				return nil, errors.NewErrorWithDetails(errors.ErrTypeDNS, errors.ErrCodeDNSNoResult, "无法解析主机名: 未找到IP地址", fmt.Sprintf("主机名: %s", resolveHost))
			}
			// 使用解析得到的第一个IP地址连接
			dialer := &net.Dialer{}
			return dialer.DialContext(ctx, network, net.JoinHostPort(ips[0].String(), port))
		}
	}

	proxy := &flexproxy.Proxy{
		Address:      proxyAddr,
		MaxRedirects: constants.DefaultMaxRedirects,
		Timeout:      p.Options.Timeout,
		Transport:    tr,
	}

	client, err := proxy.New(req)
	if err != nil {
		return nil, err
	}

	if awsurl.IsURL(proxyAddr) {
		var pg *gateway.ProxyGateway

		awsURL, err := awsurl.Parse(proxyAddr)
		if err != nil {
			return nil, err
		}

		_, err = awsURL.Credentials("")
		if err != nil {
			return nil, err
		}

		accessKeyID := awsURL.AccessKeyID
		secretAccessKey := awsURL.SecretAccessKey
		region := awsURL.Region

		validator := validation.NewNetworkValidator()
		baseURL, _, err := validator.GetBaseURL(req.URL.String())
		if err != nil {
			return nil, err
		}

		gatewayKey := getGatewayKey(baseURL, region)

		if p.Gateways[gatewayKey] == nil {
			ctx := context.Background()
			gateway, err := gateway.New(ctx, accessKeyID, secretAccessKey, region)
			if err != nil {
				return nil, err
			}

			err = gateway.SetBaseURL(baseURL)
			if err != nil {
				return nil, err
			}

			err = gateway.Start(ctx)
			if err != nil {
				return nil, err
			}

			pg = gateway

			p.mu.Lock()
			p.Gateways[gatewayKey] = pg
			p.mu.Unlock()
		} else {
			pg = p.Gateways[gatewayKey]
		}

		// 将请求URL重写为API Gateway端点URL
		gatewayEndpoint := pg.GetEndpoint()
		req.URL.Path = filepath.Join("/", gateway.StageName, req.URL.Path)
		req.URL.Host = gatewayEndpoint.Host
		req.URL.Scheme = gatewayEndpoint.Scheme
		req.Host = gatewayEndpoint.Host
	}

	if p.Options.Verbose {
		client.Transport = dump.RoundTripper(tr)
	}

	retryablehttpClient := flexproxy.ToRetryableHTTPClient(client)
	retryablehttpClient.RetryMax = constants.DefaultRetryMaxCount
	retryablehttpClient.RetryWaitMin = client.Timeout
	retryablehttpClient.RetryWaitMax = client.Timeout
	retryablehttpClient.Logger = ReleveledLogo{
		Logger:  logger.GetServerLogger().GetRawLogger(),
		Request: req,
		Verbose: p.Options.Verbose,
	}

	return retryablehttpClient, nil
}

// nonProxy handles non-proxy requests
func nonProxy(w http.ResponseWriter, req *http.Request) {
	if common.Version != "" {
		w.Header().Add(constants.HeaderXFlexProxyVersion, common.Version)
	}

	if req.URL.Path == constants.CertificatePath {
		w.Header().Add(constants.HeaderContentType, constants.MimeOctetStream)
		w.Header().Add(constants.HeaderContentDisposition, fmt.Sprint(constants.CertificateAttachmentPrefix, constants.CertificateFilename))
		w.WriteHeader(http.StatusOK)

		if _, err := w.Write(goproxy.GoproxyCa.Certificate[0]); err != nil {
			http.Error(w, constants.ErrMsgCertificateAuthFailed, constants.StatusInternalServerError)
			serverLogger.GetRawLogger().Errorf(constants.LogFormatRequestDetails, req.RemoteAddr, req.Method, req.URL, err.Error())
		}

		return
	}

	http.Error(w, constants.ErrMsgNonProxyRequest, constants.StatusInternalServerError)
}

func serverErr(req *http.Request) *http.Response {
	return goproxy.NewResponse(req, mime, constants.StatusBadGateway, constants.ErrMsgProxyServerError)
}

// createNullResponse 创建空响应（向后兼容）
func createNullResponse(req *http.Request) *http.Response {
	return responseBuilder.CreateNullResponse(req)
}

func (p *Proxy) executeTriggers(stage trigger.ProcessStage, req *http.Request, resp *http.Response, reqTime time.Duration) *http.Response {
	// 性能监控：记录函数执行开始时间
	executionStart := time.Now()
	defer func() {
		executionDuration := time.Since(executionStart)
		if executionDuration > constants.TriggerExecutionSlowThreshold {
			serverLogger.GetRawLogger().Warnf(constants.LogMsgTriggerExecutionSlow, executionDuration, stage, req.URL.String())
		}

		// 定期报告并发访问统计（每100次执行报告一次）
		p.mu.RLock()
		readCount := p.proxyReadCount
		writeCount := p.proxyWriteCount
		p.mu.RUnlock()

		if (readCount+writeCount)%100 == 0 && (readCount+writeCount) > 0 {
			serverLogger.GetRawLogger().Debugf(constants.LogMsgConcurrentProxyAccess, readCount, writeCount)
		}
	}()

	if p.triggerManager == nil {
		serverLogger.Warn(constants.LogMsgTriggerManagerEmpty)
		return nil
	}

	serverLogger.GetRawLogger().Debugf(constants.LogFormatTriggerExecution, stage, req.URL.String(), reqTime.Milliseconds())

	var finalResp *http.Response
	triggers := p.triggerManager.GetTriggersForStage(stage)
	serverLogger.GetRawLogger().Debugf(constants.LogFormatTriggerCount, len(triggers), stage)

	// 保存原始DNS解析器，以便后续恢复
	originalDNSResolver := p.dnsResolver
	defer func() {
		// 恢复原始DNS解析器
		p.dnsResolver = originalDNSResolver
	}()

	// 创建一个新的上下文，用于存储DNS解析结果
	// 检查全局配置中的DNSNoCache参数
	dnsNoCache := constants.DefaultDNSCacheDisabled
	var cacheTTL time.Duration = constants.DefaultDNSCacheTTL // 默认值为5分钟
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
		dnsNoCache = constants.DefaultDNSCacheDisabled
		// 使用配置文件中的DNSCacheTTL值
		if p.Options.RuleConfig.DNSService != nil && p.Options.RuleConfig.DNSService.DNSCacheTTL > 0 {
			cacheTTL = time.Duration(p.Options.RuleConfig.DNSService.DNSCacheTTL) * time.Second
		} else {
			cacheTTL = time.Duration(constants.DefaultDNSCacheTTLSeconds) * time.Second
		}
	}

	// 保留原始上下文中的代理信息
	proxyFromCtx := req.Context().Value(constants.ContextKeyCurrentProxy)

	dnsCtx := context.WithValue(req.Context(), constants.ContextKeyDNSCacheEnabled, !dnsNoCache)
	// 设置DNS缓存TTL为配置文件中的值
	dnsCtx = context.WithValue(dnsCtx, constants.ContextKeyDNSCacheTTL, cacheTTL)
	// 将DNS配置提供者添加到上下文中，供getDefaultDNSTimeout使用
	dnsCtx = context.WithValue(dnsCtx, "dns_config_provider", p)

	// 如果原始上下文中有代理信息，保留到新上下文
	if proxyFromCtx != nil {
		dnsCtx = context.WithValue(dnsCtx, constants.ContextKeyCurrentProxy, proxyFromCtx)
	}

	// 将上下文附加到请求
	reqWithDNSCtx := req.WithContext(dnsCtx)

	// 如果请求中包含主机名，且未禁用DNS缓存，则预先解析并存入缓存
	if req.URL != nil && req.URL.Hostname() != "" {
		hostname := req.URL.Hostname()

		// 当DNSNoCache=yes时，完全跳过预解析
		if dnsNoCache {
			serverLogger.GetRawLogger().Debugf(constants.LogFormatDNSCacheDisabled, hostname)
		} else {
			// 性能监控：记录DNS缓存访问时间
			dnsCacheStart := time.Now()

			// 使用并发安全的DNS缓存
			_, exists := getDNSCacheEntry(hostname)

			dnsCacheDuration := time.Since(dnsCacheStart)
			if dnsCacheDuration > constants.DNSCacheSlowThreshold {
				serverLogger.GetRawLogger().Warnf(constants.LogMsgDNSCacheOperationSlow, dnsCacheDuration, hostname)
			}

			// 只有当没有缓存结果时，才进行预解析（过期检查已在getDNSCacheEntry中处理）
			if !exists && p.dnsResolver != nil {
				serverLogger.GetRawLogger().Debugf(constants.LogFormatDNSPreResolve, hostname)
				// 使用当前DNS解析器解析主机名
				ips, err := p.dnsResolver.LookupIP(dnsCtx, hostname)
				if err != nil {
					serverLogger.GetRawLogger().Warnf(constants.LogFormatDNSResolveFailed, hostname, err)
				} else if len(ips) > 0 {
					serverLogger.GetRawLogger().Debugf(constants.LogFormatDNSResolveSuccess, hostname, len(ips))
				}
			} else if exists {
				serverLogger.GetRawLogger().Debugf(constants.LogFormatDNSCacheHit, hostname)
			}
		}
	}

	for _, t := range triggers {
		// 添加错误恢复机制
		func() {
			defer func() {
				if r := recover(); r != nil {
					serverLogger.GetRawLogger().Errorf(constants.LogFormatTriggerCrash, r)
				}
			}()

			// 获取事件配置并设置事件级别DNS解析器
			eventConfig := p.triggerManager.GetEventConfig(t)
			if eventConfig == nil {
				serverLogger.GetRawLogger().Warnf(constants.LogMsgTriggerConfigMissing, t)
				return // 从匿名函数返回，相当于 continue 外层循环的下一次迭代
			}
			// 从这里开始，eventConfig 保证不是 nil
			serverLogger.GetRawLogger().Debugf(constants.LogMsgProcessTriggerType, eventConfig.TriggerType, stage, eventConfig.Name)

			// 如果是状态码触发器，记录更详细的信息
			if eventConfig.TriggerType == constants.TriggerTypeStatus && resp != nil { // 与字符串比较
				serverLogger.GetRawLogger().Debugf(constants.LogFormatStatusTrigger, resp.StatusCode)
			}

			// 如果事件配置了特定的DNS查询模式，则临时切换DNS解析器
			if constants.DNSModeLocalValue != "" {
				// 根据事件配置创建临时DNS解析器
				tempDNSResolver := p.createDNSResolverForEvent(eventConfig, reqWithDNSCtx)
				if tempDNSResolver != nil {
					p.dnsResolver = tempDNSResolver
					serverLogger.GetRawLogger().Debugf(constants.LogFormatDNSResolverSwitch, constants.DNSModeLocalValue)
				}
			}

			serverLogger.GetRawLogger().Debugf(constants.LogMsgTriggerMatchAttempt, t)
			if t.Match(reqWithDNSCtx, resp, reqTime) {
				serverLogger.GetRawLogger().Infof(constants.LogFormatTriggerMatch, stage, t.GetPriority())

				// 获取触发器类型用于日志记录
				triggerType := ""
				if eventConfig != nil {
					triggerType = eventConfig.TriggerType
				}

				serverLogger.GetRawLogger().Infof(constants.LogFormatTriggerType, triggerType)

				// 执行触发器动作
				actionConfigs := t.GetActions() // This is now []common.ActionConfig
				serverLogger.GetRawLogger().Debugf(constants.LogFormatActionCount, len(actionConfigs))

				for _, actionCfg := range actionConfigs {
					serverLogger.GetRawLogger().Debugf(constants.LogFormatActionExecution, actionCfg.Type, actionCfg.Params)

					// 并发安全：使用读锁保护 currentProxy 的读取
					p.mu.RLock()
					currentProxy := p.currentProxy
					p.proxyReadCount++ // 统计读取次数
					p.mu.RUnlock()

					actCtx := context.WithValue(dnsCtx, constants.ContextKeyCurrentProxy, currentProxy)
					actCtx = context.WithValue(actCtx, constants.ContextKeyProxyResetter, p)

					var actionResp *http.Response
					var execErr error
					stopProcessing := constants.DefaultStopProcessing // 初始化stopProcessing

					if p.actionManager == nil {
						serverLogger.GetRawLogger().Errorf("ActionManager is nil in Proxy.executeTriggers for event %s", eventConfig.Name)
						execErr = errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionManagerNotInitialized, "ActionManager not initialized")
					} else {
						actionInstance, buildErr := p.actionManager.BuildActionFromConfig(actionCfg)

						// 新增日志: 检查 BuildActionFromConfig 的返回值
						if actionInstance == nil {
							serverLogger.GetRawLogger().Errorf(constants.LogMsgActionInstanceNil, actionCfg.Type, buildErr)
						} else {
							serverLogger.GetRawLogger().Debugf(constants.LogMsgActionInstanceNotNil, actionCfg.Type, actionInstance, buildErr)
						}

						if buildErr != nil {
							serverLogger.GetRawLogger().Errorf(constants.LogMsgActionBuildFailed, actionCfg.Type, eventConfig.Name, buildErr)
							execErr = buildErr
						} else {
							serverLogger.GetRawLogger().Debugf(constants.LogMsgExecutingAction, actionInstance, eventConfig.Name)

							// 新增日志，检查 actCtx 和 reqWithDNSCtx
							if actCtx == nil {
								serverLogger.GetRawLogger().Errorf(constants.LogMsgActionContextNil, actionCfg.Type)
							} else {
								serverLogger.GetRawLogger().Debugf(constants.LogMsgActionContextNotNil, actCtx)
							}
							if reqWithDNSCtx == nil {
								serverLogger.GetRawLogger().Errorf(constants.LogMsgRequestContextNil, actionCfg.Type)
							} else {
								serverLogger.GetRawLogger().Debugf(constants.LogMsgRequestContextNotNil, reqWithDNSCtx.Method, reqWithDNSCtx.URL)
							}

							actionResp, execErr = actionInstance.Execute(actCtx, reqWithDNSCtx, resp, p.Options.ProxyManager)

							// Check context for stopProcessing signal, if actions set it.
							// Example: if val, ok := actCtx.Value("stop_processing").(bool); ok && val { stopProcessing = true }
							// A common pattern is for actions like BanIP (on context cancel) to return a specific error
							// or modify the response (e.g. nil response) to signal this.
							// If context.Canceled is the error, we might want to stop.
							if stderrors.Is(execErr, context.Canceled) {
								serverLogger.GetRawLogger().Infof(constants.LogMsgActionCanceled, actionCfg.Type, eventConfig.Name)
								stopProcessing = true
								// If context is canceled, actionResp might be nil, and that's often intended.
							} else if execErr != nil {
								serverLogger.GetRawLogger().Errorf("Error executing action %s for event %s: %v", actionCfg.Type, eventConfig.Name, execErr)
							}
						}
					}

					if execErr != nil {
						serverLogger.GetRawLogger().Errorf(constants.LogMsgActionExecuteFailed, actionCfg.Type, execErr)
						// 根据动作类型决定错误处理策略
						// 对于关键动作（如ban_ip），失败时继续执行其他动作
						// 对于响应修改动作，失败时可能需要中断序列
						if actionCfg.Type == "ban_ip" || actionCfg.Type == "log" || actionCfg.Type == "statistics" {
							// 非关键动作失败，继续执行后续动作
							continue
						} else {
							// 关键动作失败，中断当前触发器的动作序列，但允许其他触发器继续
							break
						}
					}

					if requiresRetry, ok := reqWithDNSCtx.Context().Value(constants.ContextKeyActionRequiresRetry).(bool); ok && requiresRetry {
						// 动作要求重试，检查重试类型和参数
						if retryType, ok := reqWithDNSCtx.Context().Value(constants.ContextKeyRetryType).(string); ok {
							switch retryType {
							case constants.ActionTypeRetrySame: // "retry_same"
								if retryCount, ok := reqWithDNSCtx.Context().Value(constants.ContextKeyRetryCount).(int); ok && retryCount > 0 {
									serverLogger.GetRawLogger().Debugf(constants.LogMsgActionRequiresSameProxyRetry, actionCfg.Type, retryCount)
									retryResp := p.handleRetrySameAction(reqWithDNSCtx, resp)
									if retryResp != nil {
										actionResp = retryResp
									}
								}
							case constants.ActionTypeRetry: // "retry"
								if retryCount, ok := reqWithDNSCtx.Context().Value(constants.ContextKeyRetryCount).(int); ok && retryCount > 0 {
									serverLogger.GetRawLogger().Debugf(constants.LogMsgActionRequiresNewProxyRetry, actionCfg.Type, retryCount)
									retryResp := p.handleRetryAction(reqWithDNSCtx, resp)
									if retryResp != nil {
										actionResp = retryResp
									}
								}
							default:
								serverLogger.GetRawLogger().Warnf(constants.LogMsgActionUnknownRetryType, actionCfg.Type, retryType)
							}
						}
					}

					if actionResp != nil {
						finalResp = actionResp
					} else if stopProcessing { // If an action (like BanIP on context cancel) decided to stop and returned nil
						// 动作要求停止处理但没有提供响应，创建空响应确保处理完整性
						serverLogger.GetRawLogger().Debugf(constants.LogMsgActionStopProcessingNil, actionCfg.Type)
						// 如果动作要求停止处理但没有提供响应，我们需要创建一个空响应
						// 这确保了响应处理的完整性，避免nil响应导致的问题
						if finalResp == nil { // 确保如果要中断，finalResp不为nil
							// 创建空响应以确保处理链的完整性
							finalResp = createNullResponse(reqWithDNSCtx)
							serverLogger.GetRawLogger().Debugf(constants.LogMsgCreatedNullResponse, actionCfg.Type)
						}
					}

					if finalResp != nil { // 如果序列中的任何动作设置了最终响应
						serverLogger.GetRawLogger().Debugf(constants.LogMsgActionSetFinalResponse, actionCfg.Type)
						if t.GetPriority() == 1 || stopProcessing { // 为最高优先级或动作要求时停止
							serverLogger.Debug(constants.LogMsgHighPriorityTrigger)
							return // 从此触发器的匿名函数返回
						}
						// If not highest priority but an action sequence provided a response,
						// we might want to stop processing further actions in *this* trigger's sequence.
						// However, other lower-priority triggers might still run.
						// This break will exit the loop over actionConfigs for the current trigger.
						break
					}
				}
			} else {
				serverLogger.GetRawLogger().Debugf(constants.LogMsgTriggerNotMatch)
			}
		}()

		// 如果已经有了最终响应，不再继续处理其他触发器
		if finalResp != nil {
			break
		}
	}

	return finalResp
}

// createDNSResolverForEvent 根据事件配置创建DNS解析器
func (p *Proxy) createDNSResolverForEvent(eventConfig *config.EventConfig, req *http.Request) DNSResolver {
	serverLogger.GetRawLogger().Debugf(constants.LogMsgCreateDNSResolverForEvent, constants.DNSModeLocalValue)

	// 记录请求信息，帮助调试
	if req != nil {
		serverLogger.GetRawLogger().Debugf(constants.LogMsgRequestURLInfo, req.URL.String(), req.URL.Hostname())
	}

	// 检查事件配置
	if eventConfig == nil {
		serverLogger.Warn(constants.LogMsgEventConfigEmpty)
		return nil
	}

	// 使用DNS管理器创建解析器
	return dnsManager.CreateDNSResolverForEvent(req)
}

// handleRetrySameAction 处理使用相同IP重试的逻辑
func (p *Proxy) handleRetrySameAction(req *http.Request, originalResp *http.Response) *http.Response {
	// 从请求上下文中获取重试信息
	retryCount, ok := req.Context().Value(constants.ContextKeyRetrySameCount).(int)
	if !ok || retryCount <= 0 {
		serverLogger.Warn("handleRetrySameAction: 无法获取重试次数或重试次数无效")
		return nil
	}

	currentProxy, ok := req.Context().Value(constants.ContextKeyRetrySameProxy).(string)
	if !ok || currentProxy == "" {
		serverLogger.Warn(constants.LogMsgCannotGetCurrentProxy)
		return nil
	}

	serverLogger.GetRawLogger().Infof(constants.MsgRetryWithSameProxy, currentProxy, retryCount)

	var lastSuccessResp *http.Response

	// 执行重试逻辑 - 执行完整的重试次数
	for i := 0; i < retryCount; i++ {
		serverLogger.GetRawLogger().Debugf(constants.LogMsgRetrySameAttempt, i+1, currentProxy)

		// 创建重试请求的上下文
		retryCtx := context.WithValue(req.Context(), constants.ContextKeyCurrentProxy, currentProxy)
		retryCtx = context.WithValue(retryCtx, constants.ContextKeyRetryAttempt, i+1)
		retryReq := req.WithContext(retryCtx)

		// 获取客户端
		retryablehttpClient, err := p.getClient(retryReq, req.URL.Hostname())
		if err != nil {
			serverLogger.GetRawLogger().Errorf("handleRetrySameAction: 第 %d 次重试创建客户端失败: %v", i+1, err)
			continue
		}

		// 创建retryablehttp请求
		retryablehttpRequest, err := retryablehttp.FromRequest(retryReq)
		if err != nil {
			serverLogger.GetRawLogger().Errorf("handleRetrySameAction: 第 %d 次重试创建请求失败: %v", i+1, err)
			continue
		}

		// 清理FlexProxy内部标记，防止泄露到外部服务器
		header.CleanInternalHeaders(retryablehttpRequest.Header)

		// 发送重试请求
		retryResp, err := retryablehttpClient.Do(retryablehttpRequest)
		if err != nil {
			serverLogger.GetRawLogger().Errorf("handleRetrySameAction: 第 %d 次重试请求失败: %v", i+1, err)
			continue
		}

		// 检查响应是否成功
		if retryResp.StatusCode >= constants.HTTPSuccessStatusMin && retryResp.StatusCode <= constants.HTTPSuccessStatusMax {
			serverLogger.GetRawLogger().Infof(constants.LogFormatRetrySameSuccess, i+1, retryResp.StatusCode)

			// 读取响应体
			buf, err := io.ReadAll(retryResp.Body)
			if err != nil {
				serverLogger.GetRawLogger().Errorf("handleRetrySameAction: 读取重试响应体失败: %v", err)
				retryResp.Body.Close()
				continue
			}
			retryResp.Body.Close()
			retryResp.Body = io.NopCloser(bytes.NewBuffer(buf))

			// 保存最后一次成功的响应，但继续执行剩余重试
			if lastSuccessResp != nil && lastSuccessResp.Body != nil {
				lastSuccessResp.Body.Close()
			}
			lastSuccessResp = retryResp
		} else {
			serverLogger.GetRawLogger().Warnf(constants.LogFormatRetrySameError, i+1, retryResp.StatusCode)
			retryResp.Body.Close()
		}
	}

	if lastSuccessResp != nil {
		serverLogger.GetRawLogger().Infof(constants.MsgRetryCompleted, retryCount)
		return lastSuccessResp
	}

	serverLogger.GetRawLogger().Warnf(constants.MsgRetryAllFailed, retryCount)
	return nil
}

// handleRetryAction 处理使用新IP重试的逻辑
func (p *Proxy) handleRetryAction(req *http.Request, originalResp *http.Response) *http.Response {
	// 从请求上下文中获取重试信息
	retryCount, ok := req.Context().Value(constants.ContextKeyRetryCount).(int)
	if !ok || retryCount <= 0 {
		serverLogger.Warn("handleRetryAction: 无法获取重试次数或重试次数无效")
		return nil
	}

	// 检查代理池最小可用数量
	minPool := constants.DefaultMinProxyPoolSize
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.MinProxyPoolSize > 0 {
		minPool = p.Options.RuleConfig.Global.MinProxyPoolSize
	}

	// 检查代理池总数量
	totalProxies := p.Options.ProxyManager.Count()
	serverLogger.GetRawLogger().Debugf(constants.LogMsgRetryPoolInfo, totalProxies, minPool, retryCount)

	// 预先检查：如果代理池数量不足以支持重试需求，直接返回
	if totalProxies <= minPool {
		serverLogger.GetRawLogger().Warnf(constants.LogMsgRetryPoolInsufficient, totalProxies, minPool)
		return nil
	}

	// 检查是否有足够的不同IP来满足重试需求
	// 考虑到当前已经使用了一个代理，剩余可用代理数量应该足够支持重试
	availableForRetry := totalProxies - 1 // 减去当前正在使用的代理
	if availableForRetry < retryCount {
		serverLogger.GetRawLogger().Warnf(constants.LogMsgRetryInsufficientProxies, availableForRetry, retryCount)
		return nil
	}

	serverLogger.GetRawLogger().Infof(constants.MsgRetryWithNewProxy, retryCount)

	// 获取最大尝试次数配置
	maxAttempts := constants.DefaultMaxProxyAttempts
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.MaxProxyFetchAttempts > 0 {
		maxAttempts = p.Options.RuleConfig.Global.MaxProxyFetchAttempts
	}

	var lastSuccessResp *http.Response
	var usedProxies []string // 记录已使用的代理，避免重复

	// 并发安全：使用读锁保护 currentProxy 的读取
	p.mu.RLock()
	currentProxy := p.currentProxy
	p.proxyReadCount++ // 统计读取次数
	p.mu.RUnlock()

	// 将当前代理添加到已使用列表中
	if currentProxy != "" {
		usedProxies = append(usedProxies, currentProxy)
		serverLogger.GetRawLogger().Debugf(constants.LogMsgRetryAddCurrentProxy, currentProxy)
	}

	// 执行重试逻辑 - 每次使用不同的IP
	for i := 0; i < retryCount; i++ {
		serverLogger.GetRawLogger().Debugf(constants.LogMsgRetryAttemptNewProxy, i+1)

		// 再次检查代理池数量是否足够（动态检查）
		currentAvailable := p.Options.ProxyManager.Count()
		if currentAvailable <= minPool {
			serverLogger.GetRawLogger().Warnf(constants.LogMsgRetryPoolCountCheck, currentAvailable, minPool)
			break
		}

		// 获取新的代理IP
		var newProxy string
		var err error
		attemptCount := 0

		// 尝试获取未被封禁且未使用过的代理
		for attemptCount < maxAttempts {
			// 读取全局IP轮询方式
			ipRotationMode := constants.DefaultIPRotationMode
			if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.IPRotationMode != "" {
				ipRotationMode = p.Options.RuleConfig.Global.IPRotationMode
			}

			// 获取新代理 - 在重试时直接调用底层方法避免缓存
			// 获取新代理 - 使用 GetProxy 接口方法
			newProxy, err = p.Options.ProxyManager.GetProxy(ipRotationMode)
			if err != nil {
				serverLogger.GetRawLogger().Errorf(constants.LogMsgRetryGetProxyFailed, i+1, err)
				break
			}

			// 检查是否已经使用过这个代理（本次重试会话）
			sessionUsed := false
			for _, used := range usedProxies {
				if used == newProxy {
					sessionUsed = true
					break
				}
			}

			// 检查是否在全局冷却期内被使用过
			globalRecentlyUsed := globalProxyUsage.IsRecentlyUsed(newProxy)

			if sessionUsed {
				serverLogger.GetRawLogger().Debugf(constants.LogMsgRetryProxySessionUsed, newProxy)
				attemptCount++
				continue
			}

			if globalRecentlyUsed {
				serverLogger.GetRawLogger().Debugf(constants.LogMsgRetryProxyGlobalCooldown, newProxy)
				attemptCount++

				// 如果尝试次数过多，说明可能没有足够的不同代理
				if attemptCount >= maxAttempts/2 {
					serverLogger.GetRawLogger().Warnf(constants.LogMsgRetryTooManyAttempts)
				}
				continue
			}

			// 检查代理是否被封禁
			if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
				domain := req.URL.Hostname()
				url := req.URL.String()

				// 检查全局封禁
				if p.Options.ProxyManager.IsIPBanned(newProxy, "", "global") {
					serverLogger.GetRawLogger().Debugf(constants.LogMsgRetryProxyGlobalBanned, newProxy)
					attemptCount++
					continue
				}

				// 检查域名级封禁
				if p.Options.ProxyManager.IsIPBanned(newProxy, domain, "domain") {
					serverLogger.GetRawLogger().Debugf(constants.LogMsgRetryProxyDomainBanned, newProxy, domain)
					attemptCount++
					continue
				}

				// 检查URL级封禁
				if p.Options.ProxyManager.IsIPBanned(newProxy, url, "url") {
					serverLogger.GetRawLogger().Debugf(constants.LogMsgRetryProxyURLBanned, newProxy, url)
					attemptCount++
					continue
				}

				// 检查永久封禁
				if p.Options.ProxyManager.IsIPPermanentlyBlocked(newProxy) {
					serverLogger.GetRawLogger().Debugf(constants.LogMsgRetryProxyPermanentBanned, newProxy)
					attemptCount++
					continue
				}
			}

			// 找到可用的代理
			break
		}

		if attemptCount >= maxAttempts {
			serverLogger.GetRawLogger().Warnf(constants.LogMsgRetryMaxAttemptsReached, i+1, maxAttempts)
			break
		}

		if err != nil || newProxy == "" {
			serverLogger.GetRawLogger().Errorf(constants.LogMsgRetryCannotGetNewProxy, i+1, err)
			break
		}

		serverLogger.GetRawLogger().Debugf(constants.LogMsgRetryUsingNewProxy, i+1, newProxy)
		usedProxies = append(usedProxies, newProxy)

		// 标记代理为全局已使用
		globalProxyUsage.MarkAsUsed(newProxy)

		// 创建重试请求的上下文
		retryCtx := context.WithValue(req.Context(), constants.ContextKeyCurrentProxy, newProxy)
		retryCtx = context.WithValue(retryCtx, constants.ContextKeyRetryAttempt, i+1)
		retryReq := req.WithContext(retryCtx)

		// 获取客户端
		retryablehttpClient, err := p.getClient(retryReq, req.URL.Hostname())
		if err != nil {
			serverLogger.GetRawLogger().Errorf("handleRetryAction: 第 %d 次重试创建客户端失败: %v", i+1, err)
			continue
		}

		// 创建retryablehttp请求
		retryablehttpRequest, err := retryablehttp.FromRequest(retryReq)
		if err != nil {
			serverLogger.GetRawLogger().Errorf("handleRetryAction: 第 %d 次重试创建请求失败: %v", i+1, err)
			continue
		}

		// 清理FlexProxy内部标记，防止泄露到外部服务器
		header.CleanInternalHeaders(retryablehttpRequest.Header)

		// 发送重试请求
		retryResp, err := retryablehttpClient.Do(retryablehttpRequest)
		if err != nil {
			serverLogger.GetRawLogger().Errorf("handleRetryAction: 第 %d 次重试请求失败: %v", i+1, err)
			continue
		}

		// 检查响应是否成功
		if retryResp.StatusCode >= constants.HTTPSuccessStatusMin && retryResp.StatusCode <= constants.HTTPSuccessStatusMax {
			serverLogger.GetRawLogger().Infof(constants.LogMsgRetryNewProxySuccess, i+1, retryResp.StatusCode, newProxy)

			// 读取响应体
			buf, err := io.ReadAll(retryResp.Body)
			if err != nil {
				serverLogger.GetRawLogger().Errorf(constants.LogMsgRetryReadResponseFailed, err)
				retryResp.Body.Close()
				continue
			}
			retryResp.Body.Close()
			retryResp.Body = io.NopCloser(bytes.NewBuffer(buf))

			// 保存最后一次成功的响应，但继续执行剩余重试
			if lastSuccessResp != nil && lastSuccessResp.Body != nil {
				lastSuccessResp.Body.Close()
			}
			lastSuccessResp = retryResp

			// 并发安全：使用写锁保护 currentProxy 的更新
			p.mu.Lock()
			p.currentProxy = newProxy
			p.proxyWriteCount++ // 统计写入次数
			p.mu.Unlock()
		} else {
			serverLogger.GetRawLogger().Warnf(constants.LogMsgRetryNewProxyError, i+1, retryResp.StatusCode, newProxy)
			retryResp.Body.Close()
		}
	}

	if lastSuccessResp != nil {
		serverLogger.GetRawLogger().Infof("handleRetryAction: 完成重试，返回最后一次成功的响应")
		return lastSuccessResp
	}

	serverLogger.GetRawLogger().Warnf("handleRetryAction: 所有重试都失败")
	return nil
}

// tryProxyRequest 尝试使用指定代理发送请求
func (p *Proxy) tryProxyRequest(r *http.Request, domain, proxy string) (*http.Response, error) {
	retryablehttpClient, err := p.getClient(r, domain)
	if err != nil {
		return nil, err
	}

	retryablehttpRequest, err := retryablehttp.FromRequest(r)
	if err != nil {
		return nil, err
	}

	// 清理FlexProxy内部标记，防止泄露到外部服务器
	header.CleanInternalHeaders(retryablehttpRequest.Header)

	resp, err := retryablehttpClient.Do(retryablehttpRequest)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// clearCurrentProxy 清除当前代理缓存（用于Smart模式）
func (p *Proxy) clearCurrentProxy() {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.currentProxy = ""
	p.proxyOkCount = 0
	serverLogger.GetRawLogger().Debugf(constants.LogMsgCurrentProxyCacheCleared)
}

// retryWithOtherProxies 使用其他代理重试请求
func (p *Proxy) retryWithOtherProxies(r *http.Request, domain, failedProxy string, originalErr error) *http.Response {
	// 获取重试配置
	maxRetries := constants.DefaultConnectionMaxRetries
	retryDelay := time.Second
	retryBackoff := constants.DefaultRetryBackoffFactor
	maxDelay := time.Duration(constants.DefaultMaxDelaySeconds) * time.Second

	if p.Options.RuleConfig != nil {
		if p.Options.RuleConfig.Global.ProxyConnectionMaxRetries > 0 {
			maxRetries = p.Options.RuleConfig.Global.ProxyConnectionMaxRetries
		}
		if p.Options.RuleConfig.Global.ProxyConnectionRetryDelay != "" {
			if delay, err := time.ParseDuration(p.Options.RuleConfig.Global.ProxyConnectionRetryDelay); err == nil {
				retryDelay = delay
			}
		}
		if p.Options.RuleConfig.Global.ProxyConnectionRetryBackoff > 0 {
			retryBackoff = p.Options.RuleConfig.Global.ProxyConnectionRetryBackoff
		}
		if p.Options.RuleConfig.Global.ProxyConnectionMaxDelay != "" {
			if delay, err := time.ParseDuration(p.Options.RuleConfig.Global.ProxyConnectionMaxDelay); err == nil {
				maxDelay = delay
			}
		}
	}

	serverLogger.GetRawLogger().Infof(constants.LogFormatRetryStart, maxRetries, retryDelay)

	usedProxies := []string{failedProxy} // 记录已使用的代理
	currentDelay := retryDelay

	for i := 0; i < maxRetries; i++ {
		// 等待重试延迟
		if i > 0 {
			serverLogger.GetRawLogger().Debugf(constants.LogFormatRetryDelay, currentDelay)
			time.Sleep(currentDelay)

			// 计算下次延迟（指数退避）
			currentDelay = time.Duration(float64(currentDelay) * retryBackoff)
			if currentDelay > maxDelay {
				currentDelay = maxDelay
			}
		}

		// 获取新的代理
		newProxy, err := p.rotateProxy(domain)
		if err != nil {
			serverLogger.GetRawLogger().Errorf(constants.LogMsgProxyRetryGetFailed, i+1, err)
			continue
		}

		// 检查是否已经使用过这个代理
		alreadyUsed := false
		for _, used := range usedProxies {
			if used == newProxy {
				alreadyUsed = true
				break
			}
		}

		if alreadyUsed {
			serverLogger.GetRawLogger().Debugf(constants.LogMsgProxyRetrySkipUsed, i+1, newProxy)
			continue
		}

		usedProxies = append(usedProxies, newProxy)
		serverLogger.GetRawLogger().Debugf(constants.LogMsgProxyRetryUsingNew, i+1, newProxy)

		// 创建新的请求上下文，使用新代理
		retryCtx := context.WithValue(r.Context(), constants.ContextKeyCurrentProxy, newProxy)
		retryReq := r.WithContext(retryCtx)

		// 尝试使用新代理发送请求
		resp, err := p.tryProxyRequest(retryReq, domain, newProxy)
		if err != nil {
			serverLogger.GetRawLogger().Warnf(constants.LogMsgProxyRetryFailed, i+1, newProxy, err)
			continue
		}

		// 成功，读取响应体
		buf, err := io.ReadAll(resp.Body)
		if err != nil {
			serverLogger.GetRawLogger().Errorf(constants.LogMsgProxyRetryReadFailed, i+1, err)
			resp.Body.Close()
			continue
		}
		resp.Body.Close()
		resp.Body = io.NopCloser(bytes.NewBuffer(buf))

		// 清除成功代理的失败状态
		p.Options.ProxyManager.ClearProxyFailureStatus(newProxy)

		serverLogger.GetRawLogger().Infof(constants.LogMsgProxyRetrySuccess, i+1, newProxy, resp.StatusCode)
		return resp
	}

	serverLogger.GetRawLogger().Warnf(constants.LogMsgProxyRetryAllFailed, maxRetries)
	return nil
}

// isProxyPoolExhaustedError 判断是否为代理池耗尽错误
func isProxyPoolExhaustedError(err error) bool {
	if err == nil {
		return false
	}

	errMsg := err.Error()
	// 检查是否包含代理池耗尽相关的错误信息
	return strings.Contains(errMsg, constants.ProxyPoolKeywordNoAvailable) ||
		   strings.Contains(errMsg, constants.ProxyPoolKeywordNoAvailableAlt) ||
		   strings.Contains(errMsg, constants.ProxyPoolKeywordNone) ||
		   strings.Contains(errMsg, constants.ProxyPoolKeywordAllBanned) ||
		   strings.Contains(errMsg, constants.ProxyPoolKeywordPool) ||
		   strings.Contains(errMsg, constants.ProxyPoolKeywordNoProxies) ||
		   strings.Contains(errMsg, constants.ProxyPoolKeywordAllProxiesBanned)
}

// classifyProxyError 分类代理错误类型
func classifyProxyError(err error) string {
	if err == nil {
		return constants.ErrorTypeUnknown
	}

	errMsg := strings.ToLower(err.Error())

	// TLS相关错误
	if strings.Contains(errMsg, constants.ErrorKeywordTLS) || strings.Contains(errMsg, constants.ErrorKeywordHandshake) {
		if strings.Contains(errMsg, constants.ErrorKeywordTLSHandshakeRecord) {
			return constants.ErrorTypeTLSProtocolMismatch
		}
		return constants.ErrorTypeTLSError
	}

	// 连接相关错误
	if strings.Contains(errMsg, constants.ErrorKeywordConnectionRefused) || strings.Contains(errMsg, constants.ErrorKeywordConnectRefused) {
		return constants.ErrorTypeConnectionRefused
	}
	if strings.Contains(errMsg, constants.ErrorKeywordTimeout) || strings.Contains(errMsg, constants.ErrorKeywordDeadlineExceeded) {
		return constants.ErrorTypeTimeout
	}
	if strings.Contains(errMsg, constants.ErrorKeywordNetworkUnreachable) || strings.Contains(errMsg, constants.ErrorKeywordNoRouteToHost) {
		return constants.ErrorTypeNetworkUnreachable
	}

	// 代理相关错误
	if strings.Contains(errMsg, constants.ErrorKeywordProxyConnect) {
		return constants.ErrorTypeProxyConnectError
	}
	if strings.Contains(errMsg, constants.ErrorKeywordProxy) && strings.Contains(errMsg, constants.ErrorKeywordAuthentication) {
		return constants.ErrorTypeProxyAuthError
	}

	// DNS相关错误
	if strings.Contains(errMsg, constants.ErrorKeywordNoSuchHost) || strings.Contains(errMsg, constants.ErrorKeywordDNS) {
		return constants.ErrorTypeDNSError
	}

	// HTTP相关错误
	if strings.Contains(errMsg, constants.ErrorKeywordMalformedHTTP) || strings.Contains(errMsg, constants.ErrorKeywordBadRequest) {
		return constants.ErrorTypeHTTPError
	}

	return constants.ErrorTypeConnectionError
}
