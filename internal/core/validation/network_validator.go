package validation

import (
	"net"
	"net/url"
	"regexp"
	"strings"
	"unicode"
	"unicode/utf8"

	"golang.org/x/net/idna"

	"flexproxy/common/constants"
	"flexproxy/common/errors"
)

// NetworkValidator 网络地址验证器
type NetworkValidator struct {
	// 域名验证相关的正则表达式
	domainRegex     *regexp.Regexp
	ipv4Regex       *regexp.Regexp
	ipv6Regex       *regexp.Regexp
	hostnameRegex   *regexp.Regexp
	contentTypeRegex *regexp.Regexp

	// 配置选项
	allowIPv6       bool
	allowIDN        bool // 国际化域名
	maxDomainLength int
	maxLabelLength  int
}

// ValidationResult 验证结果
type ValidationResult struct {
	IsValid     bool   `json:"is_valid"`
	Type        string `json:"type"`        // "ipv4", "ipv6", "domain", "hostname", "url", "content_type"
	Original    string `json:"original"`    // 原始输入
	Normalized  string `json:"normalized"`  // 标准化后的地址
	Error       string `json:"error,omitempty"`
	ParsedURL   *url.URL `json:"-"`         // 解析后的URL（仅用于URL验证）
}

// NewNetworkValidator 创建网络验证器
func NewNetworkValidator() *NetworkValidator {
	return &NetworkValidator{
		// 严格的域名正则表达式
		domainRegex: regexp.MustCompile(`^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?$`),

		// IPv4地址正则表达式
		ipv4Regex: regexp.MustCompile(`^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`),

		// IPv6地址正则表达式（简化版）
		ipv6Regex: regexp.MustCompile(`^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$`),

		// 主机名正则表达式
		hostnameRegex: regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?$`),

		// Content-Type MIME类型正则表达式
		contentTypeRegex: regexp.MustCompile(`^[a-zA-Z][a-zA-Z0-9][a-zA-Z0-9\!\#\$\&\-\^\_]*\/[a-zA-Z0-9][a-zA-Z0-9\!\#\$\&\-\^\_\+]*(\s*;\s*[a-zA-Z0-9\-]+=([a-zA-Z0-9\-]+|"[^"]*"))*$`),

		allowIPv6:       true,
		allowIDN:        true,
		maxDomainLength: 253, // RFC 1035
		maxLabelLength:  63,  // RFC 1035
	}
}

// ValidateAddress 验证网络地址（IP或域名）
func (nv *NetworkValidator) ValidateAddress(address string) *ValidationResult {
	if address == "" {
		return &ValidationResult{
			IsValid:  false,
			Original: address,
			Error:    "地址不能为空",
		}
	}

	// 去除前后空格
	address = strings.TrimSpace(address)
	
	// 检查长度
	if len(address) > nv.maxDomainLength {
		return &ValidationResult{
			IsValid:  false,
			Original: address,
			Error:    "地址长度超过限制",
		}
	}

	// 检查是否看起来像IP地址（包含数字和点）
	looksLikeIPv4 := nv.looksLikeIPv4(address)
	looksLikeIPv6 := nv.looksLikeIPv6(address)

	// 如果看起来像IPv4，只进行IPv4验证
	if looksLikeIPv4 {
		return nv.validateIPv4(address)
	}

	// 如果看起来像IPv6，只进行IPv6验证
	if looksLikeIPv6 && nv.allowIPv6 {
		return nv.validateIPv6(address)
	}

	// 否则尝试域名验证
	return nv.validateDomain(address)
}

// validateIPv4 验证IPv4地址
func (nv *NetworkValidator) validateIPv4(address string) *ValidationResult {
	result := &ValidationResult{
		Original: address,
		Type:     "ipv4",
	}

	// 使用标准库验证
	ip := net.ParseIP(address)
	if ip == nil || ip.To4() == nil {
		result.IsValid = false
		result.Error = "无效的IPv4地址格式"
		return result
	}

	// 额外的格式检查
	if !nv.ipv4Regex.MatchString(address) {
		result.IsValid = false
		result.Error = "IPv4地址格式不规范"
		return result
	}

	// 检查特殊地址
	if nv.isPrivateOrReservedIPv4(ip) {
		// 可以选择是否允许私有地址，这里我们允许但标记
		result.Normalized = ip.String()
	} else {
		result.Normalized = ip.String()
	}

	result.IsValid = true
	return result
}

// validateIPv6 验证IPv6地址
func (nv *NetworkValidator) validateIPv6(address string) *ValidationResult {
	result := &ValidationResult{
		Original: address,
		Type:     "ipv6",
	}

	// 使用标准库验证
	ip := net.ParseIP(address)
	if ip == nil || ip.To4() != nil {
		result.IsValid = false
		result.Error = "无效的IPv6地址格式"
		return result
	}

	// 标准化IPv6地址
	result.Normalized = ip.String()
	result.IsValid = true
	return result
}

// validateDomain 验证域名
func (nv *NetworkValidator) validateDomain(domain string) *ValidationResult {
	result := &ValidationResult{
		Original: domain,
		Type:     "domain",
	}

	// 基本格式检查
	if !nv.isValidDomainFormat(domain) {
		result.IsValid = false
		result.Error = "域名格式无效"
		return result
	}

	// 处理国际化域名
	normalized, err := nv.normalizeDomain(domain)
	if err != nil {
		result.IsValid = false
		result.Error = "域名标准化失败: " + err.Error()
		return result
	}

	// 验证标准化后的域名
	if !nv.validateNormalizedDomain(normalized) {
		result.IsValid = false
		result.Error = "标准化后的域名无效"
		return result
	}

	result.Normalized = normalized
	result.IsValid = true
	return result
}

// isValidDomainFormat 检查域名基本格式
func (nv *NetworkValidator) isValidDomainFormat(domain string) bool {
	// 检查长度
	if len(domain) == 0 || len(domain) > nv.maxDomainLength {
		return false
	}

	// 不能以点开始或结束
	if strings.HasPrefix(domain, ".") || strings.HasSuffix(domain, ".") {
		return false
	}

	// 不能包含连续的点
	if strings.Contains(domain, "..") {
		return false
	}

	// 检查每个标签
	labels := strings.Split(domain, ".")
	if len(labels) < 2 {
		return false // 域名至少需要两个标签
	}

	for _, label := range labels {
		if !nv.isValidDomainLabel(label) {
			return false
		}
	}

	return true
}

// isValidDomainLabel 检查域名标签
func (nv *NetworkValidator) isValidDomainLabel(label string) bool {
	if len(label) == 0 || len(label) > nv.maxLabelLength {
		return false
	}

	// 不能以连字符开始或结束
	if strings.HasPrefix(label, "-") || strings.HasSuffix(label, "-") {
		return false
	}

	// 检查字符
	for _, r := range label {
		if !nv.isValidDomainChar(r) {
			return false
		}
	}

	return true
}

// isValidDomainChar 检查域名字符是否有效
func (nv *NetworkValidator) isValidDomainChar(r rune) bool {
	// ASCII字母数字和连字符
	if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') || r == '-' {
		return true
	}

	// 如果允许国际化域名，检查Unicode字符
	if nv.allowIDN && r > 127 {
		return unicode.IsLetter(r) || unicode.IsDigit(r)
	}

	return false
}

// normalizeDomain 标准化域名
func (nv *NetworkValidator) normalizeDomain(domain string) (string, error) {
	// 转换为小写
	domain = strings.ToLower(domain)

	// 如果包含非ASCII字符，进行IDNA转换
	if !nv.isASCII(domain) {
		if !nv.allowIDN {
			return "", errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "不支持国际化域名")
		}

		// 使用IDNA进行转换
		ascii, err := idna.ToASCII(domain)
		if err != nil {
			return "", errors.WrapError(err, errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "IDNA转换失败")
		}
		domain = ascii
	}

	return domain, nil
}

// validateNormalizedDomain 验证标准化后的域名
func (nv *NetworkValidator) validateNormalizedDomain(domain string) bool {
	// 使用正则表达式进行最终验证
	return nv.domainRegex.MatchString(domain)
}

// isASCII 检查字符串是否为纯ASCII
func (nv *NetworkValidator) isASCII(s string) bool {
	for i := 0; i < len(s); i++ {
		if s[i] >= utf8.RuneSelf {
			return false
		}
	}
	return true
}

// isPrivateOrReservedIPv4 检查是否为私有或保留的IPv4地址
func (nv *NetworkValidator) isPrivateOrReservedIPv4(ip net.IP) bool {
	// 私有地址范围
	privateRanges := []string{
		"10.0.0.0/8",
		"**********/12",
		"***********/16",
		"*********/8",   // 回环地址
		"***********/16", // 链路本地地址
	}

	for _, cidr := range privateRanges {
		_, network, err := net.ParseCIDR(cidr)
		if err != nil {
			continue
		}
		if network.Contains(ip) {
			return true
		}
	}

	return false
}

// ValidateHostPort 验证主机:端口格式
func (nv *NetworkValidator) ValidateHostPort(hostPort string) *ValidationResult {
	result := &ValidationResult{
		Original: hostPort,
		Type:     "hostport",
	}

	host, port, err := net.SplitHostPort(hostPort)
	if err != nil {
		result.IsValid = false
		result.Error = "主机:端口格式无效: " + err.Error()
		return result
	}

	// 验证主机部分
	hostResult := nv.ValidateAddress(host)
	if !hostResult.IsValid {
		result.IsValid = false
		result.Error = "主机部分无效: " + hostResult.Error
		return result
	}

	// 验证端口部分
	if !nv.isValidPort(port) {
		result.IsValid = false
		result.Error = "端口无效"
		return result
	}

	result.Normalized = net.JoinHostPort(hostResult.Normalized, port)
	result.IsValid = true
	return result
}

// isValidPort 验证端口号
func (nv *NetworkValidator) isValidPort(port string) bool {
	if port == "" {
		return false
	}

	// 检查是否为纯数字
	for _, r := range port {
		if r < '0' || r > '9' {
			return false
		}
	}

	// 检查范围 (1-65535)
	if len(port) > 5 {
		return false
	}

	// 简单的范围检查
	if port == "0" || (len(port) == 5 && port > "65535") {
		return false
	}

	return true
}

// IsValidDomain 简化的域名验证接口
func (nv *NetworkValidator) IsValidDomain(domain string) bool {
	result := nv.validateDomain(domain)
	return result.IsValid
}

// IsValidIP 简化的IP地址验证接口
func (nv *NetworkValidator) IsValidIP(ip string) bool {
	ipv4Result := nv.validateIPv4(ip)
	if ipv4Result.IsValid {
		return true
	}
	
	if nv.allowIPv6 {
		ipv6Result := nv.validateIPv6(ip)
		return ipv6Result.IsValid
	}
	
	return false
}

// SetOptions 设置验证器选项
func (nv *NetworkValidator) SetOptions(allowIPv6, allowIDN bool) {
	nv.allowIPv6 = allowIPv6
	nv.allowIDN = allowIDN
}

// looksLikeIPv4 检查地址是否看起来像IPv4格式
func (nv *NetworkValidator) looksLikeIPv4(address string) bool {
	// 检查是否包含3个点，且主要由数字和点组成
	dotCount := strings.Count(address, ".")
	if dotCount != 3 {
		return false
	}

	// 检查是否主要由数字和点组成
	for _, r := range address {
		if r != '.' && (r < '0' || r > '9') {
			return false
		}
	}

	return true
}

// looksLikeIPv6 检查地址是否看起来像IPv6格式
func (nv *NetworkValidator) looksLikeIPv6(address string) bool {
	// IPv6地址包含冒号
	return strings.Contains(address, ":")
}

// ValidateURL 验证URL格式和协议
// 支持HTTP和HTTPS协议，进行完整的URL解析和验证
func (nv *NetworkValidator) ValidateURL(urlStr string) *ValidationResult {
	result := &ValidationResult{
		Original: urlStr,
		Type:     "url",
	}

	if urlStr == "" {
		result.IsValid = false
		result.Error = "URL不能为空"
		return result
	}

	// 检查URL长度限制
	if len(urlStr) > constants.MaxURLLength {
		result.IsValid = false
		result.Error = "URL长度超过限制"
		return result
	}

	// 检查协议前缀
	if !strings.HasPrefix(urlStr, constants.HTTPPrefix) && !strings.HasPrefix(urlStr, constants.HTTPSPrefix) {
		result.IsValid = false
		result.Error = "URL必须以http://或https://开头"
		return result
	}

	// 使用标准库解析URL
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		result.IsValid = false
		result.Error = "URL格式无效: " + err.Error()
		return result
	}

	// 验证协议
	if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		result.IsValid = false
		result.Error = "不支持的协议: " + parsedURL.Scheme
		return result
	}

	// 验证主机名
	if parsedURL.Host == "" {
		result.IsValid = false
		result.Error = "URL缺少主机名"
		return result
	}

	// 验证主机名格式
	host := parsedURL.Hostname()
	if host != "" {
		hostResult := nv.ValidateAddress(host)
		if !hostResult.IsValid {
			result.IsValid = false
			result.Error = "主机名无效: " + hostResult.Error
			return result
		}
	}

	// 构建基础URL
	baseURL := parsedURL.Scheme + "://" + parsedURL.Host

	result.IsValid = true
	result.Normalized = baseURL
	result.ParsedURL = parsedURL
	return result
}

// ValidateContentType 验证Content-Type头部格式
// 检查是否符合MIME类型规范，支持参数（如charset）
func (nv *NetworkValidator) ValidateContentType(contentType string) *ValidationResult {
	result := &ValidationResult{
		Original: contentType,
		Type:     "content_type",
	}

	if contentType == "" {
		result.IsValid = false
		result.Error = "Content-Type不能为空"
		return result
	}

	// 移除前后空格
	contentType = strings.TrimSpace(contentType)

	// 基础格式检查：必须包含斜杠
	if !strings.Contains(contentType, "/") {
		result.IsValid = false
		result.Error = "Content-Type必须包含主类型/子类型格式"
		return result
	}

	// 分离主类型和参数
	parts := strings.Split(contentType, ";")
	mainType := strings.TrimSpace(parts[0])

	// 验证主类型/子类型格式
	typeParts := strings.Split(mainType, "/")
	if len(typeParts) != 2 || typeParts[0] == "" || typeParts[1] == "" {
		result.IsValid = false
		result.Error = "Content-Type主类型/子类型格式无效"
		return result
	}

	// 使用正则表达式进行详细验证
	if !nv.contentTypeRegex.MatchString(contentType) {
		result.IsValid = false
		result.Error = "Content-Type格式不符合MIME类型规范"
		return result
	}

	result.IsValid = true
	result.Normalized = strings.ToLower(mainType)
	return result
}

// GetBaseURL 从URL字符串返回基础URL和解析后的URL
// 这个方法迁移自 gateway/utils.go，提供统一的URL处理接口
func (nv *NetworkValidator) GetBaseURL(urlStr string) (string, *url.URL, error) {
	result := nv.ValidateURL(urlStr)
	if !result.IsValid {
		return "", nil, errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, result.Error)
	}

	return result.Normalized, result.ParsedURL, nil
}

// IsHTTPURL 检查给定的URL字符串是否为HTTP或HTTPS
// 这个方法迁移自 gateway/utils.go，提供统一的URL检查接口
func (nv *NetworkValidator) IsHTTPURL(urlStr string) bool {
	result := nv.ValidateURL(urlStr)
	return result.IsValid
}

// ValidateCIDR 验证CIDR网络地址格式
// 支持IPv4和IPv6的CIDR格式验证，检查IP地址和子网掩码的有效性
func (nv *NetworkValidator) ValidateCIDR(cidr string) *ValidationResult {
	result := &ValidationResult{
		Original: cidr,
		Type:     "cidr",
	}

	if cidr == "" {
		result.IsValid = false
		result.Error = "CIDR不能为空"
		return result
	}

	// 使用标准库解析CIDR
	ip, ipNet, err := net.ParseCIDR(cidr)
	if err != nil {
		result.IsValid = false
		result.Error = "CIDR格式无效: " + err.Error()
		return result
	}

	// 验证IP地址是否与网络地址匹配
	if !ip.Equal(ipNet.IP) {
		result.IsValid = false
		result.Error = "IP地址与网络地址不匹配"
		return result
	}

	// 获取子网掩码位数
	ones, bits := ipNet.Mask.Size()

	// 验证子网掩码范围
	if ones < 0 || ones > bits {
		result.IsValid = false
		result.Error = "子网掩码位数无效"
		return result
	}

	// 检查是否为IPv4或IPv6
	if ip.To4() != nil {
		// IPv4: 掩码位数应该在0-32之间
		if ones > 32 {
			result.IsValid = false
			result.Error = "IPv4子网掩码位数不能超过32"
			return result
		}
		result.Type = "cidr_ipv4"
	} else {
		// IPv6: 掩码位数应该在0-128之间
		if ones > 128 {
			result.IsValid = false
			result.Error = "IPv6子网掩码位数不能超过128"
			return result
		}
		result.Type = "cidr_ipv6"
	}

	result.IsValid = true
	result.Normalized = ipNet.String()
	return result
}

// IsValidCIDR 简化的CIDR验证接口
// 提供向后兼容的布尔返回值接口
func (nv *NetworkValidator) IsValidCIDR(cidr string) bool {
	result := nv.ValidateCIDR(cidr)
	return result.IsValid
}
