package integration

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"flexproxy/internal/config"
	"flexproxy/internal/config/hotreload"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestHotReloadIntegration 测试热重载集成功能
func TestHotReloadIntegration(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "hotreload_test")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// 创建测试配置文件
	configFile := filepath.Join(tempDir, "test_config.yaml")
	initialConfig := `
global:
  enable: true
  proxy_file: "./proxies.txt"
  
server:
  host: "127.0.0.1"
  
ports:
  http: 8080
  https: 8443
  socks: 1080

logging:
  level: "info"
  file: "./logs/flexproxy.log"
`

	err = os.WriteFile(configFile, []byte(initialConfig), 0644)
	require.NoError(t, err)

	// 加载初始配置（跳过验证以简化测试）
	cfg, err := config.LoadConfigFromYAMLWithoutValidation(configFile)
	require.NoError(t, err)

	// 创建配置管理器
	configManagerOptions := config.ConfigManagerOptions{
		MaxBackups:     5,
		BackupDir:      filepath.Join(tempDir, "backups"),
		EnableAutoSave: false,
		SaveInterval:   60,
	}
	configManager := config.NewConfigManager(configManagerOptions)

	// 创建热重载集成适配器
	integration, err := hotreload.NewHotReloadIntegration(
		cfg,
		configManager,
		nil, // 配置服务
		nil, // 使用默认logger
		nil, // 使用默认选项
	)
	require.NoError(t, err)

	// 设置配置文件路径
	integration.SetConfigFile(configFile)

	// 启动热重载系统
	err = integration.Start()
	require.NoError(t, err)
	defer integration.Stop()

	// 验证初始状态
	assert.True(t, integration.IsRunning())
	assert.Equal(t, configFile, integration.GetConfigFile())

	// 测试配置变更回调
	callbackCalled := false
	var callbackConfig *config.Config

	integration.RegisterReloadCallback(func(newCfg *config.Config) error {
		callbackCalled = true
		callbackConfig = newCfg
		return nil
	})

	// 修改配置文件
	updatedConfig := `
global:
  enable: true
  proxy_file: "./proxies.txt"
  
server:
  host: "0.0.0.0"  # 修改主机地址
  
ports:
  http: 9080       # 修改端口
  https: 9443
  socks: 1080

logging:
  level: "debug"   # 修改日志级别
  file: "./logs/flexproxy.log"
`

	err = os.WriteFile(configFile, []byte(updatedConfig), 0644)
	require.NoError(t, err)

	// 手动触发重载
	err = integration.ReloadConfig(configFile)
	require.NoError(t, err)

	// 验证回调被调用
	assert.True(t, callbackCalled, "配置变更回调应该被调用")
	assert.NotNil(t, callbackConfig, "回调配置不应该为空")

	// 验证配置变更
	if callbackConfig != nil {
		assert.Equal(t, "0.0.0.0", callbackConfig.Server.Host)
		assert.Equal(t, 9080, callbackConfig.Ports.HTTP)
		assert.Equal(t, "debug", callbackConfig.Logging.Level)
	}

	// 获取统计信息
	stats := integration.GetStats()
	assert.NotNil(t, stats)
	assert.True(t, stats["running"].(bool))
	assert.Equal(t, configFile, stats["config_file"].(string))
	assert.Equal(t, 1, stats["callback_count"].(int))
}

// TestHotReloadEngineBasic 测试热重载引擎基本功能
func TestHotReloadEngineBasic(t *testing.T) {
	// 创建测试配置
	cfg := &config.Config{
		Global: config.GlobalConfig{
			Enable:    true,
			ProxyFile: "./proxies.txt",
		},
		Server: &config.ServerConfig{
			Host: "127.0.0.1",
		},
		Ports: &config.PortsConfig{
			HTTP:  8080,
			HTTPS: 8443,
			SOCKS: 1080,
		},
		Logging: &config.LoggingConfig{
			Level: "info",
			File:  "./logs/flexproxy.log",
		},
	}

	// 创建热重载引擎
	engine, err := hotreload.NewHotReloadEngine(cfg, nil, nil)
	require.NoError(t, err)

	// 启动引擎
	err = engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	// 验证引擎状态
	assert.True(t, engine.IsRunning())

	// 创建修改后的配置
	newCfg := &config.Config{
		Global: config.GlobalConfig{
			Enable:    true,
			ProxyFile: "./proxies.txt",
		},
		Server: &config.ServerConfig{
			Host: "0.0.0.0", // 修改主机地址
		},
		Ports: &config.PortsConfig{
			HTTP:  9080, // 修改端口
			HTTPS: 9443,
			SOCKS: 1080,
		},
		Logging: &config.LoggingConfig{
			Level: "debug", // 修改日志级别
			File:  "./logs/flexproxy.log",
		},
	}

	// 处理配置变更
	result, err := engine.ProcessConfigChange(newCfg)
	require.NoError(t, err)

	// 验证结果
	assert.True(t, result.Success)
	assert.Greater(t, len(result.ChangedPaths), 0)
	assert.NotNil(t, result.ChangeReport)

	// 验证配置已更新
	currentConfig := engine.GetCurrentConfig()
	assert.Equal(t, "0.0.0.0", currentConfig.Server.Host)
	assert.Equal(t, 9080, currentConfig.Ports.HTTP)
	assert.Equal(t, "debug", currentConfig.Logging.Level)
}

// TestHotReloadConcurrency 测试热重载并发安全性
func TestHotReloadConcurrency(t *testing.T) {
	// 创建测试配置
	cfg := &config.Config{
		Global: config.GlobalConfig{
			Enable:    true,
			ProxyFile: "./proxies.txt",
		},
		Server: &config.ServerConfig{
			Host: "127.0.0.1",
		},
		Ports: &config.PortsConfig{
			HTTP:  8080,
			HTTPS: 8443,
			SOCKS: 1080,
		},
	}

	// 创建热重载引擎
	engine, err := hotreload.NewHotReloadEngine(cfg, nil, nil)
	require.NoError(t, err)

	// 启动引擎
	err = engine.Start()
	require.NoError(t, err)
	defer engine.Stop()

	// 并发测试
	const numGoroutines = 10
	const numOperations = 5

	done := make(chan bool, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer func() { done <- true }()

			for j := 0; j < numOperations; j++ {
				// 创建不同的配置变更
				newCfg := &config.Config{
					Global: config.GlobalConfig{
						Enable:    true,
						ProxyFile: "./proxies.txt",
					},
					Server: &config.ServerConfig{
						Host: "127.0.0.1",
					},
					Ports: &config.PortsConfig{
						HTTP:  8080 + id*100 + j, // 不同的端口
						HTTPS: 8443,
						SOCKS: 1080,
					},
				}

				// 处理配置变更
				_, err := engine.ProcessConfigChange(newCfg)
				if err != nil {
					t.Errorf("Goroutine %d operation %d failed: %v", id, j, err)
				}

				// 短暂休眠
				time.Sleep(10 * time.Millisecond)
			}
		}(i)
	}

	// 等待所有goroutine完成
	for i := 0; i < numGoroutines; i++ {
		select {
		case <-done:
			// 成功完成
		case <-time.After(30 * time.Second):
			t.Fatal("并发测试超时")
		}
	}

	// 验证引擎仍在运行
	assert.True(t, engine.IsRunning())
}
