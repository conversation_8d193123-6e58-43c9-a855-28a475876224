#!/bin/bash

# 修复日志格式化问题的脚本

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo "修复日志格式化问题..."

# 修复 lifecycle.go 中的问题
sed -i '' 's/lm\.logger\.Debug("注册配置项: %s, 版本: %d", item\.Path, item\.Version)/lm.logger.Debug(fmt.Sprintf("注册配置项: %s, 版本: %d", item.Path, item.Version))/g' internal/config/hotreload/lifecycle.go

sed -i '' 's/lm\.logger\.Debug("更新配置项: %s, 新版本: %d", path, newVersion)/lm.logger.Debug(fmt.Sprintf("更新配置项: %s, 新版本: %d", path, newVersion))/g' internal/config/hotreload/lifecycle.go

sed -i '' 's/lm\.logger\.Debug("清理过期配置项: %s", path)/lm.logger.Debug(fmt.Sprintf("清理过期配置项: %s", path))/g' internal/config/hotreload/lifecycle.go

sed -i '' 's/lm\.logger\.Info("更新配置项: %s, 新版本: %d", path, newVersion)/lm.logger.Info(fmt.Sprintf("更新配置项: %s, 新版本: %d", path, newVersion))/g' internal/config/hotreload/lifecycle.go

sed -i '' 's/lm\.logger\.Info("清理了 %d 个过期配置项", cleanedCount)/lm.logger.Info(fmt.Sprintf("清理了 %d 个过期配置项", cleanedCount))/g' internal/config/hotreload/lifecycle.go

sed -i '' 's/lm\.logger\.Debug("配置项清理协程已停止")/lm.logger.Debug("配置项清理协程已停止")/g' internal/config/hotreload/lifecycle.go

# 修复 mapper.go 中的问题
sed -i '' 's/cm\.logger\.Info("配置项路径映射系统初始化完成，共映射 %d 个配置项", len(cm\.pathMap))/cm.logger.Info(fmt.Sprintf("配置项路径映射系统初始化完成，共映射 %d 个配置项", len(cm.pathMap)))/g' internal/config/hotreload/mapper.go

# 修复 integration.go 中剩余的问题
sed -i '' 's/hri\.logger\.Debug("开始监控配置文件: %s", filePath)/hri.logger.Debug(fmt.Sprintf("开始监控配置文件: %s", filePath))/g' internal/config/hotreload/integration.go

sed -i '' 's/hri\.logger\.Debug("停止监控配置文件，监控的文件数: %d", len(hri\.watchedFiles))/hri.logger.Debug(fmt.Sprintf("停止监控配置文件，监控的文件数: %d", len(hri.watchedFiles)))/g' internal/config/hotreload/integration.go

# 修复 events.go 中的问题
sed -i '' 's/eb\.logger\.Debug("发布配置变更事件: %s, 路径: %s, 类型: %s", event\.ID, event\.Path, event\.Type)/eb.logger.Debug(fmt.Sprintf("发布配置变更事件: %s, 路径: %s, 类型: %s", event.ID, event.Path, event.Type))/g' internal/config/hotreload/events.go

sed -i '' 's/eb\.logger\.Debug("处理配置变更事件: %s, 路径: %s", event\.ID, event\.Path)/eb.logger.Debug(fmt.Sprintf("处理配置变更事件: %s, 路径: %s", event.ID, event.Path))/g' internal/config/hotreload/events.go

sed -i '' 's/eb\.logger\.Debug("没有找到事件订阅者: %s", event\.Type)/eb.logger.Debug(fmt.Sprintf("没有找到事件订阅者: %s", event.Type))/g' internal/config/hotreload/events.go

# 修复 differ.go 中的问题
sed -i '' 's/cd\.logger\.Info("配置差异检测完成，耗时: %v, 变更项: %d\/%d", elapsed, len(changes), totalItems)/cd.logger.Info(fmt.Sprintf("配置差异检测完成，耗时: %v, 变更项: %d\/%d", elapsed, len(changes), totalItems))/g' internal/config/hotreload/differ.go

sed -i '' 's/cd\.logger\.Warn("比较配置项失败: %s, 错误: %v", path, err)/cd.logger.Warn(fmt.Sprintf("比较配置项失败: %s, 错误: %v", path, err))/g' internal/config/hotreload/differ.go

# 修复 concurrent.go 中的问题
sed -i '' 's/cp\.logger\.Info("开始原子性配置切换，涉及 %d 个配置项", len(changes))/cp.logger.Info(fmt.Sprintf("开始原子性配置切换，涉及 %d 个配置项", len(changes)))/g' internal/config/hotreload/concurrent.go

sed -i '' 's/cp\.logger\.Debug("获取锁成功: %s, 类型: %s, 持有者: %s", lockKey, lockType, holder)/cp.logger.Debug(fmt.Sprintf("获取锁成功: %s, 类型: %s, 持有者: %s", lockKey, lockType, holder))/g' internal/config/hotreload/concurrent.go

sed -i '' 's/cp\.logger\.Debug("释放锁成功: %s, 类型: %s, 持有者: %s, 持有时长: %v", lockKey, lockType, holder, duration)/cp.logger.Debug(fmt.Sprintf("释放锁成功: %s, 类型: %s, 持有者: %s, 持有时长: %v", lockKey, lockType, holder, duration))/g' internal/config/hotreload/concurrent.go

# 修复 engine.go 中的问题
sed -i '' 's/hre\.logger\.Info("注册了 %d 个配置项到生命周期管理器", len(configPaths))/hre.logger.Info(fmt.Sprintf("注册了 %d 个配置项到生命周期管理器", len(configPaths)))/g' internal/config/hotreload/engine.go

sed -i '' 's/hre\.logger\.Info("检测到配置变更: %s", summary)/hre.logger.Info(fmt.Sprintf("检测到配置变更: %s", summary))/g' internal/config/hotreload/engine.go

sed -i '' 's/hre\.logger\.Debug("应用配置变更: %s, 类型: %s", change\.Path, change\.Type)/hre.logger.Debug(fmt.Sprintf("应用配置变更: %s, 类型: %s", change.Path, change.Type))/g' internal/config/hotreload/engine.go

echo "日志格式化问题修复完成"
