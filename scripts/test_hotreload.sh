#!/bin/bash

# FlexProxy 热重载系统测试脚本
# 用于执行完整的热重载功能测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}========================================${NC}"
    echo -e "${PURPLE} $1${NC}"
    echo -e "${PURPLE}========================================${NC}"
}

# 检查Go环境
check_go_environment() {
    log_header "检查Go环境"
    
    if ! command -v go &> /dev/null; then
        log_error "Go未安装或不在PATH中"
        exit 1
    fi
    
    GO_VERSION=$(go version | awk '{print $3}')
    log_info "Go版本: $GO_VERSION"
    
    # 检查Go模块
    if [ ! -f "go.mod" ]; then
        log_error "未找到go.mod文件"
        exit 1
    fi
    
    log_success "Go环境检查通过"
}

# 编译检查
compile_check() {
    log_header "编译检查"
    
    log_info "检查项目编译状态..."
    if go build ./... > /dev/null 2>&1; then
        log_success "项目编译成功"
    else
        log_error "项目编译失败"
        go build ./...
        exit 1
    fi
}

# 运行热重载核心测试
run_core_tests() {
    log_header "热重载核心功能测试"
    
    log_info "运行热重载引擎测试..."
    if go test ./internal/config/hotreload -v -timeout 60s; then
        log_success "热重载引擎测试通过"
    else
        log_error "热重载引擎测试失败"
        return 1
    fi
}

# 运行集成测试
run_integration_tests() {
    log_header "热重载集成测试"
    
    log_info "运行简化热重载测试..."
    if go test ./tests/integration -v -run TestSimplified -timeout 120s; then
        log_success "简化热重载测试通过"
    else
        log_error "简化热重载测试失败"
        return 1
    fi
    
    log_info "运行性能测试..."
    if go test ./tests/integration -v -run TestHotReloadPerformance -timeout 120s; then
        log_success "性能测试通过"
    else
        log_error "性能测试失败"
        return 1
    fi
}

# 运行基准测试
run_benchmark_tests() {
    log_header "热重载性能基准测试"
    
    log_info "运行基准测试..."
    if go test ./internal/config/hotreload -bench=. -benchmem -timeout 300s; then
        log_success "基准测试完成"
    else
        log_warning "基准测试可能不存在或失败"
    fi
}

# 生成测试报告
generate_test_report() {
    log_header "生成测试报告"
    
    REPORT_DIR="test_reports"
    mkdir -p "$REPORT_DIR"
    
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    REPORT_FILE="$REPORT_DIR/hotreload_test_$TIMESTAMP.txt"
    
    log_info "生成详细测试报告..."
    
    {
        echo "FlexProxy 热重载系统测试报告"
        echo "生成时间: $(date)"
        echo "Go版本: $(go version)"
        echo "项目路径: $PROJECT_ROOT"
        echo ""
        echo "========================================="
        echo "测试结果汇总"
        echo "========================================="
        
        # 运行测试并捕获输出
        echo ""
        echo "--- 核心功能测试 ---"
        go test ./internal/config/hotreload -v -timeout 60s 2>&1 || true
        
        echo ""
        echo "--- 集成测试 ---"
        go test ./tests/integration -v -run TestSimplified -timeout 120s 2>&1 || true
        
        echo ""
        echo "--- 性能测试 ---"
        go test ./tests/integration -v -run TestHotReloadPerformance -timeout 120s 2>&1 || true
        
        echo ""
        echo "--- 基准测试 ---"
        go test ./internal/config/hotreload -bench=. -benchmem -timeout 300s 2>&1 || true
        
    } > "$REPORT_FILE"
    
    log_success "测试报告已生成: $REPORT_FILE"
}

# 清理测试环境
cleanup_test_environment() {
    log_header "清理测试环境"
    
    # 清理临时文件
    find . -name "*.test" -delete 2>/dev/null || true
    find . -name "test_*" -type d -exec rm -rf {} + 2>/dev/null || true
    
    log_success "测试环境清理完成"
}

# 显示帮助信息
show_help() {
    echo "FlexProxy 热重载系统测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -c, --core     仅运行核心功能测试"
    echo "  -i, --integration  仅运行集成测试"
    echo "  -p, --performance  仅运行性能测试"
    echo "  -b, --benchmark    仅运行基准测试"
    echo "  -r, --report   生成详细测试报告"
    echo "  -a, --all      运行所有测试（默认）"
    echo ""
    echo "示例:"
    echo "  $0                 # 运行所有测试"
    echo "  $0 -c              # 仅运行核心功能测试"
    echo "  $0 -i -p           # 运行集成测试和性能测试"
    echo "  $0 -r              # 生成详细测试报告"
}

# 主函数
main() {
    local run_core=false
    local run_integration=false
    local run_performance=false
    local run_benchmark=false
    local generate_report=false
    local run_all=true
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--core)
                run_core=true
                run_all=false
                shift
                ;;
            -i|--integration)
                run_integration=true
                run_all=false
                shift
                ;;
            -p|--performance)
                run_performance=true
                run_all=false
                shift
                ;;
            -b|--benchmark)
                run_benchmark=true
                run_all=false
                shift
                ;;
            -r|--report)
                generate_report=true
                run_all=false
                shift
                ;;
            -a|--all)
                run_all=true
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 开始测试
    log_header "FlexProxy 热重载系统测试"
    
    # 基础检查
    check_go_environment
    compile_check
    
    local test_failed=false
    
    # 根据参数运行相应测试
    if [ "$run_all" = true ]; then
        run_core_tests || test_failed=true
        run_integration_tests || test_failed=true
        run_benchmark_tests || test_failed=true
    else
        [ "$run_core" = true ] && (run_core_tests || test_failed=true)
        [ "$run_integration" = true ] && (run_integration_tests || test_failed=true)
        [ "$run_performance" = true ] && (run_integration_tests || test_failed=true)
        [ "$run_benchmark" = true ] && (run_benchmark_tests || test_failed=true)
    fi
    
    # 生成报告
    if [ "$generate_report" = true ]; then
        generate_test_report
    fi
    
    # 清理环境
    cleanup_test_environment
    
    # 测试结果总结
    log_header "测试完成"
    
    if [ "$test_failed" = true ]; then
        log_error "部分测试失败，请检查上述输出"
        exit 1
    else
        log_success "所有测试通过！"
        log_info "FlexProxy热重载系统功能正常"
    fi
}

# 执行主函数
main "$@"
