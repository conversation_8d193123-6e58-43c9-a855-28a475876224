#!/usr/bin/env python3

import os
import re
import glob

def fix_logging_in_file(filepath):
    """修复单个文件中的日志格式化问题"""
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 匹配所有的日志调用模式
    patterns = [
        # logger.Info("text: %s", var) -> logger.Info(fmt.Sprintf("text: %s", var))
        (r'(\w+\.logger\.(Debug|Info|Warn|Error))\("([^"]*%[^"]*)", ([^)]+)\)', 
         r'\1(fmt.Sprintf("\3", \4))'),
        
        # logger.Info("text: %d", var) -> logger.Info(fmt.Sprintf("text: %d", var))
        (r'(\w+\.logger\.(Debug|Info|Warn|Error))\("([^"]*%[^"]*)", ([^)]+)\)', 
         r'\1(fmt.Sprintf("\3", \4))'),
         
        # logger.Info("text: %v", var) -> logger.Info(fmt.Sprintf("text: %v", var))
        (r'(\w+\.logger\.(Debug|Info|Warn|Error))\("([^"]*%[^"]*)", ([^)]+)\)', 
         r'\1(fmt.Sprintf("\3", \4))'),
    ]
    
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)
    
    # 如果内容有变化，写回文件
    if content != original_content:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"修复了文件: {filepath}")
        return True
    
    return False

def main():
    """主函数"""
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    hotreload_dir = os.path.join(project_root, 'internal', 'config', 'hotreload')
    
    print("开始修复日志格式化问题...")
    
    # 查找所有Go文件
    go_files = glob.glob(os.path.join(hotreload_dir, '*.go'))
    
    fixed_count = 0
    for go_file in go_files:
        if fix_logging_in_file(go_file):
            fixed_count += 1
    
    print(f"修复完成，共修复了 {fixed_count} 个文件")

if __name__ == "__main__":
    main()
