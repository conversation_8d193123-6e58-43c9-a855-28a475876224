---
type: "always_apply"
---

- 完全遵守软件工程开发理念：高内聚、低耦合
- 代码注释需要中文进行注释
- 不需要保持任何向后兼容性
- 所有代码相关的问题都要基于实际存在的代码和配置结构,而不是基于推测

**代码质量要求**：
- 禁止简化实现（不允许使用任何占位符、TODO标记或简化的实现逻辑）
- 完整业务逻辑（每个方法都必须包含完整的业务逻辑实现，不能只是返回默认值）
- 产级别错误处理（所有错误情况都必须有详细的错误处理和恢复机制）
- 完整的字段映射（确保所有配置结构体字段都正确映射，不能跳过或忽略任何字段）

**禁止的做法**：
- 不允许注释掉有问题的代码
- 不允许使用临时的占位符实现
- 不允许跳过复杂的业务逻辑
- 不允许使用简单的返回语句来"修复"编译错误
- 不允许过度设计

**验证标准**：
- 编译必须 100% 成功，无任何错误或警告
- 所有方法都必须有完整的实现，不能有空方法体
- 错误处理必须详细且有意义，包含中文错误信息
- 日志记录必须完整且使用中文