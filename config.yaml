# FlexProxy 完整配置文件模板
# 本文件包含所有可配置的选项，基于 common/config_types.go 结构体定义
# 版本: 1.0.0
# 更新时间: 2025-01-12

# ============================================================================
# 全局配置 (Global Configuration)
# ============================================================================
global:
  # 基础配置
  enable: true                              # 是否启用代理服务 (必需)
  proxy_file: "./proxies.txt"               # 代理文件路径 (必需)
  
  # 全局封禁IP配置
  global_banned_ips:                        # 全局封禁IP列表
    - ip: "***********00"                   # IP地址 (必需，有效IP格式)
      duration: 3600                        # 封禁时长：整数(秒)、Go时间格式或"reboot"
    - ip: "*********"
      duration: "1h30m"                     # Go时间格式示例
    - ip: "***********"
      duration: "reboot"                    # 重启后解封
  
  # 全局封禁域名配置
  banned_domains:                           # 全局封禁域名列表
    - domain: "malicious-site.com"          # 域名 (必需，FQDN格式)
      duration: 86400                       # 封禁时长：整数(秒)、Go时间格式或"reboot"
    - domain: "spam-domain.net"
      duration: "24h"                       # Go时间格式示例
    - domain: "blocked-forever.com"
      duration: "reboot"                    # 重启后解封
  
  # IP访问控制
  blocked_ips:                              # 阻止访问的IP地址列表 (有效IP格式)
    - "***********"
    - "************"
    - "*********"
    - "***********"
  
  trusted_ips:                              # 信任的IP地址列表 (有效IP格式)
    - "127.0.0.1"                           # 本地回环地址IPv4
    - "::1"                                 # 本地回环地址IPv6
    - "***********"                         # 内网网关IP示例
    - "********"                            # 内网IP示例
  
  # URL过滤配置
  excluded_patterns:                        # 排除的URL模式列表 (支持正则表达式和通配符)
    - "*.local"
    - "localhost:*"
    - "127.0.0.1:*"
    - "*.internal.company.com"
    - "*.png"
    - "*.jpg"
    - "*.jpeg"
    - "*.gif"
    - "*.svg"
    - "*.ico"
    - "*.css"
    - "*.js"
    - "*.woff"
    - "*.woff2"
    - "*.ttf"
    - "*.mp3"
    - "*.mp4"
    - "*.avi"
    - "*.mov"
    - "*.pdf"
    - "*.zip"
    - "*.rar"
  
  excluded_scope: "all"                     # 排除范围: extension, domain, url, all
  
  # 规则配置
  rule_priority: 50                         # 规则优先级: 0-100 (数值越高优先级越高)
  default_process_stage: "pre"              # 默认处理阶段: pre, post_header, post_body
  
  # 代理轮换配置
  ip_rotation_mode: "sequential"            # IP轮换模式: random, sequential, quality, smart
  min_proxy_pool_size: 15                   # 最小代理池大小: >=1
  max_proxy_fetch_attempts: 5               # 从代理池中寻找未封禁代理的最大尝试次数: 1-10

  # 代理连接失败自动重试配置（当单个代理连接失败时自动尝试其他代理）
  proxy_connection_retry_enabled: true      # 是否启用代理连接失败自动重试功能: true/false
  proxy_connection_max_retries: 3           # 代理连接失败时的最大重试次数（尝试其他代理）: 0-10
  proxy_connection_retry_delay: "1s"        # 代理连接重试的初始延迟时间: 支持时间格式如1s, 500ms
  proxy_connection_retry_backoff: 2.0       # 代理连接重试延迟的指数退避倍数（每次重试延迟=上次延迟×此倍数，如1s→2s→4s）: >=1.0
  proxy_connection_max_delay: "10s"         # 代理连接重试的最大延迟时间（防止延迟时间无限增长）: 支持时间格式

  # 触发器系统重试配置（用于触发器动作中的重试功能）
  retry_proxy_reuse_policy: "cooldown"      # 触发器重试时的代理复用策略: allow, deny, cooldown
  retry_proxy_cooldown_time: 60             # 触发器重试时代理的冷却时间(秒): >=0
  retry_proxy_global_tracking: true         # 触发器重试时是否启用全局代理跟踪: true/false

  # 命令行参数对应的配置选项
  verbose: false                            # 详细模式，对应 -v, --verbose 参数
  max_retries: 3                            # HTTP请求最大重试次数，对应 --max-retries 参数
  daemon: false                             # 守护进程模式，对应 -d, --daemon 参数
  sync: false                               # 同步模式，对应 -s, --sync 参数
  watch: false                              # 监控文件变化，对应 -w, --watch 参数

# ============================================================================
# 服务器配置 (Server Configuration)
# ============================================================================
server:
  # 基础网络配置
  host: "0.0.0.0"                          # 服务器监听地址 (必需)

  # 完整监听地址配置 (可选，优先级高于 host + ports 组合配置)
  listen_address: "0.0.0.0:8080"         # 完整监听地址，如果设置则忽略 host 和 ports 配置

  # 命令行参数对应的配置选项
  auth: ""                                # 代理服务器认证，格式为 username:password，对应 -A, --auth 参数
  type: "http"                            # 代理服务器类型: http, socks5，对应 --type 参数

  # 连接池配置
  max_idle_conns: 200                       # 最大空闲连接数: >=0
  max_idle_conns_per_host: 10               # 每个主机最大空闲连接数: >=0
  max_conns_per_host: 50                    # 每个主机最大连接数: >=0
  
  # 缓冲区配置
  buffer_size: 16384                        # 缓冲区大小: >=1024
  max_header_bytes: 1048576                 # 最大请求头字节数: >=1024
  debounce_delay: "100ms"                   # 防抖延迟
  
  # 压缩配置
  compression:
    enabled: true                           # 是否启用压缩
    algorithms: ["gzip", "deflate"]         # 压缩算法: gzip, deflate, br
    level: 6                                # 压缩级别: -1 到 9
    min_size: 1024                          # 最小压缩大小: >=0
  
  # HTTP/2配置
  http2:
    enabled: true                           # 是否启用HTTP/2
    max_concurrent_streams: 250             # 最大并发流数: >=1
    max_frame_size: 1048576                 # 最大帧大小: >=16384
    initial_window_size: 65536              # 初始窗口大小: >=65535
    server_push: false                      # 是否启用服务器推送
  
  # 性能分析配置
  profiling:
    enabled: false                          # 是否启用性能分析
    port: 6060                              # 性能分析端口: 1-65535

# ============================================================================
# 代理配置 (Proxy Configuration)
# ============================================================================
proxy:
  # 基础配置
  strategy: "sequential"                    # 代理策略: random, sequential, quality, custom
  load_balancer: "round_robin"              # 负载均衡器: round_robin, weighted_round_robin, least_connections, response_time, ip_hash
  max_retries: 3                            # 代理服务模块内部操作失败时的重试次数（如代理池刷新、健康检查等）: >=0

  # 代理池配置
  pool_size: 100                            # 代理池大小: >=1
  rotation_interval: 300                    # 轮换间隔(秒): >=0
  
  # 健康检查配置
  health_check:
    enabled: true                           # 是否启用健康检查
    path: "/health"                         # 健康检查路径
    max_consecutive_failures: 3             # 最大连续失败次数: >=1
    max_consecutive_successes: 2            # 最大连续成功次数: >=1
  
  # 质量评分配置
  quality_score:
    default: 0.5                            # 默认质量分数: 0-1
    success_rate_weight: 0.6                # 成功率权重: 0-1
    response_time_weight: 0.4               # 响应时间权重: 0-1
    max_failure_rate: 0.3                   # 最大失败率: 0-1
    top_proxy_ratio: 0.2                    # 顶级代理比例: 0-1
    response_time_baseline: 1000            # 响应时间基线(毫秒): >=0
    smoothing_factor: 0.1                   # 平滑因子: 0-1
  
  # 故障转移配置
  failover:
    enabled: true                           # 是否启用故障转移
    max_failures: 5                         # 最大失败次数: >=1
    failure_window: "5m"                    # 失败时间窗口
    recovery_time: "10m"                    # 恢复时间

# ============================================================================
# 缓存配置 (Cache Configuration)
# ============================================================================
cache:
  # 全局缓存配置
  global:
    enabled: true                           # 是否启用全局缓存
    default_ttl: "3600s"                    # 默认缓存TTL
    default_size: 1000                      # 最大缓存条目数: >=1
    cleanup_interval: "300s"                # 清理间隔
    max_memory_usage: "512MB"               # 最大内存使用量
    eviction_policy: "lru"                  # 淘汰策略: lru, lfu, fifo, random
  
  # 模块缓存配置
  modules:
    dns:
      enabled: true                         # DNS缓存启用状态
      ttl: "300s"                           # DNS缓存TTL
      size: 1000                            # DNS缓存大小
      cleanup_interval: "600s"              # DNS缓存清理间隔
      eviction_policy: "lru"                # DNS缓存淘汰策略
      compression: false                    # DNS缓存压缩
    
    proxy:
      enabled: true                         # 代理缓存启用状态
      ttl: "1800s"                          # 代理缓存TTL
      size: 500                             # 代理缓存大小
      cleanup_interval: "900s"              # 代理缓存清理间隔
      eviction_policy: "lru"                # 代理缓存淘汰策略
      compression: false                    # 代理缓存压缩
    
    session:
      enabled: true                         # 会话缓存启用状态
      ttl: "7200s"                          # 会话缓存TTL
      size: 2000                            # 会话缓存大小
      cleanup_interval: "1800s"             # 会话缓存清理间隔
      eviction_policy: "lru"                # 会话缓存淘汰策略
      compression: false                    # 会话缓存压缩
    
    regex:
      enabled: true                         # 正则缓存启用状态
      ttl: "3600s"                          # 正则缓存TTL
      size: 100                             # 正则缓存大小
      cleanup_interval: "1800s"             # 正则缓存清理间隔
      eviction_policy: "lru"                # 正则缓存淘汰策略
      compression: false                    # 正则缓存压缩
    
    auth:
      enabled: true                         # 认证缓存启用状态
      ttl: "1800s"                          # 认证缓存TTL
      size: 500                             # 认证缓存大小
      cleanup_interval: "900s"              # 认证缓存清理间隔
      eviction_policy: "lru"                # 认证缓存淘汰策略
      compression: false                    # 认证缓存压缩
    
    rate:
      enabled: true                         # 限流缓存启用状态
      ttl: "300s"                           # 限流缓存TTL
      size: 1000                            # 限流缓存大小
      cleanup_interval: "300s"              # 限流缓存清理间隔
      eviction_policy: "lru"                # 限流缓存淘汰策略
      compression: false                    # 限流缓存压缩
  
  # 缓存键前缀配置
  key_prefixes:
    proxy_list: "proxy:list"                # 代理列表缓存键前缀
    proxy_status: "proxy:status:"           # 代理状态缓存键前缀
    user_session: "user:session:"           # 用户会话缓存键前缀
    rate_limit: "rate:limit:"               # 限流缓存键前缀
    dns_query: "dns:query:"                 # DNS查询缓存键前缀
    auth_token: "auth:token:"               # 认证令牌缓存键前缀
  
  # 缓存策略配置
  policies:
    # 缓存预热策略
    warmup:
      enabled: false                        # 是否启用预热
      on_startup: false                     # 启动时预热
      sources: []                           # 预热数据源
      batch_size: 100                       # 批处理大小: >=1

    # 缓存失效策略
    invalidation:
      strategy: "ttl"                       # 失效策略: ttl, manual, event
      events: []                            # 触发失效的事件
      patterns: []                          # 失效模式

    # 缓存同步策略
    sync:
      enabled: false                        # 是否启用同步
      mode: "master"                        # 同步模式: master, slave, cluster
      interval: "60s"                       # 同步间隔

    # 缓存压缩策略
    compression:
      enabled: false                        # 是否启用压缩
      algorithm: "gzip"                     # 压缩算法: gzip, zlib, lz4
      level: 6                              # 压缩级别: 1-9
      min_size: 1024                        # 最小压缩大小: >=1
  
  # 缓存存储配置
  storage:
    type: "memory"                          # 存储类型: memory, redis, file, hybrid
    
    # 内存缓存配置
    memory:
      max_size: 10000                       # 最大条目数: >=1
      max_memory: "512MB"                   # 最大内存使用
      eviction_policy: "lru"                # 淘汰策略: lru, lfu, fifo, random
      shards: 16                            # 分片数: >=1
    
    # Redis缓存配置
    redis:
      address: "localhost:6379"             # Redis地址
      password: ""                          # Redis密码
      database: 0                           # Redis数据库: 0-15
      pool_size: 10                         # 连接池大小: >=1
      max_retries: 3                        # Redis连接失败时的最大重试次数: >=0
      dial_timeout: "5s"                    # 连接超时
    
    # 文件缓存配置
    file:
      directory: "./cache"                  # 缓存目录
      max_file_size: "100MB"                # 最大文件大小
      max_files: 1000                       # 最大文件数: >=1
      compression: true                     # 是否压缩
    
    # 混合缓存配置
    hybrid:
      l1: "memory"                          # L1缓存类型: memory
      l2: "redis"                           # L2缓存类型: redis, file
      l1_size: 1000                         # L1缓存大小: >=1
      l2_size: 10000                        # L2缓存大小: >=1

# ============================================================================
# 日志配置 (Logging Configuration)
# ============================================================================
logging:
  level: "debug"                            # 日志级别: debug, info, warn, error, fatal
  format: "text"                            # 日志格式: json, text
  file: "./logs/flexproxy.log"              # 日志文件路径
  max_size: 100                             # 日志文件最大大小(MB): >=1
  max_backups: 10                           # 最大备份文件数: >=0

  # 命令行参数对应的配置选项
  output_file: ""                           # 日志输出文件路径，对应 -o, --output 参数
  max_age: 30                               # 日志文件最大保存天数: >=1
  time_format: "2006-01-02T15:04:05.000Z07:00" # 时间格式

# ============================================================================
# 监控配置 (Monitoring Configuration)
# ============================================================================
monitoring:
  path: "/metrics"                          # 指标路径

  # 指标配置
  metrics:
    request_duration: "histogram"           # 请求持续时间指标类型
    request_count: "counter"                # 请求计数指标类型
    proxy_status: "gauge"                   # 代理状态指标类型
    cache_hit_rate: "gauge"                 # 缓存命中率指标类型

  # 标签配置
  labels:
    service: "flexproxy"                    # 服务标签
    version: "1.0.0"                        # 版本标签
    environment: "production"               # 环境标签

# ============================================================================
# 安全配置 (Security Configuration)
# ============================================================================
security:

  # 认证配置
  auth:
    type: "basic"                           # 认证类型: none, basic, bearer, apikey
    token_expiry: "24h"                     # Token过期时间

  # 加密配置
  encryption:
    algorithm: "aes256"                     # 加密算法: aes256, rsa
    key_length: 32                          # 密钥长度: >=16

  # TLS配置
  tls:
    enabled: false                          # 是否启用TLS
    cert_file: ""                           # 证书文件路径
    key_file: ""                            # 私钥文件路径
    min_version: "1.2"                      # 最小TLS版本
    max_version: "1.3"                      # 最大TLS版本

# ============================================================================
# 限流配置 (Rate Limiting Configuration)
# ============================================================================
rate_limiting:
  algorithm: "token_bucket"                 # 限流算法: token_bucket, leaky_bucket, fixed_window, sliding_window
  rate: 200                                 # 限流速率(请求/秒): >=1
  burst: 200                                # 突发容量: >=1
  window: "1m"                              # 时间窗口
  cleanup_period: "5m"                      # 清理周期

# ============================================================================
# DNS服务配置 (DNS Service Configuration)
# ============================================================================
dns_service:

  # 查询配置
  lookup_mode: "system"                     # DNS查找模式: system, custom, hybrid
  timeout: "5s"                             # DNS查询超时时间
  retries: 3                                # DNS查询失败时的重试次数: >=0

  # 缓存配置
  cache:
    enabled: true                           # 是否启用DNS缓存
    ttl: "300s"                             # DNS缓存TTL (生存时间)
    cleanup_interval: "600s"                # 缓存清理间隔
    max_size: 1000                          # 最大缓存条目数: >=1

  # 服务器配置
  servers:
    # 主DNS服务器
    primary:
      - server: "*******:53"               # 服务器地址 (必需)
        protocol: "udp"                     # 协议: udp, tcp, doh, dot
        timeout: 5000                       # 超时时间(毫秒): 1000-30000
        priority: 1                         # 优先级: 0-100
        tags: ["cloudflare", "primary"]     # 标签

      - server: "*******:53"
        protocol: "udp"
        timeout: 5000
        priority: 2
        tags: ["google", "backup"]

    # 备用DNS服务器
    secondary:
      - server: "*******:53"
        protocol: "udp"
        timeout: 5000
        priority: 1
        tags: ["google", "secondary"]

      - server: "*******:53"
        protocol: "udp"
        timeout: 5000
        priority: 2
        tags: ["cloudflare", "secondary"]

    # 回退策略
    fallback: "system"                      # 当所有自定义DNS失败时的回退策略: system, none

  # IP版本优先级
  ip_version_priority: "ipv4"               # IP协议版本优先级: ipv4, ipv6, dual

  # HTTP代理DNS配置
  http_proxy_dns: false                     # 是否启用HTTP代理DNS（兼容性字段）
  http_proxy_dns_config: ""                 # HTTP代理DNS配置：
                                            # 映射格式: "host1=ip1,host2=ip2"
                                            # DNS服务器格式: "dns.google.com" 或 "https://dns.google.com/dns-query"
                                            # 示例:
                                            # - "example.com=*******,test.com=*******"  # 主机名映射
                                            # - "*******:53"                             # UDP DNS服务器
                                            # - "https://dns.google.com/dns-query"       # DoH服务器

  # 反向DNS查询配置
  reverse_lookup:
    enabled: true                           # 是否启用反向DNS查询
    mode: "dns"                             # 反向查询模式: no, dns, file
    source: ""                              # 当mode为file时的文件路径

    # 反向DNS查询模式说明：
    # - no: 禁用反向DNS查询
    # - dns: 使用系统DNS进行反向查询
    # - file: 从文件加载IP到域名的映射关系
    #
    # file模式文件格式要求：
    # 1. 每行格式：IP地址 域名（空格分隔）
    # 2. 支持注释行（以#开头）
    # 3. 忽略空行和格式错误的行
    # 4. 示例文件内容：
    #    # 公共DNS服务器
    #    ******* dns.google
    #    ******* one.one.one.one
    #
    #    # 本地网络设备
    #    127.0.0.1 localhost
    #    *********** router.local
    #
    #    # 示例网站
    #    ************* example.com
    #
    # 注意：文件修改后会自动重新加载（热重载）

# ============================================================================
# 超时配置 (Timeouts Configuration)
# ============================================================================
timeouts:
  # 网络超时
  network:
    connect: "15s"                          # 连接超时
    read: "30s"                             # 读取超时
    write: "30s"                            # 写入超时

    # 命令行参数对应的配置选项
    connection_timeout: "30s"               # 连接超时时间，对应 -t, --timeout 参数
    idle: "120s"                            # 空闲超时

  # DNS超时
  dns:
    query: "5s"                             # DNS查询超时
    resolve: "10s"                          # DNS解析超时

  # 代理超时
  proxy:
    retry: "1s"                             # 代理操作重试的间隔时间
    max_retry: "30s"                        # 代理操作重试的最大间隔时间
    health_check: "10s"                     # 健康检查超时
    cooldown: "60s"                         # 代理冷却时间

  # 监控超时
  monitoring:
    interval: "30s"                         # 监控间隔
    collection: "5s"                        # 数据收集超时

  # 缓存超时
  cache:
    cleanup: "300s"                         # 缓存清理间隔

  # 动作超时
  action:
    default: "30s"                          # 默认动作超时
    request: "10s"                          # 请求动作超时
    bypass: "30s"                           # 绕过代理超时

# ============================================================================
# 端口配置 (Ports Configuration)
# 统一管理所有服务端口的配置节
# ============================================================================
ports:
  # 主要服务端口
  http: 9080                                # HTTP代理端口: 1-65535
  https: 9443                               # HTTPS代理端口: 1-65535
  socks: 1080                               # SOCKS代理端口: 1-65535

  # 管理和监控端口
  monitoring: 9090                          # 监控端口: 1-65535
  debug: 8081                               # 调试端口: 1-65535
  admin: 8082                               # 管理端口: 1-65535

  # 协议特定端口
  dns: 53                                   # DNS服务端口: 1-65535
  dhcp: 67                                  # DHCP服务端口: 1-65535

  # 端口范围配置
  ranges:
    # 动态端口分配范围
    dynamic_start: 32768                    # 动态端口起始: 1024-65535
    dynamic_end: 65535                      # 动态端口结束: 1024-65535

    # 保留端口范围
    reserved_start: 1                       # 保留端口起始: 1-1023
    reserved_end: 1023                      # 保留端口结束: 1-1023

    # 用户端口范围
    user_start: 1024                        # 用户端口起始: 1024-65535
    user_end: 32767                         # 用户端口结束: 1024-65535

  # 端口冲突检测
  conflict_detection:
    enabled: true                           # 是否启用冲突检测
    check_local: true                       # 检查本地端口占用
    exclude_ports: [22, 80, 443]            # 排除检测的端口
    auto_resolve: true                      # 自动解决冲突
    fallback_ports: [8090, 8091, 8092]     # 备用端口列表

# ============================================================================
# 代理检查配置 (Proxy Check Configuration)
# ============================================================================
proxy_check:
  enabled: false                            # 是否启用代理检查，对应 -c, --check 参数
  max_goroutines: 50                        # 最大协程数，对应 -g, --goroutine 参数
  country_codes: []                         # 仅检查特定国家代码，对应 --only-cc 参数
  # 示例：
  # country_codes: ["US", "CN", "JP"]       # 仅检查美国、中国、日本的代理

# ============================================================================
# 模块配置 (Modules Configuration)
# ============================================================================
modules:
  # 启用的模块列表
  enabled:
    - "server"                              # HTTP服务器模块
    - "proxy"                               # 代理模块
    - "cache"                               # 缓存模块
    - "logging"                             # 日志模块
    - "monitoring"                          # 监控模块
    - "security"                            # 安全模块
    - "dns_service"                         # DNS服务模块
    - "rate_limiting"                       # 限流模块

  # 禁用的模块列表
  disabled:
    - "rate_limiting"                       # 限流模块
    - "plugins"                             # 插件模块
    - "development"                         # 开发模块

  # 模块依赖关系
  dependencies:
    # 核心依赖 - 这些模块必须启用
    core:
      - "server"
      - "logging"

    # 模块依赖映射
    dependencies:
      proxy: ["server", "cache"]            # 代理模块依赖服务器和缓存模块
      monitoring: ["server"]                # 监控模块依赖服务器模块
      security: ["server"]                  # 安全模块依赖服务器模块
      dns_service: ["cache"]                # DNS服务依赖缓存模块

    # 可选依赖
    optional:
      proxy: ["dns_service"]                # 代理模块可选依赖DNS服务
      cache: ["monitoring"]                 # 缓存模块可选依赖监控

    # 冲突模块
    conflicts:
      development: ["security"]             # 开发模块与安全模块冲突

  # 模块启动顺序
  startup_order:
    # 启动阶段定义
    phases:
      - name: "core"                        # 核心阶段
        modules: ["logging", "cache"]       # 核心模块
        timeout: "30s"                      # 阶段超时
        required: true                      # 是否必需

      - name: "services"                    # 服务阶段
        modules: ["server", "dns_service"]  # 服务模块
        timeout: "60s"                      # 阶段超时
        required: true                      # 是否必需

      - name: "features"                    # 功能阶段
        modules: ["proxy", "security", "monitoring"] # 功能模块
        timeout: "90s"                      # 阶段超时
        required: false                     # 是否必需

    # 阶段间延迟
    phase_delay: "5s"                       # 阶段间延迟

    # 并行启动配置
    parallel:
      enabled: true                         # 是否启用并行启动
      max_workers: 4                        # 最大工作线程数: >=1
      timeout: "120s"                       # 并行启动超时

  # 模块生命周期配置
  lifecycle:
    # 启动超时
    startup_timeout: "120s"                 # 模块启动超时

    # 停止超时
    shutdown_timeout: "60s"                 # 模块停止超时

    # 重启策略
    restart_policy:
      enabled: true                         # 是否启用重启策略
      max_retries: 3                        # 模块重启失败时的最大重试次数: >=0
      retry_delay: "10s"                    # 模块重启重试的延迟时间
      backoff_multiplier: 2.0               # 模块重启重试延迟的退避倍数: >=1
      max_delay: "300s"                     # 模块重启重试的最大延迟时间

    # 优雅停止配置
    graceful_shutdown:
      enabled: true                         # 是否启用优雅停止
      timeout: "30s"                        # 优雅停止超时
      force_after: "60s"                    # 强制停止时间
      wait_for_connections: true            # 是否等待连接结束

  # 模块健康检查配置
  health_check:
    enabled: true                           # 是否启用健康检查
    interval: "30s"                         # 检查间隔
    timeout: "10s"                          # 检查超时

    # 健康检查端点
    endpoints:
      server: "/health"                     # 服务器健康检查端点
      proxy: "/proxy/health"                # 代理健康检查端点
      cache: "/cache/health"                # 缓存健康检查端点

    # 失败阈值
    failure_threshold: 3                    # 失败阈值: >=1

    # 成功阈值
    success_threshold: 2                    # 成功阈值: >=1

    # 自动重启
    auto_restart: true                      # 是否自动重启失败的模块

# ============================================================================
# 高级配置 (Advanced Configuration)
# ============================================================================
advanced:
  enabled: true                             # 是否启用高级功能

  # 错误恢复配置（高级功能，用于系统级错误恢复）
  error_recovery:
    max_retry_attempts: 3                   # 系统错误恢复的最大重试次数: >=0
    initial_retry_delay: "1s"               # 系统错误恢复的初始重试延迟时间
    max_retry_delay: "30s"                  # 系统错误恢复的最大重试延迟时间
    retry_multiplier: 2.0                   # 系统错误恢复重试延迟的倍数: >=1.0
    failure_threshold: 5                    # 触发熔断器的失败阈值: >=1
    success_threshold: 3                    # 恢复正常的成功阈值: >=1
    circuit_timeout: "60s"                  # 熔断器超时时间
    circuit_reset_timeout: "300s"           # 熔断器重置超时时间

  # 追踪配置
  tracing:
    enabled: true                           # 是否启用追踪
    hex_generator_length: 16                # 十六进制生成器长度: 8-64
    sequence_modulus: 10000                 # 序列模数: >=1000

  # 性能配置
  performance:
    worker_pool_size: 10                    # 工作池大小: >=1
    queue_size: 1000                        # 队列大小: >=1
    batch_size: 100                         # 批处理大小: >=1
    flush_interval: "5s"                    # 刷新间隔

  # 调试配置
  debug:
    enabled: false                          # 是否启用调试模式
    verbose_logging: false                  # 详细日志记录
    dump_requests: false                    # 转储请求
    dump_responses: false                   # 转储响应
    profile_enabled: false                  # 性能分析启用

# ============================================================================
# 路径配置 (Paths Configuration)
# ============================================================================
paths:
  base_dir: "./"                            # 基础目录
  config_dir: "./config"                    # 配置文件目录
  logs_dir: "./logs"                        # 日志文件目录
  data_dir: "./data"                        # 数据文件目录
  temp_dir: "./temp"                        # 临时文件目录
  backup_dir: "./backup"                    # 备份文件目录
  file_permission: 644                      # 文件权限 (八进制)
  dir_permission: 755                       # 目录权限 (八进制)

  # 文件扩展名配置
  extensions:
    config: ".yaml"                         # 配置文件扩展名
    log: ".log"                             # 日志文件扩展名
    data: ".db"                             # 数据文件扩展名
    temp: ".tmp"                            # 临时文件扩展名
    backup: ".bak"                          # 备份文件扩展名
    plugin: ".so"                           # 插件文件扩展名

# ============================================================================
# 系统配置 (System Configuration)
# ============================================================================
system:
  os_detection: false                       # 操作系统检测
  arch_detection: true                      # 架构检测

  # 信号处理配置
  signal_handling:
    graceful_shutdown: true                 # 优雅关闭
    shutdown_timeout: "30s"                 # 关闭超时时间
    signals: ["SIGTERM", "SIGINT", "SIGQUIT"] # 处理的信号列表

  # 资源限制配置
  limits:
    max_memory: "1GB"                       # 最大内存使用
    max_cpu_percent: 80                     # 最大CPU使用百分比: 1-100
    max_file_descriptors: 10000             # 最大文件描述符数: >=1
    max_goroutines: 10000                   # 最大协程数: >=1

# ============================================================================
# 协议配置 (Protocols Configuration)
# ============================================================================
protocols:
  # HTTP协议配置
  http:
    enabled: false                          # 是否启用HTTP协议
    version: "1.1"                          # HTTP版本: 1.0, 1.1, 2.0
    keep_alive: true                        # 保持连接
    compression: true                       # 启用压缩

  # HTTPS协议配置
  https:
    enabled: true                           # 是否启用HTTPS协议
    version: "1.1"                          # HTTPS版本: 1.0, 1.1, 2.0
    keep_alive: true                        # 保持连接
    compression: true                       # 启用压缩
    verify_ssl: true                        # SSL验证

  # SOCKS4协议配置
  socks4:
    enabled: false                          # 是否启用SOCKS4协议

  # SOCKS5协议配置
  socks5:
    enabled: false                          # 是否启用SOCKS5协议
    auth_required: false                    # 是否需要认证

  # DNS协议配置
  dns:
    udp: true                               # 启用UDP DNS
    tcp: false                              # 启用TCP DNS
    tls: false                              # 启用DNS over TLS
    https: false                            # 启用DNS over HTTPS
    doh: false                              # 启用DoH

# ============================================================================
# 插件配置 (Plugins Configuration)
# ============================================================================
plugins:
  enabled: false                            # 是否启用插件系统
  directory: "./plugins"                    # 插件目录
  auto_load: true                           # 自动加载插件

  # 可用插件配置
  available:
    - name: "example_plugin"                # 插件名称
      enabled: false                        # 是否启用插件
      config:                               # 插件配置
        param1: "value1"                    # 插件参数1
        param2: 42                          # 插件参数2

# ============================================================================
# 配置管理 (Configuration Management)
# ============================================================================
config_management:
  # 热重载配置
  hot_reload:
    enabled: true                               # 是否启用热重载 (默认: true)
    reload_interval: "60s"                      # 重载检查间隔 (默认: 30s)
    backup_on_reload: true                      # 重载时是否备份 (默认: true)
    rollback_on_error: true                     # 错误时是否回滚 (默认: true)
    debounce_delay: "2s"                        # 防抖延迟 (默认: 2s)
    max_reload_retries: 3                       # 最大重载重试次数 (默认: 3)

    # 风险评估配置
    risk_assessment:
      event_count_multiplier: 5.0               # 事件数量倍数阈值 (默认: 5.0)
      action_count_multiplier: 5.0              # 动作数量倍数阈值 (默认: 5.0)
      data_size_threshold: "1GB"                # 数据大小阈值 (默认: 1GB)
      max_directly_affected_services: 3         # 最大直接影响服务数 (默认: 3)
      max_indirectly_affected_services: 5       # 最大间接影响服务数 (默认: 5)
      min_security_score: 60                    # 最低安全评分 (默认: 60)
      security_warning_score: 80                # 安全警告评分 (默认: 80)
      max_performance_impact: 30                # 最大性能影响百分比 (默认: 30)

      # 自定义风险规则
      custom_risk_rules:
        - rule_id: "rule_001"                   # 规则ID
          name: "服务端口变更"                   # 规则名称
          pattern: "server.listen_address"      # 匹配模式
          risk_level: "high"                    # 风险等级: low/medium/high
          auto_reload: false                    # 是否允许自动重载
          description: "服务端口变更需要人工确认" # 规则描述
        - rule_id: "rule_002"
          name: "代理配置变更"
          pattern: "proxy.*"
          risk_level: "medium"
          auto_reload: true
          description: "代理配置变更影响中等"

    # 无人值守模式配置
    unattended_mode:
      enabled: false                            # 是否启用无人值守模式 (默认: false)
      auto_approve_threshold: "medium"          # 自动批准阈值: low/medium/high (默认: medium)
      manual_confirm_timeout: "300s"            # 手动确认超时时间 (默认: 300s)
      fallback_strategy: "auto_approve"         # 降级策略: auto_approve/auto_rollback/maintain_current/notify (默认: auto_approve)
      max_wait_time: "5m"                       # 最大等待时间 (默认: 5m)
      emergency_rollback: true                  # 紧急回滚开关 (默认: true)
      business_hours_only: false                # 仅在业务时间启用 (默认: false)
      business_hours_start: "09:00"             # 业务时间开始 (默认: 09:00)
      business_hours_end: "18:00"               # 业务时间结束 (默认: 18:00)
      business_hours_timezone: "Local"          # 业务时间时区 (默认: Local)
      notification_webhook: ""                  # 通知webhook地址 (可选)
      notification_retries: 3                   # 通知重试次数 (默认: 3)

  # 备份配置
  backup:
    enabled: true                               # 是否启用备份 (默认: true)
    backup_dir: "./backups"                     # 备份目录 (默认: ./backups)
    max_backups: 10                             # 最大备份数量 (默认: 10)
    compress: true                              # 是否压缩备份 (默认: true)
    auto_cleanup: true                          # 自动清理旧备份 (默认: true)
    retention_days: 30                          # 备份保留天数 (默认: 30)

# ============================================================================
# 开发配置 (Development Configuration)
# ============================================================================
development:
  enabled: false                            # 是否启用开发功能
  mode: "production"                        # 模式: development, testing, production
  hot_reload: false                         # 热重载

  # 测试配置
  testing:
    enabled: false                          # 是否启用测试模式
    mock_responses: false                   # 模拟响应
    test_data_dir: "./testdata"             # 测试数据目录

  # 性能分析配置
  profiling:
    enabled: false                          # 是否启用性能分析
    cpu_profile: false                      # CPU性能分析
    memory_profile: false                   # 内存性能分析
    block_profile: false                    # 阻塞性能分析
    mutex_profile: false                    # 互斥锁性能分析

# ============================================================================
# 动作序列配置 (Actions Configuration)
# ============================================================================
# 定义可重用的动作序列，供事件触发时执行
# 支持16种动作类型：log, banip, ban_domain, block_request, modify_request,
# modify_response, cache_response, script, retry_same, retry, banipdomain,
# save_to_pool, cache, request_url, null_response, bypass_proxy
actions:
  # 状态码200处理动作 - 触发器动作：使用相同代理重试1次
  status_200_handler:
    sequence:
      - type: "log"                         # 记录日志
        message: "检测到状态码200，触发器动作：使用相同代理重试 - URL: {{.url}}"
        level: "info"
      - type: "retry_same"                  # 触发器动作：使用相同代理重试
        retry_count: 1                      # 触发器重试次数：1次

  # 状态码304处理动作 - 触发器动作：使用不同代理重试3次
  status_304_handler:
    sequence:
      - type: "log"                         # 记录日志
        message: "检测到状态码304，触发器动作：使用不同代理重试 - URL: {{.url}}"
        level: "info"
      - type: "retry"                       # 触发器动作：使用不同代理重试
        retry_count: 3                      # 触发器重试次数：3次

  # 响应修改动作示例 - 展示增强后的 modify_response 功能
  response_modification_examples:
    sequence:
      # 基础响应头修改
      - type: "modify_response"
        headers:                            # 添加或修改响应头
          X-Proxy-Modified: "true"          # 添加自定义头部
          Cache-Control: "no-cache, no-store" # 修改缓存控制
          X-Frame-Options: "DENY"           # 添加安全头部
        remove_headers:                     # 删除响应头
          - "Server"                        # 删除服务器信息
          - "X-Powered-By"                  # 删除技术栈信息
        status_code: 200                    # 修改状态码
        auto_content_type: true             # 自动设置Content-Type

      # 高级响应体修改 - JSON格式
      - type: "modify_response"
        body_config:
          content: |                        # 响应体内容
            {
              "status": "success",
              "message": "Response modified by FlexProxy",
              "timestamp": "{{.timestamp}}",
              "original_url": "{{.url}}"
            }
          content_type: "application/json"  # 内容类型
          format: "json"                    # 内容格式
          encoding: "utf-8"                 # 字符编码
        headers:
          X-Content-Modified: "true"        # 标记内容已修改
          Access-Control-Allow-Origin: "*"  # CORS头部

      # 高级响应体修改 - XML格式
      - type: "modify_response"
        body_config:
          content: |                        # XML响应体
            <?xml version="1.0" encoding="UTF-8"?>
            <response>
              <status>success</status>
              <message>XML response modified</message>
              <url>{{.url}}</url>
            </response>
          content_type: "application/xml"   # XML内容类型
          format: "xml"                     # XML格式
          encoding: "utf-8"                 # UTF-8编码

      # 关键词智能修改示例
      - type: "modify_response"
        keyword_operations:
          headers:                          # 响应头关键词操作
            - operation: "add"              # 操作类型：添加
              pattern: "X-Security-*"       # 匹配模式
              new_value: "enabled"          # 新值
            - operation: "replace"          # 操作类型：替换
              pattern: "Cache-Control"      # 匹配头部名称
              old_value: "public"           # 旧值
              new_value: "private"          # 新值
          body:                             # 响应体关键词操作
            - operation: "replace"          # 操作类型：替换
              pattern: "error"              # 匹配模式
              replacement: "success"        # 替换值
              format: "json"                # 内容格式

      # 压缩编码处理示例
      - type: "modify_response"
        headers:
          Content-Encoding: "gzip"          # 设置gzip压缩
        body_config:
          content: "Compressed response content"
          format: "text"
          encoding: "utf-8"
        # 系统会自动处理gzip压缩

      # 分块传输编码示例
      - type: "modify_response"
        headers:
          Transfer-Encoding: "chunked"      # 设置分块传输
        body_config:
          content: "Large response content that will be chunked"
          format: "text"
        # 系统会自动删除Content-Length并处理分块编码

# ============================================================================
# 事件配置 (Events Configuration)
# ============================================================================
# 定义各种触发条件和对应的处理动作
# 支持的触发器类型：status, body, max_request_time, conn_time_out, min_request_time,
# url, domain, combined, javascript, request_body, request_header, response_header
events:
  # 状态码200触发事件 - 触发器动作：使用相同代理重试1次
  - name: "status_200_trigger"              # 事件名称
    enable: true                            # 是否启用事件
    trigger_type: "status"                  # 触发器类型：状态码触发
    process_stage: "post_header"            # 处理阶段：响应头处理后
    priority: 10                            # 优先级
    conditions:                             # 条件定义
      - name: "status_200_condition"        # 条件名称
        enable: true                        # 是否启用条件
        status_codes:                       # 状态码条件
          codes: [200]                      # 状态码列表
          relation: "or"                    # 关系：or
    matches:                                # 匹配规则
      - name: "status_200_match"            # 匹配名称
        enable: true                        # 是否启用匹配
        conditions: ["status_200_condition"] # 匹配的条件
        actions:                            # 执行的动作
          - type: "log"                     # 动作类型：日志记录
            message: "检测到状态码200，触发器动作：相同代理重试"
            level: "info"
          - type: "retry_same"              # 触发器动作类型：相同代理重试
            retry_count: 1                  # 触发器重试次数：1次

  # 状态码304触发事件 - 触发器动作：使用不同代理重试3次
  - name: "status_304_trigger"              # 事件名称
    enable: true                            # 是否启用事件
    trigger_type: "status"                  # 触发器类型：状态码触发
    process_stage: "post_header"            # 处理阶段：响应头处理后
    priority: 20                            # 优先级（高于200状态码处理）
    conditions:                             # 条件定义
      - name: "status_304_condition"        # 条件名称
        enable: true                        # 是否启用条件
        status_codes:                       # 状态码条件
          codes: [304]                      # 状态码列表
          relation: "or"                    # 关系：or
    matches:                                # 匹配规则
      - name: "status_304_match"            # 匹配名称
        enable: true                        # 是否启用匹配
        conditions: ["status_304_condition"] # 匹配的条件
        actions:                            # 执行的动作
          - type: "log"                     # 动作类型：日志记录
            message: "检测到状态码304，触发器动作：不同代理重试"
            level: "info"
          - type: "retry"                   # 触发器动作类型：不同代理重试
            retry_count: 3                  # 触发器重试次数：3次
