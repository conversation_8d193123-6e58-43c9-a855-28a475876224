package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"flexproxy/internal/config"
	"flexproxy/internal/config/hotreload"
)

// HotReloadDemo 演示FlexProxy热重载功能
func main() {
	fmt.Println("🚀 FlexProxy 热重载功能演示")
	fmt.Println("========================================")

	// 创建初始配置
	initialConfig := &config.Config{
		Global: config.GlobalConfig{
			Enable:              true,
			ProxyFile:           "./proxies.txt",
			IPRotationMode:      "sequential",
			MinProxyPoolSize:    1,
			MaxProxyFetchAttempts: 3,
			BlockedIPs:          []string{"********"},
			TrustedIPs:          []string{"127.0.0.1", "::1"},
		},
		Server: &config.ServerConfig{
			Host:          "127.0.0.1",
			ListenAddress: "127.0.0.1:8080",
			BufferSize:    4096,
		},
		Ports: &config.PortsConfig{
			HTTP:  8080,
			HTTPS: 8443,
			SOCKS: 1080,
		},
		Logging: &config.LoggingConfig{
			Level:  "info",
			Format: "json",
			File:   "./logs/flexproxy.log",
		},
	}

	fmt.Printf("📋 初始配置:\n")
	printConfig(initialConfig)

	// 创建热重载引擎
	fmt.Println("\n🔧 创建热重载引擎...")
	engine, err := hotreload.NewHotReloadEngine(initialConfig, nil, nil)
	if err != nil {
		log.Fatalf("创建热重载引擎失败: %v", err)
	}

	// 启动引擎
	fmt.Println("▶️  启动热重载引擎...")
	if err := engine.Start(); err != nil {
		log.Fatalf("启动热重载引擎失败: %v", err)
	}
	defer engine.Stop()

	fmt.Println("✅ 热重载引擎启动成功!")

	// 演示配置变更
	go demonstrateConfigChanges(engine)

	// 等待中断信号
	fmt.Println("\n⏳ 演示运行中... (按 Ctrl+C 退出)")
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	<-c

	fmt.Println("\n🛑 演示结束")
}

// demonstrateConfigChanges 演示各种配置变更
func demonstrateConfigChanges(engine *hotreload.HotReloadEngine) {
	time.Sleep(2 * time.Second)

	// 演示1: 全局配置变更
	fmt.Println("\n🔄 演示1: 全局配置变更")
	newConfig1 := &config.Config{
		Global: config.GlobalConfig{
			Enable:              false, // 修改启用状态
			ProxyFile:           "./new_proxies.txt", // 修改代理文件
			IPRotationMode:      "random", // 修改轮换模式
			MinProxyPoolSize:    1,
			MaxProxyFetchAttempts: 3,
			BlockedIPs:          []string{"********", "********"}, // 修改封禁IP
			TrustedIPs:          []string{"***********"}, // 修改信任IP
		},
		Server: &config.ServerConfig{
			Host:          "127.0.0.1",
			ListenAddress: "127.0.0.1:8080",
			BufferSize:    4096,
		},
		Ports: &config.PortsConfig{
			HTTP:  8080,
			HTTPS: 8443,
			SOCKS: 1080,
		},
		Logging: &config.LoggingConfig{
			Level:  "info",
			Format: "json",
			File:   "./logs/flexproxy.log",
		},
	}

	result1, err := engine.ProcessConfigChange(newConfig1)
	if err != nil {
		fmt.Printf("❌ 配置变更失败: %v\n", err)
	} else {
		fmt.Printf("✅ 配置变更成功! 变更路径数: %d, 耗时: %v\n", 
			len(result1.ChangedPaths), result1.Duration)
		printChangedPaths(result1.ChangedPaths)
	}

	time.Sleep(3 * time.Second)

	// 演示2: 服务器配置变更
	fmt.Println("\n🔄 演示2: 服务器配置变更")
	newConfig2 := &config.Config{
		Global: newConfig1.Global,
		Server: &config.ServerConfig{
			Host:          "0.0.0.0", // 修改主机地址
			ListenAddress: "0.0.0.0:9080", // 修改监听地址
			BufferSize:    8192, // 修改缓冲区大小
		},
		Ports: &config.PortsConfig{
			HTTP:  9080, // 修改HTTP端口
			HTTPS: 9443, // 修改HTTPS端口
			SOCKS: 1081, // 修改SOCKS端口
		},
		Logging: &config.LoggingConfig{
			Level:  "debug", // 修改日志级别
			Format: "text", // 修改日志格式
			File:   "./logs/new_flexproxy.log", // 修改日志文件
		},
	}

	result2, err := engine.ProcessConfigChange(newConfig2)
	if err != nil {
		fmt.Printf("❌ 配置变更失败: %v\n", err)
	} else {
		fmt.Printf("✅ 配置变更成功! 变更路径数: %d, 耗时: %v\n", 
			len(result2.ChangedPaths), result2.Duration)
		printChangedPaths(result2.ChangedPaths)
	}

	time.Sleep(3 * time.Second)

	// 演示3: 多配置同时变更
	fmt.Println("\n🔄 演示3: 多配置同时变更")
	newConfig3 := &config.Config{
		Global: config.GlobalConfig{
			Enable:              true, // 重新启用
			ProxyFile:           "./final_proxies.txt", // 再次修改代理文件
			IPRotationMode:      "quality", // 修改为质量模式
			MinProxyPoolSize:    1,
			MaxProxyFetchAttempts: 3,
			BlockedIPs:          []string{"********"}, // 修改封禁IP
			TrustedIPs:          []string{"127.0.0.1", "***********"}, // 修改信任IP
		},
		Server: &config.ServerConfig{
			Host:          "127.0.0.1", // 恢复本地主机
			ListenAddress: "127.0.0.1:8888", // 新的端口
			BufferSize:    16384, // 更大的缓冲区
		},
		Ports: &config.PortsConfig{
			HTTP:  8888,
			HTTPS: 8889,
			SOCKS: 8890,
		},
		Logging: &config.LoggingConfig{
			Level:  "warn", // 修改为警告级别
			Format: "json", // 恢复JSON格式
			File:   "./logs/final_flexproxy.log",
		},
		Monitoring: &config.MonitoringConfig{
			Path: "/metrics",
			Metrics: map[string]string{
				"requests_total": "counter",
				"response_time":  "histogram",
				"active_connections": "gauge",
			},
		},
		Security: &config.SecurityConfig{
			Auth: &config.AuthConfig{
				Type:        "bearer",
				TokenExpiry: "12h",
			},
		},
	}

	result3, err := engine.ProcessConfigChange(newConfig3)
	if err != nil {
		fmt.Printf("❌ 配置变更失败: %v\n", err)
	} else {
		fmt.Printf("✅ 配置变更成功! 变更路径数: %d, 耗时: %v\n", 
			len(result3.ChangedPaths), result3.Duration)
		printChangedPaths(result3.ChangedPaths)
	}

	// 显示最终配置
	fmt.Println("\n📋 最终配置:")
	finalConfig := engine.GetCurrentConfig()
	printConfig(finalConfig)

	fmt.Println("\n🎉 热重载演示完成!")
}

// printConfig 打印配置信息
func printConfig(cfg *config.Config) {
	fmt.Printf("  全局配置:\n")
	fmt.Printf("    启用状态: %v\n", cfg.Global.Enable)
	fmt.Printf("    代理文件: %s\n", cfg.Global.ProxyFile)
	fmt.Printf("    轮换模式: %s\n", cfg.Global.IPRotationMode)
	fmt.Printf("    封禁IP: %v\n", cfg.Global.BlockedIPs)
	fmt.Printf("    信任IP: %v\n", cfg.Global.TrustedIPs)

	if cfg.Server != nil {
		fmt.Printf("  服务器配置:\n")
		fmt.Printf("    主机: %s\n", cfg.Server.Host)
		fmt.Printf("    监听地址: %s\n", cfg.Server.ListenAddress)
		fmt.Printf("    缓冲区大小: %d\n", cfg.Server.BufferSize)
	}

	if cfg.Ports != nil {
		fmt.Printf("  端口配置:\n")
		fmt.Printf("    HTTP: %d\n", cfg.Ports.HTTP)
		fmt.Printf("    HTTPS: %d\n", cfg.Ports.HTTPS)
		fmt.Printf("    SOCKS: %d\n", cfg.Ports.SOCKS)
	}

	if cfg.Logging != nil {
		fmt.Printf("  日志配置:\n")
		fmt.Printf("    级别: %s\n", cfg.Logging.Level)
		fmt.Printf("    格式: %s\n", cfg.Logging.Format)
		fmt.Printf("    文件: %s\n", cfg.Logging.File)
	}

	if cfg.Monitoring != nil {
		fmt.Printf("  监控配置:\n")
		fmt.Printf("    路径: %s\n", cfg.Monitoring.Path)
		fmt.Printf("    指标数量: %d\n", len(cfg.Monitoring.Metrics))
	}

	if cfg.Security != nil && cfg.Security.Auth != nil {
		fmt.Printf("  安全配置:\n")
		fmt.Printf("    认证类型: %s\n", cfg.Security.Auth.Type)
		fmt.Printf("    令牌过期: %s\n", cfg.Security.Auth.TokenExpiry)
	}
}

// printChangedPaths 打印变更路径
func printChangedPaths(paths []hotreload.ConfigPath) {
	if len(paths) == 0 {
		fmt.Println("    无变更路径")
		return
	}

	fmt.Println("    变更路径:")
	for i, path := range paths {
		if i < 5 { // 只显示前5个路径
			fmt.Printf("      - %s\n", string(path))
		} else if i == 5 {
			fmt.Printf("      - ... (还有 %d 个路径)\n", len(paths)-5)
			break
		}
	}
}
